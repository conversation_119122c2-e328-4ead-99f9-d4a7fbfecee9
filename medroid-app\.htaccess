# Handle Flutter web routing
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /

  # If the requested resource exists, serve it
  RewriteCond %{REQUEST_FILENAME} -f [OR]
  RewriteCond %{REQUEST_FILENAME} -d
  RewriteRule ^ - [L]

  # Otherwise, serve index.html
  RewriteRule ^ index.html [L]
</IfModule>

# Enable CORS - Specific configuration for backend.medroid.ai
<IfModule mod_headers.c>
  # Allow CORS from specific domains
  SetEnvIf Origin "https?://(www\.)?(backend\.medroid\.ai|app\.medroid\.ai)$" CORS_ALLOW_ORIGIN=$0
  Header set Access-Control-Allow-Origin %{CORS_ALLOW_ORIGIN}e env=CORS_ALLOW_ORIGIN
  Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
  Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-TOKEN"
  Header set Access-Control-Allow-Credentials "true"

  # Handle preflight requests
  RewriteEngine On
  RewriteCond %{REQUEST_METHOD} OPTIONS
  RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"

  # Don't cache HTML and data
  ExpiresByType text/html "access plus 0 seconds"
  ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# Compress text files
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json
</IfModule>

# Security headers
<IfModule mod_headers.c>
  # Protect against XSS attacks
  Header set X-XSS-Protection "1; mode=block"

  # Prevent MIME-type sniffing
  Header set X-Content-Type-Options "nosniff"

  # Referrer policy
  Header set Referrer-Policy "strict-origin-when-cross-origin"

  # Content Security Policy - Adjust as needed
  # Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://download.agora.io https://kit.fontawesome.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://backend.medroid.ai; connect-src 'self' https://backend.medroid.ai wss://*.agora.io; font-src 'self' data:;"
</IfModule>

# Force HTTPS
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteCond %{HTTPS} off
  RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Allow Cross-Origin Resource Sharing (CORS) for fonts
<FilesMatch "\.(ttf|ttc|otf|eot|woff|woff2|font.css)$">
  <IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
  </IfModule>
</FilesMatch>

# Disable server signature
ServerSignature Off
