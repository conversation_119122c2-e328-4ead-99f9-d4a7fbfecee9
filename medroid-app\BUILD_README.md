# Medroid Build System

## 🚀 Automated Build Versioning

This project includes an automated build versioning system that helps track deployments and avoid caching issues.

### Features

- **Automatic Version Increment**: Each build increments the patch version (1.0.1 → 1.0.2 → 1.0.3)
- **Build Date Tracking**: Automatically updates the build date
- **Visual Version Display**: Shows version number in bottom-right corner of the app
- **Cache Busting**: Helps identify when new builds are deployed

### Build Scripts

#### For macOS/Linux:
```bash
./build_web.sh
```

#### For Windows:
```batch
build_web.bat
```

#### Manual Build:
```bash
# Increment version
dart run increment_build.dart

# Build web app
flutter build web
```

### Version Display

The version number appears in the bottom-right corner of the app:
- **Mobile**: Shows `v1.0.3`
- **Web**: Shows `v1.0.3 (2025-05-28)` with full date

### Files

- `lib/utils/build_info.dart` - Contains version information
- `increment_build.dart` - Script to increment version numbers
- `build_web.sh` - macOS/Linux build script
- `build_web.bat` - Windows build script

### Usage

1. **Before each deployment**, run the build script
2. **Version number will auto-increment** from 1.0.x to 1.0.(x+1)
3. **Upload the build/web folder** to your web server
4. **Users can see the new version** in the corner to confirm updates

### Removing Version Display

When ready for production, simply remove the `BuildVersionOverlay` from `main.dart`:

```dart
// Remove this wrapper:
return BuildVersionOverlay(
  showFullVersion: kIsWeb,
  child: responsiveChild,
);

// Replace with:
return responsiveChild;
```

### Current Version

The current build version is: **v1.0.3 (2025-05-28)**
