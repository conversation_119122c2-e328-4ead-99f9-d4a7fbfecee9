# Medroid Notification System

This document provides an overview of the Medroid notification system implementation, which supports both mobile and web platforms.

## Overview

The Medroid notification system is a comprehensive solution that enables sending push notifications to mobile devices and web browsers. It supports various notification types, scheduling, templating, analytics, and a powerful admin interface for managing all aspects of the notification system.

## Architecture

### Mobile Notifications
- Uses Firebase Cloud Messaging (FCM) for delivering notifications to iOS and Android devices
- Handles token registration, notification delivery, and tracking
- Supports rich notifications with images, actions, and deep links

### Web Notifications
- Implements browser-based push notifications using the Web Push API and Firebase
- Uses service workers to handle background notifications
- Supports offline notification delivery and interaction

### Backend
- Laravel-based API for notification management
- Scheduled notification processing
- Analytics tracking and reporting
- Template management

### Admin Interface
- Vue.js-based admin panel for notification management
- Real-time analytics dashboard
- Template editor
- Scheduled notification management

## Components

### Mobile App (Flutter)
- `NotificationService`: Handles FCM integration for mobile platforms
- `WebNotificationService`: Handles web platform notifications
- `NotificationBadge`: UI component for displaying notification count
- Service worker for handling background notifications

### Backend (Laravel)
- `NotificationController`: API endpoints for user notifications
- `NotificationManagementController`: Admin API for notification management
- `NotificationTemplateController`: API for template management
- `NotificationAnalyticsController`: API for analytics
- `NotificationSettingsController`: API for notification settings
- `NotificationService`: Core service for sending notifications
- `ProcessScheduledNotifications`: Command for processing scheduled notifications

### Admin Panel (Vue.js)
- `NotificationManager.vue`: Main component for notification management
- `NotificationForm.vue`: Form for creating/editing notifications
- `ScheduledNotifications.vue`: Component for managing scheduled notifications
- `NotificationTemplates.vue`: Component for managing templates
- `NotificationAnalytics.vue`: Component for viewing analytics
- `NotificationSettings.vue`: Component for configuring settings

## Features

- **Multi-platform Support**: Works on iOS, Android, and Web browsers
- **Rich Notifications**: Support for images, actions, and deep links
- **Scheduled Notifications**: Schedule notifications to be sent at a specific time
- **Templates**: Create and manage reusable notification templates
- **Analytics**: Track delivery, open, and click rates
- **Admin Interface**: Comprehensive UI for managing all aspects of notifications
- **Personalization**: Use variables to personalize notification content

## Implementation Details

### Mobile Implementation

The mobile implementation uses Firebase Cloud Messaging (FCM) for delivering notifications to iOS and Android devices. The `NotificationService` class handles token registration, notification delivery, and tracking.

```dart
// Example of how notifications are handled in the mobile app
class NotificationService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  Future<void> initialize() async {
    // Request permission
    NotificationSettings settings = await _firebaseMessaging.requestPermission();
    
    // Get FCM token
    String? token = await _firebaseMessaging.getToken();
    
    // Register token with backend
    if (token != null) {
      await registerDeviceToken(token);
    }
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background/terminated messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification clicks
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationClick);
  }
  
  // Other methods...
}
```

### Web Implementation

The web implementation uses the Web Push API and Firebase for delivering notifications to web browsers. The `WebNotificationService` class handles permission requests, token registration, and notification delivery.

```dart
// Example of how notifications are handled in the web app
class WebNotificationService {
  final ValueNotifier<int> unreadCountNotifier = ValueNotifier<int>(0);
  
  Future<void> initialize() async {
    // Set up polling for new notifications
    _setupPolling();
  }
  
  void _setupPolling() {
    // Poll every 30 seconds for new notifications
    Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkForNewNotifications();
    });
  }
  
  // Other methods...
}
```

### Backend Implementation

The backend implementation uses Laravel for handling notification management, scheduling, and delivery. The `NotificationService` class is responsible for sending notifications to users.

```php
// Example of how notifications are sent from the backend
class NotificationService
{
    public function sendPushNotification(User $user, string $title, string $body, string $type, array $data = [])
    {
        // Create notification record
        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => $title,
            'body' => $body,
            'type' => $type,
            'data' => $data,
        ]);
        
        // Get device tokens
        $deviceTokens = DeviceToken::where('user_id', $user->id)->pluck('token')->toArray();
        
        // Send to each device
        foreach ($deviceTokens as $token) {
            // Send notification using Firebase
            // ...
        }
        
        return true;
    }
    
    // Other methods...
}
```

## Setup Instructions

### Mobile Setup

1. Configure Firebase in your Flutter project
2. Add the required permissions to your app's manifest files
3. Initialize the notification service in your app's main.dart file

### Web Setup

1. Configure Firebase in your web project
2. Add the firebase-messaging-sw.js service worker file to your web directory
3. Initialize the web notification service in your app

### Backend Setup

1. Run the migrations to create the required database tables:
   ```
   php artisan migrate
   ```

2. Add the scheduled command to your server's crontab:
   ```
   * * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
   ```

3. Configure Firebase credentials in your .env file:
   ```
   FIREBASE_SERVER_KEY=your_server_key
   FIREBASE_API_KEY=your_api_key
   FIREBASE_PROJECT_ID=your_project_id
   FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   FIREBASE_APP_ID=your_app_id
   ```

## Usage Examples

### Sending a Notification

```php
// Example of sending a notification from a controller
public function sendNotification(Request $request)
{
    $user = Auth::user();
    
    $this->notificationService->sendPushNotification(
        $user,
        'New Message',
        'You have a new message from Dr. Smith',
        'message',
        [
            'conversation_id' => 123,
            'sender_id' => 456
        ]
    );
    
    return response()->json(['message' => 'Notification sent']);
}
```

### Creating a Scheduled Notification

```php
// Example of creating a scheduled notification
public function scheduleNotification(Request $request)
{
    $scheduledAt = Carbon::now()->addHours(2);
    
    ScheduledNotification::create([
        'title' => 'Appointment Reminder',
        'body' => 'Your appointment with Dr. Smith is tomorrow at 10:00 AM',
        'type' => 'appointment',
        'recipients' => [1, 2, 3], // User IDs
        'data' => [
            'appointment_id' => 123
        ],
        'scheduled_at' => $scheduledAt,
        'status' => 'scheduled'
    ]);
    
    return response()->json(['message' => 'Notification scheduled']);
}
```

## Troubleshooting

### Common Issues

1. **Notifications not being delivered**:
   - Check that Firebase is properly configured
   - Verify that the device token is registered correctly
   - Check that the user has granted notification permissions

2. **Web notifications not working**:
   - Ensure the service worker is properly registered
   - Check that the browser supports web push notifications
   - Verify that the user has granted notification permissions

3. **Scheduled notifications not being sent**:
   - Check that the cron job is running correctly
   - Verify that the scheduled_at time is in the correct format
   - Check the logs for any errors

## Further Reading

For more detailed information, refer to the following resources:

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Web Push API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)
- [Laravel Scheduling Documentation](https://laravel.com/docs/scheduling)
- [Full Notification System Documentation](medroid_app_backend/NOTIFICATIONS.md)
