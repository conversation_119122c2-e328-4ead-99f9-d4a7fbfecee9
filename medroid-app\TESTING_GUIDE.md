# 🧪 Agora Video Call Testing Guide

## 📋 Pre-Test Setup

### 1. Run the App
```bash
cd medroid-app
flutter run -d chrome --verbose
```

### 2. Prepare Two Browser Windows/Tabs
- **Window 1**: Patient login
- **Window 2**: Provider login (or incognito mode)

## 🔍 What to Look For

### **Debug Console Output**
When testing, watch for these key log messages:

```
=== AGORA SESSION DATA ===
Channel: medroid-[hash]
Patient UID: 1000+
Provider UID: 2000+
User is [Patient/Provider]
========================

=== AGORA DEBUG STATE ===
Phase: Channel Joined Successfully
Local UID: [should match user role]
Remote UID: [should be opposite role when other joins]
========================
```

### **Debug Overlay (Visible in Debug Mode)**
Look for a black overlay in top-left showing:
- Role: Patient/Provider
- Local UID: 1000+ (Patient) or 2000+ (Provider)
- Remote UID: null initially, then other user's UID
- Channel: medroid-[hash]
- Initialized: true

## 🎯 Step-by-Step Testing

### **Step 1: Create Test Appointment**
1. Login as Provider
2. Create an appointment with a patient
3. Note the appointment ID

### **Step 2: Test Patient Login**
1. **Open Window 1** (Patient)
2. Login as the patient for the appointment
3. Navigate to video consultation
4. **Expected Results**:
   - Debug overlay shows "Role: Patient"
   - Local UID in 1000+ range
   - Remote UID: null
   - Local video appears in small PiP window (bottom-right)
   - Main screen shows "Waiting for other participant..."

### **Step 3: Test Provider Login**
1. **Open Window 2** (Provider)
2. Login as the provider
3. Navigate to same video consultation
4. **Expected Results**:
   - Debug overlay shows "Role: Provider"
   - Local UID in 2000+ range
   - Remote UID: should show Patient's UID (1000+ range)
   - Local video appears in small PiP window (bottom-right)
   - Main screen should show Patient's video full-screen

### **Step 4: Verify Patient Window**
1. **Switch back to Window 1** (Patient)
2. **Expected Results**:
   - Remote UID now shows Provider's UID (2000+ range)
   - Main screen should show Provider's video full-screen
   - Local video still in PiP window

## ✅ Success Criteria

### **Video Positioning**
- ✅ **Local Video**: Small PiP window (100x140px, bottom-right)
- ✅ **Remote Video**: Full-screen background
- ✅ **Controls**: Always visible at bottom

### **UID Validation**
- ✅ **Patient Local UID**: 1000 + patient_id
- ✅ **Provider Local UID**: 2000 + provider_id
- ✅ **Remote UID**: Shows other participant's UID
- ✅ **No UID Collision**: Local ≠ Remote

### **User Experience**
- ✅ **Join Sequence**: Patient waits → Provider joins → Both see each other
- ✅ **Video Quality**: Clear video streams
- ✅ **Controls Work**: Mute, camera toggle, end call
- ✅ **No Duplicates**: Each user appears only once

## 🚨 Common Issues to Check

### **Issue 1: Both Users See Same Video**
**Symptoms**: Both users see the same video stream
**Debug**: Check if Local UID = Remote UID in debug overlay
**Cause**: UID collision or incorrect role assignment

### **Issue 2: No Remote Video**
**Symptoms**: Remote UID stays null, no video appears
**Debug**: Check console for "Remote user joined" messages
**Possible Causes**:
- Network connectivity issues
- Token expiration
- Different channels
- Firewall blocking WebRTC

### **Issue 3: Wrong Video Positioning**
**Symptoms**: Videos in wrong positions (both full-screen or both PiP)
**Debug**: Check if _remoteUid is properly set
**Cause**: Event handler not triggering correctly

### **Issue 4: Local Video Not Showing**
**Symptoms**: PiP window empty or shows placeholder
**Debug**: Check "Building local video with UID: X" messages
**Cause**: Camera permissions or incorrect local UID

## 📊 Testing Checklist

### **Before Starting**
- [ ] App builds without errors
- [ ] Debug mode enabled (debug overlay visible)
- [ ] Two browser windows/devices ready
- [ ] Valid appointment created

### **Patient Test**
- [ ] Patient can login successfully
- [ ] Video consultation screen loads
- [ ] Debug overlay shows "Role: Patient"
- [ ] Local UID in 1000+ range
- [ ] Local video appears in PiP
- [ ] Waiting message displayed
- [ ] Console shows proper session data

### **Provider Test**
- [ ] Provider can login successfully
- [ ] Joins same video consultation
- [ ] Debug overlay shows "Role: Provider"
- [ ] Local UID in 2000+ range
- [ ] Local video appears in PiP
- [ ] Remote video shows Patient full-screen
- [ ] Console shows "Remote user joined"

### **Cross-Verification**
- [ ] Patient window now shows Provider's video
- [ ] Both debug overlays show correct Remote UIDs
- [ ] No duplicate video streams
- [ ] Controls work on both sides
- [ ] Audio works bidirectionally

## 🔧 Debugging Commands

### **Console Commands to Run**
```javascript
// Check Agora global objects
console.log(window.agoraWebRTC);
console.log(window.localTracks_medroid);

// Check video elements
console.log(document.querySelectorAll('video'));
console.log(document.getElementById('local-video-container'));
console.log(document.getElementById('remote-video-container'));
```

### **Network Tab Checks**
- Look for WebSocket connections to Agora servers
- Check for successful token requests to your backend
- Verify no CORS errors

## 📝 Test Results Template

```
=== TEST RESULTS ===
Date: [DATE]
Flutter Version: [VERSION]
Browser: Chrome [VERSION]

Patient Test:
- Login: ✅/❌
- Local UID: [VALUE] (Expected: 1000+)
- Local Video: ✅/❌
- Debug Overlay: ✅/❌

Provider Test:
- Login: ✅/❌
- Local UID: [VALUE] (Expected: 2000+)
- Local Video: ✅/❌
- Remote Video: ✅/❌
- Debug Overlay: ✅/❌

Cross-Verification:
- Patient sees Provider: ✅/❌
- Provider sees Patient: ✅/❌
- UID Ranges Correct: ✅/❌
- No Duplicates: ✅/❌

Issues Found:
[List any issues]

Console Errors:
[Copy any error messages]
==================
```

Run through this testing guide and let me know the results. The debug overlay and console logs will give us detailed information about what's working and what needs further adjustment.
