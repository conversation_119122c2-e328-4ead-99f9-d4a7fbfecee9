# This file configures the analyzer to use the lint rule set from `package:flutter_lints`
include: package:flutter_lints/flutter.yaml

# For lint rules and documentation, see http://dart-lang.github.io/linter/lints.
linter:
  rules:
    # Style rules
    - camel_case_types
    - library_names
    - avoid_empty_else
    - avoid_print
    - avoid_unnecessary_containers
    - avoid_web_libraries_in_flutter
    - no_logic_in_create_state
    - prefer_const_constructors
    - prefer_const_declarations
    - prefer_single_quotes
    - sized_box_for_whitespace
    - use_key_in_widget_constructors

# Analysis options
analyzer:
  errors:
    # Treat missing required parameters as a warning (not a hint)
    missing_required_param: warning
    # Treat missing returns as a warning (not a hint)
    missing_return: warning
    # Allow having TODOs in the code
    todo: ignore
  exclude:
    - "lib/generated_plugin_registrant.dart"