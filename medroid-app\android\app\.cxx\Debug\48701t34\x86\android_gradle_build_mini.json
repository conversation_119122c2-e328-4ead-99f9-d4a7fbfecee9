{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\laragon\\www\\medroid-app\\medroid-app\\android\\app\\.cxx\\Debug\\48701t34\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\laragon\\www\\medroid-app\\medroid-app\\android\\app\\.cxx\\Debug\\48701t34\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}