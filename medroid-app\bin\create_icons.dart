import 'dart:io';

void main() async {
  // Create the icons directory if it doesn't exist
  final iconsDir = Directory('assets/icons');
  if (!await iconsDir.exists()) {
    await iconsDir.create(recursive: true);
  }

  // Create a simple pink circle for Medroid icon
  await createSimpleIcon(
    'assets/icons/medroid.png',
    [0xEC, 0x48, 0x99, 0xFF], // Pink color
    'M',
  );

  // Create a simple orange circle for Instagram icon
  await createSimpleIcon(
    'assets/icons/instagram.png',
    [0xF5, 0x60, 0x40, 0xFF], // Instagram orange
    'IG',
  );

  // Create a simple black circle for TikTok icon
  await createSimpleIcon(
    'assets/icons/tiktok.png',
    [0x00, 0x00, 0x00, 0xFF], // Black
    'TT',
  );

  // Create a simple blue circle for Social icon
  await createSimpleIcon(
    'assets/icons/social.png',
    [0x42, 0x67, 0xB2, 0xFF], // Facebook blue
    'S',
  );

  print('Icons created successfully!');
}

// This is a very simple PNG file with a colored square
Future<void> createSimpleIcon(String path, List<int> color, String text) async {
  // Create a simple 24x24 PNG file
  // PNG header
  final List<int> pngData = [
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // "IHDR"
    0x00, 0x00, 0x00, 0x18, // Width: 24 pixels
    0x00, 0x00, 0x00, 0x18, // Height: 24 pixels
    0x08, // Bit depth: 8 bits per sample
    0x06, // Color type: RGBA
    0x00, // Compression method: deflate
    0x00, // Filter method: adaptive
    0x00, // Interlace method: no interlace
    0x31, 0x7D, 0x0E, 0x4A, // CRC

    // IDAT chunk with minimal data (a colored square)
    0x00, 0x00, 0x00, 0x1D, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // "IDAT"
    0x78, 0x9C, 0x63, 0x60, 0x60, 0x60, // zlib header
  ];

  // Add color data (simplified - just a colored square)
  for (int i = 0; i < 24; i++) {
    pngData.addAll(color);
  }

  // End of PNG
  pngData.addAll([
    0x06, 0x7E, 0x42, 0x3E, // CRC for IDAT
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // "IEND"
    0xAE, 0x42, 0x60, 0x82, // CRC for IEND
  ]);

  // Write to file
  final file = File(path);
  await file.writeAsBytes(pngData);
  print('Created $path');
}
