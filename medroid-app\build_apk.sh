#!/bin/bash

# <PERSON>ript to build APK with fixes for dependency issues

echo "Starting APK build process with fixes..."

# Step 1: Update dependencies in pubspec.yaml
echo "Updating dependencies in pubspec.yaml..."

# Create a temporary file with updated dependencies
cat > pubspec_temp.yaml << 'EOL'
name: medroid_app
description: Healthcare Social Feed & AI Clinical Assistant App

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and styling
  cupertino_icons: ^1.0.5
  cached_network_image: ^3.2.3
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0
  lottie: ^2.3.2
  google_fonts: ^4.0.4

  # State management
  flutter_bloc: ^8.1.2
  provider: ^6.0.5

  # Storage and preferences
  shared_preferences: ^2.1.1

  # Networking
  http: ^0.13.6
  dio: ^5.1.2

  # Utilities
  intl: ^0.18.1
  url_launcher: ^6.1.11
  image_picker: ^0.8.7+5
  path_provider: ^2.0.15
  share_plus: ^7.0.2
  connectivity_plus: ^4.0.1
  table_calendar: ^3.0.9
  flutter_markdown: ^0.6.15
  crypto: ^3.0.3

  # Location services
  geocoding: ^2.1.0
  geolocator: ^9.0.2
  flutter_typeahead: ^4.6.2

  # Media handling
  video_player: ^2.6.1
  chewie: ^1.5.0

  # Video consultation
  # JitsiMeet and WebRTC dependencies removed

  # Agora SDK for video consultations
  agora_rtc_engine: ^6.5.2
  permission_handler: ^10.2.0
  flutter_inappwebview: ^5.7.2+3

  # Audio recording
  record: ^4.4.4
  audioplayers: ^5.1.0

  # Payment processing
  flutter_stripe: ^9.5.0+1
  flutter_stripe_web: ^4.5.0

  # Firebase
  firebase_core: ^2.15.1
  firebase_messaging: ^14.6.7
  # Removed flutter_local_notifications to avoid build issues

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1

# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/subtle_pattern.png
    - assets/images/headache.png
    - assets/images/sleep.png
    - assets/images/weight.png
    - assets/images/skin.png
    - assets/images/appointment.png
    - assets/images/mental.png
    - assets/icons/
    - assets/animations/
    - assets/html/

  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
    - family: NotoSansCJK
      fonts:
        - asset: assets/fonts/NotoSansCJKjp-Regular.otf
          weight: 400
    - family: NotoSansSymbols
      fonts:
        - asset: assets/fonts/NotoSansSymbols-Regular.ttf
          weight: 400
EOL

# Replace the pubspec.yaml file
mv pubspec_temp.yaml pubspec.yaml

# Step 2: Update Android build.gradle
echo "Updating Android build.gradle..."

# Create a temporary file with updated build.gradle
cat > android/app/build.gradle.kts.temp << 'EOL'
plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.example.medroid_app"
    compileSdk = 35 // Updated to support plugins requiring SDK 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.medroid_app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23 // Updated to support record_android plugin
        targetSdk = 33
        versionCode = 1
        versionName = "1.0.0"
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
EOL

# Replace the build.gradle.kts file
mv android/app/build.gradle.kts.temp android/app/build.gradle.kts

# Step 3: Create a simplified NotificationService
echo "Creating simplified NotificationService..."

# Create a temporary file with simplified NotificationService
cat > lib/services/notification_service.dart.temp << 'EOL'
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:medroid_app/models/notification_model.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';

class NotificationService {
  final ApiService _apiService = ApiService();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // Stream controller for notification count
  final ValueNotifier<int> unreadCountNotifier = ValueNotifier<int>(0);

  // Stream for handling notification taps
  final Stream<RemoteMessage> onMessageOpenedApp =
      FirebaseMessaging.onMessageOpenedApp;

  // Initialize the notification service
  Future<void> initialize() async {
    // Skip initialization on web platform
    if (kIsWeb) {
      debugPrint('Skipping notification initialization on web platform');
      return;
    }

    // Firebase should already be initialized in main.dart

    // Request permission for iOS
    if (Platform.isIOS) {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
    }

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // We're not showing local notifications in this simplified version
      // but we still refresh the unread count
      _showLocalNotification(message);
      _refreshUnreadCount();
    });

    // Get the token and register it with the backend
    _getTokenAndRegister();

    // Refresh unread count
    _refreshUnreadCount();
  }

  // Get the FCM token and register it with the backend
  Future<void> _getTokenAndRegister() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _registerDeviceToken(token);
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _registerDeviceToken(newToken);
      });
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  // Register the device token with the backend
  Future<void> _registerDeviceToken(String token) async {
    try {
      final deviceType = Platform.isAndroid
          ? 'android'
          : Platform.isIOS
              ? 'ios'
              : 'web';

      await _apiService.post(
        Constants.deviceTokenEndpoint,
        {
          'token': token,
          'device_type': deviceType,
        },
      );
    } catch (e) {
      debugPrint('Error registering device token: $e');
    }
  }

  // Show a local notification - simplified version without flutter_local_notifications
  void _showLocalNotification(RemoteMessage message) {
    // In a real app, we would show a local notification here
    // But for this simplified version, we'll just log the notification
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      debugPrint(
          'Received notification: ${notification.title} - ${notification.body}');
      debugPrint('Notification data: $data');
    }
  }

  // Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> data) {
    final String type = data['type'] ?? '';
    debugPrint('Handling notification tap for type: $type');

    // Handle different notification types
    switch (type) {
      case 'appointment_reminder':
        // Navigate to appointment details
        break;
      case 'appointment_booked':
        // Navigate to appointment details
        break;
      case 'appointment_cancelled':
        // Navigate to appointment list
        break;
      default:
        // Navigate to notification list
        break;
    }
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications(
      {int limit = 20, int offset = 0}) async {
    try {
      final response = await _apiService.get(
        '${Constants.notificationsEndpoint}?limit=$limit&offset=$offset',
      );

      final List<NotificationModel> notifications = [];
      if (response != null && response['notifications'] != null) {
        for (final item in response['notifications']) {
          notifications.add(NotificationModel.fromJson(item));
        }
      }

      return notifications;
    } catch (e) {
      debugPrint('Error getting notifications: $e');
      return [];
    }
  }

  // Get unread notification count
  Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.get(Constants.notificationsUnreadCountEndpoint);
      final int count = response != null ? response['unread_count'] ?? 0 : 0;
      unreadCountNotifier.value = count;
      return count;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Refresh unread count
  Future<void> _refreshUnreadCount() async {
    await getUnreadCount();
  }

  // Mark a notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService
          .post('${Constants.notificationsEndpoint}/$notificationId/read', {});
      await _refreshUnreadCount();
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      await _apiService.post('${Constants.notificationsEndpoint}/read-all', {});
      unreadCountNotifier.value = 0;
      return true;
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      return false;
    }
  }
}
EOL

# Replace the NotificationService file
mv lib/services/notification_service.dart.temp lib/services/notification_service.dart

# Step 4: Update dependencies
echo "Running flutter pub get..."
flutter pub get

# Step 5: Build the APK
echo "Building APK..."
flutter build apk --release

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "APK built successfully!"
    echo "APK location: $(pwd)/build/app/outputs/flutter-apk/app-release.apk"
else
    echo "APK build failed. Please check the error messages above."
fi
