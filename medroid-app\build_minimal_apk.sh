#!/bin/bash

# <PERSON>ript to build a minimal APK with only essential features

echo "Starting minimal APK build process..."

# Step 1: Create a minimal version of pubspec.yaml
echo "Creating minimal pubspec.yaml..."

# Create a temporary file with minimal dependencies
cat > pubspec_temp.yaml << 'EOL'
name: medroid_app
description: Healthcare Social Feed & AI Clinical Assistant App

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and styling
  cupertino_icons: ^1.0.5
  cached_network_image: ^3.2.3
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0
  lottie: ^2.3.2
  google_fonts: ^4.0.4

  # State management
  flutter_bloc: ^8.1.2
  provider: ^6.0.5

  # Storage and preferences
  shared_preferences: ^2.1.1

  # Networking
  http: ^0.13.6
  dio: ^5.1.2

  # Utilities
  intl: ^0.18.1
  url_launcher: ^6.1.11
  image_picker: ^0.8.7+5
  path_provider: ^2.0.15
  share_plus: ^7.0.2
  connectivity_plus: ^4.0.1
  table_calendar: ^3.0.9
  flutter_markdown: ^0.6.15
  crypto: ^3.0.3

  # Location services
  geocoding: ^2.1.0
  geolocator: ^9.0.2
  flutter_typeahead: ^4.6.2

  # Media handling
  video_player: ^2.6.1
  chewie: ^1.5.0

  # Firebase
  firebase_core: ^2.15.1
  firebase_messaging: ^14.6.7

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1

# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/subtle_pattern.png
    - assets/images/headache.png
    - assets/images/sleep.png
    - assets/images/weight.png
    - assets/images/skin.png
    - assets/images/appointment.png
    - assets/images/mental.png
    - assets/icons/
    - assets/animations/
    - assets/html/

  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
    - family: NotoSansCJK
      fonts:
        - asset: assets/fonts/NotoSansCJKjp-Regular.otf
          weight: 400
    - family: NotoSansSymbols
      fonts:
        - asset: assets/fonts/NotoSansSymbols-Regular.ttf
          weight: 400
EOL

# Replace the pubspec.yaml file
mv pubspec_temp.yaml pubspec.yaml

# Step 2: Update Android build.gradle
echo "Updating Android build.gradle..."

# Create a temporary file with updated build.gradle
cat > android/app/build.gradle.kts.temp << 'EOL'
plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.example.medroid_app"
    compileSdk = 35 // Updated to support plugins requiring SDK 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.medroid_app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23 // Updated to support record_android plugin
        targetSdk = 33
        versionCode = 1
        versionName = "1.0.0"
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
EOL

# Replace the build.gradle.kts file
mv android/app/build.gradle.kts.temp android/app/build.gradle.kts

# Step 3: Create a minimal version of the app
echo "Creating minimal version of main.dart..."

# Create a temporary file with minimal main.dart
cat > lib/main.dart.temp << 'EOL'
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/providers/theme_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase for both web and mobile
  try {
    await Firebase.initializeApp();
    debugPrint('Firebase initialized successfully');
  } catch (e) {
    debugPrint('Error initializing Firebase: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final providers = [
      RepositoryProvider<AuthService>(
        create: (context) => AuthService(),
      ),
      RepositoryProvider<ApiService>(
        create: (context) => ApiService(),
      ),
    ];

    return MultiRepositoryProvider(
      providers: providers,
      child: ChangeNotifierProvider(
        create: (context) => ThemeProvider(),
        child: Consumer<ThemeProvider>(
          builder: (context, themeProvider, _) {
            return MaterialApp(
              title: 'Medroid',
              themeMode: themeProvider.themeMode,
              theme: ThemeData(
                primarySwatch: Colors.pink,
                visualDensity: VisualDensity.adaptivePlatformDensity,
                brightness: Brightness.light,
                fontFamily: 'NotoSans',
                textTheme: Typography.material2018()
                    .black
                    .apply(fontFamily: 'NotoSans'),
                primaryTextTheme: Typography.material2018()
                    .black
                    .apply(fontFamily: 'NotoSans'),
              ),
              darkTheme: ThemeData(
                primarySwatch: Colors.pink,
                visualDensity: VisualDensity.adaptivePlatformDensity,
                brightness: Brightness.dark,
                fontFamily: 'NotoSans',
                textTheme: Typography.material2018()
                    .white
                    .apply(fontFamily: 'NotoSans'),
                primaryTextTheme: Typography.material2018()
                    .white
                    .apply(fontFamily: 'NotoSans'),
              ),
              home: const HomeScreen(),
            );
          },
        ),
      ),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Medroid'),
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 100,
              width: 100,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [Color(0xFFEC4899), Color(0xFF8B5CF6)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x4DEC4899),
                    blurRadius: 12,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: const Center(
                child: Icon(
                  Icons.medical_services,
                  size: 50,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Medroid',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Healthcare Social Feed & AI Clinical Assistant',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 48),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFEC4899),
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text(
                'Start Chat',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
EOL

# Replace the main.dart file
mv lib/main.dart.temp lib/main.dart

# Step 4: Update dependencies
echo "Running flutter pub get..."
flutter pub get

# Step 5: Clean the build directory
echo "Cleaning build directory..."
flutter clean

# Step 6: Build the APK
echo "Building APK..."
flutter build apk --release

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "APK built successfully!"
    echo "APK location: $(pwd)/build/app/outputs/flutter-apk/app-release.apk"
else
    echo "APK build failed. Please check the error messages above."
fi
