from PIL import Image, ImageDraw

# Create a new 16x16 image with a subtle pattern
img = Image.new('RGBA', (16, 16), color=(255, 255, 255, 0))
draw = ImageDraw.Draw(img)

# Draw a subtle grid pattern
for x in range(0, 16, 4):
    for y in range(0, 16, 4):
        draw.rectangle([(x, y), (x+1, y+1)], fill=(200, 200, 200, 30))

# Save the image to a file
img.save('assets/images/subtle_pattern.png')

print("New pattern image created successfully!")
