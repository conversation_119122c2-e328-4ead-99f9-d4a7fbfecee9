import 'dart:io';

void main() {
  final file = File('lib/utils/build_info.dart');
  
  if (!file.existsSync()) {
    print('build_info.dart not found!');
    exit(1);
  }
  
  String content = file.readAsStringSync();
  
  // Extract current build number
  final buildNumberRegex = RegExp(r'buildNumber = "(\d+)\.(\d+)\.(\d+)"');
  final match = buildNumberRegex.firstMatch(content);
  
  if (match == null) {
    print('Could not find build number pattern!');
    exit(1);
  }
  
  final major = int.parse(match.group(1)!);
  final minor = int.parse(match.group(2)!);
  final patch = int.parse(match.group(3)!);
  
  // Increment patch version
  final newPatch = patch + 1;
  final newBuildNumber = '$major.$minor.$newPatch';
  
  // Get current date
  final now = DateTime.now();
  final dateString = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  
  // Replace build number and date
  content = content.replaceAll(
    buildNumberRegex,
    'buildNumber = "$newBuildNumber"',
  );
  
  content = content.replaceAll(
    RegExp(r'buildDate = "\d{4}-\d{2}-\d{2}"'),
    'buildDate = "$dateString"',
  );
  
  // Write back to file
  file.writeAsStringSync(content);
  
  print('Build number incremented to: $newBuildNumber');
  print('Build date updated to: $dateString');
}
