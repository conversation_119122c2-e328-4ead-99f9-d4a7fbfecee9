<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>API_KEY</key>
	<string>AIzaSyBq9WEYaQNrp1nKvOdXzR-zmWHsdHPwWhc</string>
	<key>GCM_SENDER_ID</key>
	<string>539973599625</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.medroid.medroidapp</string>
	<key>PROJECT_ID</key>
	<string>medroid-app-e58be</string>
	<key>STORAGE_BUCKET</key>
	<string>medroid-app-e58be.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:539973599625:ios:8548cf004fe0f4d55ba158</string>
</dict>
</plist>