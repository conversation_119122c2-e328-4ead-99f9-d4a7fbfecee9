import 'dart:convert';
import 'package:http/http.dart' as http;

// A simple Flutter script to test AI API integration directly
// Run with: dart run lib/ai_test.dart

class AiService {
  final String openAiApiKey =
      '********************************************************************************************************************************************************************';
  final String mistralApiKey = 'vZabYNGkXr4F3XNidMLxH9QjZsYSH6a7';

  Future<String> testMistralApi(String prompt) async {
    final url = Uri.parse('https://api.mistral.ai/v1/chat/completions');

    final data = {
      'model': 'mistral-medium',
      'messages': [
        {'role': 'user', 'content': prompt}
      ],
      'temperature': 0.7,
      'top_p': 0.9,
      'max_tokens': 1000
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $mistralApiKey',
      },
      body: jsonEncode(data),
    );

    if (response.statusCode == 200) {
      final result = jsonDecode(response.body);
      return result['choices'][0]['message']['content'];
    } else {
      return 'Error: ${response.body}';
    }
  }

  Future<String> testOpenAiApi(String prompt) async {
    final url = Uri.parse('https://api.openai.com/v1/chat/completions');

    final data = {
      'model': 'gpt-4o-mini',
      'messages': [
        {
          'role': 'system',
          'content': 'You are a helpful healthcare assistant.'
        },
        {'role': 'user', 'content': prompt}
      ],
      'temperature': 1.0,
      'max_tokens': 1000
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $openAiApiKey',
      },
      body: jsonEncode(data),
    );

    if (response.statusCode == 200) {
      final result = jsonDecode(response.body);
      return result['choices'][0]['message']['content'];
    } else {
      return 'Error: ${response.body}';
    }
  }
}

void main() async {
  final aiService = AiService();

  // Test prompt for health question
  const prompt =
      "I've been having a headache and dizziness for the past 3 days. What could this be?";

  print('Testing Mistral AI API...');
  try {
    final mistralResponse = await aiService.testMistralApi(prompt);
    print('Mistral Response: $mistralResponse\n');
  } catch (e) {
    print('Error testing Mistral API: $e\n');
  }

  print('Testing OpenAI API...');
  try {
    final openAiResponse = await aiService.testOpenAiApi(prompt);
    print('OpenAI Response: $openAiResponse\n');
  } catch (e) {
    print('Error testing OpenAI API: $e\n');
  }

  // Test for emergency detection
  const emergencyPrompt =
      "I'm having severe chest pain that radiates to my left arm and difficulty breathing.";

  print('Testing emergency detection with OpenAI...');
  try {
    final response = await aiService.testOpenAiApi(emergencyPrompt);
    print('Emergency Response: $response');
  } catch (e) {
    print('Error testing emergency detection: $e');
  }
}
