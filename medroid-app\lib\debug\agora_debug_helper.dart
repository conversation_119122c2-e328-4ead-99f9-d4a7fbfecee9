import 'package:flutter/material.dart';

/// Debug helper class for Agora video consultation issues
class AgoraDebugHelper {
  static void logVideoCallState({
    required String appointmentId,
    required bool isProvider,
    required String userName,
    int? localUid,
    int? remoteUid,
    String? channelName,
    bool? isAgoraInitialized,
    String? phase,
  }) {
    debugPrint('=== AGORA DEBUG STATE ===');
    debugPrint('Phase: ${phase ?? "Unknown"}');
    debugPrint('Appointment ID: $appointmentId');
    debugPrint('User: $userName (${isProvider ? "Provider" : "Patient"})');
    debugPrint('Local UID: $localUid');
    debugPrint('Remote UID: $remoteUid');
    debugPrint('Channel: $channelName');
    debugPrint('Agora Initialized: $isAgoraInitialized');
    debugPrint('========================');
  }

  static void logVideoRenderingIssue({
    required String issue,
    int? localUid,
    int? remoteUid,
    bool? isLocalVideoVisible,
    bool? isRemoteVideoVisible,
    String? additionalInfo,
  }) {
    debugPrint('=== VIDEO RENDERING ISSUE ===');
    debugPrint('Issue: $issue');
    debugPrint('Local UID: $localUid');
    debugPrint('Remote UID: $remoteUid');
    debugPrint('Local Video Visible: $isLocalVideoVisible');
    debugPrint('Remote Video Visible: $isRemoteVideoVisible');
    if (additionalInfo != null) {
      debugPrint('Additional Info: $additionalInfo');
    }
    debugPrint('============================');
  }

  static void logUidMismatch({
    required int? expectedLocalUid,
    required int? actualLocalUid,
    required int? expectedRemoteUid,
    required int? actualRemoteUid,
    required bool isProvider,
  }) {
    debugPrint('=== UID MISMATCH DETECTED ===');
    debugPrint('User Role: ${isProvider ? "Provider" : "Patient"}');
    debugPrint('Expected Local UID: $expectedLocalUid');
    debugPrint('Actual Local UID: $actualLocalUid');
    debugPrint('Expected Remote UID: $expectedRemoteUid');
    debugPrint('Actual Remote UID: $actualRemoteUid');
    debugPrint('Local UID Match: ${expectedLocalUid == actualLocalUid}');
    debugPrint('Remote UID Match: ${expectedRemoteUid == actualRemoteUid}');
    debugPrint('=============================');
  }

  static Widget buildDebugOverlay({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
    required bool isAgoraInitialized,
  }) {
    return Positioned(
      top: 100,
      left: 10,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'DEBUG INFO',
              style: const TextStyle(
                color: Colors.yellow,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Role: ${isProvider ? "Provider" : "Patient"}',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Local UID: $localUid',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Remote UID: $remoteUid',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Channel: $channelName',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Initialized: $isAgoraInitialized',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }

  static void validateVideoSetup({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
  }) {
    debugPrint('=== VALIDATING VIDEO SETUP ===');
    
    // Check if UIDs are in expected ranges
    if (localUid != null) {
      if (isProvider && localUid < 2000) {
        debugPrint('WARNING: Provider UID ($localUid) is not in expected range (2000+)');
      } else if (!isProvider && localUid < 1000) {
        debugPrint('WARNING: Patient UID ($localUid) is not in expected range (1000+)');
      } else {
        debugPrint('✓ Local UID is in correct range');
      }
    } else {
      debugPrint('ERROR: Local UID is null');
    }

    if (remoteUid != null) {
      if (isProvider && remoteUid >= 2000) {
        debugPrint('WARNING: Remote UID ($remoteUid) appears to be another provider');
      } else if (!isProvider && remoteUid >= 1000 && remoteUid < 2000) {
        debugPrint('WARNING: Remote UID ($remoteUid) appears to be another patient');
      } else {
        debugPrint('✓ Remote UID appears to be correct role');
      }
    } else {
      debugPrint('INFO: Remote UID is null (no remote user yet)');
    }

    if (channelName == null || channelName.isEmpty) {
      debugPrint('ERROR: Channel name is null or empty');
    } else {
      debugPrint('✓ Channel name is set: $channelName');
    }

    debugPrint('==============================');
  }
}
