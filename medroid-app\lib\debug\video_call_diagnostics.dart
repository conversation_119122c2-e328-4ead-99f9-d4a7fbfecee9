import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// Diagnostic helper for video call issues
class VideoCallDiagnostics {
  
  /// Run comprehensive diagnostics on video call state
  static Map<String, dynamic> runDiagnostics({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
    required bool isAgoraInitialized,
    required String appointmentId,
  }) {
    final results = <String, dynamic>{};
    final issues = <String>[];
    final warnings = <String>[];
    
    // Check UID ranges
    if (localUid != null) {
      if (isProvider) {
        if (localUid < 2000) {
          issues.add('Provider UID ($localUid) should be 2000+');
        } else {
          results['local_uid_valid'] = true;
        }
      } else {
        if (localUid < 1000 || localUid >= 2000) {
          issues.add('Patient UID ($localUid) should be 1000-1999');
        } else {
          results['local_uid_valid'] = true;
        }
      }
    } else {
      issues.add('Local UID is null');
    }
    
    // Check remote UID logic
    if (remoteUid != null) {
      if (remoteUid == localUid) {
        issues.add('Remote UID equals Local UID - this will cause video conflicts');
      }
      
      if (isProvider) {
        if (remoteUid >= 2000) {
          warnings.add('Provider seeing another Provider UID ($remoteUid)');
        }
      } else {
        if (remoteUid >= 1000 && remoteUid < 2000) {
          warnings.add('Patient seeing another Patient UID ($remoteUid)');
        }
      }
      results['remote_uid_valid'] = true;
    } else {
      results['remote_uid_valid'] = false;
      warnings.add('No remote user detected yet');
    }
    
    // Check channel name
    if (channelName == null || channelName.isEmpty) {
      issues.add('Channel name is null or empty');
    } else if (!channelName.startsWith('medroid-')) {
      warnings.add('Channel name format unexpected: $channelName');
    } else {
      results['channel_valid'] = true;
    }
    
    // Check initialization
    if (!isAgoraInitialized) {
      issues.add('Agora not initialized');
    } else {
      results['agora_initialized'] = true;
    }
    
    // Overall health score
    int score = 0;
    if (results['local_uid_valid'] == true) score += 25;
    if (results['remote_uid_valid'] == true) score += 25;
    if (results['channel_valid'] == true) score += 25;
    if (results['agora_initialized'] == true) score += 25;
    
    results['health_score'] = score;
    results['issues'] = issues;
    results['warnings'] = warnings;
    results['timestamp'] = DateTime.now().toIso8601String();
    
    return results;
  }
  
  /// Generate a diagnostic report
  static String generateReport({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
    required bool isAgoraInitialized,
    required String appointmentId,
  }) {
    final diagnostics = runDiagnostics(
      localUid: localUid,
      remoteUid: remoteUid,
      isProvider: isProvider,
      channelName: channelName,
      isAgoraInitialized: isAgoraInitialized,
      appointmentId: appointmentId,
    );
    
    final buffer = StringBuffer();
    buffer.writeln('=== VIDEO CALL DIAGNOSTICS ===');
    buffer.writeln('Timestamp: ${diagnostics['timestamp']}');
    buffer.writeln('Appointment: $appointmentId');
    buffer.writeln('User Role: ${isProvider ? "Provider" : "Patient"}');
    buffer.writeln('Health Score: ${diagnostics['health_score']}/100');
    buffer.writeln('');
    
    buffer.writeln('Current State:');
    buffer.writeln('- Local UID: $localUid');
    buffer.writeln('- Remote UID: $remoteUid');
    buffer.writeln('- Channel: $channelName');
    buffer.writeln('- Initialized: $isAgoraInitialized');
    buffer.writeln('');
    
    final issues = diagnostics['issues'] as List<String>;
    if (issues.isNotEmpty) {
      buffer.writeln('🚨 CRITICAL ISSUES:');
      for (final issue in issues) {
        buffer.writeln('- $issue');
      }
      buffer.writeln('');
    }
    
    final warnings = diagnostics['warnings'] as List<String>;
    if (warnings.isNotEmpty) {
      buffer.writeln('⚠️  WARNINGS:');
      for (final warning in warnings) {
        buffer.writeln('- $warning');
      }
      buffer.writeln('');
    }
    
    if (issues.isEmpty && warnings.isEmpty) {
      buffer.writeln('✅ All checks passed!');
    }
    
    buffer.writeln('Expected Behavior:');
    if (isProvider) {
      buffer.writeln('- Provider Local UID: 2000+');
      buffer.writeln('- Should see Patient Remote UID: 1000-1999');
    } else {
      buffer.writeln('- Patient Local UID: 1000-1999');
      buffer.writeln('- Should see Provider Remote UID: 2000+');
    }
    
    buffer.writeln('==============================');
    
    return buffer.toString();
  }
  
  /// Check if video call setup is healthy
  static bool isHealthy({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
    required bool isAgoraInitialized,
    required String appointmentId,
  }) {
    final diagnostics = runDiagnostics(
      localUid: localUid,
      remoteUid: remoteUid,
      isProvider: isProvider,
      channelName: channelName,
      isAgoraInitialized: isAgoraInitialized,
      appointmentId: appointmentId,
    );
    
    final issues = diagnostics['issues'] as List<String>;
    return issues.isEmpty && diagnostics['health_score'] >= 75;
  }
  
  /// Get specific recommendations based on current state
  static List<String> getRecommendations({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
    required bool isAgoraInitialized,
    required String appointmentId,
  }) {
    final recommendations = <String>[];
    
    if (localUid == null) {
      recommendations.add('Check backend token generation - local UID not assigned');
    }
    
    if (remoteUid == null) {
      recommendations.add('Wait for other participant to join, or check network connectivity');
    }
    
    if (localUid != null && remoteUid != null && localUid == remoteUid) {
      recommendations.add('CRITICAL: UID collision detected - check backend UID assignment logic');
    }
    
    if (!isAgoraInitialized) {
      recommendations.add('Initialize Agora service before attempting video calls');
    }
    
    if (channelName == null) {
      recommendations.add('Check appointment session data - channel name missing');
    }
    
    return recommendations;
  }
  
  /// Widget to display diagnostics in debug mode
  static Widget buildDiagnosticsPanel({
    required int? localUid,
    required int? remoteUid,
    required bool isProvider,
    required String? channelName,
    required bool isAgoraInitialized,
    required String appointmentId,
  }) {
    if (!kDebugMode) return const SizedBox.shrink();
    
    final isHealthy = VideoCallDiagnostics.isHealthy(
      localUid: localUid,
      remoteUid: remoteUid,
      isProvider: isProvider,
      channelName: channelName,
      isAgoraInitialized: isAgoraInitialized,
      appointmentId: appointmentId,
    );
    
    return Positioned(
      top: 200,
      left: 10,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isHealthy ? Colors.green.withOpacity(0.8) : Colors.red.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              isHealthy ? '✅ HEALTHY' : '🚨 ISSUES',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Tap for details',
              style: const TextStyle(color: Colors.white70, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }
}
