import 'dart:convert';
import 'package:http/http.dart' as http;

// A standalone test script that doesn't depend on the Flutter framework
// Run with: dart run lib/direct_test.dart

void main() async {
  print('Medroid AI API Test');
  print('=================\n');

  const openAiApiKey =
      '********************************************************************************************************************************************************************';
  const mistralApiKey = 'vZabYNGkXr4F3XNidMLxH9QjZsYSH6a7';

  // Test Mistral API
  print('Testing Mistral AI API...');
  try {
    final mistralResponse = await callMistralApi(mistral<PERSON><PERSON><PERSON><PERSON>,
        "I've been having headaches and dizziness for the past week. What could be causing this?");

    print('Mistral API Response:');
    print(mistralResponse);
    print('\n---\n');
  } catch (e) {
    print('Error with Mistral API: $e\n');
  }

  // Test OpenAI API
  print('Testing OpenAI API...');
  try {
    final openAiResponse = await callOpenAiApi(openAiApiKey,
        "I've been having headaches and dizziness for the past week. What could be causing this?");

    print('OpenAI API Response:');
    print(openAiResponse);
    print('\n---\n');
  } catch (e) {
    print('Error with OpenAI API: $e\n');
  }

  // Test emergency detection
  print('Testing emergency scenario with OpenAI...');
  try {
    final emergencyResponse = await callOpenAiApi(openAiApiKey,
        "I have severe chest pain radiating to my left arm and jaw, and I'm having trouble breathing");

    print('Emergency scenario response:');
    print(emergencyResponse);
    print('\n---\n');
  } catch (e) {
    print('Error with emergency test: $e\n');
  }

  // Test medication info
  print('Testing medication information query...');
  try {
    final medResponse = await callOpenAiApi(openAiApiKey,
        'What is ibuprofen used for and what are its side effects?');

    print('Medication info response:');
    print(medResponse);
  } catch (e) {
    print('Error with medication test: $e\n');
  }
}

Future<String> callMistralApi(String apiKey, String prompt) async {
  final url = Uri.parse('https://api.mistral.ai/v1/chat/completions');

  final data = {
    'model': 'mistral-medium',
    'messages': [
      {'role': 'user', 'content': prompt}
    ],
    'temperature': 0.7,
    'max_tokens': 800
  };

  final response = await http.post(
    url,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $apiKey',
    },
    body: jsonEncode(data),
  );

  if (response.statusCode == 200) {
    final result = jsonDecode(response.body);
    return result['choices'][0]['message']['content'];
  } else {
    throw 'API call failed with status ${response.statusCode}: ${response.body}';
  }
}

Future<String> callOpenAiApi(String apiKey, String prompt) async {
  final url = Uri.parse('https://api.openai.com/v1/chat/completions');

  final data = {
    'model': 'gpt-4o-mini',
    'messages': [
      {
        'role': 'system',
        'content':
            'You are a helpful healthcare assistant providing accurate medical information. For emergency symptoms, emphasize seeking immediate medical care.'
      },
      {'role': 'user', 'content': prompt}
    ],
    'temperature': 0.7,
    'max_tokens': 800
  };

  final response = await http.post(
    url,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $apiKey',
    },
    body: jsonEncode(data),
  );

  if (response.statusCode == 200) {
    final result = jsonDecode(response.body);
    return result['choices'][0]['message']['content'];
  } else {
    throw 'API call failed with status ${response.statusCode}: ${response.body}';
  }
}
