import 'dart:convert';
import 'package:medroid_app/models/provider.dart';

class Appointment {
  final dynamic id;
  final dynamic patientId;
  final dynamic providerId;
  final dynamic serviceId;
  final DateTime date;
  final Map<String, String> timeSlot;
  final String reason;
  final String status;
  final String notes;
  final bool isTelemedicine;
  final String? videoSessionId;
  final String? videoSessionUrl;
  final DateTime? consultationStartedAt;
  final DateTime? consultationEndedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final HealthcareProvider? provider;
  final Map<String, dynamic>? patient;
  final Map<String, dynamic>? service;
  final String? paymentStatus;
  final String? paymentIntentId;
  final double? amount;
  final DateTime? paymentDueBy;
  final DateTime? paidAt;

  Appointment({
    required this.id,
    required this.patientId,
    required this.providerId,
    this.serviceId,
    required this.date,
    required this.timeSlot,
    required this.reason,
    required this.status,
    required this.notes,
    this.isTelemedicine = false,
    this.videoSessionId,
    this.videoSessionUrl,
    this.consultationStartedAt,
    this.consultationEndedAt,
    required this.createdAt,
    required this.updatedAt,
    this.provider,
    this.patient,
    this.service,
    this.paymentStatus,
    this.paymentIntentId,
    this.amount,
    this.paymentDueBy,
    this.paidAt,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    // Handle time slot conversion
    Map<String, String> timeSlotMap = {};
    if (json['time_slot'] != null) {
      if (json['time_slot'] is Map) {
        timeSlotMap = Map<String, String>.from(json['time_slot']);
      } else if (json['time_slot'] is String) {
        // Handle case where time_slot might be a JSON string
        try {
          final Map<String, dynamic> decoded =
              jsonDecode(json['time_slot'] as String);
          timeSlotMap = Map<String, String>.from(decoded);
        } catch (e) {
          timeSlotMap = {'start_time': '', 'end_time': ''};
        }
      }
    } else {
      timeSlotMap = {'start_time': '', 'end_time': ''};
    }

    return Appointment(
      id: json['id']?.toString() ?? json['_id']?.toString() ?? '',
      patientId: json['patient_id']?.toString() ?? '',
      providerId: json['provider_id']?.toString() ?? '',
      serviceId: json['service_id']?.toString(),
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      timeSlot: timeSlotMap,
      reason: json['reason'] ?? '',
      status: json['status'] ?? '',
      notes: json['notes'] ?? '',
      isTelemedicine: json['is_telemedicine'] ?? false,
      videoSessionId: json['video_session_id'],
      videoSessionUrl: json['video_session_url'],
      consultationStartedAt: json['consultation_started_at'] != null
          ? DateTime.parse(json['consultation_started_at'])
          : null,
      consultationEndedAt: json['consultation_ended_at'] != null
          ? DateTime.parse(json['consultation_ended_at'])
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
      provider: json['provider'] != null
          ? HealthcareProvider.fromJson(json['provider'])
          : null,
      patient: json['patient'],
      service: json['service'],
      paymentStatus: json['payment_status'],
      paymentIntentId: json['payment_intent_id'],
      amount: json['amount'] != null
          ? double.parse(json['amount'].toString())
          : null,
      paymentDueBy: json['payment_due_by'] != null
          ? DateTime.parse(json['payment_due_by'])
          : null,
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'provider_id': providerId,
      'service_id': serviceId,
      'date': date.toIso8601String(),
      'time_slot': timeSlot,
      'reason': reason,
      'status': status,
      'notes': notes,
      'is_telemedicine': isTelemedicine,
      'video_session_id': videoSessionId,
      'video_session_url': videoSessionUrl,
      'consultation_started_at': consultationStartedAt?.toIso8601String(),
      'consultation_ended_at': consultationEndedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'payment_status': paymentStatus,
      'payment_intent_id': paymentIntentId,
      'amount': amount,
      'payment_due_by': paymentDueBy?.toIso8601String(),
      'paid_at': paidAt?.toIso8601String(),
    };
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  String get providerName {
    return provider?.displayName ?? 'Unknown Provider';
  }

  /// Get the consultation duration in minutes
  int? get consultationDurationMinutes {
    if (consultationStartedAt == null) {
      return null;
    }

    final endTime = consultationEndedAt ?? DateTime.now();
    return endTime.difference(consultationStartedAt!).inMinutes;
  }

  /// Format the consultation duration as a string (e.g., "45 minutes")
  String? get formattedConsultationDuration {
    final duration = consultationDurationMinutes;

    if (duration == null) {
      return null;
    }

    if (duration < 60) {
      return '$duration minute${duration == 1 ? '' : 's'}';
    }

    final hours = duration ~/ 60;
    final minutes = duration % 60;

    if (minutes == 0) {
      return '$hours hour${hours == 1 ? '' : 's'}';
    }

    return '$hours hour${hours == 1 ? '' : 's'} $minutes minute${minutes == 1 ? '' : 's'}';
  }

  String get patientName {
    if (patient != null && patient!['user'] != null) {
      return patient!['user']['name'] ?? 'Unknown Patient';
    }
    return 'Unknown Patient';
  }

  String? get serviceName {
    if (service != null) {
      return service!['name'];
    }
    return null;
  }

  bool get canJoinVideoConsultation {
    // Only check if it's a telemedicine appointment with scheduled status
    // The video session details will be initialized when joining
    return isTelemedicine && status.toLowerCase() == 'scheduled';
  }

  String get statusColor {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return '#3F51B5'; // primary color
      case 'completed':
        return '#4CAF50'; // green
      case 'cancelled':
        return '#F44336'; // red
      case 'pending_payment':
        return '#FF9800'; // orange
      default:
        return '#9E9E9E'; // grey
    }
  }

  bool get isPendingPayment => status.toLowerCase() == 'pending_payment';

  String get formattedAmount {
    if (amount == null) return '\$0.00';
    return '\$${amount!.toStringAsFixed(2)}';
  }

  // Create a copy of this appointment with updated fields
  Appointment copyWith({
    dynamic id,
    dynamic patientId,
    dynamic providerId,
    dynamic serviceId,
    DateTime? date,
    Map<String, String>? timeSlot,
    String? reason,
    String? status,
    String? notes,
    bool? isTelemedicine,
    String? videoSessionId,
    String? videoSessionUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    HealthcareProvider? provider,
    Map<String, dynamic>? patient,
    Map<String, dynamic>? service,
    String? paymentStatus,
    String? paymentIntentId,
    double? amount,
    DateTime? paymentDueBy,
    DateTime? paidAt,
  }) {
    return Appointment(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      providerId: providerId ?? this.providerId,
      serviceId: serviceId ?? this.serviceId,
      date: date ?? this.date,
      timeSlot: timeSlot ?? this.timeSlot,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      isTelemedicine: isTelemedicine ?? this.isTelemedicine,
      videoSessionId: videoSessionId ?? this.videoSessionId,
      videoSessionUrl: videoSessionUrl ?? this.videoSessionUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      provider: provider ?? this.provider,
      patient: patient ?? this.patient,
      service: service ?? this.service,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
      amount: amount ?? this.amount,
      paymentDueBy: paymentDueBy ?? this.paymentDueBy,
      paidAt: paidAt ?? this.paidAt,
    );
  }
}
