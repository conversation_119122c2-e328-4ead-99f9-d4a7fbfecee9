class AppointmentPreferences {
  final String? preferredLocation;
  final double? latitude;
  final double? longitude;
  final int searchRadius; // in kilometers
  final String? preferredGender;
  final String? preferredLanguage;

  AppointmentPreferences({
    this.preferredLocation,
    this.latitude,
    this.longitude,
    this.searchRadius = 25, // Default 25 km radius
    this.preferredGender,
    this.preferredLanguage,
  });

  factory AppointmentPreferences.fromJson(Map<String, dynamic> json) {
    return AppointmentPreferences(
      preferredLocation: json['preferred_location'],
      latitude: json['latitude'] != null ? double.parse(json['latitude'].toString()) : null,
      longitude: json['longitude'] != null ? double.parse(json['longitude'].toString()) : null,
      searchRadius: json['search_radius'] != null ? int.parse(json['search_radius'].toString()) : 25,
      preferredGender: json['preferred_gender'],
      preferredLanguage: json['preferred_language'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'preferred_location': preferredLocation,
      'latitude': latitude,
      'longitude': longitude,
      'search_radius': searchRadius,
      'preferred_gender': preferredGender,
      'preferred_language': preferredLanguage,
    };
  }

  AppointmentPreferences copyWith({
    String? preferredLocation,
    double? latitude,
    double? longitude,
    int? searchRadius,
    String? preferredGender,
    String? preferredLanguage,
  }) {
    return AppointmentPreferences(
      preferredLocation: preferredLocation ?? this.preferredLocation,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      searchRadius: searchRadius ?? this.searchRadius,
      preferredGender: preferredGender ?? this.preferredGender,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
    );
  }
}
