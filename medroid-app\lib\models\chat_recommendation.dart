class ChatRecommendation {
  final String type;
  final String content;

  ChatRecommendation({
    required this.type,
    required this.content,
  });

  factory ChatRecommendation.fromJson(Map<String, dynamic> json) {
    return ChatRecommendation(
      type: json['type'] ?? 'general',
      content: json['content'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'content': content,
    };
  }
}
