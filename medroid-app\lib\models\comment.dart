class Comment {
  final String id;
  final String socialContentId;
  final String userId;
  final String content;
  final String? parentId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User user;
  final List<Comment> replies;
  final Map<String, int> reactionCounts;
  final String? userReaction;
  final bool canEdit;
  final bool canDelete;

  Comment({
    required this.id,
    required this.socialContentId,
    required this.userId,
    required this.content,
    this.parentId,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
    this.replies = const [],
    this.reactionCounts = const {},
    this.userReaction,
    this.canEdit = false,
    this.canDelete = false,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    // Safely parse replies
    List<Comment> parseReplies() {
      try {
        final repliesData = json['replies'];
        if (repliesData == null) return [];

        if (repliesData is List) {
          return repliesData
              .whereType<Map<String, dynamic>>()
              .map((reply) => Comment.fromJson(reply))
              .toList();
        }
        return [];
      } catch (e) {
        // Silently handle parsing errors and return empty list
        return [];
      }
    }

    // Safely parse reaction counts
    Map<String, int> parseReactionCounts() {
      try {
        final reactionData = json['reaction_counts'];
        if (reactionData == null) return {};

        if (reactionData is Map) {
          return Map<String, int>.from(reactionData);
        }
        return {};
      } catch (e) {
        // Silently handle parsing errors and return empty map
        return {};
      }
    }

    return Comment(
      id: json['id'].toString(),
      socialContentId: json['social_content_id'].toString(),
      userId: json['user_id'].toString(),
      content: json['content'] ?? '',
      parentId: json['parent_id']?.toString(),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      user: User.fromJson(json['user'] ?? {}),
      replies: parseReplies(),
      reactionCounts: parseReactionCounts(),
      userReaction: json['user_reaction'],
      canEdit: json['can_edit'] ?? false,
      canDelete: json['can_delete'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'social_content_id': socialContentId,
      'user_id': userId,
      'content': content,
      'parent_id': parentId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user': user.toJson(),
      'replies': replies.map((reply) => reply.toJson()).toList(),
    };
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

class User {
  final String id;
  final String name;
  final String? avatar;

  User({
    required this.id,
    required this.name,
    this.avatar,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    try {
      return User(
        id: json['id']?.toString() ?? '',
        name: json['name'] ?? 'Unknown User',
        avatar: json['avatar'] ?? json['profile_image'],
      );
    } catch (e) {
      // Return a default user if parsing fails
      return User(
        id: '',
        name: 'Unknown User',
        avatar: null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
    };
  }

  String get initials {
    if (name.isEmpty) return '?';
    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
    return name[0].toUpperCase();
  }
}
