import 'package:medroid_app/models/social_post.dart';

class FeedResult {
  final List<SocialPost> posts;
  final bool hasMore;
  final int currentPage;
  final int lastPage;

  FeedResult({
    required this.posts,
    required this.hasMore,
    required this.currentPage,
    required this.lastPage,
  });
}

class LikeResult {
  final bool liked;
  final int likeCount;

  LikeResult({
    required this.liked,
    required this.likeCount,
  });
}

class SaveResult {
  final bool saved;
  final int saveCount;

  SaveResult({
    required this.saved,
    required this.saveCount,
  });
}
