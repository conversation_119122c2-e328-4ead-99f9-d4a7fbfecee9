class Payment {
  final String id;
  final String appointmentId;
  final double amount;
  final String currency;
  final String status;
  final DateTime createdAt;
  final String? paymentMethodId;
  final String? paymentIntentId;
  final String? providerId;
  final String? serviceId;
  final Map<String, dynamic>? metadata;

  Payment({
    required this.id,
    required this.appointmentId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.createdAt,
    this.paymentMethodId,
    this.paymentIntentId,
    this.providerId,
    this.serviceId,
    this.metadata,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      appointmentId: json['appointment_id'],
      amount: (json['amount'] is int)
          ? (json['amount'] / 100).toDouble()
          : json['amount'].toDouble(),
      currency: json['currency'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      paymentMethodId: json['payment_method_id'],
      paymentIntentId: json['payment_intent_id'],
      providerId: json['provider_id'],
      serviceId: json['service_id'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'appointment_id': appointmentId,
      'amount': amount,
      'currency': currency,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'payment_method_id': paymentMethodId,
      'payment_intent_id': paymentIntentId,
      'provider_id': providerId,
      'service_id': serviceId,
      'metadata': metadata,
    };
  }

  String get formattedAmount {
    return '\$${amount.toStringAsFixed(2)}';
  }

  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get formattedStatus {
    switch (status) {
      case 'succeeded':
        return 'Paid';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status;
    }
  }
}
