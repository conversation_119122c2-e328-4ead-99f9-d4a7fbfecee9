import 'dart:convert';

class HealthcareProvider {
  final dynamic id;
  final dynamic userId;
  final String specialization;
  final String licenseNumber;
  final String verificationStatus;
  final List<PracticeLocation> practiceLocations;
  final List<AvailabilitySlot> availability;
  final double rating;
  final List<String> services;
  final Map<String, dynamic> pricing;
  final UserDetails? user;

  HealthcareProvider({
    required this.id,
    required this.userId,
    required this.specialization,
    required this.licenseNumber,
    required this.verificationStatus,
    required this.practiceLocations,
    required this.availability,
    required this.rating,
    required this.services,
    required this.pricing,
    this.user,
  });

  factory HealthcareProvider.fromJson(Map<String, dynamic> json) {
    // Helper method to parse pricing which can be either a string (JSON) or a map
    Map<String, dynamic> parsePricing(dynamic pricing) {
      if (pricing == null) {
        return {};
      }

      try {
        if (pricing is String) {
          // If it's a string, try to parse it as JSON
          final Map<String, dynamic> parsedPricing = jsonDecode(pricing);
          return parsedPricing;
        } else if (pricing is Map) {
          // If it's already a map, return it
          return Map<String, dynamic>.from(pricing);
        }
      } catch (e) {
        print('Error parsing pricing: $e');
      }

      return {};
    }

    // Handle practice_locations which can be either a string (JSON) or a list
    List<PracticeLocation> parsePracticeLocations(dynamic practiceLocations) {
      if (practiceLocations == null) {
        return [];
      }

      try {
        if (practiceLocations is String) {
          // If it's a string, try to parse it as JSON
          final List<dynamic> parsedLocations = jsonDecode(practiceLocations);
          if (parsedLocations.isEmpty) {
            return [];
          }

          // If the parsed JSON is just a list of strings
          if (parsedLocations[0] is String) {
            return parsedLocations
                .map((loc) => PracticeLocation(
                      address: loc,
                      coordinates: [0.0, 0.0],
                    ))
                .toList();
          }

          // If the parsed JSON is a list of objects
          return parsedLocations
              .map((loc) => PracticeLocation.fromJson(loc))
              .toList();
        } else if (practiceLocations is List) {
          // If it's already a list, map it directly
          return practiceLocations
              .map((loc) => loc is String
                  ? PracticeLocation(address: loc, coordinates: [0.0, 0.0])
                  : PracticeLocation.fromJson(loc))
              .toList();
        }
      } catch (e) {
        print('Error parsing practice locations: $e');
      }

      return [];
    }

    // Parse weekly_availability into AvailabilitySlot objects
    List<AvailabilitySlot> parseWeeklyAvailability(dynamic weeklyAvailability) {
      if (weeklyAvailability == null) {
        return [];
      }

      try {
        if (weeklyAvailability is String) {
          // If it's a string, try to parse it as JSON
          final List<dynamic> parsedAvailability =
              jsonDecode(weeklyAvailability);
          return parsedAvailability
              .map((avail) => AvailabilitySlot.fromJson(avail))
              .toList();
        } else if (weeklyAvailability is List) {
          // If it's already a list, map it directly
          return weeklyAvailability
              .map((avail) => AvailabilitySlot.fromJson(avail))
              .toList();
        }
      } catch (e) {
        print('Error parsing weekly availability: $e');
      }

      return [];
    }

    // Debug the incoming data
    print('Parsing provider data: ${json['id']} - ${json['specialization']}');

    // Use weekly_availability if available, otherwise fall back to availability
    final availabilityData =
        json['weekly_availability'] ?? json['availability'];

    return HealthcareProvider(
      id: json['id'] ?? json['_id'] ?? '',
      userId: json['user_id'] ?? '',
      specialization: json['specialization'] ?? '',
      licenseNumber: json['license_number'] ?? '',
      verificationStatus: json['verification_status'] ?? '',
      practiceLocations: parsePracticeLocations(json['practice_locations']),
      availability: parseWeeklyAvailability(availabilityData),
      rating: json['rating'] is num ? (json['rating'] as num).toDouble() : 0.0,
      // Default to empty list for services if not provided
      services: List<String>.from(json['services'] ?? []),
      pricing: parsePricing(json['pricing']),
      user: json['user'] != null ? UserDetails.fromJson(json['user']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'user_id': userId,
      'specialization': specialization,
      'license_number': licenseNumber,
      'verification_status': verificationStatus,
      'practice_locations':
          practiceLocations.map((loc) => loc.toJson()).toList(),
      'availability': availability.map((avail) => avail.toJson()).toList(),
      'rating': rating,
      'services': services,
      'pricing': pricing,
      if (user != null) 'user': user!.toJson(),
    };
  }

  String get displayName {
    // First try to get the name from the user object
    if (user != null && user!.name.isNotEmpty) {
      return user!.name;
    }

    // If user is not available, use the specialization to create a more meaningful name
    if (specialization.isNotEmpty) {
      // Create a name based on specialization
      final specialty = specialization.split(' ').last;
      return 'Dr. ${specialty.substring(0, 1).toUpperCase()}${specialty.substring(1)}';
    }

    // If specialization is not available, try to extract from userId if it's a number
    if (userId != null) {
      // For demo purposes, use a placeholder with the userId
      return 'Dr. Smith';
    }

    // Default fallback
    return 'Dr. Unknown';
  }

  String get profileImage => user?.profileImage ?? 'assets/doctor.png';

  bool isAvailableOn(String day) {
    return availability
        .any((slot) => slot.day.toLowerCase() == day.toLowerCase());
  }

  List<TimeSlot> getAvailableSlotsForDay(String day) {
    final daySlot = availability.firstWhere(
      (slot) => slot.day.toLowerCase() == day.toLowerCase(),
      orElse: () => AvailabilitySlot(day: day, slots: []),
    );

    return daySlot.slots;
  }

  double getConsultationFee() {
    final fee = pricing['consultation'];
    if (fee is num) {
      return fee.toDouble();
    } else if (fee is String) {
      try {
        return double.parse(fee);
      } catch (e) {
        return 0.0;
      }
    }
    return 0.0;
  }

  String formattedConsultationFee() {
    return '\$${getConsultationFee().toStringAsFixed(2)}';
  }
}

class PracticeLocation {
  final String address;
  final List<double> coordinates;

  PracticeLocation({
    required this.address,
    required this.coordinates,
  });

  factory PracticeLocation.fromJson(Map<String, dynamic> json) {
    final List<double> coords = [];

    if (json['coordinates'] is List) {
      for (var coord in json['coordinates']) {
        if (coord is num) {
          coords.add(coord.toDouble());
        }
      }
    }

    return PracticeLocation(
      address: json['address'] ?? '',
      coordinates: coords.length == 2 ? coords : [0.0, 0.0],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'coordinates': coordinates,
    };
  }

  double get latitude => coordinates[0];
  double get longitude => coordinates[1];
}

class AvailabilitySlot {
  final String day;
  final List<TimeSlot> slots;

  AvailabilitySlot({
    required this.day,
    required this.slots,
  });

  factory AvailabilitySlot.fromJson(Map<String, dynamic> json) {
    return AvailabilitySlot(
      day: json['day'] ?? '',
      slots: (json['slots'] as List<dynamic>?)
              ?.map((slot) => TimeSlot.fromJson(slot))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'slots': slots.map((slot) => slot.toJson()).toList(),
    };
  }
}

class TimeSlot {
  final String startTime;
  final String endTime;

  TimeSlot({
    required this.startTime,
    required this.endTime,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start_time': startTime,
      'end_time': endTime,
    };
  }

  String get displayText => '$startTime - $endTime';
}

class UserDetails {
  final dynamic id;
  final String name;
  final String email;
  final String role;
  final String profileImage;

  UserDetails({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.profileImage,
  });

  factory UserDetails.fromJson(Map<String, dynamic> json) {
    return UserDetails(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? '',
      profileImage: json['profile_image'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'email': email,
      'role': role,
      'profile_image': profileImage,
    };
  }
}

class Appointment {
  final dynamic id;
  final dynamic patientId;
  final dynamic providerId;
  final DateTime date;
  final TimeSlot timeSlot;
  final String reason;
  final String status;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final HealthcareProvider? provider;
  final Map<String, dynamic>? patient;

  Appointment({
    required this.id,
    required this.patientId,
    required this.providerId,
    required this.date,
    required this.timeSlot,
    required this.reason,
    required this.status,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.provider,
    this.patient,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id'] ?? json['_id'] ?? '',
      patientId: json['patient_id'] ?? '',
      providerId: json['provider_id'] ?? '',
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      timeSlot: TimeSlot.fromJson(
          json['time_slot'] ?? {'start_time': '', 'end_time': ''}),
      reason: json['reason'] ?? '',
      status: json['status'] ?? '',
      notes: json['notes'] ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
      provider: json['provider'] != null
          ? HealthcareProvider.fromJson(json['provider'])
          : null,
      patient: json['patient'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'patient_id': patientId,
      'provider_id': providerId,
      'date': date.toIso8601String(),
      'time_slot': timeSlot.toJson(),
      'reason': reason,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  String get providerName {
    return provider?.displayName ?? 'Unknown Provider';
  }

  String get patientName {
    if (patient != null && patient!['user'] != null) {
      return patient!['user']['name'] ?? 'Unknown Patient';
    }
    return 'Unknown Patient';
  }

  String get statusColor {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return '#3F51B5'; // primary color
      case 'completed':
        return '#4CAF50'; // green
      case 'cancelled':
        return '#F44336'; // red
      default:
        return '#9E9E9E'; // grey
    }
  }
}
