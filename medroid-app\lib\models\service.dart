import 'package:medroid_app/models/provider.dart';

class Service {
  final String id;
  final String providerId;
  final String name;
  final String description;
  final int duration;
  final double price;
  final String category;
  final List<AvailabilitySlot> availability;
  final bool active;
  final bool isTelemedicine;
  final DateTime createdAt;
  final DateTime updatedAt;
  final HealthcareProvider? provider;

  Service({
    required this.id,
    required this.providerId,
    required this.name,
    required this.description,
    required this.duration,
    required this.price,
    required this.category,
    required this.availability,
    required this.active,
    this.isTelemedicine = false,
    required this.createdAt,
    required this.updatedAt,
    this.provider,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    double parsePrice(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (e) {
          return 0.0;
        }
      }
      return 0.0;
    }

    return Service(
      id: json['id']?.toString() ?? '',
      providerId: json['provider_id']?.toString() ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      duration: json['duration'] ?? 30,
      price: parsePrice(json['price']),
      category: json['category'] ?? '',
      availability: (json['availability'] as List<dynamic>?)
              ?.map((avail) => AvailabilitySlot.fromJson(avail))
              .toList() ??
          [],
      active: json['active'] ?? true,
      isTelemedicine: json['is_telemedicine'] ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
      provider: json['provider'] != null
          ? HealthcareProvider.fromJson(json['provider'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'provider_id': providerId,
      'name': name,
      'description': description,
      'duration': duration,
      'price': price,
      'category': category,
      'availability': availability.map((avail) => avail.toJson()).toList(),
      'active': active,
      'is_telemedicine': isTelemedicine,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedPrice {
    return '\$${price.toStringAsFixed(2)}';
  }

  String get formattedDuration {
    if (duration < 60) {
      return '$duration minutes';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '$hours hour${hours > 1 ? 's' : ''}';
      } else {
        return '$hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''}';
      }
    }
  }
}
