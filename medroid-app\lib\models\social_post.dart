import 'package:flutter/material.dart';
import 'package:medroid_app/utils/constants.dart';

class SocialPost {
  final String id;
  final String source;
  final String sourceId;
  final String contentType;
  final String mediaUrl;
  final String caption;
  final String? title; // Added title field
  final List<String> healthTopics;
  final double relevanceScore;
  final Map<String, dynamic> engagementMetrics;
  final String filteredStatus;
  final DateTime createdAt;
  final String? userName;
  final String? userAvatar;
  bool isLiked;
  bool isSaved;
  final bool canEdit;
  final bool canDelete;

  SocialPost({
    required this.id,
    required this.source,
    required this.sourceId,
    required this.contentType,
    required this.mediaUrl,
    required this.caption,
    this.title,
    required this.healthTopics,
    required this.relevanceScore,
    required this.engagementMetrics,
    required this.filteredStatus,
    required this.createdAt,
    this.userName,
    this.userAvatar,
    this.isLiked = false,
    this.isSaved = false,
    this.canEdit = false,
    this.canDelete = false,
  });

  factory SocialPost.fromJson(Map<String, dynamic> json) {
    // Extract user information if available
    String? userName;
    String? userAvatar;

    if (json['user'] != null) {
      userName = json['user']['name'];
      userAvatar = json['user']['avatar'];
      // Format the avatar URL if it exists
      if (userAvatar != null && userAvatar.isNotEmpty && userAvatar != 'null') {
        userAvatar = _formatAvatarUrl(userAvatar);
      } else {
        userAvatar = null;
      }
    } else if (json['author'] != null) {
      userName = json['author']['name'];
      userAvatar = json['author']['avatar'];
      // Format the avatar URL if it exists
      if (userAvatar != null && userAvatar.isNotEmpty && userAvatar != 'null') {
        userAvatar = _formatAvatarUrl(userAvatar);
      } else {
        userAvatar = null;
      }
    }

    // Handle ID which could be int or string
    String id = '';
    if (json['_id'] != null) {
      id = json['_id'].toString();
    } else if (json['id'] != null) {
      id = json['id'].toString();
    }

    return SocialPost(
      id: id,
      source: json['source'] ?? '',
      sourceId: json['source_id'] ?? '',
      contentType: json['content_type'] ?? '',
      mediaUrl: _formatMediaUrl(json['media_url'] ?? ''),
      caption: json['caption'] ?? '',
      title: json['title'], // Parse title field
      healthTopics: List<String>.from(json['health_topics'] ?? []),
      relevanceScore:
          double.tryParse(json['relevance_score'].toString()) ?? 0.0,
      engagementMetrics:
          Map<String, dynamic>.from(json['engagement_metrics'] ?? {}),
      filteredStatus: json['filtered_status'] ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      userName: userName,
      userAvatar: userAvatar,
      isLiked: json['liked'] ?? false,
      isSaved: json['saved'] ?? false,
      canEdit: json['can_edit'] ?? false,
      canDelete: json['can_delete'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      '_id': id,
      'source': source,
      'source_id': sourceId,
      'content_type': contentType,
      'media_url': mediaUrl,
      'caption': caption,
      'title': title, // Include title field
      'health_topics': healthTopics,
      'relevance_score': relevanceScore,
      'engagement_metrics': engagementMetrics,
      'filtered_status': filteredStatus,
      'created_at': createdAt.toIso8601String(),
      'liked': isLiked,
      'saved': isSaved,
      'can_edit': canEdit,
      'can_delete': canDelete,
    };

    // Add user information if available
    if (userName != null || userAvatar != null) {
      data['user'] = {
        'name': userName,
        'avatar': userAvatar,
      };
    }

    return data;
  }

  int get likeCount {
    return engagementMetrics['likes'] ?? 0;
  }

  int get shareCount {
    return engagementMetrics['shares'] ?? 0;
  }

  int get saveCount {
    return engagementMetrics['saves'] ?? 0;
  }

  // Return icon data instead of asset path
  IconData get sourceIconData {
    switch (source.toLowerCase()) {
      case 'instagram':
        return Icons.photo_camera;
      case 'tiktok':
        return Icons.music_note;
      case 'internal':
        return Icons.medical_services;
      default:
        return Icons.public;
    }
  }

  // Return icon color based on source
  Color get sourceIconColor {
    switch (source.toLowerCase()) {
      case 'instagram':
        return const Color(0xFFC13584); // Instagram pink
      case 'tiktok':
        return const Color(0xFF000000); // TikTok black
      case 'internal':
        return const Color(0xFFEC4899); // Medroid pink
      default:
        return const Color(0xFF4267B2); // Facebook blue
    }
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  // Helper method to format media URLs
  static String _formatMediaUrl(String url) {
    if (url.isEmpty) {
      return '';
    }

    // If the URL already starts with http:// or https://, return it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If the URL starts with /storage, prepend the base URL
    if (url.startsWith('/storage')) {
      return '${Constants.baseStorageUrl}$url';
    }

    // If the URL starts with /images, prepend the base URL
    if (url.startsWith('/images')) {
      return '${Constants.baseStorageUrl}$url';
    }

    // If the URL is a relative path without leading slash, add it
    if (!url.startsWith('/')) {
      return '${Constants.baseStorageUrl}/$url';
    }

    // Otherwise, just prepend the base URL
    return '${Constants.baseStorageUrl}$url';
  }

  // Helper method to format avatar URLs (similar to profile image handling)
  static String _formatAvatarUrl(String url) {
    if (url.isEmpty) {
      return '';
    }

    // If the URL already starts with http:// or https://, return it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If the URL starts with /storage, prepend the base URL
    if (url.startsWith('/storage')) {
      return '${Constants.baseStorageUrl}$url';
    }

    // If the URL is a relative path without leading slash, add it
    if (!url.startsWith('/')) {
      return '${Constants.baseStorageUrl}/$url';
    }

    // Otherwise, just prepend the base URL
    return '${Constants.baseStorageUrl}$url';
  }

  // No fallback images - we only use actual uploaded content
}
