import '../utils/constants.dart';

class Story {
  final String id;
  final String userId;
  final String mediaUrl;
  final String mediaType;
  final String? caption;
  final DateTime createdAt;
  final DateTime expiresAt;
  final bool isActive;
  final List<String> viewers;
  final bool isViewed;
  final int viewersCount;
  final String timeRemaining;
  final StoryUser user;

  Story({
    required this.id,
    required this.userId,
    required this.mediaUrl,
    required this.mediaType,
    this.caption,
    required this.createdAt,
    required this.expiresAt,
    required this.isActive,
    this.viewers = const [],
    this.isViewed = false,
    this.viewersCount = 0,
    this.timeRemaining = '',
    required this.user,
  });

  factory Story.fromJson(Map<String, dynamic> json) {
    return Story(
      id: json['id'].toString(),
      userId: json['user_id'].toString(),
      mediaUrl: _formatMediaUrl(json['media_url'] ?? ''),
      mediaType: json['media_type'] ?? 'image',
      caption: json['caption'],
      createdAt: DateTime.parse(json['created_at']),
      expiresAt: DateTime.parse(json['expires_at']),
      isActive: json['is_active'] ?? true,
      viewers: List<String>.from(json['viewers'] ?? []),
      isViewed: json['is_viewed'] ?? false,
      viewersCount: json['viewers_count'] ?? 0,
      timeRemaining: json['time_remaining'] ?? '',
      user: StoryUser.fromJson(json['user'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'media_url': mediaUrl,
      'media_type': mediaType,
      'caption': caption,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'is_active': isActive,
      'viewers': viewers,
      'is_viewed': isViewed,
      'viewers_count': viewersCount,
      'time_remaining': timeRemaining,
      'user': user.toJson(),
    };
  }

  bool get isExpired {
    return DateTime.now().isAfter(expiresAt);
  }

  String get formattedTimeRemaining {
    if (isExpired) return 'Expired';

    final now = DateTime.now();
    final difference = expiresAt.difference(now);

    if (difference.inHours > 0) {
      return '${difference.inHours}h ${difference.inMinutes % 60}m';
    } else {
      return '${difference.inMinutes}m';
    }
  }

  // Helper method to format media URLs
  static String _formatMediaUrl(String url) {
    if (url.isEmpty) {
      return '';
    }

    // If the URL already starts with http:// or https://, return it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If the URL starts with /storage, prepend the base URL
    if (url.startsWith('/storage')) {
      return '${Constants.baseStorageUrl}$url';
    }

    // If the URL starts with /images, prepend the base URL
    if (url.startsWith('/images')) {
      return '${Constants.baseStorageUrl}$url';
    }

    // If the URL is a relative path without leading slash, add it
    if (!url.startsWith('/')) {
      return '${Constants.baseStorageUrl}/$url';
    }

    // Otherwise, just prepend the base URL
    return '${Constants.baseStorageUrl}$url';
  }
}

class StoryUser {
  final String id;
  final String name;
  final String? avatar;

  StoryUser({
    required this.id,
    required this.name,
    this.avatar,
  });

  factory StoryUser.fromJson(Map<String, dynamic> json) {
    return StoryUser(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? 'Unknown User',
      avatar: json['avatar'] ?? json['profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
    };
  }

  String get initials {
    if (name.isEmpty) return '?';
    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
    return name[0].toUpperCase();
  }
}

class StoryGroup {
  final String userId;
  final String username;
  final String? userAvatar;
  final int storiesCount;
  final DateTime latestStoryTime;
  final bool hasUnviewed;
  final bool isCurrentUser;
  final List<Story> stories;

  StoryGroup({
    required this.userId,
    required this.username,
    this.userAvatar,
    required this.storiesCount,
    required this.latestStoryTime,
    required this.hasUnviewed,
    required this.isCurrentUser,
    required this.stories,
  });

  factory StoryGroup.fromJson(Map<String, dynamic> json) {
    return StoryGroup(
      userId: json['user_id'].toString(),
      username: json['username'] ?? 'Unknown User',
      userAvatar: json['user_avatar'],
      storiesCount: json['stories_count'] ?? 0,
      latestStoryTime: DateTime.parse(json['latest_story_time']),
      hasUnviewed: json['has_unviewed'] ?? false,
      isCurrentUser: json['is_current_user'] ?? false,
      stories: (json['stories'] as List<dynamic>?)
              ?.map((story) => Story.fromJson(story))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'user_avatar': userAvatar,
      'stories_count': storiesCount,
      'latest_story_time': latestStoryTime.toIso8601String(),
      'has_unviewed': hasUnviewed,
      'is_current_user': isCurrentUser,
      'stories': stories.map((story) => story.toJson()).toList(),
    };
  }

  String get userInitials {
    if (username.isEmpty) return '?';
    final words = username.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
    return username[0].toUpperCase();
  }
}
