class TimeSlot {
  final String startTime;
  final String endTime;
  
  TimeSlot({
    required this.startTime,
    required this.endTime,
  });
  
  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: json['start_time'],
      endTime: json['end_time'],
    );
  }
  
  Map<String, String> toJson() {
    return {
      'start_time': startTime,
      'end_time': endTime,
    };
  }
  
  @override
  String toString() {
    return '$startTime - $endTime';
  }
}
