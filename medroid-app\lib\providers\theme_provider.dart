import 'package:flutter/material.dart';

class ThemeProvider with ChangeNotifier {
  // Always use light mode - dark mode is disabled
  ThemeMode get themeMode => ThemeMode.light;

  // Always return false since dark mode is disabled
  bool get isDarkMode => false;

  ThemeProvider();

  // These methods are kept for compatibility but do nothing
  Future<void> setThemeMode(ThemeMode mode) async {
    // Do nothing - always stay in light mode
  }

  Future<void> toggleTheme() async {
    // Do nothing - always stay in light mode
  }
}
