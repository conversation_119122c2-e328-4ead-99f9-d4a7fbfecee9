import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/provider.dart';
import 'package:medroid_app/models/service.dart';
import 'package:medroid_app/screens/payment_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:medroid_app/utils/gradient_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/responsive_container.dart';
import 'package:medroid_app/widgets/main_navigation_sidebar.dart';
import 'package:medroid_app/utils/app_colors.dart';

class AppointmentBookingScreen extends StatefulWidget {
  final HealthcareProvider provider;
  final Service? service;

  const AppointmentBookingScreen({
    Key? key,
    required this.provider,
    this.service,
  }) : super(key: key);

  @override
  _AppointmentBookingScreenState createState() =>
      _AppointmentBookingScreenState();
}

class _AppointmentBookingScreenState extends State<AppointmentBookingScreen> {
  DateTime _selectedDay = DateTime.now();
  DateTime _focusedDay = DateTime.now();
  List<TimeSlot> _availableTimeSlots = [];
  TimeSlot? _selectedTimeSlot;
  bool _isLoading = false;
  String _errorMessage = '';
  final TextEditingController _reasonController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAvailableTimeSlots();
  }

  @override
  void dispose() {
    _reasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableTimeSlots() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _availableTimeSlots = [];
      _selectedTimeSlot = null;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Format dates for API request
      final startDate = DateFormat('yyyy-MM-dd').format(_selectedDay);
      final endDate = DateFormat('yyyy-MM-dd').format(_selectedDay);

      try {
        final data = await apiService.getAvailableTimeSlots(
          providerId: widget.provider.id.toString(),
          startDate: startDate,
          endDate: endDate,
          serviceId: widget.service?.id,
        );

        // Parse available slots from API response
        final List<TimeSlot> slots = [];

        try {
          // Use logger instead of print
          debugPrint('API response data: $data');

          // Handle different response formats
          if (data is Map<String, dynamic>) {
            // Format 1: Response with available_slots map
            if (data.containsKey('available_slots')) {
              final availableSlots = data['available_slots'];

              if (availableSlots is Map<String, dynamic>) {
                // If available_slots is a map with dates as keys
                if (availableSlots.containsKey(startDate)) {
                  final daySlots = availableSlots[startDate];
                  if (daySlots is List) {
                    _processTimeSlots(daySlots, slots, startDate);
                  }
                }
              }
            }
          } else if (data is List) {
            // Format 2: List of slot data
            for (final slotData in data) {
              // Check if slotData is a Map and contains the required fields
              if (slotData is Map<String, dynamic>) {
                final date = slotData['date'];
                final timeSlots = slotData['time_slots'];

                if (date == startDate && timeSlots is List) {
                  _processTimeSlots(timeSlots, slots, startDate);
                }
              }
            }
          }
        } catch (e) {
          debugPrint('Error parsing time slots: $e');
          // If there's an error parsing the data, we'll just use an empty list
        }

        if (mounted) {
          setState(() {
            _availableTimeSlots = slots;
            _isLoading = false;
          });
        }
      } catch (e) {
        debugPrint('Error getting available time slots from API: $e');
        // Fall back to provider's default availability
        _useProviderDefaultAvailability();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _useProviderDefaultAvailability() {
    // Get the day of week (Monday, Tuesday, etc.)
    final dayOfWeek = DateFormat('EEEE').format(_selectedDay);

    // Get available slots for the selected day from the provider's weekly availability
    final availableSlotsForDay =
        widget.provider.getAvailableSlotsForDay(dayOfWeek);

    // Filter out past time slots if the selected day is today
    final filteredSlots = _filterPastTimeSlots(
        availableSlotsForDay, DateFormat('yyyy-MM-dd').format(_selectedDay));

    if (mounted) {
      setState(() {
        _availableTimeSlots = filteredSlots;
        _isLoading = false;
      });
    }
  }

  // Process time slots from API response and filter out past slots
  void _processTimeSlots(List timeSlots, List<TimeSlot> slots, String date) {
    for (final timeSlot in timeSlots) {
      if (timeSlot is Map<String, dynamic> &&
          timeSlot.containsKey('start_time') &&
          timeSlot.containsKey('end_time')) {
        final newSlot = TimeSlot(
          startTime: timeSlot['start_time'],
          endTime: timeSlot['end_time'],
        );

        // Only add the slot if it's not in the past
        if (!_isTimeSlotInPast(newSlot, date)) {
          slots.add(newSlot);
        }
      }
    }
  }

  // Filter out past time slots
  List<TimeSlot> _filterPastTimeSlots(List<TimeSlot> slots, String date) {
    return slots.where((slot) => !_isTimeSlotInPast(slot, date)).toList();
  }

  // Check if a time slot is in the past
  bool _isTimeSlotInPast(TimeSlot slot, String date) {
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);

    // Add buffer time (5 minutes) to account for booking process
    const bufferMinutes = 5;

    // If the date is in the future, the slot is not in the past
    if (date.compareTo(today) > 0) {
      return false;
    }

    // If the date is today, check the time
    if (date == today) {
      final currentTime = TimeOfDay.fromDateTime(now);
      final slotStartTime = _parseTimeOfDay(slot.startTime);

      // Convert both times to minutes since midnight for comparison
      final currentMinutes =
          currentTime.hour * 60 + currentTime.minute + bufferMinutes;
      final slotMinutes = slotStartTime.hour * 60 + slotStartTime.minute;

      // Log for debugging
      debugPrint('Current time: $currentTime ($currentMinutes mins)');
      debugPrint('Slot time: $slotStartTime ($slotMinutes mins)');

      // If the slot start time is earlier than or equal to the current time + buffer, it's in the past
      return slotMinutes <= currentMinutes;
    }

    // If the date is in the past, the slot is in the past
    return date.compareTo(today) < 0;
  }

  // Parse a time string (HH:MM) to TimeOfDay
  TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  Future<void> _bookAppointment() async {
    if (_selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a time slot')),
      );
      return;
    }

    if (_reasonController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please enter a reason for the appointment')),
      );
      return;
    }

    // Check if service is selected - required for payment
    if (widget.service == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('A service must be selected for this appointment')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Add debug logging
      debugPrint(
          'Creating appointment with payment for date: ${DateFormat('yyyy-MM-dd').format(_selectedDay)}');
      debugPrint(
          'Time slot: ${_selectedTimeSlot!.startTime} - ${_selectedTimeSlot!.endTime}');
      debugPrint('Service ID: ${widget.service!.id}');

      try {
        // Create appointment with payment
        final response = await apiService.createAppointmentWithPayment(
          providerId: widget.provider.id.toString(),
          serviceId: widget.service!.id,
          date: DateFormat('yyyy-MM-dd').format(_selectedDay),
          timeSlot: {
            'start_time': _selectedTimeSlot!.startTime,
            'end_time': _selectedTimeSlot!.endTime,
          },
          reason: _reasonController.text,
          notes:
              _notesController.text.isNotEmpty ? _notesController.text : null,
        );

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // Navigate to the payment screen
          if (response['success'] == true &&
              response.containsKey('appointment') &&
              response.containsKey('payment')) {
            // Import the payment screen at the top of the file
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PaymentScreen(
                  appointmentData: response['appointment'],
                  paymentData: response['payment'],
                  onPaymentComplete: (success, paymentId) {
                    if (success) {
                      // Payment successful
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Payment successful. Appointment confirmed!'),
                          backgroundColor: Colors.green,
                        ),
                      );

                      // Navigate back to the provider marketplace
                      Navigator.pop(context); // Pop payment screen
                      Navigator.pop(context); // Pop appointment booking
                      Navigator.pop(context); // Pop provider details
                    } else {
                      // Payment failed
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Payment failed. Please try again.'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                ),
              ),
            );
          } else {
            // Something went wrong with the appointment creation
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text(response['message'] ?? 'Failed to create appointment'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (apiError) {
        // Handle specific API errors
        String errorMessage = apiError.toString();

        // Check for validation errors
        if (errorMessage.contains('validation.after')) {
          errorMessage =
              'Cannot book appointments in the past. Please select a future time slot.';
        } else if (errorMessage.contains('validation')) {
          errorMessage =
              'Validation error: Please check your appointment details.';
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = errorMessage;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $errorMessage')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Unexpected error: ${e.toString()}';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error booking appointment: $_errorMessage')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = GradientColors.primaryGradient[0];
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // For desktop and tablet layouts, use sidebar navigation
    if (isDesktop || isTablet) {
      return Scaffold(
        backgroundColor: AppColors.backgroundLight,
        body: Row(
          children: [
            // Use the reusable sidebar widget
            MainNavigationSidebar(
              currentIndex: -1, // No specific tab selected for appointments
              onNavigationChanged: _navigateToTab,
              onConversationSelected: _openSidebarConversation,
            ),
            // Main content area
            Expanded(
              child: ResponsiveCenteredContainer(
                padding: EdgeInsets.zero,
                constraints: null,
                child: _buildAppointmentBookingContent(context,
                    isDesktop: isDesktop),
              ),
            ),
          ],
        ),
      );
    }

    // Mobile layout (original layout)
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Book Appointment',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: themeColor,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _buildAppointmentBookingContent(context, isDesktop: false),
    );
  }

  // Navigation methods for sidebar
  void _navigateToTab(int index) {
    // Navigate to different tabs based on index
    switch (index) {
      case 0: // Chat
        Navigator.pushReplacementNamed(context, '/main');
        break;
      case 1: // Discover
        Navigator.pushReplacementNamed(context, '/main');
        break;
      case 3: // Shop
        Navigator.pushReplacementNamed(context, '/main');
        break;
      case 4: // Profile
        Navigator.pushReplacementNamed(context, '/main');
        break;
    }
  }

  void _openSidebarConversation(String conversationId) {
    // Navigate to specific conversation
    Navigator.pushReplacementNamed(
      context,
      '/main',
      arguments: {'conversationId': conversationId},
    );
  }

  Widget _buildAppointmentBookingContent(BuildContext context,
      {required bool isDesktop}) {
    final themeColor = GradientColors.primaryGradient[0];

    if (_isLoading && _availableTimeSlots.isEmpty) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(themeColor),
        ),
      );
    }

    if (isDesktop) {
      return _buildDesktopLayout(context);
    } else {
      return _buildMobileLayout(context);
    }
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: ResponsiveUtils.responsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProviderInfo(context),
            const SizedBox(height: 20),
            _buildCalendar(),
            const SizedBox(height: 20),
            _buildTimeSlots(),
            const SizedBox(height: 20),
            _buildAppointmentDetails(),
            const SizedBox(height: 24),
            _buildBookButton(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Container(
      color: AppColors.backgroundLight,
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey, width: 0.2),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon:
                      const Icon(Icons.arrow_back, color: AppColors.tealSurge),
                  onPressed: () => Navigator.pop(context),
                ),
                const SizedBox(width: 16),
                const Text(
                  'Book Appointment',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(32),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 1200),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProviderInfo(context),
                    const SizedBox(height: 32),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left column - Calendar
                        Expanded(
                          flex: 1,
                          child: _buildCalendar(),
                        ),
                        const SizedBox(width: 32),
                        // Right column - Time slots and details
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              _buildTimeSlots(),
                              const SizedBox(height: 32),
                              _buildAppointmentDetails(),
                              const SizedBox(height: 32),
                              _buildBookButton(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProviderInfo(BuildContext context) {
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: ResponsiveUtils.value(
        context: context,
        mobile: const EdgeInsets.all(24),
        tablet: const EdgeInsets.all(28),
        desktop: const EdgeInsets.all(32),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.provider.displayName,
            style: TextStyle(
              fontSize: ResponsiveUtils.responsiveFontSize(
                context,
                mobile: 20,
                tablet: 22,
                desktop: 24,
              ),
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: themeColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              widget.provider.specialization,
              style: TextStyle(
                fontSize: ResponsiveUtils.responsiveFontSize(
                  context,
                  mobile: 14,
                  tablet: 15,
                  desktop: 16,
                ),
                fontWeight: FontWeight.w500,
                color: themeColor,
              ),
            ),
          ),
          if (widget.service != null) ...[
            const SizedBox(height: 20),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.medical_services_outlined,
                        size: 18,
                        color: Colors.black.withOpacity(0.7),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.service!.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.timer_outlined,
                              size: 16,
                              color: Colors.black.withOpacity(0.6),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              widget.service!.formattedDuration,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.black.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.attach_money,
                              size: 16,
                              color: Colors.black.withOpacity(0.6),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              widget.service!.formattedPrice,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCalendar() {
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: ResponsiveUtils.value(
        context: context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(20),
        desktop: const EdgeInsets.all(24),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8, bottom: 16),
            child: Text(
              'Select Date',
              style: TextStyle(
                fontSize: ResponsiveUtils.responsiveFontSize(
                  context,
                  mobile: 18,
                  tablet: 20,
                  desktop: 22,
                ),
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          TableCalendar(
            firstDay: DateTime.now(),
            lastDay: DateTime.now().add(const Duration(days: 90)),
            focusedDay: _focusedDay,
            selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
            calendarFormat: CalendarFormat.month,
            availableCalendarFormats: const {CalendarFormat.month: 'Month'},
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: TextStyle(
                fontSize: ResponsiveUtils.responsiveFontSize(
                  context,
                  mobile: 16,
                  tablet: 18,
                  desktop: 20,
                ),
                fontWeight: FontWeight.w600,
              ),
              leftChevronIcon: Icon(
                Icons.chevron_left,
                color: themeColor,
                size: ResponsiveUtils.value(
                  context: context,
                  mobile: 28.0,
                  tablet: 32.0,
                  desktop: 36.0,
                ),
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right,
                color: themeColor,
                size: ResponsiveUtils.value(
                  context: context,
                  mobile: 28.0,
                  tablet: 32.0,
                  desktop: 36.0,
                ),
              ),
            ),
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay;
              });
              _loadAvailableTimeSlots();
            },
            calendarStyle: CalendarStyle(
              selectedDecoration: BoxDecoration(
                color: themeColor,
                shape: BoxShape.circle,
              ),
              todayDecoration: BoxDecoration(
                color: themeColor.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              weekendTextStyle: const TextStyle(color: Colors.red),
              outsideDaysVisible: false,
              cellMargin: ResponsiveUtils.value(
                context: context,
                mobile: const EdgeInsets.all(6),
                tablet: const EdgeInsets.all(8),
                desktop: const EdgeInsets.all(10),
              ),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black.withOpacity(0.7),
              ),
              weekendStyle: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.red.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlots() {
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 20,
                color: themeColor,
              ),
              const SizedBox(width: 8),
              const Text(
                'Available Time Slots',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _isLoading
              ? Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(themeColor),
                  ),
                )
              : _errorMessage.isNotEmpty
                  ? Center(
                      child: Column(
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 40,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Error: $_errorMessage',
                            style: const TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : _availableTimeSlots.isEmpty
                      ? Center(
                          child: Column(
                            children: [
                              Icon(
                                Icons.event_busy,
                                color: Colors.grey.shade400,
                                size: 40,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'No available time slots for this day',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      : LayoutBuilder(
                          builder: (context, constraints) {
                            // Responsive grid for time slots
                            final isDesktop =
                                ResponsiveUtils.isDesktop(context);
                            final isTablet = ResponsiveUtils.isTablet(context);

                            if (isDesktop || isTablet) {
                              // Grid layout for desktop/tablet
                              final crossAxisCount = isDesktop ? 2 : 2;
                              return GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: crossAxisCount,
                                  childAspectRatio: 3.5,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                ),
                                itemCount: _availableTimeSlots.length,
                                itemBuilder: (context, index) {
                                  return _buildTimeSlotChip(
                                      _availableTimeSlots[index]);
                                },
                              );
                            } else {
                              // Wrap layout for mobile
                              return Wrap(
                                spacing: 10,
                                runSpacing: 12,
                                children: _availableTimeSlots
                                    .map((slot) => _buildTimeSlotChip(slot))
                                    .toList(),
                              );
                            }
                          },
                        ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotChip(TimeSlot slot) {
    final themeColor = GradientColors.primaryGradient[0];
    final isSelected = _selectedTimeSlot != null &&
        _selectedTimeSlot!.startTime == slot.startTime &&
        _selectedTimeSlot!.endTime == slot.endTime;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTimeSlot = isSelected ? null : slot;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? themeColor : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? themeColor : Colors.grey.shade300,
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: themeColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Text(
          '${slot.startTime} - ${slot.endTime}',
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentDetails() {
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description_outlined,
                size: 20,
                color: themeColor,
              ),
              const SizedBox(width: 8),
              const Text(
                'Appointment Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _reasonController,
            decoration: InputDecoration(
              labelText: 'Reason for appointment *',
              labelStyle: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
              hintText: 'Please describe your symptoms or reason for visit',
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: themeColor,
                  width: 1.5,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            maxLines: 3,
            style: const TextStyle(
              fontSize: 15,
            ),
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _notesController,
            decoration: InputDecoration(
              labelText: 'Additional notes (optional)',
              labelStyle: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
              hintText: 'Any additional information you want to share',
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: themeColor,
                  width: 1.5,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            maxLines: 3,
            style: const TextStyle(
              fontSize: 15,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookButton() {
    final themeColor = GradientColors.primaryGradient[0];
    final isButtonEnabled = !_isLoading && _selectedTimeSlot != null;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 8),
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: isButtonEnabled ? GradientColors.getPrimaryGradient() : null,
        color: isButtonEnabled ? null : Colors.grey.shade300,
        boxShadow: isButtonEnabled
            ? [
                BoxShadow(
                  color: themeColor.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isButtonEnabled ? _bookAppointment : null,
          borderRadius: BorderRadius.circular(16),
          child: Center(
            child: _isLoading
                ? const SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_month,
                        color: Colors.white,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Book Appointment',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
