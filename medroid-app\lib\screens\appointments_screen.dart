import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/screens/reschedule_appointment_screen.dart';
import 'package:medroid_app/screens/main_video_consultation_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/payment_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/main_navigation_sidebar.dart';
import 'package:medroid_app/widgets/responsive_container.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({Key? key}) : super(key: key);

  @override
  _AppointmentsScreenState createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen> {
  bool _isLoading = true;
  bool _isProcessingPayment = false;
  List<Appointment> _appointments = [];
  String _filter = 'upcoming'; // 'upcoming', 'past', 'all'
  late PaymentService _paymentService;

  @override
  void initState() {
    super.initState();
    _paymentService = PaymentService();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/appointments/user');

      if (response != null) {
        final List<Appointment> appointments = [];
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }

        setState(() {
          _appointments = appointments;
          _isLoading = false;
        });
      } else {
        setState(() {
          _appointments = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading appointments: $e');
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading appointments: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        return _appointments
            .where((appointment) =>
                appointment.date.isAfter(now) ||
                (appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'past':
        return _appointments
            .where((appointment) =>
                appointment.date.isBefore(now) &&
                !(appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  void _joinVideoConsultation(Appointment appointment) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MainVideoConsultationScreen(
          appointmentId: appointment.id.toString(),
        ),
      ),
    );
  }

  Future<void> _rescheduleAppointment(Appointment appointment) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RescheduleAppointmentScreen(
          appointment: appointment,
        ),
      ),
    );

    if (result == true) {
      // Refresh appointments if rescheduled successfully
      _loadAppointments();
    }
  }

  Future<void> _cancelAppointment(Appointment appointment) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Appointment'),
        content:
            const Text('Are you sure you want to cancel this appointment?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      if (!mounted) return;
      final apiService = RepositoryProvider.of<ApiService>(context);

      // First try to update status to cancelled
      await apiService.updateAppointment(
        appointmentId: appointment.id.toString(),
        status: 'cancelled',
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Appointment cancelled successfully'),
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Refresh appointments
      _loadAppointments();
    } catch (e) {
      debugPrint('Error cancelling appointment: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error cancelling appointment: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _deleteAppointment(Appointment appointment) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Appointment'),
        content: const Text(
          'Are you sure you want to delete this appointment? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      if (!mounted) return;
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Delete the appointment
      final success = await apiService.deleteAppointment(
        appointment.id.toString(),
      );

      if (!mounted) return;
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment deleted successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Refresh appointments
        _loadAppointments();
      } else {
        throw Exception('Failed to delete appointment');
      }
    } catch (e) {
      debugPrint('Error deleting appointment: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting appointment: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _processPayment(Appointment appointment) async {
    if (appointment.amount == null || appointment.amount! <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid payment amount'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isProcessingPayment = true;
    });

    try {
      // First, check if the slot is still available
      final apiService = RepositoryProvider.of<ApiService>(context);
      final isAvailable = await apiService.checkSlotAvailability(
        providerId: appointment.providerId.toString(),
        date: DateFormat('yyyy-MM-dd').format(appointment.date),
        timeSlot: appointment.timeSlot,
      );

      if (!mounted) return;

      if (!isAvailable) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'This time slot is no longer available. Please choose another time.'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        setState(() {
          _isProcessingPayment = false;
        });

        // Refresh appointments to show updated status
        _loadAppointments();
        return;
      }

      // Create payment intent
      final paymentIntentResponse = await _paymentService.createPaymentIntent(
        appointmentId: appointment.id.toString(),
        amount: appointment.amount!,
        currency: 'usd',
      );

      if (!mounted) return;

      final paymentIntentId = paymentIntentResponse['id'];
      final clientSecret = paymentIntentResponse['client_secret'];

      // For web, we'll use a different approach - direct server confirmation
      if (kIsWeb) {
        // Show a dialog to inform the user about the payment process
        final shouldProceed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Payment Confirmation'),
            content: const Text(
              'Since you are using a web browser, we will process your payment directly. In a production environment, this would redirect to a secure payment page.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Proceed'),
              ),
            ],
          ),
        );

        if (shouldProceed != true || !mounted) {
          setState(() {
            _isProcessingPayment = false;
          });
          return;
        }

        // Simulate a successful payment for web
        final confirmResult = await _paymentService.confirmPayment(
          paymentIntentId: paymentIntentId,
          appointmentId: appointment.id.toString(),
          isWebSimulation: true,
        );

        if (!mounted) return;

        if (confirmResult['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment successful! Appointment confirmed.'),
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Refresh appointments
          _loadAppointments();
        } else {
          throw Exception(
              'Payment confirmation failed: ${confirmResult['message']}');
        }
      } else {
        // For mobile, use the Flutter Stripe SDK
        // Fetch and initialize Stripe with live publishable key from backend
        final publishableKey = await _paymentService.getStripePublishableKey();
        await _paymentService.initializeStripe(publishableKey);

        // Process payment with Stripe
        final paymentResult = await _paymentService.processPayment(
          paymentIntentId: paymentIntentId,
          paymentIntentClientSecret: clientSecret,
        );

        if (!mounted) return;

        if (paymentResult['success'] == true) {
          // Confirm payment on the server
          final confirmResult = await _paymentService.confirmPayment(
            paymentIntentId: paymentIntentId,
            appointmentId: appointment.id.toString(),
          );

          if (!mounted) return;

          if (confirmResult['success'] == true) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Payment successful! Appointment confirmed.'),
                behavior: SnackBarBehavior.floating,
              ),
            );

            // Refresh appointments
            _loadAppointments();
          } else {
            throw Exception(
                'Payment confirmation failed: ${confirmResult['message']}');
          }
        } else {
          throw Exception(
              'Payment processing failed: ${paymentResult['error']}');
        }
      }
    } catch (e) {
      debugPrint('Error processing payment: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error processing payment: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPayment = false;
        });
      }
    }
  }

  // Navigation methods for sidebar
  void _navigateToTab(int index) {
    // Navigate to main navigation with the selected tab
    Navigator.pushReplacementNamed(
      context,
      '/main',
      arguments: {
        'tabIndex': index,
      },
    );
  }

  void _openSidebarConversation(String conversationId) {
    // Navigate to the main screen with the chat tab selected and the conversation ID
    Navigator.pushReplacementNamed(
      context,
      '/main',
      arguments: {
        'tabIndex': 0, // Chat tab
        'conversationId': conversationId,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a desktop-sized screen
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // For desktop and tablet layouts, use sidebar navigation
    if (isDesktop || isTablet) {
      return Scaffold(
        backgroundColor: AppColors.backgroundLight,
        body: Row(
          children: [
            // Use the reusable sidebar widget
            MainNavigationSidebar(
              currentIndex: -1, // No specific tab selected for appointments
              onNavigationChanged: _navigateToTab,
              onConversationSelected: _openSidebarConversation,
            ),
            // Main content area
            Expanded(
              child: ResponsiveCenteredContainer(
                padding: EdgeInsets.zero,
                constraints: null,
                child: _buildAppointmentsContent(context, isDesktop: true),
              ),
            ),
          ],
        ),
      );
    }

    // Mobile layout (original layout)
    return Scaffold(
      backgroundColor:
          AppColors.backgroundLight, // Match Discover screen background
      appBar: AppBar(
        backgroundColor:
            AppColors.backgroundLight, // Match Discover screen background
        title: const Text('My Appointments'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAppointments,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildAppointmentsContent(context, isDesktop: false),
    );
  }

  Widget _buildAppointmentsContent(BuildContext context,
      {required bool isDesktop}) {
    return Column(
      children: [
        // Add title and refresh button at the top for desktop
        if (isDesktop)
          Padding(
            padding: const EdgeInsets.fromLTRB(32, 24, 32, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'My Appointments',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.refresh_outlined,
                    size: 24,
                  ),
                  onPressed: _loadAppointments,
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
        _buildFilterChips(),
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredAppointments.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No ${_filter == "all" ? "" : _filter} appointments found',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: () {
                              // Navigate to provider marketplace to book an appointment
                              Navigator.pushNamed(context, '/providers');
                            },
                            child: const Text('Book an Appointment'),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadAppointments,
                      child: ListView.builder(
                        padding: EdgeInsets.all(isDesktop ? 32 : 16),
                        itemCount: _filteredAppointments.length,
                        itemBuilder: (context, index) {
                          final appointment = _filteredAppointments[index];
                          return _buildAppointmentCard(appointment);
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white, // Keep white for the filter chips
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            const SizedBox(width: 16),
            _buildFilterChip('Upcoming', 'upcoming'),
            const SizedBox(width: 12),
            _buildFilterChip('Past', 'past'),
            const SizedBox(width: 12),
            _buildFilterChip('All', 'all'),
            const SizedBox(width: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String filterValue) {
    final isSelected = _filter == filterValue;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? const Color.fromRGBO(23, 195, 178, 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? AppColors.tealSurge : Colors.grey.shade300,
          width: 1.5,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _filter = filterValue;
          });
        },
        borderRadius: BorderRadius.circular(20),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? AppColors.tealSurge : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentCard(Appointment appointment) {
    final dateFormat = DateFormat('EEEE, MMMM d');
    final formattedDate = dateFormat.format(appointment.date);

    // Determine if this is a telemedicine appointment that can be joined
    final bool canJoinVideo = appointment.canJoinVideoConsultation;

    // Parse the status color
    final Color statusColor = Color(int.parse(
      appointment.statusColor.replaceFirst('#', '0xFF'),
    ));

    // Get theme gradient color for buttons
    const themeColor = AppColors.tealSurge;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status indicator - thin line at the top
            Container(
              height: 4,
              width: double.infinity,
              color: statusColor,
            ),

            // Appointment details
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top row with date and status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Date with icon
                      Row(
                        children: [
                          Icon(Icons.calendar_today,
                              size: 18, color: Colors.black.withOpacity(0.7)),
                          const SizedBox(width: 8),
                          Text(
                            formattedDate,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),

                      // Status chip
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(statusColor.red,
                              statusColor.green, statusColor.blue, 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          appointment.status.toUpperCase(),
                          style: TextStyle(
                            color: statusColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Time and telemedicine indicator
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 18, color: Colors.black.withOpacity(0.7)),
                      const SizedBox(width: 8),
                      Text(
                        '${appointment.timeSlot['start_time']} - ${appointment.timeSlot['end_time']}',
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.black.withOpacity(0.8),
                        ),
                      ),
                      if (appointment.isTelemedicine)
                        Container(
                          margin: const EdgeInsets.only(left: 12),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(23, 195, 178, 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.videocam, size: 14, color: themeColor),
                              SizedBox(width: 4),
                              Text(
                                'Telemedicine',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: themeColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Consultation duration (if completed)
                  if (appointment.consultationStartedAt != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          Icon(Icons.timer,
                              size: 18, color: Colors.black.withOpacity(0.7)),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Consultation: ${appointment.formattedConsultationDuration ?? 'In progress'}',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Provider
                  Row(
                    children: [
                      Icon(Icons.person,
                          size: 18, color: Colors.black.withOpacity(0.7)),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          appointment.providerName,
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: Colors.black.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Service if available
                  if (appointment.serviceName != null) ...[
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(Icons.medical_services,
                            size: 18, color: Colors.black.withOpacity(0.7)),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            appointment.serviceName!,
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.black.withOpacity(0.8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],

                  // Reason
                  const SizedBox(height: 12),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.note,
                          size: 18, color: Colors.black.withOpacity(0.7)),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          appointment.reason,
                          style: TextStyle(
                            fontSize: 15,
                            color: Colors.black.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Action buttons
                  const SizedBox(height: 20),

                  // Payment button for pending payment appointments
                  if (appointment.isPendingPayment &&
                      appointment.amount != null)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.payment, size: 18),
                        label: Text('Pay Now ${appointment.formattedAmount}'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: const Color(0xFFFF9800), // Orange
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        onPressed: () => _processPayment(appointment),
                      ),
                    ),

                  // Video consultation button
                  if (canJoinVideo)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.videocam, size: 18),
                        label: const Text('Start Video Consultation'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: themeColor,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        onPressed: () => _joinVideoConsultation(appointment),
                      ),
                    ),

                  // Action buttons for upcoming appointments
                  if (appointment.status != 'cancelled' &&
                      appointment.status != 'pending_payment' &&
                      (appointment.date.isAfter(DateTime.now()) ||
                          (appointment.date.day == DateTime.now().day &&
                              appointment.date.month == DateTime.now().month &&
                              appointment.date.year == DateTime.now().year)))
                    Row(
                      children: [
                        // Reschedule button
                        Expanded(
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.calendar_today, size: 16),
                            label: const Text('Reschedule'),
                            onPressed: () =>
                                _rescheduleAppointment(appointment),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: themeColor,
                              side: const BorderSide(
                                  color: themeColor, width: 1.5),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Cancel button
                        Expanded(
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.cancel_outlined, size: 16),
                            label: const Text('Cancel'),
                            onPressed: () => _cancelAppointment(appointment),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.red,
                              side: const BorderSide(
                                  color: Colors.red, width: 1.5),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                  // Delete button for cancelled, pending payment, or past appointments
                  if (appointment.status == 'cancelled' ||
                      appointment.status == 'pending_payment' ||
                      (appointment.date.isBefore(DateTime.now()) &&
                          !(appointment.date.day == DateTime.now().day &&
                              appointment.date.month == DateTime.now().month &&
                              appointment.date.year == DateTime.now().year)))
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        icon: const Icon(Icons.delete_outline, size: 16),
                        label: const Text('Delete'),
                        onPressed: () => _deleteAppointment(appointment),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red, width: 1.5),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
