import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/appointment_card.dart';

class AppointmentsScreenDesktop extends StatefulWidget {
  const AppointmentsScreenDesktop({Key? key}) : super(key: key);

  @override
  _AppointmentsScreenDesktopState createState() =>
      _AppointmentsScreenDesktopState();
}

class _AppointmentsScreenDesktopState extends State<AppointmentsScreenDesktop> {
  bool _isLoading = true;
  List<Appointment> _appointments = [];
  String _filter = 'upcoming'; // 'upcoming', 'past', 'all'

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/appointments/user');

      if (response != null) {
        final List<Appointment> appointments = [];
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }

        setState(() {
          _appointments = appointments;
          _isLoading = false;
        });
      } else {
        setState(() {
          _appointments = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading appointments: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading appointments: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        return _appointments
            .where((appointment) =>
                appointment.date.isAfter(now) ||
                (appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'past':
        return _appointments
            .where((appointment) =>
                appointment.date.isBefore(now) &&
                !(appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _filter == value;

    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: ChoiceChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            setState(() {
              _filter = value;
            });
          }
        },
        backgroundColor: Colors.white,
        selectedColor: AppColors.tealSurge.withValues(alpha: 0.2),
        labelStyle: TextStyle(
          color: isSelected ? AppColors.tealSurge : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: isSelected ? AppColors.tealSurge : Colors.grey.shade300,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'My Appointments',
          style: TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.tealSurge),
            onPressed: _loadAppointments,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter chips
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            child: Row(
              children: [
                _buildFilterChip('Upcoming', 'upcoming'),
                _buildFilterChip('Past', 'past'),
                _buildFilterChip('All', 'all'),
              ],
            ),
          ),

          // Divider
          const Divider(height: 1),

          // Appointments list
          Expanded(
            child: _isLoading
                ? const Center(
                    child:
                        CircularProgressIndicator(color: AppColors.tealSurge))
                : _filteredAppointments.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 64, color: Colors.grey[300]),
                            const SizedBox(height: 16),
                            Text(
                              'No ${_filter == "all" ? "" : _filter} appointments found',
                              style: TextStyle(
                                  fontSize: 18, color: Colors.grey[600]),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton(
                              onPressed: () {
                                // Navigate to provider marketplace to book an appointment
                                Navigator.pushNamed(context, '/providers');
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.tealSurge,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Book an Appointment'),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadAppointments,
                        color: AppColors.tealSurge,
                        child: LayoutBuilder(builder: (context, constraints) {
                          // Determine layout based on available width
                          final availableWidth =
                              constraints.maxWidth - 32; // Account for padding
                          const cardWidth = 400.0; // Optimal card width
                          final crossAxisCount =
                              (availableWidth / cardWidth).floor().clamp(1, 3);

                          // Use a more flexible approach for desktop
                          if (crossAxisCount == 1) {
                            // Single column layout for narrow screens
                            return ListView.builder(
                              padding: const EdgeInsets.all(16.0),
                              itemCount: _filteredAppointments.length,
                              itemBuilder: (context, index) {
                                return Container(
                                  constraints:
                                      const BoxConstraints(maxWidth: 600),
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: AppointmentCard(
                                    appointment: _filteredAppointments[index],
                                    onActionCompleted: _loadAppointments,
                                  ),
                                );
                              },
                            );
                          }

                          // Multi-column grid layout with flexible height
                          return Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: GridView.builder(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: crossAxisCount,
                                childAspectRatio:
                                    0.75, // Reduced to minimize white space
                                crossAxisSpacing: 16,
                                mainAxisSpacing: 16,
                              ),
                              itemCount: _filteredAppointments.length,
                              itemBuilder: (context, index) {
                                return AppointmentCard(
                                  appointment: _filteredAppointments[index],
                                  onActionCompleted: _loadAppointments,
                                );
                              },
                            ),
                          );
                        }),
                      ),
          ),
        ],
      ),
    );
  }
}
