import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/sso_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/gradient_button.dart';
import 'package:medroid_app/widgets/gradient_text.dart';
import 'package:medroid_app/widgets/app_logo.dart';
import 'package:medroid_app/widgets/sso_button.dart';
import 'package:medroid_app/screens/sso_additional_details_screen.dart';
import 'dart:async';

class AuthScreen extends StatefulWidget {
  final String initialMode;
  final Function(String)? onLoginSuccess;

  const AuthScreen({
    Key? key,
    this.initialMode = 'login',
    this.onLoginSuccess,
  }) : super(key: key);

  @override
  AuthScreenState createState() => AuthScreenState();
}

class AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  bool _isLogin = true;
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  final _formKey = GlobalKey<FormState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Feature slides animation
  late AnimationController _slideController;
  late PageController _pageController;
  int _currentSlide = 0;
  Timer? _slideTimer;

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _referralCodeController = TextEditingController();

  String _selectedGender = 'male';
  final AuthService _authService = AuthService();
  final SSOService _ssoService = SSOService();

  // Feature slides data
  final List<Map<String, dynamic>> _featureSlides = [
    {
      'icon': Icons.psychology_outlined,
      'title': 'AI Doctor Chat',
      'subtitle': 'Intelligent health conversations',
      'description':
          'Get instant medical advice and health insights from our advanced AI doctor, available 24/7 to answer your health questions.',
      'color': AppColors.tealSurge,
    },
    {
      'icon': Icons.calendar_month_outlined,
      'title': 'Book Appointments',
      'subtitle': 'Schedule with healthcare providers',
      'description':
          'Easily book appointments with verified healthcare professionals in your area. Choose from various specialties and time slots.',
      'color': AppColors.coralPop,
    },
    {
      'icon': Icons.local_hospital_outlined,
      'title': 'Healthcare Marketplace',
      'subtitle': 'Browse and book providers',
      'description':
          'Discover healthcare providers, read reviews, compare services, and book appointments all in one convenient platform.',
      'color': AppColors.mintGlow,
    },
    {
      'icon': Icons.people_outline,
      'title': 'Health Community',
      'subtitle': 'Connect with health-focused content',
      'description':
          'Join a community of health-conscious individuals. Share experiences, get tips, and stay motivated on your wellness journey.',
      'color': AppColors.slateGrey,
    },
    {
      'icon': Icons.person_outline,
      'title': 'Track Your Journey',
      'subtitle': 'Profile & health history',
      'description':
          'Keep track of your health appointments, chat history, and wellness progress all in one secure, personalized profile.',
      'color': AppColors.midnightNavy,
    },
  ];
  final ApiService _apiService = ApiService();

  bool _isGoogleLoading = false;
  bool _isAppleLoading = false;

  // Chat animation state
  List<Map<String, dynamic>> _chatMessages = [];
  int _currentMessageIndex = 0;
  Timer? _chatTimer;

  @override
  void initState() {
    super.initState();
    // Set initial mode based on widget parameter
    _isLogin = widget.initialMode == 'login';

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Initialize slide controller and page controller
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pageController = PageController();

    // Start auto-slide timer for feature slides
    _startSlideTimer();

    // Initialize chat messages
    _initializeChatMessages();

    _animationController.forward();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _dobController.dispose();
    _referralCodeController.dispose();
    _animationController.dispose();
    _slideController.dispose();
    _pageController.dispose();
    _slideTimer?.cancel();
    _chatTimer?.cancel();
    super.dispose();
  }

  // Start auto-slide timer for feature slides
  void _startSlideTimer() {
    _slideTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (mounted && _pageController.hasClients) {
        final nextPage = (_currentSlide + 1) % _featureSlides.length;
        _pageController.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  // Initialize chat messages
  void _initializeChatMessages() {
    final messages = [
      {
        'text': 'Hi! I\'m your AI health assistant. How are you feeling today?',
        'isUser': false,
        'timestamp': DateTime.now(),
      },
      {
        'text': 'I\'ve been having trouble sleeping lately. Any suggestions?',
        'isUser': true,
        'timestamp': DateTime.now().add(const Duration(seconds: 2)),
      },
      {
        'text':
            'Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep.',
        'isUser': false,
        'timestamp': DateTime.now().add(const Duration(seconds: 4)),
      },
      {
        'text': 'That makes sense. I\'ll try that tonight. Thank you!',
        'isUser': true,
        'timestamp': DateTime.now().add(const Duration(seconds: 6)),
      },
      {
        'text':
            'You\'re welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?',
        'isUser': false,
        'timestamp': DateTime.now().add(const Duration(seconds: 8)),
      },
      {
        'text':
            'Yes, that would be helpful. I think stress might be affecting my sleep.',
        'isUser': true,
        'timestamp': DateTime.now().add(const Duration(seconds: 10)),
      },
      {
        'text':
            'Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system.',
        'isUser': false,
        'timestamp': DateTime.now().add(const Duration(seconds: 12)),
      },
      {
        'text':
            'I\'ll definitely try that breathing technique. Thank you for the personalized advice!',
        'isUser': true,
        'timestamp': DateTime.now().add(const Duration(seconds: 14)),
      },
    ];

    _chatMessages.clear();
    _currentMessageIndex = 0;

    // Start the chat animation
    _startChatAnimation(messages);
  }

  // Start chat animation
  void _startChatAnimation(List<Map<String, dynamic>> messages) {
    _chatTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (_currentMessageIndex < messages.length) {
        setState(() {
          _chatMessages.add(messages[_currentMessageIndex]);
          _currentMessageIndex++;
        });
      } else {
        timer.cancel();
        // Restart animation after a pause
        Timer(const Duration(seconds: 4), () {
          if (mounted) {
            _initializeChatMessages();
          }
        });
      }
    });
  }

  // Toggle between login and registration forms
  void _toggleAuthMode() {
    setState(() {
      _isLogin = !_isLogin;
      _animationController.reset();
      _animationController.forward();
    });
  }

  // Basic email validation
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Handle form submission
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isLogin) {
        // Handle login
        final loginResult = await _authService.login(
          email: _emailController.text,
          password: _passwordController.text,
        );

        final bool success = loginResult['success'] as bool;
        final String role = loginResult['role'] as String;

        if (mounted && success) {
          // Get the token
          final token = await _authService.getToken() ?? '';

          // Check if this is a provider login
          if (role == 'provider') {
            // Navigate to provider dashboard
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/provider-dashboard');
            }
          } else {
            // Call the onLoginSuccess callback if provided for patients
            if (widget.onLoginSuccess != null) {
              widget.onLoginSuccess!(token);
            } else {
              // Navigate to the main app screen on success
              if (mounted) {
                Navigator.of(context).pushReplacementNamed('/');
              }
            }
          }
        } else if (mounted) {
          // Show error on failure
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Login failed. Please check your credentials.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        // Handle registration (always as patient)
        final success = await _authService.register(
          name: _nameController.text,
          email: _emailController.text,
          password: _passwordController.text,
          role: 'patient', // Always register as patient
          gender: _selectedGender,
          dateOfBirth:
              _dobController.text.isNotEmpty ? _dobController.text : null,
          referralCode: _referralCodeController.text.isNotEmpty
              ? _referralCodeController.text
              : null,
        );

        if (mounted && success) {
          // Call the onLoginSuccess callback if provided
          if (widget.onLoginSuccess != null) {
            // Get the token and pass it to the callback
            final token = await _authService.getToken() ?? '';
            widget.onLoginSuccess!(token);
          } else {
            // Navigate to the main app screen on success
            Navigator.of(context).pushReplacementNamed('/');
          }
        } else if (mounted) {
          // Show error on failure
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Registration failed. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Handle Google Sign-In
  Future<void> _handleGoogleSignIn() async {
    setState(() {
      _isGoogleLoading = true;
    });

    try {
      final ssoData = await _ssoService.signInWithGoogle();

      if (ssoData != null && mounted) {
        final result = await _ssoService.authenticateWithBackend(ssoData);

        if (result['success'] && mounted) {
          if (result['needs_additional_info'] == true) {
            // Navigate to additional details screen
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => SSOAdditionalDetailsScreen(
                  tempToken: result['token'],
                  userData: result['user'],
                ),
              ),
            );
          } else {
            // Save token and user data, then navigate to main app
            await _apiService.setAuthToken(result['token']);
            await _authService.saveUserData(result['user']);

            if (widget.onLoginSuccess != null) {
              widget.onLoginSuccess!(result['token']);
            } else if (mounted) {
              Navigator.of(context).pushReplacementNamed('/');
            }
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'Google sign-in failed'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google sign-in error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGoogleLoading = false;
        });
      }
    }
  }

  // Handle Apple Sign-In
  Future<void> _handleAppleSignIn() async {
    setState(() {
      _isAppleLoading = true;
    });

    try {
      final ssoData = await _ssoService.signInWithApple();

      if (ssoData != null && mounted) {
        final result = await _ssoService.authenticateWithBackend(ssoData);

        if (result['success'] && mounted) {
          if (result['needs_additional_info'] == true) {
            // Navigate to additional details screen
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => SSOAdditionalDetailsScreen(
                  tempToken: result['token'],
                  userData: result['user'],
                ),
              ),
            );
          } else {
            // Save token and user data, then navigate to main app
            await _apiService.setAuthToken(result['token']);
            await _authService.saveUserData(result['user']);

            if (widget.onLoginSuccess != null) {
              widget.onLoginSuccess!(result['token']);
            } else if (mounted) {
              Navigator.of(context).pushReplacementNamed('/');
            }
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'Apple sign-in failed'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple sign-in error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAppleLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Use two-panel layout for desktop/tablet, single panel for mobile
    if (isDesktop || isTablet) {
      return _buildTwoPanelLayout(context, false, isDesktop, screenWidth);
    } else {
      return _buildSinglePanelLayout(context, false);
    }
  }

  // Two-panel layout for desktop and tablet (Claude-inspired)
  Widget _buildTwoPanelLayout(BuildContext context, bool isDarkMode,
      bool isDesktop, double screenWidth) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: Row(
          children: [
            // Left panel - Authentication form (50% width)
            Expanded(
              flex: 1,
              child: Container(
                color: Colors.white,
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 48.0),
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Container(
                        constraints: const BoxConstraints(maxWidth: 400),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Claude-inspired header
                            _buildClaudeHeader(context),

                            const SizedBox(height: 40),

                            // Claude-inspired auth form
                            _buildClaudeAuthForm(context, isDesktop),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Right panel - Feature visualization (50% width)
            Expanded(
              flex: 1,
              child: Container(
                color: const Color(0xFFF8F9FA),
                child: _buildClaudeFeaturePanel(context, isDesktop),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Single panel layout for mobile (Claude-inspired design)
  Widget _buildSinglePanelLayout(BuildContext context, bool isDarkMode) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Claude-inspired header
                      _buildClaudeHeader(context),

                      const SizedBox(height: 40),

                      // Claude-inspired auth form
                      _buildClaudeAuthForm(context, false), // false for mobile
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDarkMode,
      [double logoSize = 120]) {
    return Column(
      children: [
        // Medroid icon with animation
        Image.asset(
          'assets/images/medroid_icon.png',
          width: logoSize,
          height: logoSize,
          errorBuilder: (context, error, stackTrace) {
            return AppLogo(
              size: logoSize,
              animate: true,
              showShadow: true,
            );
          },
        ),

        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildAuthForm(bool isDarkMode) {
    // No need for isDesktop variable here as it's not used in this method
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Name field (only for registration)
        if (!_isLogin) ...[
          _buildInputField(
            controller: _nameController,
            label: 'Full Name',
            icon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your name';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
        ],

        // Email field
        _buildInputField(
          controller: _emailController,
          label: 'Email',
          icon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email';
            }
            if (!_isValidEmail(value)) {
              return 'Please enter a valid email';
            }
            return null;
          },
        ),

        const SizedBox(height: 20),

        // Password field
        _buildInputField(
          controller: _passwordController,
          label: 'Password',
          icon: Icons.lock_outline,
          obscureText: !_isPasswordVisible,
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              size: 20,
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your password';
            }
            if (!_isLogin && value.length < 8) {
              return 'Password must be at least 8 characters';
            }
            return null;
          },
        ),

        // Gender and DOB fields (only for registration)
        if (!_isLogin) ...[
          const SizedBox(height: 20),

          // Date of Birth field with improved UI
          GestureDetector(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: DateTime.now().subtract(
                    const Duration(days: 365 * 25)), // Default to 25 years ago
                firstDate: DateTime(1900),
                lastDate: DateTime.now(),
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: ColorScheme.light(
                        primary: AppColors.tealSurge,
                        onPrimary: Colors.white,
                        surface: isDarkMode ? Colors.grey[800]! : Colors.white,
                        onSurface: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    child: child!,
                  );
                },
              );

              if (picked != null) {
                setState(() {
                  _dobController.text =
                      "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
                });
              }
            },
            child: AbsorbPointer(
              child: _buildInputField(
                controller: _dobController,
                label: 'Date of Birth',
                icon: Icons.calendar_today,
                validator: (value) {
                  // Date of birth is optional
                  return null;
                },
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Referral Code field
          _buildInputField(
            controller: _referralCodeController,
            label: 'Referral Code (Optional)',
            icon: Icons.card_giftcard,
            validator: (value) {
              // Referral code is optional
              return null;
            },
          ),

          const SizedBox(height: 20),

          // Gender selection with improved UI
          Row(
            children: [
              Icon(
                Icons.person_outline,
                size: 20,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                'Gender:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: isDarkMode
                  ? const Color(0x33000000) // Colors.black with 20% opacity
                  : const Color(0xB3FFFFFF), // Colors.white with 70% opacity
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: _buildGenderOption(
                    label: 'Male',
                    value: 'male',
                    isSelected: _selectedGender == 'male',
                    isDarkMode: isDarkMode,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildGenderOption(
                    label: 'Female',
                    value: 'female',
                    isSelected: _selectedGender == 'female',
                    isDarkMode: isDarkMode,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildGenderOption(
                    label: 'Other',
                    value: 'other',
                    isSelected: _selectedGender == 'other',
                    isDarkMode: isDarkMode,
                  ),
                ),
              ],
            ),
          ),
        ],

        // No role selection - always patient
      ],
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    required String? Function(String?) validator,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: controller.text.isNotEmpty
                ? const Color(0x1A17C3B2) // Teal color with 10% opacity
                : Colors.transparent,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: obscureText,
        style: TextStyle(
          fontSize: isDesktop ? 14 : 16,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
        decoration: InputDecoration(
          labelText: label,
          floatingLabelBehavior: FloatingLabelBehavior.auto,
          labelStyle: TextStyle(
            fontSize: isDesktop ? 13 : 14,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          prefixIcon: Icon(
            icon,
            size: isDesktop ? 18 : 20,
            color: controller.text.isNotEmpty
                ? AppColors.tealSurge
                : isDarkMode
                    ? Colors.grey[400]
                    : Colors.grey[600],
          ),
          suffixIcon: suffixIcon,
          filled: true,
          fillColor: isDarkMode
              ? const Color(0x33000000) // Colors.black with 20% opacity
              : const Color(0xB3FFFFFF), // Colors.white with 70% opacity
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: controller.text.isNotEmpty
                  ? const Color(0x8017C3B2) // Teal color with 50% opacity
                  : isDarkMode
                      ? Colors.grey[800]!
                      : Colors.grey[300]!,
              width: controller.text.isNotEmpty ? 1.5 : 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: AppColors.tealSurge,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: AppColors.error,
              width: 1.5,
            ),
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: 20,
            vertical: isDesktop ? 14 : 16,
          ),
        ),
        validator: validator,
        onChanged: (_) =>
            setState(() {}), // Rebuild to update styling based on text
      ),
    );
  }

  // Role selection has been removed

  Widget _buildSubmitButton() {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return _isLoading
        ? Center(
            child: Container(
              width: 48,
              height: 48,
              padding: const EdgeInsets.all(8.0),
              child: const CircularProgressIndicator(),
            ),
          )
        : AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: isDesktop ? 50 : 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x3D17C3B2), // Teal color with 24% opacity
                  blurRadius: 12,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: GradientButton(
              onPressed: _submitForm,
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: isDesktop ? 14 : 16),
              borderRadius: BorderRadius.circular(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _isLogin ? 'Sign In' : 'Create Account',
                    style: TextStyle(
                      fontSize: isDesktop ? 15 : 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_rounded,
                    size: isDesktop ? 18 : 20,
                  ),
                ],
              ),
            ),
          );
  }

  Widget _buildGenderOption({
    required String label,
    required String value,
    required bool isSelected,
    required bool isDarkMode,
  }) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedGender = value;
        });
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDarkMode
                  ? const Color(0x3317C3B2) // Teal color with 20% opacity
                  : const Color(0x1A17C3B2)) // Teal color with 10% opacity
              : isDarkMode
                  ? const Color(0x33000000) // Colors.black with 20% opacity
                  : const Color(0xB3FFFFFF), // Colors.white with 70% opacity
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? AppColors.tealSurge
                : isDarkMode
                    ? Colors.grey[800]!
                    : Colors.grey[300]!,
            width: 1.5,
          ),
        ),
        child: Center(
          child: Text(
            label,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected
                  ? AppColors.tealSurge
                  : isDarkMode
                      ? Colors.grey[300]
                      : Colors.grey[700],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildToggleAuthModeButton(bool isDarkMode) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Wrap(
      alignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        Text(
          _isLogin ? 'Don\'t have an account?' : 'Already have an account?',
          style: TextStyle(
            fontSize: isDesktop ? 13 : 14,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        TextButton(
          onPressed: _isLoading ? null : _toggleAuthMode,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: GradientText(
            _isLogin ? 'Sign Up' : 'Sign In',
            gradient: AppColors.getPrimaryGradient(),
            style: TextStyle(
              fontSize: isDesktop ? 13 : 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  // Claude-inspired header
  Widget _buildClaudeHeader(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 768;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main heading
        Text(
          'Your health,\nour priority',
          style: GoogleFonts.inter(
            fontSize: isDesktop ? 48 : 36,
            fontWeight: FontWeight.w400,
            color: Colors.black,
            height: 1.1,
            letterSpacing: -0.5,
          ),
        ),

        const SizedBox(height: 16),

        // Subtitle
        Text(
          'AI-powered healthcare that puts your wellness first.',
          style: GoogleFonts.inter(
            fontSize: isDesktop ? 16 : 14,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF6B7280),
            height: 1.4,
          ),
        ),
      ],
    );
  }

  // Claude-inspired auth form
  Widget _buildClaudeAuthForm(BuildContext context, bool isDesktop) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Google SSO button
          _buildClaudeGoogleButton(),

          const SizedBox(height: 16),

          // OR divider
          _buildClaudeDivider(),

          const SizedBox(height: 16),

          // Name input (only for signup)
          if (!_isLogin) ...[
            _buildClaudeNameInput(),
            const SizedBox(height: 16),
          ],

          // Email input
          _buildClaudeEmailInput(),

          const SizedBox(height: 16),

          // Password input
          _buildClaudePasswordInput(),

          // Additional signup fields
          if (!_isLogin) ...[
            const SizedBox(height: 16),

            // Date of Birth
            _buildClaudeDateOfBirthInput(),

            const SizedBox(height: 16),

            // Gender selection
            _buildClaudeGenderSelection(),

            const SizedBox(height: 16),

            // Referral code
            _buildClaudeReferralCodeInput(),
          ],

          const SizedBox(height: 16),

          // Continue button
          _buildClaudeContinueButton(),

          const SizedBox(height: 16),

          // Auth links (Sign up, Forgot password)
          _buildClaudeAuthLinks(),

          const SizedBox(height: 24),

          // Privacy policy text
          _buildClaudePrivacyText(),
        ],
      ),
    );
  }

  // Claude-inspired Google button
  Widget _buildClaudeGoogleButton() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: _isGoogleLoading ? null : _handleGoogleSignIn,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isGoogleLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF6B7280)),
                    ),
                  )
                else ...[
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.g_mobiledata,
                      size: 16,
                      color: Color(0xFF4285F4),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Continue with Google',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF374151),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Claude-inspired divider
  Widget _buildClaudeDivider() {
    return Row(
      children: [
        const Expanded(
          child: Divider(
            color: Color(0xFFE5E7EB),
            thickness: 1,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'OR',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF9CA3AF),
            ),
          ),
        ),
        const Expanded(
          child: Divider(
            color: Color(0xFFE5E7EB),
            thickness: 1,
          ),
        ),
      ],
    );
  }

  // Claude-inspired name input
  Widget _buildClaudeNameInput() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _nameController,
        style: GoogleFonts.inter(
          fontSize: 14,
          color: const Color(0xFF374151),
        ),
        decoration: InputDecoration(
          hintText: 'Enter your full name',
          hintStyle: GoogleFonts.inter(
            fontSize: 14,
            color: const Color(0xFF9CA3AF),
          ),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter your name';
          }
          return null;
        },
      ),
    );
  }

  // Claude-inspired email input
  Widget _buildClaudeEmailInput() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
        style: GoogleFonts.inter(
          fontSize: 14,
          color: const Color(0xFF374151),
        ),
        decoration: InputDecoration(
          hintText: 'Enter your personal or work email',
          hintStyle: GoogleFonts.inter(
            fontSize: 14,
            color: const Color(0xFF9CA3AF),
          ),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter your email';
          }
          if (!_isValidEmail(value)) {
            return 'Please enter a valid email';
          }
          return null;
        },
      ),
    );
  }

  // Claude-inspired password input
  Widget _buildClaudePasswordInput() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _passwordController,
        obscureText: !_isPasswordVisible,
        style: GoogleFonts.inter(
          fontSize: 14,
          color: const Color(0xFF374151),
        ),
        decoration: InputDecoration(
          hintText: 'Enter your password',
          hintStyle: GoogleFonts.inter(
            fontSize: 14,
            color: const Color(0xFF9CA3AF),
          ),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: const Color(0xFF6B7280),
              size: 20,
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter your password';
          }
          if (!_isLogin && value.length < 8) {
            return 'Password must be at least 8 characters';
          }
          return null;
        },
      ),
    );
  }

  // Claude-inspired date of birth input
  Widget _buildClaudeDateOfBirthInput() {
    return GestureDetector(
      onTap: () async {
        final DateTime? picked = await showDatePicker(
          context: context,
          initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
          firstDate: DateTime(1900),
          lastDate: DateTime.now(),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Color(0xFF374151),
                  onPrimary: Colors.white,
                  surface: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: child!,
            );
          },
        );

        if (picked != null) {
          setState(() {
            _dobController.text =
                "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
          });
        }
      },
      child: AbsorbPointer(
        child: Container(
          height: 48,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color(0xFFE5E7EB)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextFormField(
            controller: _dobController,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF374151),
            ),
            decoration: InputDecoration(
              hintText: 'Date of Birth (Optional)',
              hintStyle: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF9CA3AF),
              ),
              border: InputBorder.none,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              suffixIcon: const Icon(
                Icons.calendar_today,
                color: Color(0xFF6B7280),
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Claude-inspired gender selection
  Widget _buildClaudeGenderSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gender',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildClaudeGenderOption('Male', 'male'),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildClaudeGenderOption('Female', 'female'),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildClaudeGenderOption('Other', 'other'),
            ),
          ],
        ),
      ],
    );
  }

  // Claude-inspired gender option
  Widget _buildClaudeGenderOption(String label, String value) {
    final isSelected = _selectedGender == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedGender = value;
        });
      },
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF374151) : Colors.white,
          border: Border.all(
            color:
                isSelected ? const Color(0xFF374151) : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : const Color(0xFF374151),
            ),
          ),
        ),
      ),
    );
  }

  // Claude-inspired referral code input
  Widget _buildClaudeReferralCodeInput() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _referralCodeController,
        style: GoogleFonts.inter(
          fontSize: 14,
          color: const Color(0xFF374151),
        ),
        decoration: InputDecoration(
          hintText: 'Referral Code (Optional)',
          hintStyle: GoogleFonts.inter(
            fontSize: 14,
            color: const Color(0xFF9CA3AF),
          ),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          suffixIcon: const Icon(
            Icons.card_giftcard,
            color: Color(0xFF6B7280),
            size: 20,
          ),
        ),
      ),
    );
  }

  // Claude-inspired auth links
  Widget _buildClaudeAuthLinks() {
    return Column(
      children: [
        // Sign up / Sign in toggle
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isLogin
                  ? 'Don\'t have an account? '
                  : 'Already have an account? ',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF6B7280),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _isLogin = !_isLogin;
                });
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                _isLogin ? 'Sign up' : 'Sign in',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF374151),
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),

        // Forgot password (only show in login mode)
        if (_isLogin) ...[
          const SizedBox(height: 8),
          TextButton(
            onPressed: () {
              // TODO: Implement forgot password functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Forgot password functionality coming soon'),
                ),
              );
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              'Forgot password?',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF6B7280),
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Claude-inspired continue button
  Widget _buildClaudeContinueButton() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.coralPop,
            AppColors.coralPop.withValues(alpha: 0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppColors.coralPop.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: _isLoading
              ? null
              : () {
                  if (_formKey.currentState!.validate()) {
                    _submitForm();
                  }
                },
          child: Center(
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    _isLogin ? 'Sign In' : 'Sign Up',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  // Claude-inspired privacy text
  Widget _buildClaudePrivacyText() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: GoogleFonts.inter(
          fontSize: 12,
          color: AppColors.slateGrey,
          height: 1.4,
        ),
        children: [
          const TextSpan(text: 'By continuing, you acknowledge Medroid\'s '),
          TextSpan(
            text: 'Privacy Policy',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: AppColors.midnightNavy,
              decoration: TextDecoration.underline,
            ),
          ),
          const TextSpan(text: '.'),
        ],
      ),
    );
  }

  // Claude-inspired feature panel with chat
  Widget _buildClaudeFeaturePanel(BuildContext context, bool isDesktop) {
    return Container(
      padding: EdgeInsets.all(isDesktop ? 48 : 24),
      child: Column(
        children: [
          // Header
          Container(
            alignment: Alignment.topLeft,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Experience Medroid',
                  style: GoogleFonts.inter(
                    fontSize: isDesktop ? 24 : 20,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF111827),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Chat with our AI doctor for instant health insights',
                  style: GoogleFonts.inter(
                    fontSize: isDesktop ? 14 : 13,
                    color: const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: isDesktop ? 32 : 20),

          // Chat interface - very compact
          Expanded(
            child: Container(
              padding: EdgeInsets.all(isDesktop ? 16 : 12), // Smaller padding
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE5E7EB)),
              ),
              child: Column(
                children: [
                  // Chat messages - very compact
                  Expanded(
                    child: ListView.builder(
                      padding: EdgeInsets.zero, // Remove default padding
                      itemCount: _chatMessages.length,
                      itemBuilder: (context, index) {
                        final message = _chatMessages[index];
                        return _buildChatBubble(message, isDesktop);
                      },
                    ),
                  ),

                  // Chat input (disabled/demo) - very compact
                  Container(
                    margin: EdgeInsets.only(
                        top: isDesktop ? 12 : 8), // Smaller margin
                    padding: EdgeInsets.symmetric(
                        horizontal: isDesktop ? 12 : 10,
                        vertical: isDesktop ? 8 : 6), // Smaller padding
                    decoration: BoxDecoration(
                      color: const Color(0xFFF9FAFB),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE5E7EB)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Type your health question here...',
                            style: GoogleFonts.inter(
                              fontSize: isDesktop ? 14 : 13,
                              color: const Color(0xFF9CA3AF),
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.all(isDesktop ? 8 : 6),
                          decoration: BoxDecoration(
                            color: AppColors.coralPop,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            Icons.send,
                            size: isDesktop ? 16 : 14,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: isDesktop ? 24 : 16),

          // Learn more section - more compact
          Container(
            alignment: Alignment.center,
            child: TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                    horizontal: isDesktop ? 16 : 12,
                    vertical: isDesktop ? 8 : 6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Learn more about Medroid',
                    style: GoogleFonts.inter(
                      fontSize: isDesktop ? 14 : 13,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward,
                    size: isDesktop ? 16 : 14,
                    color: const Color(0xFF6B7280),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Chat bubble widget - very compact like screenshot
  Widget _buildChatBubble(Map<String, dynamic> message, bool isDesktop) {
    final isUser = message['isUser'] as bool;
    final text = message['text'] as String;

    return Container(
      margin:
          EdgeInsets.only(bottom: isDesktop ? 4 : 3), // Much smaller spacing
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          // Message bubble - very compact
          Flexible(
            child: Container(
              constraints: BoxConstraints(maxWidth: isDesktop ? 260 : 220),
              padding: EdgeInsets.symmetric(
                  horizontal: isDesktop ? 12 : 10,
                  vertical: isDesktop ? 8 : 6), // Much smaller padding
              decoration: BoxDecoration(
                color: isUser ? AppColors.midnightNavy : AppColors.cloudWhite,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(12),
                  topRight: const Radius.circular(12),
                  bottomLeft: Radius.circular(isUser ? 12 : 3),
                  bottomRight: Radius.circular(isUser ? 3 : 12),
                ),
                border: isUser
                    ? null
                    : Border.all(
                        color: AppColors.slateGrey.withValues(alpha: 0.3),
                        width: 0.5),
              ),
              child: Text(
                text,
                style: GoogleFonts.inter(
                  fontSize: isDesktop ? 12 : 11, // Smaller font
                  color: isUser ? Colors.white : AppColors.midnightNavy,
                  height: 1.2, // Tighter line height
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
