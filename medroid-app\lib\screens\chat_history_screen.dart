import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/screens/ai_chat_screen.dart';
import 'package:medroid_app/widgets/chat_scaffold.dart';

class ChatHistoryScreen extends StatefulWidget {
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const ChatHistoryScreen({Key? key, this.onCreateNewChat}) : super(key: key);

  @override
  State<ChatHistoryScreen> createState() => _ChatHistoryScreenState();
}

class _ChatHistoryScreenState extends State<ChatHistoryScreen> {
  List<dynamic> _conversations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Check if token is valid before making the request
      final isValid = await apiService.isTokenValid();
      if (!isValid) {
        debugPrint('Token is not valid, showing error message');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Authentication required. Please log in again.';
        });
        return;
      }

      // Get chat history with a larger page size to ensure we get all conversations
      final conversations = await apiService.getChatHistory(perPage: 50);

      if (mounted) {
        setState(() {
          _conversations = conversations;
          _isLoading = false;
        });
      }

      // If we got no conversations, try to diagnose the issue
      if (conversations.isEmpty) {
        debugPrint('No conversations returned, checking for issues');

        // Try to load again after a short delay
        Future.delayed(const Duration(seconds: 2), () async {
          if (mounted) {
            try {
              final retryConversations =
                  await apiService.getChatHistory(perPage: 50);
              if (mounted && retryConversations.isNotEmpty) {
                setState(() {
                  _conversations = retryConversations;
                });
                debugPrint(
                    'Successfully loaded ${retryConversations.length} conversations on retry');
              }
            } catch (retryError) {
              debugPrint('Error on retry: $retryError');
            }
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading chat history: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error loading chat history: $e';
        });

        // Try to load from cache after a short delay
        Future.delayed(const Duration(seconds: 1), () async {
          if (mounted) {
            try {
              final apiService = RepositoryProvider.of<ApiService>(context);
              final cachedConversations = await apiService.getChatHistory();

              if (mounted && cachedConversations.isNotEmpty) {
                setState(() {
                  _conversations = cachedConversations;
                  _errorMessage = null;
                });
                debugPrint(
                    'Successfully loaded ${cachedConversations.length} conversations from cache');
              }
            } catch (cacheError) {
              debugPrint('Error loading from cache: $cacheError');
            }
          }
        });
      }
    }
  }

  void _openConversation(String conversationId) {
    // Navigate to the main screen with the chat tab selected and the conversation ID
    Navigator.pushNamed(
      context,
      '/main',
      arguments: {
        'tabIndex': 0, // Chat tab
        'conversationId': conversationId,
      },
    );
  }

  Future<void> _shareConversation(dynamic conversation) async {
    final String conversationId = conversation['id'].toString();

    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Conversation'),
        content: const Text(
            'This will share your conversation to the public feed where other users can see it. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Share'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    // Check if widget is still mounted before proceeding
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    // Get the API service before the async gap and store it in a local variable
    final apiService = RepositoryProvider.of<ApiService>(context);

    try {
      final result = await apiService.shareConversationToFeed(conversationId);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result
                ? 'Conversation shared to feed successfully'
                : 'Failed to share conversation'),
          ),
        );

        // Refresh the list to show the updated public status
        _loadChatHistory();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing conversation: $e'),
          ),
        );
      }
    }
  }

  Future<void> _deleteConversation(dynamic conversation) async {
    final String conversationId = conversation['id'].toString();

    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Conversation'),
        content: const Text(
            'Are you sure you want to delete this conversation? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    // Check if widget is still mounted before proceeding
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    // Get the API service before the async gap and store it in a local variable
    final apiService = RepositoryProvider.of<ApiService>(context);

    try {
      final success = await apiService.deleteConversation(conversationId);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Conversation deleted successfully'
                : 'Failed to delete conversation'),
          ),
        );

        if (success) {
          _loadChatHistory();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting conversation: $e'),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChatScaffold(
      appBar: null,
      backgroundColor:
          const Color(0xFFF3F9F7), // Match Discover screen background
      showChatInput: true,
      hintText: 'Ask about your health history...',
      onCreateNewChat: widget.onCreateNewChat,
      body: Column(
        children: [
          // Add title and refresh button at the top
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'History',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.refresh_outlined,
                    size: 24,
                  ),
                  onPressed: _loadChatHistory,
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

          // Main content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(_errorMessage!),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadChatHistory,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _conversations.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.chat_bubble_outline,
                                    size: 64, color: Colors.grey),
                                const SizedBox(height: 16),
                                const Text(
                                  'No conversations yet',
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'Start a new chat to get help with your health questions',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.add),
                                  label: const Text('Start New Chat'),
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              const AIChatScreen()),
                                    ).then((_) => _loadChatHistory());
                                  },
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _conversations.length,
                            itemBuilder: (context, index) {
                              final conversation = _conversations[index];
                              final date = conversation['updated_at'] != null
                                  ? DateTime.parse(conversation['updated_at'])
                                  : DateTime.now();

                              // Format date
                              final formattedDate =
                                  DateFormat('MMM d, yyyy • h:mm a')
                                      .format(date);

                              // Get the conversation title
                              String title = conversation['title'] ??
                                  'Health Conversation';

                              // Check if the conversation is public
                              final bool isPublic =
                                  conversation['is_public'] == true;

                              // Get a preview of the conversation content
                              String previewText = '';
                              if (conversation['messages'] != null &&
                                  (conversation['messages'] as List)
                                      .isNotEmpty) {
                                final messages =
                                    conversation['messages'] as List;
                                // Find the first assistant message for the preview
                                for (final message in messages) {
                                  if (message['role'] == 'assistant') {
                                    String content = message['content'] ?? '';
                                    if (content.length > 80) {
                                      content =
                                          '${content.substring(0, 77)}...';
                                    }
                                    previewText = content;
                                    break;
                                  }
                                }
                              }

                              if (previewText.isEmpty) {
                                previewText = 'No messages yet';
                              }

                              return Slidable(
                                key: ValueKey(conversation['id']),
                                endActionPane: ActionPane(
                                  motion: const ScrollMotion(),
                                  children: [
                                    SlidableAction(
                                      onPressed: (context) =>
                                          _shareConversation(conversation),
                                      backgroundColor: const Color(0xFF17C3B2),
                                      foregroundColor: Colors.white,
                                      icon: Icons.share,
                                      label: 'Share',
                                    ),
                                    SlidableAction(
                                      onPressed: (context) =>
                                          _deleteConversation(conversation),
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      icon: Icons.delete,
                                      label: 'Delete',
                                    ),
                                  ],
                                ),
                                child: GestureDetector(
                                  onTap: () => _openConversation(
                                      conversation['id'].toString()),
                                  child: Container(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black
                                              .withAlpha(13), // ~0.05 opacity
                                          blurRadius: 10,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Title row with public badge
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  title,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16,
                                                  ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              if (isPublic)
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 8,
                                                      vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        const Color(0xFF17C3B2)
                                                            .withAlpha(25),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                    border: Border.all(
                                                      color: const Color(
                                                          0xFF17C3B2),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  child: const Text(
                                                    'Public',
                                                    style: TextStyle(
                                                      fontSize: 10,
                                                      color: Color(0xFF17C3B2),
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),

                                          // Preview text
                                          Text(
                                            previewText,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey[700],
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 12),

                                          // Date and action buttons
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                formattedDate,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey[500],
                                                ),
                                              ),
                                              Row(
                                                children: [
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.share_outlined,
                                                      size: 18,
                                                      color: Colors.grey[600],
                                                    ),
                                                    constraints:
                                                        const BoxConstraints(),
                                                    padding:
                                                        const EdgeInsets.all(8),
                                                    onPressed: () =>
                                                        _shareConversation(
                                                            conversation),
                                                    tooltip: 'Share',
                                                  ),
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.delete_outline,
                                                      size: 18,
                                                      color: Colors.grey[600],
                                                    ),
                                                    constraints:
                                                        const BoxConstraints(),
                                                    padding:
                                                        const EdgeInsets.all(8),
                                                    onPressed: () =>
                                                        _deleteConversation(
                                                            conversation),
                                                    tooltip: 'Delete',
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
}
