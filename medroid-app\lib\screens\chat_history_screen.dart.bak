import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/screens/ai_chat_screen.dart';
import 'package:medroid_app/widgets/chat_scaffold.dart';

class ChatHistoryScreen extends StatefulWidget {
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const ChatHistoryScreen({Key? key, this.onCreateNewChat}) : super(key: key);

  @override
  State<ChatHistoryScreen> createState() => _ChatHistoryScreenState();
}

class _ChatHistoryScreenState extends State<ChatHistoryScreen> {
  List<dynamic> _conversations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversations = await apiService.getChatHistory();

      setState(() {
        _conversations = conversations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading chat history: $e';
      });
    }
  }

  Future<void> _showEditTitleDialog(Map<String, dynamic> conversation) async {
    final TextEditingController titleController = TextEditingController();
    titleController.text = conversation['title'] ?? '';
    bool isGenerating = false;

    if (!mounted) return;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        return AlertDialog(
          title: const Text('Edit Conversation Title'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Title',
                  hintText: 'Enter a descriptive title',
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              if (isGenerating)
                const Center(
                  child: CircularProgressIndicator(),
                )
              else
                OutlinedButton.icon(
                  icon: const Icon(Icons.auto_awesome),
                  label: const Text('Generate AI Summary Title'),
                  onPressed: () async {
                    setState(() {
                      isGenerating = true;
                    });

                    final apiService =
                        RepositoryProvider.of<ApiService>(context);
                    final result = await apiService.generateConversationTitle(
                        conversation['id'].toString());

                    if (mounted) {
                      setState(() {
                        isGenerating = false;
                      });

                      if (result['success'] == true) {
                        titleController.text = result['title'];
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text(result['message'] ??
                                  'Failed to generate title')),
                        );
                      }
                    }
                  },
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                final newTitle = titleController.text.trim();
                if (newTitle.isNotEmpty) {
                  Navigator.of(context).pop();

                  final apiService = RepositoryProvider.of<ApiService>(context);
                  final success = await apiService.updateConversationTitle(
                      conversation['id'].toString(), newTitle);

                  if (success && mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Title updated')),
                    );
                    // Refresh the chat history
                    _loadChatHistory();
                  } else if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Failed to update title')),
                    );
                  }
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      }),
    );

    titleController.dispose();
  }

  Future<void> _confirmDelete(Map<String, dynamic> conversation) async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Delete'),
          content:
              const Text('Are you sure you want to delete this conversation?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final success =
          await apiService.deleteConversation(conversation['id'].toString());

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Conversation deleted')),
        );
        _loadChatHistory();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to delete conversation')),
        );
      }
    }
  }

  void _openConversation(String conversationId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AIChatScreen(initialConversationId: conversationId),
      ),
    ).then((_) => _loadChatHistory());
  }

  @override
  Widget build(BuildContext context) {
    return ChatScaffold(
      // Remove app bar
      appBar: null,
      backgroundColor: Colors.white,
      showChatInput: true,
      hintText: 'Ask about your health history...',
      onCreateNewChat: widget.onCreateNewChat,
      body: Column(
        children: [
          // Add title and refresh button at the top
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'History',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadChatHistory,
                ),
              ],
            ),
          ),

          // Main content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(_errorMessage!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadChatHistory,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _conversations.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.chat_bubble_outline,
                              size: 64, color: Colors.grey),
                          const SizedBox(height: 16),
                          const Text(
                            'No conversations yet',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Start a new chat to get help with your health questions',
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.add),
                            label: const Text('Start New Chat'),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => const AIChatScreen()),
                              ).then((_) => _loadChatHistory());
                            },
                          ),
                        ],
                      ),
                    )
                  : Column(
                      children: [
                        // Stats summary
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color:
                                        AppTheme.primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    children: [
                                      Text(
                                        '${_conversations.length}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: AppTheme.primaryColor,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Conversations',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    children: [
                                      Text(
                                        '${_conversations.where((c) => c['escalated'] == true).length}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: Colors.orange,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Escalated',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    children: [
                                      const Text(
                                        'Today',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: Colors.green,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Last Chat',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Chat list
                        Expanded(
                          child: RefreshIndicator(
                            onRefresh: _loadChatHistory,
                            child: ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: _conversations.length,
                              itemBuilder: (context, index) {
                                final conversation = _conversations[index];
                                final date = conversation['updated_at'] != null
                                    ? DateTime.parse(conversation['updated_at'])
                                    : DateTime.now();

                                // Format date
                                final formattedDate =
                                    DateFormat('MMM d, yyyy • h:mm a')
                                        .format(date);

                                // Get the conversation title or generate a default one
                                String? title = conversation['title'];

                                // If title is null or empty, generate a better default title
                                if (title == null || title.isEmpty) {
                                  // Try to generate a title from the first few messages
                                  if (conversation['messages'] is List &&
                                      conversation['messages'].isNotEmpty) {
                                    // Find the first user message
                                    final firstUserMsg =
                                        conversation['messages'].firstWhere(
                                            (m) => m['role'] == 'user',
                                            orElse: () => {'content': ''});

                                    String userContent =
                                        firstUserMsg['content'] ?? '';

                                    // Create a concise title from the first user message
                                    if (userContent.isNotEmpty) {
                                      // Limit to first sentence or first 40 chars
                                      int endIndex = userContent.indexOf('.');
                                      if (endIndex == -1 || endIndex > 40) {
                                        endIndex = userContent.length > 40
                                            ? 40
                                            : userContent.length;
                                      }

                                      title =
                                          userContent.substring(0, endIndex);
                                      if (title.length >= 40) {
                                        title = '${title.substring(0, 37)}...';
                                      }

                                      // Clean up the title - remove common question starters
                                      final cleaners = [
                                        'hi',
                                        'hello',
                                        'hey',
                                        'can you',
                                        'could you',
                                        'please',
                                        'i need',
                                        'i want',
                                        'i have',
                                        'i am'
                                      ];

                                      // At this point we know title is not null because we just assigned it above
                                      // We need to create a local non-nullable variable
                                      String nonNullTitle = title;
                                      String lowerTitle =
                                          nonNullTitle.toLowerCase();

                                      for (var cleaner in cleaners) {
                                        if (lowerTitle.startsWith(cleaner)) {
                                          // Remove the starter and any following spaces
                                          nonNullTitle = nonNullTitle
                                              .substring(cleaner.length)
                                              .trim();
                                          // Capitalize the first letter if needed
                                          if (nonNullTitle.isNotEmpty) {
                                            nonNullTitle =
                                                nonNullTitle[0].toUpperCase() +
                                                    nonNullTitle.substring(1);
                                          }
                                          break;
                                        }
                                      }

                                      // If title became empty after cleaning or is too short, use a better default
                                      if (nonNullTitle.isEmpty ||
                                          nonNullTitle.length < 3) {
                                        nonNullTitle = 'Health Conversation';
                                      }

                                      // Assign back to title
                                      title = nonNullTitle;
                                    } else {
                                      title = 'Health Conversation';
                                    }
                                  } else {
                                    title = 'Health Conversation';
                                  }

                                  // Schedule a title generation in the background
                                  // Capture the context and conversation ID before the async gap
                                  final conversationId =
                                      conversation['id'].toString();
                                  final apiService =
                                      RepositoryProvider.of<ApiService>(
                                          context);

                                  Future.microtask(() async {
                                    try {
                                      final result = await apiService
                                          .generateConversationTitle(
                                              conversationId);

                                      if (result['success'] == true &&
                                          mounted) {
                                        // Refresh the chat history to show the new title
                                        _loadChatHistory();
                                      }
                                    } catch (e) {
                                      // Silently fail - we'll just keep the default title
                                      debugPrint(
                                          'Error auto-generating title: $e');
                                    }
                                  });
                                }

                                // Get preview text from the first user message
                                String previewText = '';
                                if (conversation['messages'] is List &&
                                    conversation['messages'].isNotEmpty) {
                                  final firstUserMsg = conversation['messages']
                                      .firstWhere((m) => m['role'] == 'user',
                                          orElse: () => {'content': ''});
                                  previewText = firstUserMsg['content'] ?? '';
                                  if (previewText.length > 100) {
                                    previewText =
                                        '${previewText.substring(0, 97)}...';
                                  }
                                }

                                // Extract health concerns for display
                                final List<String> healthConcerns =
                                    conversation['health_concerns'] != null
                                        ? List<String>.from(
                                            conversation['health_concerns'])
                                        : [];

                                // Get message count
                                final int messageCount =
                                    conversation['messages'] != null
                                        ? (conversation['messages'] as List)
                                            .length
                                        : 0;

                                // Check if conversation is public
                                final bool isPublic =
                                    conversation['is_public'] == true;

                                return Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.grey.shade100,
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withAlpha(8),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(16),
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(16),
                                      onTap: () => _openConversation(
                                          conversation['id'].toString()),
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // Header with title and actions
                                            Row(
                                              children: [
                                                Container(
                                                  width: 40,
                                                  height: 40,
                                                  decoration: BoxDecoration(
                                                    color:
                                                        const Color(0xFFF5F7FA),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: const Icon(
                                                    Icons.chat_rounded,
                                                    color: Color(0xFF6B7280),
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        title ??
                                                            'Health Conversation',
                                                        style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                      const SizedBox(height: 2),
                                                      Text(
                                                        formattedDate,
                                                        style: TextStyle(
                                                          color:
                                                              Colors.grey[600],
                                                          fontSize: 12,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                // Message count badge
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                                  decoration: BoxDecoration(
                                                    color: Colors.grey.shade100,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Text(
                                                    '$messageCount messages',
                                                    style: TextStyle(
                                                      color:
                                                          Colors.grey.shade700,
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                PopupMenuButton<String>(
                                                  icon: const Icon(
                                                      Icons.more_vert,
                                                      size: 20),
                                                  onSelected: (value) async {
                                                    if (value == 'edit') {
                                                      _showEditTitleDialog(
                                                          conversation);
                                                    } else if (value ==
                                                        'delete') {
                                                      _confirmDelete(
                                                          conversation);
                                                    } else if (value ==
                                                        'toggle_public') {
                                                      final apiService =
                                                          RepositoryProvider.of<
                                                                  ApiService>(
                                                              context);

                                                      setState(() {
                                                        _isLoading = true;
                                                      });

                                                      try {
                                                        // First toggle the public status
                                                        final result =
                                                            await apiService
                                                                .toggleConversationPublicStatus(
                                                          conversation['id']
                                                              .toString(),
                                                          !isPublic, // Toggle the current status
                                                        );

                                                        if (result['success']) {
                                                          // If making public, also share to feed
                                                          if (!isPublic) {
                                                            final shared = await apiService
                                                                .shareConversationToFeed(
                                                                    conversation[
                                                                            'id']
                                                                        .toString());

                                                            if (mounted) {
                                                              if (shared) {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  const SnackBar(
                                                                    content: Text(
                                                                        'Conversation shared to discovery feed'),
                                                                  ),
                                                                );
                                                              } else {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  const SnackBar(
                                                                    content: Text(
                                                                        'Made public but failed to share to discovery feed'),
                                                                  ),
                                                                );
                                                              }
                                                            }
                                                          } else {
                                                            // If making private
                                                            if (mounted) {
                                                              ScaffoldMessenger
                                                                      .of(context)
                                                                  .showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                      'Conversation is now private and removed from feed'),
                                                                ),
                                                              );
                                                            }
                                                          }
                                                        } else if (mounted) {
                                                          ScaffoldMessenger.of(
                                                                  context)
                                                              .showSnackBar(
                                                            SnackBar(
                                                              content: Text(result[
                                                                      'message'] ??
                                                                  'Failed to update public status'),
                                                            ),
                                                          );
                                                        }
                                                      } catch (e) {
                                                        if (mounted) {
                                                          ScaffoldMessenger.of(
                                                                  context)
                                                              .showSnackBar(
                                                            SnackBar(
                                                              content: Text(
                                                                  'Error: $e'),
                                                            ),
                                                          );
                                                        }
                                                      } finally {
                                                        if (mounted) {
                                                          setState(() {
                                                            _isLoading = false;
                                                          });
                                                          _loadChatHistory();
                                                        }
                                                      }
                                                    }
                                                  },
                                                  itemBuilder: (context) => [
                                                    const PopupMenuItem(
                                                      value: 'edit',
                                                      child: Row(
                                                        children: [
                                                          Icon(Icons.edit,
                                                              size: 18),
                                                          SizedBox(width: 8),
                                                          Text('Edit Title'),
                                                        ],
                                                      ),
                                                    ),
                                                    PopupMenuItem(
                                                      value: 'toggle_public',
                                                      child: Row(
                                                        children: [
                                                          Icon(
                                                            isPublic
                                                                ? Icons
                                                                    .public_off
                                                                : Icons.public,
                                                            size: 18,
                                                            color: isPublic
                                                                ? Colors.orange
                                                                : Colors.green,
                                                          ),
                                                          const SizedBox(
                                                              width: 8),
                                                          Text(
                                                            isPublic
                                                                ? 'Remove from Feed'
                                                                : 'Share to Feed',
                                                            style: TextStyle(
                                                              color: isPublic
                                                                  ? Colors
                                                                      .orange
                                                                  : Colors
                                                                      .green,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    const PopupMenuItem(
                                                      value: 'delete',
                                                      child: Row(
                                                        children: [
                                                          Icon(Icons.delete,
                                                              size: 18,
                                                              color:
                                                                  Colors.red),
                                                          SizedBox(width: 8),
                                                          Text('Delete',
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .red)),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),

                                            const SizedBox(height: 12),
                                            const Divider(height: 1),
                                            const SizedBox(height: 12),

                                            // Preview text
                                            Text(
                                              previewText,
                                              style: TextStyle(
                                                color: Colors.grey[700],
                                                fontSize: 14,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),

                                            const SizedBox(height: 12),

                                            // Health concerns and escalation status
                                            Row(
                                              children: [
                                                // Health topics
                                                if (healthConcerns.isNotEmpty)
                                                  Expanded(
                                                    child: Wrap(
                                                      spacing: 6,
                                                      runSpacing: 6,
                                                      children: healthConcerns
                                                          .take(3)
                                                          .map((topic) {
                                                        return Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal: 8,
                                                                  vertical: 4),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: AppTheme
                                                                .primaryColor
                                                                .withAlpha(
                                                                    26), // 0.1 opacity = 26/255
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        12),
                                                          ),
                                                          child: Text(
                                                            topic,
                                                            style:
                                                                const TextStyle(
                                                              color: AppTheme
                                                                  .primaryColor,
                                                              fontSize: 12,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                          ),
                                                        );
                                                      }).toList(),
                                                    ),
                                                  ),

                                                // Escalation badge
                                                if (conversation['escalated'] ==
                                                    true)
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 8,
                                                        vertical: 4),
                                                    decoration: BoxDecoration(
                                                      color: Colors.red.shade50,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                      border: Border.all(
                                                        color:
                                                            Colors.red.shade200,
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                            Icons
                                                                .warning_amber_rounded,
                                                            size: 14,
                                                            color: Colors
                                                                .red.shade700),
                                                        const SizedBox(
                                                            width: 4),
                                                        Text(
                                                          'Medical Attention',
                                                          style: TextStyle(
                                                            color: Colors
                                                                .red.shade700,
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),

                                                // Public badge
                                                if (isPublic)
                                                  const SizedBox(width: 8),
                                                if (isPublic)
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 8,
                                                        vertical: 4),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          Colors.green.shade50,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                      border: Border.all(
                                                        color: Colors
                                                            .green.shade200,
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(Icons.public,
                                                            size: 14,
                                                            color: Colors.green
                                                                .shade700),
                                                        const SizedBox(
                                                            width: 4),
                                                        Text(
                                                          'Shared',
                                                          style: TextStyle(
                                                            color: Colors
                                                                .green.shade700,
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
