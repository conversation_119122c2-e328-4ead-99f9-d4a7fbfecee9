import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/screens/ai_chat_screen.dart';
import 'package:medroid_app/widgets/chat_scaffold.dart';

class ChatHistoryScreen extends StatefulWidget {
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const ChatHistoryScreen({Key? key, this.onCreateNewChat}) : super(key: key);

  @override
  State<ChatHistoryScreen> createState() => _ChatHistoryScreenState();
}

class _ChatHistoryScreenState extends State<ChatHistoryScreen> {
  List<dynamic> _conversations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversations = await apiService.getChatHistory();

      setState(() {
        _conversations = conversations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading chat history: $e';
      });
    }
  }

  void _openConversation(String conversationId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AIChatScreen(initialConversationId: conversationId),
      ),
    ).then((_) => _loadChatHistory());
  }

  @override
  Widget build(BuildContext context) {
    return ChatScaffold(
      appBar: null,
      backgroundColor: Colors.white,
      showChatInput: true,
      hintText: 'Ask about your health history...',
      onCreateNewChat: widget.onCreateNewChat,
      body: Column(
        children: [
          // Add title and refresh button at the top
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'History',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadChatHistory,
                ),
              ],
            ),
          ),

          // Main content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(_errorMessage!),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadChatHistory,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _conversations.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.chat_bubble_outline,
                                    size: 64, color: Colors.grey),
                                const SizedBox(height: 16),
                                const Text(
                                  'No conversations yet',
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'Start a new chat to get help with your health questions',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.add),
                                  label: const Text('Start New Chat'),
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              const AIChatScreen()),
                                    ).then((_) => _loadChatHistory());
                                  },
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _conversations.length,
                            itemBuilder: (context, index) {
                              final conversation = _conversations[index];
                              final date = conversation['updated_at'] != null
                                  ? DateTime.parse(conversation['updated_at'])
                                  : DateTime.now();

                              // Format date
                              final formattedDate =
                                  DateFormat('MMM d, yyyy • h:mm a')
                                      .format(date);

                              // Get the conversation title
                              String title = conversation['title'] ??
                                  'Health Conversation';

                              return Card(
                                margin: const EdgeInsets.only(bottom: 12),
                                child: ListTile(
                                  title: Text(
                                    title,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  subtitle: Text(formattedDate),
                                  trailing: const Icon(Icons.arrow_forward_ios,
                                      size: 16),
                                  onTap: () => _openConversation(
                                      conversation['id'].toString()),
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
}
