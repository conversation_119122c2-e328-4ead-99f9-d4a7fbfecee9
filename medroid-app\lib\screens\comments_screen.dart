import 'dart:async';
import 'package:flutter/material.dart';
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/models/comment.dart';
import 'package:medroid_app/services/comment_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/comment_item.dart';

class CommentsScreen extends StatefulWidget {
  final SocialPost post;
  final CommentService commentService;

  const CommentsScreen({
    Key? key,
    required this.post,
    required this.commentService,
  }) : super(key: key);

  @override
  State<CommentsScreen> createState() => _CommentsScreenState();
}

class _CommentsScreenState extends State<CommentsScreen> {
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Comment> _comments = [];
  bool _isLoading = true;
  bool _isPosting = false;
  String? _replyingToCommentId;
  String? _replyingToUsername;
  int _commentCount = 0;
  Timer? _commentCountTimer;

  @override
  void initState() {
    super.initState();
    _loadComments();
    _startCommentCountTimer();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    _commentCountTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadComments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final comments = await widget.commentService.getComments(widget.post.id);
      setState(() {
        _comments = comments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading comments: $e')),
        );
      }
    }
  }

  Future<void> _postComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    setState(() {
      _isPosting = true;
    });

    try {
      final comment = await widget.commentService.postComment(
        widget.post.id,
        content,
        parentId: _replyingToCommentId,
      );

      setState(() {
        if (_replyingToCommentId != null) {
          // Add reply to the parent comment
          final parentIndex =
              _comments.indexWhere((c) => c.id == _replyingToCommentId);
          if (parentIndex != -1) {
            _comments[parentIndex].replies.add(comment);
          }
        } else {
          // Add new top-level comment
          _comments.insert(0, comment);
        }
        _commentController.clear();
        _replyingToCommentId = null;
        _replyingToUsername = null;
        _isPosting = false;
      });

      // Scroll to bottom to show new comment
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    } catch (e) {
      setState(() {
        _isPosting = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error posting comment: $e')),
        );
      }
    }
  }

  void _replyToComment(Comment comment) {
    setState(() {
      _replyingToCommentId = comment.id;
      _replyingToUsername = comment.user.name;
    });
    FocusScope.of(context).requestFocus(FocusNode());
  }

  void _cancelReply() {
    setState(() {
      _replyingToCommentId = null;
      _replyingToUsername = null;
    });
  }

  void _startCommentCountTimer() {
    _commentCountTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _updateCommentCount();
    });
  }

  Future<void> _updateCommentCount() async {
    try {
      final count = await widget.commentService.getCommentCount(widget.post.id);
      if (mounted && count != _commentCount) {
        setState(() {
          _commentCount = count;
        });
      }
    } catch (e) {
      // Silently fail for real-time updates
    }
  }

  void _onCommentUpdated() {
    _loadComments();
    _updateCommentCount();
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          _commentCount > 0 ? 'Comments ($_commentCount)' : 'Comments',
          style: TextStyle(fontSize: isDesktop ? 20 : 18),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body:
          isDesktop || isTablet ? _buildDesktopLayout() : _buildMobileLayout(),
    );
  }

  Widget _buildDesktopLayout() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 800 : 600,
        ),
        child: _buildMobileLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Column(
      children: [
        // Comments list
        Expanded(
          child: _isLoading
              ? Center(
                  child: CircularProgressIndicator(
                    valueColor:
                        const AlwaysStoppedAnimation<Color>(AppColors.coralPop),
                    strokeWidth: isDesktop ? 3 : 2,
                  ),
                )
              : _comments.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.chat_bubble_outline,
                            size: isDesktop
                                ? 80
                                : isTablet
                                    ? 72
                                    : 64,
                            color: Colors.grey[300],
                          ),
                          SizedBox(height: isDesktop ? 24 : 16),
                          Text(
                            'No comments yet',
                            style: TextStyle(
                              fontSize: isDesktop
                                  ? 22
                                  : isTablet
                                      ? 20
                                      : 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: isDesktop ? 12 : 8),
                          Text(
                            'Be the first to comment on this post',
                            style: TextStyle(
                              fontSize: isDesktop
                                  ? 16
                                  : isTablet
                                      ? 15
                                      : 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      controller: _scrollController,
                      padding: EdgeInsets.all(isDesktop
                          ? 24
                          : isTablet
                              ? 20
                              : 16),
                      itemCount: _comments.length,
                      itemBuilder: (context, index) {
                        final comment = _comments[index];
                        return CommentItem(
                          comment: comment,
                          commentService: widget.commentService,
                          socialContentId: widget.post.id,
                          onCommentUpdated: _onCommentUpdated,
                          onReply: () => _replyToComment(comment),
                        );
                      },
                    ),
        ),

        // Reply indicator
        if (_replyingToCommentId != null)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop
                  ? 24
                  : isTablet
                      ? 20
                      : 16,
              vertical: isDesktop ? 12 : 8,
            ),
            color: Colors.grey[100],
            child: Row(
              children: [
                Icon(
                  Icons.reply,
                  size: isDesktop ? 20 : 16,
                  color: Colors.grey[600],
                ),
                SizedBox(width: isDesktop ? 12 : 8),
                Expanded(
                  child: Text(
                    'Replying to $_replyingToUsername',
                    style: TextStyle(
                      fontSize: isDesktop
                          ? 16
                          : isTablet
                              ? 15
                              : 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.close,
                    size: isDesktop ? 24 : 20,
                  ),
                  onPressed: _cancelReply,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

        // Comment input
        Container(
          padding: EdgeInsets.all(isDesktop
              ? 24
              : isTablet
                  ? 20
                  : 16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: isDesktop ? 6 : 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  decoration: InputDecoration(
                    hintText: _replyingToCommentId != null
                        ? 'Reply to $_replyingToUsername...'
                        : 'Add a comment...',
                    hintStyle: TextStyle(
                      fontSize: isDesktop
                          ? 16
                          : isTablet
                              ? 15
                              : 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(isDesktop ? 28 : 24),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: isDesktop ? 20 : 16,
                      vertical: isDesktop ? 12 : 8,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: isDesktop
                        ? 16
                        : isTablet
                            ? 15
                            : 14,
                  ),
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) {
                    if (_commentController.text.trim().isNotEmpty) {
                      _postComment();
                    }
                  },
                ),
              ),
              SizedBox(width: isDesktop ? 12 : 8),
              GestureDetector(
                onTap: _isPosting ? null : _postComment,
                child: CircleAvatar(
                  radius: isDesktop
                      ? 22
                      : isTablet
                          ? 20
                          : 18,
                  backgroundColor:
                      _isPosting ? Colors.grey : AppColors.coralPop,
                  child: _isPosting
                      ? SizedBox(
                          width: isDesktop ? 20 : 16,
                          height: isDesktop ? 20 : 16,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(
                          Icons.send,
                          size: isDesktop
                              ? 22
                              : isTablet
                                  ? 20
                                  : 18,
                          color: Colors.white,
                        ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
