import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/image_cropper_widget.dart';
import 'package:image/image.dart' as img;

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({Key? key}) : super(key: key);

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final TextEditingController _captionController = TextEditingController();
  final List<String> _selectedTopics = [];
  // Default health topics to show while loading from API
  List<String> _availableTopics = [
    'Mental Health',
    'Nutrition',
    'Fitness',
    'Wellness',
    'Women\'s Health',
    'Men\'s Health',
    'Heart Health',
    'Diabetes',
    'Cancer',
    'COVID-19',
    'Vaccines',
    'Sleep',
    'Stress Management',
    'Weight Loss',
    'Pregnancy',
    'Pediatrics',
    'Aging',
    'Allergies'
  ];
  File? _imageFile;
  Uint8List? _webImageBytes;
  XFile? _pickedImage;
  bool _isLoading = false;
  bool _isLoadingTopics = true;

  @override
  void initState() {
    super.initState();
    // Set _isLoadingTopics to false immediately since we have default topics
    setState(() {
      _isLoadingTopics = false;
    });
    // Still try to load topics from API
    _loadTopics();
  }

  @override
  void dispose() {
    _captionController.dispose();
    super.dispose();
  }

  // Helper property to check if an image is selected
  bool get _hasImage => _imageFile != null || _webImageBytes != null;

  // Helper method to get the appropriate image provider based on platform
  ImageProvider _getImageProvider() {
    if (kIsWeb && _webImageBytes != null) {
      return MemoryImage(_webImageBytes!);
    } else if (!kIsWeb && _imageFile != null) {
      return FileImage(_imageFile!);
    }
    // Fallback - should never reach here due to _hasImage check
    return const AssetImage('assets/images/placeholder.png');
  }

  Future<void> _loadTopics() async {
    // Don't set loading to true since we already have default topics
    // We'll just try to update them in the background

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final topics = await apiService.getFeedTopics();

      if (!mounted) return;

      // Only update if we got a non-empty list from the API
      if (topics.isNotEmpty) {
        setState(() {
          _availableTopics = topics;
        });
      }
    } catch (e) {
      // Just log the error, don't show a snackbar since we have default topics
      debugPrint('Error loading topics: ${e.toString()}');
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      _pickedImage = image;

      // Check if image needs cropping (not 4:3 aspect ratio)
      final bytes = await image.readAsBytes();
      final decodedImage = img.decodeImage(bytes);

      if (decodedImage != null) {
        final aspectRatio = decodedImage.width / decodedImage.height;
        final targetAspectRatio = 4.0 / 3.0;
        final tolerance = 0.05; // 5% tolerance

        if ((aspectRatio - targetAspectRatio).abs() > tolerance) {
          // Image needs cropping - show crop screen
          if (!mounted) return;

          final result = await Navigator.push<Map<String, dynamic>>(
            context,
            MaterialPageRoute(
              builder: (context) => ImageCropperWidget(
                imageFile: kIsWeb ? null : File(image.path),
                imageBytes: kIsWeb ? bytes : null,
                fileName: image.name,
                onCropped: (File? croppedFile, Uint8List? croppedBytes) {
                  Navigator.pop(context, {
                    'file': croppedFile,
                    'bytes': croppedBytes,
                  });
                },
                onCancel: () {
                  Navigator.pop(context);
                },
              ),
            ),
          );

          if (result != null) {
            setState(() {
              if (kIsWeb) {
                _webImageBytes = result['bytes'];
                _imageFile = null;
              } else {
                _imageFile = result['file'];
                _webImageBytes = null;
              }
            });
          }
        } else {
          // Image is already 4:3, use as is
          if (kIsWeb) {
            setState(() {
              _webImageBytes = bytes;
              _imageFile = null;
            });
          } else {
            setState(() {
              _imageFile = File(image.path);
              _webImageBytes = null;
            });
          }
        }
      }
    }
  }

  Future<void> _createPost() async {
    if (_captionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a caption')),
      );
      return;
    }

    if (_selectedTopics.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select at least one health topic')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      if (kIsWeb && _webImageBytes != null && _pickedImage != null) {
        // For web, we need to handle the image differently
        await apiService.createPostWeb(
          caption: _captionController.text.trim(),
          topics: _selectedTopics,
          imageBytes: _webImageBytes,
          fileName: _pickedImage!.name,
        );
      } else {
        // For mobile platforms
        await apiService.createPost(
          caption: _captionController.text.trim(),
          topics: _selectedTopics,
          imageFile: _imageFile,
        );
      }

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Post created successfully')),
      );
      Navigator.pop(context);
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating post: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 768;
    final maxWidth = isDesktop ? 600.0 : double.infinity;

    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(isDesktop ? 10 : 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.coralPop.withValues(alpha: 0.1),
                    AppColors.coralPop.withValues(alpha: 0.05)
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.coralPop.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.edit,
                color: AppColors.coralPop,
                size: isDesktop ? 20 : 18,
              ),
            ),
            SizedBox(width: isDesktop ? 16 : 12),
            Text(
              'Create Post',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: isDesktop ? 20 : 18,
                letterSpacing: 0.2,
                color: AppColors.midnightNavy,
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.cloudWhite,
        foregroundColor: AppColors.midnightNavy,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(2),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  AppColors.slateGrey.withValues(alpha: 0.2),
                  Colors.transparent
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
            ),
            height: 2,
          ),
        ),
        actions: [
          Container(
            margin: EdgeInsets.only(right: isDesktop ? 24 : 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              boxShadow: _isLoading
                  ? null
                  : [
                      BoxShadow(
                        color: AppColors.coralPop.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createPost,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.coralPop,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding: EdgeInsets.symmetric(
                    horizontal: isDesktop ? 24 : 20,
                    vertical: isDesktop ? 12 : 10,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  disabledBackgroundColor:
                      AppColors.slateGrey.withValues(alpha: 0.3),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.share, size: 16),
                          SizedBox(width: 6),
                          Text(
                            'Share',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Center(
            child: SizedBox(
              width: maxWidth,
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: isDesktop ? 32 : 16,
                    vertical: isDesktop ? 24 : 16,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Caption input with premium styling
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: isDesktop ? 24 : 16,
                          vertical: isDesktop ? 20 : 16,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.cloudWhite,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.slateGrey.withValues(alpha: 0.2),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.slateGrey.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: _captionController,
                          decoration: InputDecoration(
                            hintText: 'Share your health insights...',
                            border: InputBorder.none,
                            hintStyle: TextStyle(
                              color: AppColors.slateGrey,
                              fontSize: isDesktop ? 18 : 16,
                            ),
                          ),
                          maxLines: isDesktop ? 6 : 5,
                          maxLength: 500,
                          style: TextStyle(
                            fontSize: isDesktop ? 18 : 16,
                            height: 1.5,
                            color: AppColors.midnightNavy,
                          ),
                        ),
                      ),

                      SizedBox(height: isDesktop ? 32 : 24),

                      // Image preview and picker with premium styling - 4:3 aspect ratio
                      GestureDetector(
                        onTap: _pickImage,
                        child: AspectRatio(
                          aspectRatio:
                              4 / 3, // Instagram-style 4:3 aspect ratio
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: AppColors.backgroundLight,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color:
                                    AppColors.slateGrey.withValues(alpha: 0.2),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.slateGrey
                                      .withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              image: _hasImage
                                  ? DecorationImage(
                                      image: _getImageProvider(),
                                      fit: BoxFit.cover,
                                    )
                                  : null,
                            ),
                            child: !_hasImage
                                ? Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        padding:
                                            EdgeInsets.all(isDesktop ? 16 : 12),
                                        decoration: BoxDecoration(
                                          color: AppColors.coralPop
                                              .withValues(alpha: 0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.add_photo_alternate,
                                          size: isDesktop ? 32 : 28,
                                          color: AppColors.coralPop,
                                        ),
                                      ),
                                      SizedBox(height: isDesktop ? 16 : 12),
                                      Text(
                                        'Add a photo to your post',
                                        style: TextStyle(
                                          color: AppColors.slateGrey,
                                          fontWeight: FontWeight.w500,
                                          fontSize: isDesktop ? 16 : 14,
                                        ),
                                      ),
                                      SizedBox(height: isDesktop ? 8 : 4),
                                      Text(
                                        'PNG, JPG or JPEG (max. 5MB)',
                                        style: TextStyle(
                                          color: AppColors.slateGrey
                                              .withValues(alpha: 0.7),
                                          fontSize: isDesktop ? 14 : 12,
                                        ),
                                      ),
                                    ],
                                  )
                                : Stack(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(16),
                                        child: Container(
                                          width: double.infinity,
                                          height: double.infinity,
                                          color: Colors.black
                                              .withValues(alpha: 0.2),
                                        ),
                                      ),
                                      Positioned(
                                        top: 8,
                                        right: 8,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black
                                                .withValues(alpha: 0.6),
                                            shape: BoxShape.circle,
                                          ),
                                          child: IconButton(
                                            icon: Icon(
                                              Icons.close,
                                              color: Colors.white,
                                              size: isDesktop ? 24 : 20,
                                            ),
                                            onPressed: () {
                                              setState(() {
                                                _imageFile = null;
                                                _webImageBytes = null;
                                                _pickedImage = null;
                                              });
                                            },
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        bottom: 8,
                                        right: 8,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black
                                                .withValues(alpha: 0.6),
                                            shape: BoxShape.circle,
                                          ),
                                          child: IconButton(
                                            icon: Icon(
                                              Icons.edit,
                                              color: Colors.white,
                                              size: isDesktop ? 24 : 20,
                                            ),
                                            onPressed: _pickImage,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                      ),

                      SizedBox(height: isDesktop ? 32 : 24),

                      // Health topics with premium styling
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Health Topics Header with Animation
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.coralPop.withValues(alpha: 0.1),
                                  AppColors.cloudWhite,
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.slateGrey
                                      .withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: isDesktop ? 24 : 16,
                              vertical: isDesktop ? 16 : 12,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(isDesktop ? 12 : 8),
                                  decoration: BoxDecoration(
                                    color: AppColors.cloudWhite,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.coralPop
                                            .withValues(alpha: 0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.tag,
                                    color: AppColors.coralPop,
                                    size: isDesktop ? 24 : 20,
                                  ),
                                ),
                                SizedBox(width: isDesktop ? 16 : 12),
                                Text(
                                  'Health Topics',
                                  style: TextStyle(
                                    fontSize: isDesktop ? 20 : 18,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.midnightNavy,
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isDesktop ? 14 : 10,
                                    vertical: isDesktop ? 8 : 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _selectedTopics.isNotEmpty
                                        ? AppColors.coralPop
                                        : AppColors.slateGrey
                                            .withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    '${_selectedTopics.length} selected',
                                    style: TextStyle(
                                      color: _selectedTopics.isNotEmpty
                                          ? Colors.white
                                          : AppColors.slateGrey,
                                      fontSize: isDesktop ? 14 : 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: isDesktop ? 20 : 16),
                          // Health Topics Selection Area
                          _isLoadingTopics
                              ? Center(
                                  child: Padding(
                                    padding:
                                        EdgeInsets.all(isDesktop ? 24.0 : 16.0),
                                    child: const CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        AppColors.coralPop,
                                      ),
                                    ),
                                  ),
                                )
                              : Container(
                                  padding: EdgeInsets.all(isDesktop ? 24 : 16),
                                  decoration: BoxDecoration(
                                    color: AppColors.cloudWhite,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: AppColors.slateGrey
                                          .withValues(alpha: 0.2),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.slateGrey
                                            .withValues(alpha: 0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (_selectedTopics.isEmpty)
                                        Padding(
                                          padding: EdgeInsets.only(
                                              bottom: isDesktop ? 16 : 12),
                                          child: Text(
                                            'Select at least one health topic for your post',
                                            style: TextStyle(
                                              color: AppColors.slateGrey,
                                              fontStyle: FontStyle.italic,
                                              fontSize: isDesktop ? 16 : 14,
                                            ),
                                          ),
                                        ),
                                      // Health topics grid with more visible styling
                                      GridView.count(
                                        crossAxisCount: isDesktop ? 3 : 2,
                                        childAspectRatio: isDesktop ? 2.5 : 3.0,
                                        mainAxisSpacing: isDesktop ? 16 : 10,
                                        crossAxisSpacing: isDesktop ? 16 : 10,
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        children: _availableTopics.map((topic) {
                                          final isSelected =
                                              _selectedTopics.contains(topic);
                                          return GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                if (isSelected) {
                                                  _selectedTopics.remove(topic);
                                                } else {
                                                  _selectedTopics.add(topic);
                                                }
                                              });
                                            },
                                            child: AnimatedContainer(
                                              duration: const Duration(
                                                  milliseconds: 300),
                                              decoration: BoxDecoration(
                                                color: isSelected
                                                    ? AppColors.coralPop
                                                        .withValues(alpha: 0.1)
                                                    : AppColors.cloudWhite,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: isSelected
                                                      ? AppColors.coralPop
                                                      : AppColors.slateGrey
                                                          .withValues(
                                                              alpha: 0.3),
                                                  width: isSelected ? 2 : 1,
                                                ),
                                                boxShadow: isSelected
                                                    ? [
                                                        BoxShadow(
                                                          color: AppColors
                                                              .coralPop
                                                              .withValues(
                                                                  alpha: 0.2),
                                                          blurRadius: 4,
                                                          offset: const Offset(
                                                              0, 2),
                                                        ),
                                                      ]
                                                    : null,
                                              ),
                                              child: Stack(
                                                children: [
                                                  Center(
                                                    child: Text(
                                                      topic,
                                                      style: TextStyle(
                                                        color: isSelected
                                                            ? AppColors.coralPop
                                                            : AppColors
                                                                .slateGrey,
                                                        fontWeight: isSelected
                                                            ? FontWeight.bold
                                                            : FontWeight.normal,
                                                        fontSize:
                                                            isDesktop ? 16 : 14,
                                                      ),
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                  if (isSelected)
                                                    Positioned(
                                                      top: 5,
                                                      right: 5,
                                                      child: Container(
                                                        padding: EdgeInsets.all(
                                                            isDesktop ? 4 : 2),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: AppColors
                                                              .coralPop,
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                        child: Icon(
                                                          Icons.check,
                                                          color: Colors.white,
                                                          size: isDesktop
                                                              ? 16
                                                              : 12,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                      ),
                                    ],
                                  ),
                                ),
                        ],
                      ),

                      SizedBox(height: isDesktop ? 32 : 24),

                      // Disclaimer with premium styling
                      Container(
                        padding: EdgeInsets.all(isDesktop ? 20 : 16),
                        decoration: BoxDecoration(
                          color: AppColors.coralPop.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.coralPop.withValues(alpha: 0.2),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.slateGrey.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: AppColors.coralPop,
                              size: isDesktop ? 24 : 20,
                            ),
                            SizedBox(width: isDesktop ? 16 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Content Guidelines',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.coralPop,
                                      fontSize: isDesktop ? 16 : 14,
                                    ),
                                  ),
                                  SizedBox(height: isDesktop ? 8 : 4),
                                  Text(
                                    'Your post will be reviewed for health accuracy before appearing in the feed. Posts should be informative, respectful, and follow community guidelines.',
                                    style: TextStyle(
                                      fontSize: isDesktop ? 14 : 12,
                                      color: AppColors.slateGrey,
                                      height: 1.5,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Add extra space at the bottom for better scrolling
                      SizedBox(height: isDesktop ? 60 : 40),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: AppColors.cloudWhite.withValues(alpha: 0.8),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.coralPop),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
