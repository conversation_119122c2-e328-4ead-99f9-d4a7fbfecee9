import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:image_picker/image_picker.dart';
import 'package:medroid_app/services/story_service.dart';
import 'package:medroid_app/utils/app_colors.dart';

class CreateStoryScreen extends StatefulWidget {
  final StoryService storyService;

  const CreateStoryScreen({
    Key? key,
    required this.storyService,
  }) : super(key: key);

  @override
  State<CreateStoryScreen> createState() => _CreateStoryScreenState();
}

class _CreateStoryScreenState extends State<CreateStoryScreen> {
  final TextEditingController _captionController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  File? _selectedMedia;
  Uint8List? _webImage;
  String _mediaType = 'image';
  bool _isUploading = false;

  @override
  void dispose() {
    _captionController.dispose();
    super.dispose();
  }

  Future<void> _pickMedia() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1080,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        if (kIsWeb) {
          // For web, read the file as bytes
          final bytes = await pickedFile.readAsBytes();
          setState(() {
            _webImage = bytes;
            _selectedMedia = null;
            _mediaType = 'image';
          });
        } else {
          // For mobile, use File
          setState(() {
            _selectedMedia = File(pickedFile.path);
            _webImage = null;
            _mediaType = 'image';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1080,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        if (kIsWeb) {
          // For web, read the file as bytes
          final bytes = await pickedFile.readAsBytes();
          setState(() {
            _webImage = bytes;
            _selectedMedia = null;
            _mediaType = 'image';
          });
        } else {
          // For mobile, use File
          setState(() {
            _selectedMedia = File(pickedFile.path);
            _webImage = null;
            _mediaType = 'image';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error taking photo: $e')),
        );
      }
    }
  }

  Future<void> _createStory() async {
    if (_selectedMedia == null && _webImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an image for your story')),
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      if (kIsWeb && _webImage != null) {
        // For web, use the createStoryFromBytes method
        await widget.storyService.createStoryFromBytes(
          _webImage!,
          'story_image.jpg',
          caption: _captionController.text.trim().isNotEmpty
              ? _captionController.text.trim()
              : null,
          mediaType: _mediaType,
        );
      } else if (_selectedMedia != null) {
        // For mobile, use the original createStory method
        await widget.storyService.createStory(
          _selectedMedia!,
          caption: _captionController.text.trim().isNotEmpty
              ? _captionController.text.trim()
              : null,
          mediaType: _mediaType,
        );
      }

      if (mounted) {
        Navigator.pop(context, true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Story created successfully!')),
        );
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating story: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          'Create Story',
          style: TextStyle(fontSize: isDesktop ? 20 : 18),
        ),
        actions: [
          if (_selectedMedia != null || _webImage != null)
            Padding(
              padding: EdgeInsets.only(right: isDesktop ? 16 : 8),
              child: TextButton(
                onPressed: _isUploading ? null : _createStory,
                child: _isUploading
                    ? SizedBox(
                        width: isDesktop ? 24 : 20,
                        height: isDesktop ? 24 : 20,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Share',
                        style: TextStyle(
                          color: AppColors.coralPop,
                          fontWeight: FontWeight.bold,
                          fontSize: isDesktop ? 18 : 16,
                        ),
                      ),
              ),
            ),
        ],
      ),
      body: isDesktop || isTablet
          ? _buildDesktopLayout()
          : (_selectedMedia == null && _webImage == null)
              ? _buildMediaSelector()
              : _buildStoryEditor(),
    );
  }

  Widget _buildDesktopLayout() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    if (_selectedMedia == null && _webImage == null) {
      return _buildMediaSelector();
    }

    return Row(
      children: [
        // Left side - Image preview (60% width)
        Expanded(
          flex: 6,
          child: Container(
            margin: EdgeInsets.all(isDesktop ? 24 : 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              image: DecorationImage(
                image: kIsWeb && _webImage != null
                    ? MemoryImage(_webImage!)
                    : FileImage(_selectedMedia!) as ImageProvider,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),

        // Right side - Controls (40% width)
        Expanded(
          flex: 4,
          child: Container(
            padding: EdgeInsets.all(isDesktop ? 24 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Add Caption',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isDesktop ? 20 : 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: isDesktop ? 24 : 16),

                // Caption input
                Expanded(
                  child: TextField(
                    controller: _captionController,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isDesktop ? 16 : 14,
                    ),
                    decoration: InputDecoration(
                      hintText: 'Write a caption for your story...',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontSize: isDesktop ? 16 : 14,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey[700]!),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey[700]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.coralPop),
                      ),
                      filled: true,
                      fillColor: Colors.grey[900],
                      contentPadding: EdgeInsets.all(isDesktop ? 16 : 12),
                    ),
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    maxLength: 500,
                  ),
                ),

                SizedBox(height: isDesktop ? 24 : 16),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _selectedMedia = null;
                            _webImage = null;
                            _captionController.clear();
                          });
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey[700]!),
                          padding: EdgeInsets.symmetric(
                            vertical: isDesktop ? 16 : 12,
                          ),
                        ),
                        child: Text(
                          'Change Photo',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isDesktop ? 16 : 14,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSelector() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600;

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 600 : double.infinity,
        ),
        padding: EdgeInsets.symmetric(horizontal: isDesktop ? 40 : 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_library_outlined,
              size: isDesktop
                  ? 120
                  : isTablet
                      ? 100
                      : 80,
              color: Colors.grey[400],
            ),
            SizedBox(height: isDesktop ? 32 : 24),
            Text(
              'Select a photo for your story',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: isDesktop
                    ? 24
                    : isTablet
                        ? 20
                        : 18,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isDesktop ? 48 : 40),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: _pickMedia,
                ),
                _buildActionButton(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: _takePhoto,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600;

    final buttonSize = isDesktop
        ? 140.0
        : isTablet
            ? 130.0
            : 120.0;
    final iconSize = isDesktop
        ? 48.0
        : isTablet
            ? 44.0
            : 40.0;
    final fontSize = isDesktop
        ? 16.0
        : isTablet
            ? 15.0
            : 14.0;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: buttonSize,
        height: buttonSize,
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(isDesktop ? 20 : 16),
          border: Border.all(color: Colors.grey[700]!),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: AppColors.coralPop,
            ),
            SizedBox(height: isDesktop ? 12 : 8),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: fontSize,
                fontWeight: isDesktop ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryEditor() {
    return Column(
      children: [
        // Image preview
        Expanded(
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              image: DecorationImage(
                image: kIsWeb && _webImage != null
                    ? MemoryImage(_webImage!)
                    : FileImage(_selectedMedia!) as ImageProvider,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),

        // Caption input
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _captionController,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Add a caption...',
              hintStyle: TextStyle(color: Colors.grey[400]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[700]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[700]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.coralPop),
              ),
              filled: true,
              fillColor: Colors.grey[900],
            ),
            maxLines: 3,
            maxLength: 500,
          ),
        ),

        // Action buttons
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedMedia = null;
                      _webImage = null;
                      _captionController.clear();
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey[700]!),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text(
                    'Change Photo',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
