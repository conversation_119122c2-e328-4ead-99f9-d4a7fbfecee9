import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/models/story.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/comment_service.dart';
import 'package:medroid_app/services/story_service.dart';
import 'package:medroid_app/screens/create_post_screen.dart';
import 'package:medroid_app/screens/notifications_screen.dart';
import 'package:medroid_app/screens/comments_screen.dart';
import 'package:medroid_app/screens/create_story_screen.dart';
import 'package:medroid_app/screens/story_viewer_screen.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/story_item.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';
import 'package:medroid_app/widgets/notification_badge.dart';
import 'package:medroid_app/widgets/share_card_dialog.dart';

class FeedScreen extends StatefulWidget {
  const FeedScreen({Key? key}) : super(key: key);

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen>
    with SingleTickerProviderStateMixin {
  final List<SocialPost> _posts = [];
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _hasMorePosts = true;
  int _currentPage = 1;
  List<String> _availableTopics = [];
  String? _selectedTopic;

  // Tab controller for Discover/Following tabs
  late TabController _tabController;

  // Services
  late CommentService _commentService;
  late StoryService _storyService;

  // Stories data
  List<StoryGroup> _storyGroups = [];
  bool _storiesLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Initialize services
    final authService = RepositoryProvider.of<AuthService>(context);
    _commentService = CommentService(authService);
    _storyService = StoryService(authService);

    _loadFeed();
    _loadTopics();
    _loadStories();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (!_isLoading && _hasMorePosts) {
        _loadMorePosts();
      }
    }
  }

  Future<void> _refreshFeed() async {
    setState(() {
      _posts.clear();
      _currentPage = 1;
      _hasMorePosts = true;
      _isLoading = true;
      _hasError = false;
    });
    await Future.wait([
      _loadFeed(),
      _loadStories(),
    ]);
  }

  Future<void> _loadFeed() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Get posts from backend API (internal posts)
      final apiService = RepositoryProvider.of<ApiService>(context);
      final result = await apiService.getFeed(
        page: _currentPage,
        topic: _selectedTopic,
      );

      if (!mounted) return;

      setState(() {
        // Add posts in reverse order (newest first)
        _posts.addAll(result.posts.reversed.toList());
        _hasMorePosts = result.hasMore;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoading || !_hasMorePosts) return;

    setState(() {
      _isLoading = true;
      _currentPage++;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final result = await apiService.getFeed(
        page: _currentPage,
        topic: _selectedTopic,
      );

      if (!mounted) return;

      setState(() {
        // Add more posts in reverse order (newest first)
        _posts.addAll(result.posts.reversed.toList());
        _hasMorePosts = result.hasMore;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadTopics() async {
    try {
      if (!mounted) return;

      final apiService = RepositoryProvider.of<ApiService>(context);
      final topics = await apiService.getFeedTopics();

      if (!mounted) return;

      setState(() {
        _availableTopics = topics;
      });
    } catch (e) {
      debugPrint('Error loading topics: $e');
    }
  }

  Future<void> _loadStories() async {
    if (!mounted) return;

    setState(() {
      _storiesLoading = true;
    });

    try {
      final stories = await _storyService.getStories();

      if (!mounted) return;

      setState(() {
        _storyGroups = stories;
        _storiesLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _storiesLoading = false;
      });
      debugPrint('Error loading stories: $e');
    }
  }

  void _viewStoryGroup(StoryGroup storyGroup) {
    if (storyGroup.isCurrentUser && storyGroup.stories.isEmpty) {
      // If it's the current user and they have no stories, open create story screen
      _createNewStory();
    } else if (storyGroup.stories.isNotEmpty) {
      // Navigate to story viewer
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StoryViewerScreen(
            storyGroup: storyGroup,
            storyService: _storyService,
          ),
        ),
      ).then((_) {
        // Refresh stories when returning from viewer to update viewed status
        _loadStories();
      });
    } else {
      // No stories to view
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${storyGroup.username} has no stories'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  void _createNewStory() {
    debugPrint('_createNewStory called - navigating to CreateStoryScreen');
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CreateStoryScreen(
            storyService: _storyService,
          ),
        ),
      ).then((result) {
        debugPrint('Returned from CreateStoryScreen with result: $result');
        if (result == true) {
          // Refresh stories if a new story was created
          _loadStories();
        }
      });
    } catch (e) {
      debugPrint('Error navigating to CreateStoryScreen: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error opening story creator: $e')),
      );
    }
  }

  Future<void> _toggleLikePost(SocialPost post) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final result = await apiService.likeContent(post.id);

      if (!mounted) return;

      setState(() {
        final index = _posts.indexWhere((p) => p.id == post.id);
        if (index != -1) {
          _posts[index].isLiked = result.liked;
          // Update engagement metrics for likes
          _posts[index] = SocialPost(
            id: _posts[index].id,
            source: _posts[index].source,
            sourceId: _posts[index].sourceId,
            contentType: _posts[index].contentType,
            mediaUrl: _posts[index].mediaUrl,
            caption: _posts[index].caption,
            healthTopics: _posts[index].healthTopics,
            relevanceScore: _posts[index].relevanceScore,
            filteredStatus: _posts[index].filteredStatus,
            createdAt: _posts[index].createdAt,
            isLiked: result.liked,
            isSaved: _posts[index].isSaved,
            userName: _posts[index].userName,
            userAvatar: _posts[index].userAvatar,
            engagementMetrics: {
              ..._posts[index].engagementMetrics,
              'likes': result.likeCount
            },
          );
        }
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    }
  }

  Future<void> _toggleSavePost(SocialPost post) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final result = await apiService.saveContent(post.id);

      if (!mounted) return;

      setState(() {
        final index = _posts.indexWhere((p) => p.id == post.id);
        if (index != -1) {
          _posts[index].isSaved = result.saved;
          // Update engagement metrics for saves
          _posts[index] = SocialPost(
            id: _posts[index].id,
            source: _posts[index].source,
            sourceId: _posts[index].sourceId,
            contentType: _posts[index].contentType,
            mediaUrl: _posts[index].mediaUrl,
            caption: _posts[index].caption,
            healthTopics: _posts[index].healthTopics,
            relevanceScore: _posts[index].relevanceScore,
            filteredStatus: _posts[index].filteredStatus,
            createdAt: _posts[index].createdAt,
            isLiked: _posts[index].isLiked,
            isSaved: result.saved,
            userName: _posts[index].userName,
            userAvatar: _posts[index].userAvatar,
            engagementMetrics: {
              ..._posts[index].engagementMetrics,
              'saves': result.saveCount
            },
          );
        }
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    }
  }

  Future<void> _deletePost(SocialPost post) async {
    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Delete Post'),
          content: const Text(
              'Are you sure you want to delete this post? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // Show loading indicator
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Deleting post...'),
          duration: Duration(seconds: 1),
        ),
      );

      debugPrint('Attempting to delete post with ID: ${post.id}');
      debugPrint('Post source: ${post.source}, userName: ${post.userName}');

      final apiService = RepositoryProvider.of<ApiService>(context);

      try {
        // Check if user has permission to delete this post
        if (!post.canDelete) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You do not have permission to delete this post'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        final success = await apiService.deletePost(post.id);

        if (!mounted) return;

        if (success) {
          // Remove the post from the list
          setState(() {
            _posts.removeWhere((p) => p.id == post.id);
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Post deleted successfully')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text(
                    'Failed to delete post - server returned an unexpected response')),
          );
        }
      } catch (apiError) {
        debugPrint('API error when deleting post: $apiError');
        if (!mounted) return;

        // Check for specific error messages
        String errorMessage = apiError.toString();

        if (errorMessage.contains('not authorized') ||
            errorMessage.contains('permission') ||
            errorMessage.contains('Unauthorized')) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You do not have permission to delete this post'),
              backgroundColor: Colors.red,
            ),
          );
        } else if (errorMessage.contains('Authentication required') ||
            errorMessage.contains('auth token')) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please sign in to delete posts'),
              backgroundColor: Colors.red,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete post: ${apiError.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error in _deletePost method: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sharePost(SocialPost post) async {
    try {
      // Prepare content for the share card
      String title = 'Health Insight';
      String content = post.caption;
      String? subtitle = post.userName != null ? 'by ${post.userName}' : null;

      // Show the share card dialog
      await showShareCardDialog(
        context,
        title: title,
        content: content,
        subtitle: subtitle,
        qrCodeData: 'https://medroid.ai/feed',
        accentColor: AppColors.coralPop,
      );
    } catch (e) {
      debugPrint('Error sharing post: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing post: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Filter by Health Topic',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _selectedTopic == null,
                        onSelected: (selected) {
                          setState(() {
                            _selectedTopic = null;
                          });
                          Navigator.pop(context);
                          _refreshFeed();
                        },
                      ),
                      ..._availableTopics.map((topic) {
                        return FilterChip(
                          label: Text(topic),
                          selected: _selectedTopic == topic,
                          onSelected: (selected) {
                            setState(() {
                              _selectedTopic = selected ? topic : null;
                            });
                            Navigator.pop(context);
                            _refreshFeed();
                          },
                        );
                      }).toList(),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Scaffold(
      backgroundColor:
          const Color(0xFFF3F9F7), // Solid color background #f3f9f7
      extendBodyBehindAppBar: true,
      body: Container(
        color: const Color(0xFFF3F9F7), // Solid color background #f3f9f7
        child: RefreshIndicator(
          onRefresh: _refreshFeed,
          color: AppColors.tealSurge,
          child: Stack(
            children: [
              // Main content
              SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // App bar with tabs
                    Container(
                      padding: EdgeInsets.fromLTRB(isDesktop ? 24 : 16,
                          isDesktop ? 24 : 16, isDesktop ? 24 : 16, 0),
                      decoration: const BoxDecoration(
                        color: Colors.transparent,
                      ),
                      child: Column(
                        children: [
                          // Header with notification bell, chat, and profile
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Left side - Notification bell with badge and chat icon
                              Row(
                                children: [
                                  // Notification bell with badge
                                  Container(
                                    height: isDesktop ? 48 : 40,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(
                                          isDesktop ? 24 : 20),
                                    ),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: isDesktop ? 16 : 12),
                                    child: NotificationBadge(
                                      iconColor: Colors.black,
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                const NotificationsScreen(),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  SizedBox(width: isDesktop ? 12 : 8),
                                  // Create post icon
                                  Container(
                                    height: isDesktop ? 48 : 40,
                                    width: isDesktop ? 48 : 40,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(
                                          isDesktop ? 24 : 20),
                                    ),
                                    child: IconButton(
                                      icon: Icon(
                                        Icons.add,
                                        color: Colors.black,
                                        size: isDesktop ? 24 : 20,
                                      ),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                const CreatePostScreen(),
                                          ),
                                        ).then((_) {
                                          // Refresh feed when returning from create post screen
                                          _refreshFeed();
                                        });
                                      },
                                      padding: EdgeInsets.zero,
                                    ),
                                  ),
                                ],
                              ),
                              // Right side - User profile with click functionality
                              FutureBuilder<Map<String, dynamic>?>(
                                future:
                                    RepositoryProvider.of<AuthService>(context)
                                        .getCurrentUser(),
                                builder: (context, snapshot) {
                                  final userData = snapshot.data;
                                  final hasProfileImage = userData != null &&
                                      userData['profile_image'] != null &&
                                      userData['profile_image']
                                          .toString()
                                          .isNotEmpty;

                                  return GestureDetector(
                                    onTap: () {
                                      // Navigate to profile page
                                      Navigator.pushNamed(context, '/profile');
                                    },
                                    child: Container(
                                      height: isDesktop ? 48 : 40,
                                      width: isDesktop ? 48 : 40,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(
                                            isDesktop ? 24 : 20),
                                        // Add subtle shadow to indicate it's clickable
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black
                                                .withValues(alpha: 0.1),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: hasProfileImage
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      isDesktop ? 24 : 20),
                                              child: PlatformAwareNetworkImage(
                                                imageUrl:
                                                    userData['profile_image']
                                                        .toString(),
                                                fit: BoxFit.cover,
                                                width: isDesktop ? 48 : 40,
                                                height: isDesktop ? 48 : 40,
                                                errorWidget:
                                                    (context, url, error) {
                                                  return Icon(
                                                    Icons.person,
                                                    color: Colors.grey,
                                                    size: isDesktop ? 28 : 24,
                                                  );
                                                },
                                              ),
                                            )
                                          : Icon(
                                              Icons.person,
                                              color: Colors.grey,
                                              size: isDesktop ? 28 : 24,
                                            ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),

                          // Discover and Following tabs
                          Container(
                            margin: EdgeInsets.only(top: isDesktop ? 24 : 16),
                            child: Row(
                              children: [
                                // Discover tab (selected)
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Discover',
                                      style: TextStyle(
                                        fontSize: isDesktop ? 22 : 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                    SizedBox(height: isDesktop ? 6 : 4),
                                    Container(
                                      height: isDesktop ? 3 : 2,
                                      width: isDesktop ? 80 : 60,
                                      color: Colors.black,
                                    ),
                                  ],
                                ),
                                SizedBox(width: isDesktop ? 32 : 24),
                                // Following tab (unselected)
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Following',
                                      style: TextStyle(
                                        fontSize: isDesktop ? 22 : 18,
                                        fontWeight: FontWeight.normal,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    SizedBox(height: isDesktop ? 6 : 4),
                                    Container(
                                      height: isDesktop ? 3 : 2,
                                      width: isDesktop ? 80 : 60,
                                      color: Colors.transparent,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Stories row
                    Container(
                      height: isDesktop ? 130 : 110,
                      padding:
                          EdgeInsets.symmetric(vertical: isDesktop ? 12 : 8),
                      child: _storiesLoading
                          ? const Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.coralPop),
                              ),
                            )
                          : _storyGroups.isEmpty
                              ? Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: isDesktop ? 16 : 10),
                                  child: Row(
                                    children: [
                                      // Always show "Add Story" button even when no stories
                                      Container(
                                        width: 80,
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 6),
                                        child: Column(
                                          children: [
                                            // Add Story button
                                            GestureDetector(
                                              onTap: () {
                                                debugPrint(
                                                    'Add Story button tapped!');
                                                _createNewStory();
                                              },
                                              child: Container(
                                                width: 64,
                                                height: 64,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: Colors.white,
                                                  border: Border.all(
                                                    color: AppColors.coralPop,
                                                    width: 2,
                                                  ),
                                                ),
                                                child: const Icon(
                                                  Icons.add,
                                                  color: AppColors.coralPop,
                                                  size: 24,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            const Text(
                                              'Add Story',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.black87,
                                              ),
                                              maxLines: 1,
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      ),
                                      // "No stories available" text
                                      Expanded(
                                        child: Center(
                                          child: Text(
                                            'No stories available',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 14,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: isDesktop ? 16 : 10),
                                  itemCount: _storyGroups.length +
                                      1, // +1 for "Add Story" item
                                  itemBuilder: (context, index) {
                                    if (index == 0) {
                                      // Always show "Add Story" as the first item
                                      return Container(
                                        width: 80,
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 6),
                                        child: Column(
                                          children: [
                                            // Add Story button
                                            GestureDetector(
                                              onTap: _createNewStory,
                                              child: Container(
                                                width: 64,
                                                height: 64,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: Colors.white,
                                                  border: Border.all(
                                                    color: Colors.grey[300]!,
                                                    width: 1,
                                                  ),
                                                ),
                                                child: const Icon(
                                                  Icons.add,
                                                  color: Colors.grey,
                                                  size: 24,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            const Text(
                                              'Add Story',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.black87,
                                              ),
                                              maxLines: 1,
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      );
                                    } else {
                                      // Show user stories (including current user's existing stories)
                                      final adjustedIndex = index - 1;
                                      if (adjustedIndex < _storyGroups.length) {
                                        final storyGroup =
                                            _storyGroups[adjustedIndex];
                                        return StoryItem(
                                          imageUrl: storyGroup.userAvatar ?? '',
                                          username: storyGroup.username,
                                          isViewed: !storyGroup.hasUnviewed,
                                          isCurrentUser:
                                              storyGroup.isCurrentUser,
                                          onTap: () =>
                                              _viewStoryGroup(storyGroup),
                                        );
                                      } else {
                                        return const SizedBox.shrink();
                                      }
                                    }
                                  },
                                ),
                    ),

                    // Content area
                    Expanded(
                      child: _hasError
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.error_outline,
                                      size: isDesktop ? 64 : 48,
                                      color: Colors.grey),
                                  SizedBox(height: isDesktop ? 24 : 16),
                                  Text(
                                    'Error: $_errorMessage',
                                    style: TextStyle(
                                      fontSize: isDesktop ? 16 : 14,
                                    ),
                                  ),
                                  SizedBox(height: isDesktop ? 24 : 16),
                                  ElevatedButton(
                                    onPressed: _refreshFeed,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.coralPop,
                                      foregroundColor: Colors.white,
                                      padding: EdgeInsets.symmetric(
                                          horizontal: isDesktop ? 32 : 24,
                                          vertical: isDesktop ? 16 : 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    child: Text(
                                      'Try Again',
                                      style: TextStyle(
                                        fontSize: isDesktop ? 16 : 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : _posts.isEmpty && !_isLoading
                              ? _buildEmptyState()
                              : isDesktop
                                  // Grid view for desktop
                                  ? GridView.builder(
                                      controller: _scrollController,
                                      padding: const EdgeInsets.fromLTRB(
                                          16, 8, 16, 80),
                                      gridDelegate:
                                          const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 2,
                                        childAspectRatio: 0.85,
                                        crossAxisSpacing: 16,
                                        mainAxisSpacing: 16,
                                      ),
                                      itemCount: _posts.length +
                                          (_hasMorePosts ? 1 : 0),
                                      itemBuilder: (context, index) {
                                        if (index == _posts.length) {
                                          return const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(16.0),
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                            Color>(
                                                        AppColors.coralPop),
                                              ),
                                            ),
                                          );
                                        }
                                        final post = _posts[index];
                                        return _buildPostCard(post);
                                      },
                                    )
                                  // List view for mobile
                                  : ListView.builder(
                                      controller: _scrollController,
                                      padding: const EdgeInsets.fromLTRB(
                                          8, 0, 8, 80),
                                      itemCount: _posts.length +
                                          (_hasMorePosts ? 1 : 0),
                                      itemBuilder: (context, index) {
                                        if (index == _posts.length) {
                                          return const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(16.0),
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                            Color>(
                                                        AppColors.coralPop),
                                              ),
                                            ),
                                          );
                                        }
                                        final post = _posts[index];
                                        return _buildPostCard(post);
                                      },
                                    ),
                    ),
                  ],
                ),
              ),

              // Removed floating action buttons as they're now in the top bar

              // Loading indicator
              if (_isLoading && _posts.isEmpty)
                Container(
                  color:
                      const Color(0xFFF3F9F7), // Solid color background #f3f9f7
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.coralPop),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(isDesktop ? 32 : 24),
            decoration: const BoxDecoration(
              color: Color.fromRGBO(139, 233, 200, 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.feed_outlined,
              size: isDesktop ? 80 : 64,
              color: AppColors.coralPop,
            ),
          ),
          SizedBox(height: isDesktop ? 32 : 24),
          Text(
            'No posts yet',
            style: TextStyle(
              fontSize: isDesktop ? 24 : 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: isDesktop ? 16 : 12),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: isDesktop ? 48 : 32),
            child: Text(
              'Be the first to share health insights with the community',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
                fontSize: isDesktop ? 18 : 16,
              ),
            ),
          ),
          SizedBox(height: isDesktop ? 32 : 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreatePostScreen(),
                ),
              ).then((_) => _refreshFeed());
            },
            icon: Icon(Icons.add, size: isDesktop ? 20 : 16),
            label: Text(
              'Create First Post',
              style: TextStyle(
                fontSize: isDesktop ? 16 : 14,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.coralPop,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                  horizontal: isDesktop ? 32 : 24,
                  vertical: isDesktop ? 16 : 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show comment section for a post
  void _showCommentSection(SocialPost post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommentsScreen(
          post: post,
          commentService: _commentService,
        ),
      ),
    );
  }

  // Show post options menu
  void _showPostOptions(SocialPost post) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.share, color: AppColors.coralPop),
                title: const Text('Share Post'),
                onTap: () {
                  Navigator.pop(context);
                  // Share functionality
                },
              ),
              ListTile(
                leading: const Icon(Icons.report, color: AppColors.coralPop),
                title: const Text('Report Post'),
                onTap: () {
                  Navigator.pop(context);
                  // Report functionality
                },
              ),
              // Only show delete option if user has permission
              if (post.canDelete)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Delete Post'),
                  onTap: () {
                    Navigator.pop(context);
                    _deletePost(post);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  // Format time ago from DateTime
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} ${(difference.inDays / 365).floor() == 1 ? 'year' : 'years'} ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} ${(difference.inDays / 30).floor() == 1 ? 'month' : 'months'} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildPostCard(SocialPost post) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 800, // Maximum height for entire post card
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize:
                MainAxisSize.min, // Prevent overflow by using minimum size
            children: [
              // Post header with controls at top right
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // User avatar
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        shape: BoxShape.circle,
                      ),
                      child: (post.userAvatar != null &&
                              post.userAvatar!.isNotEmpty &&
                              post.userAvatar != 'null')
                          ? ClipOval(
                              child: PlatformAwareNetworkImage(
                                imageUrl: post.userAvatar!,
                                fit: BoxFit.cover,
                                width: 50,
                                height: 50,
                                errorWidget: (context, url, error) {
                                  return Container(
                                    width: 50,
                                    height: 50,
                                    color: Colors.grey[200],
                                    child: Icon(
                                      Icons.person,
                                      color: Colors.grey[400],
                                      size: 30,
                                    ),
                                  );
                                },
                              ),
                            )
                          : Icon(
                              Icons.person,
                              color: Colors.grey[400],
                              size: 30,
                            ),
                    ),
                    const SizedBox(width: 12),
                    // User info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            post.userName ?? 'Shravya Shetty',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            _formatTimeAgo(post.createdAt),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Post control buttons at top right
                    IconButton(
                      icon: const Icon(
                        Icons.more_vert,
                        color: Colors.grey,
                        size: 24,
                      ),
                      onPressed: () => _showPostOptions(post),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Post title (only if available)
              if (post.title != null && post.title!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                  child: Text(
                    post.title!,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),

              // Post caption/content
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: Text(
                  post.caption,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  maxLines: 10, // Limit lines to prevent overflow
                  overflow:
                      TextOverflow.ellipsis, // Add ellipsis if text is too long
                ),
              ),

              // Post media (only if available) - respects original aspect ratio
              if (post.mediaUrl.isNotEmpty && post.mediaUrl != 'null')
                Flexible(
                  child: Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      maxHeight:
                          400, // Reduced maximum height to prevent overflow
                    ),
                    child: PlatformAwareNetworkImage(
                      imageUrl: post.mediaUrl,
                      fit: BoxFit
                          .contain, // Changed from cover to contain to preserve aspect ratio
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 300, // Fixed height for error state
                          color: Colors.grey[100],
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported_outlined,
                              color: Colors.grey[400],
                              size: 40,
                            ),
                          ),
                        );
                      },
                      placeholder: (context, url) {
                        return Container(
                          height: 300, // Fixed height for loading state
                          color: Colors.grey[50],
                          child: const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.coralPop),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

              // Engagement metrics (likes and comments)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: Row(
                  children: [
                    Text(
                      '${post.likeCount} likes',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${post.engagementMetrics['comments'] ?? 0} comments',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),

              // Divider
              Divider(color: Colors.grey[200], height: 1),

              // Action buttons - premium design with proper spacing
              Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Colors.grey[200]!, width: 1),
                    bottom: BorderSide(color: Colors.grey[200]!, width: 1),
                  ),
                ),
                child: IntrinsicHeight(
                  child: Row(
                    children: [
                      // Like button
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _toggleLikePost(post),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    post.isLiked
                                        ? Icons.thumb_up
                                        : Icons.thumb_up_outlined,
                                    color: post.isLiked
                                        ? AppColors.tealSurge
                                        : Colors.grey[700],
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Like',
                                    style: TextStyle(
                                      color: post.isLiked
                                          ? AppColors.tealSurge
                                          : Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Vertical divider
                      Container(
                        height: 24,
                        width: 1,
                        color: Colors.grey[200],
                      ),

                      // Comment button
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _showCommentSection(post),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.chat_bubble_outline,
                                    color: Colors.grey[700],
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Comment',
                                    style: TextStyle(
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Vertical divider
                      Container(
                        height: 24,
                        width: 1,
                        color: Colors.grey[200],
                      ),

                      // Share button
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _sharePost(post),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.share_outlined,
                                    color: Colors.grey[700],
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Share',
                                    style: TextStyle(
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
