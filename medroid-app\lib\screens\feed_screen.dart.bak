import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/widgets/chat_scaffold.dart';

class FeedScreen extends StatefulWidget {
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const FeedScreen({Key? key, this.onCreateNewChat}) : super(key: key);

  @override
  _FeedScreenState createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  final List<SocialPost> _posts = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  String _sortBy = 'relevance';
  // Default to internal source only since we're temporarily disabling external sources
  String? _selectedSource = 'internal';
  String? _selectedContentType;
  List<String> _topics = [];
  String? _selectedTopic;

  final ScrollController _scrollController = ScrollController();
  bool _hasMorePosts = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _loadTopics();

    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMorePosts();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final data = await apiService.getFeed(
        sortBy: _sortBy,
        source: _selectedSource,
        contentType: _selectedContentType,
      );

      final posts = data.map((item) => SocialPost.fromJson(item)).toList();

      setState(() {
        _posts.clear();
        _posts.addAll(posts);
        _isLoading = false;
        _hasMorePosts = posts.length >= 10; // Assuming page size is 10
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoading || !_hasMorePosts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // In a real app, you'd track the current page or use cursor-based pagination
      // Here we're just simulating by using the last post's ID
      final lastPostId = _posts.isNotEmpty ? _posts.last.id : null;

      final data = await apiService.getFeed(
        sortBy: _sortBy,
        source: _selectedSource,
        contentType: _selectedContentType,
      );

      final newPosts = data.map((item) => SocialPost.fromJson(item)).toList();

      setState(() {
        _posts.addAll(newPosts);
        _isLoading = false;
        _hasMorePosts = newPosts.length >= 10; // Assuming page size is 10
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadTopics() async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final topics = await apiService.getFeedTopics();

      setState(() {
        _topics = topics;
      });
    } catch (e) {
      print('Error loading topics: $e');

      // If we get a session expired error, just use an empty list of topics
      // This prevents the error from being displayed to the user
      if (e.toString().contains('Your session has expired')) {
        setState(() {
          _topics = [];
        });
      }
    }
  }

  Future<void> _refreshFeed() async {
    await _loadInitialData();
  }

  Future<void> _toggleLikePost(SocialPost post) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final isLiked = await apiService.toggleLikeFeedItem(post.id);

      final index = _posts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        setState(() {
          _posts[index].isLiked = isLiked;
          final currentLikes = _posts[index].engagementMetrics['likes'] ?? 0;
          _posts[index].engagementMetrics['likes'] =
              isLiked ? currentLikes + 1 : currentLikes - 1;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to like post: $e')),
      );
    }
  }

  Future<void> _toggleSavePost(SocialPost post) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final isSaved = await apiService.toggleSaveFeedItem(post.id);

      final index = _posts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        setState(() {
          _posts[index].isSaved = isSaved;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save post: $e')),
      );
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Feed',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Sort By',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'relevance',
                        groupValue: _sortBy,
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      const Text('Relevance'),
                      Radio<String>(
                        value: 'recent',
                        groupValue: _sortBy,
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      const Text('Most Recent'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Source',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _selectedSource == null,
                        onSelected: (selected) {
                          setState(() {
                            _selectedSource = selected ? null : _selectedSource;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('Internal'),
                        selected: _selectedSource == 'internal',
                        onSelected: (selected) {
                          setState(() {
                            _selectedSource = selected ? 'internal' : null;
                          });
                        },
                      ),
                      // Instagram and TikTok options temporarily removed
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Content Type',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _selectedContentType == null,
                        onSelected: (selected) {
                          setState(() {
                            _selectedContentType =
                                selected ? null : _selectedContentType;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('Image'),
                        selected: _selectedContentType == 'image',
                        onSelected: (selected) {
                          setState(() {
                            _selectedContentType = selected ? 'image' : null;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('Video'),
                        selected: _selectedContentType == 'video',
                        onSelected: (selected) {
                          setState(() {
                            _selectedContentType = selected ? 'video' : null;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('Text'),
                        selected: _selectedContentType == 'text',
                        onSelected: (selected) {
                          setState(() {
                            _selectedContentType = selected ? 'text' : null;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Health Topics',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  _topics.isEmpty
                      ? const Text('Loading topics...')
                      : SizedBox(
                          height: 50,
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: FilterChip(
                                  label: const Text('All'),
                                  selected: _selectedTopic == null,
                                  onSelected: (selected) {
                                    setState(() {
                                      _selectedTopic =
                                          selected ? null : _selectedTopic;
                                    });
                                  },
                                ),
                              ),
                              ..._topics.map((topic) {
                                return Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: FilterChip(
                                    label: Text(topic),
                                    selected: _selectedTopic == topic,
                                    onSelected: (selected) {
                                      setState(() {
                                        _selectedTopic =
                                            selected ? topic : null;
                                      });
                                    },
                                  ),
                                );
                              }).toList(),
                            ],
                          ),
                        ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Cancel'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          _sortBy = _sortBy;
                          _selectedSource = _selectedSource;
                          _selectedContentType = _selectedContentType;
                          _selectedTopic = _selectedTopic;
                          Navigator.pop(context);
                          _refreshFeed();
                        },
                        child: const Text('Apply Filters'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChatScaffold(
      // Remove app bar completely
      appBar: null,
      showChatInput: false, // No chat input needed for coming soon screen
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Add title at the top
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Discover',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _refreshFeed,
                  ),
                ],
              ),
            ),

            // Main content
            Expanded(
              child: RefreshIndicator(
          onRefresh: _refreshFeed,
          child: _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading feed: $_errorMessage',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _refreshFeed,
                        child: const Text('Try Again'),
                      ),
                    ],
                  ),
                )
              : _posts.isEmpty && !_isLoading
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.article_outlined,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No posts found',
                            style: TextStyle(color: Colors.grey),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _refreshFeed,
                            child: const Text('Refresh'),
                          ),
                        ],
                      ),
                    )
                  : Stack(
                      children: [
                        ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16),
                          itemCount: _posts.length + (_hasMorePosts ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index == _posts.length) {
                              return const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: CircularProgressIndicator(),
                                ),
                              );
                            }

                            final post = _posts[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Post header
                                  Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      children: [
                                        Image.asset(
                                          post.sourceIcon,
                                          width: 24,
                                          height: 24,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: const Text(
                                            'Health Post',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          post.formattedDate,
                                          style: const TextStyle(
                                            color: Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Post content
                                  if (post.mediaUrl.isNotEmpty)
                                    Image.network(
                                      post.mediaUrl,
                                      width: double.infinity,
                                      height: 200,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          width: double.infinity,
                                          height: 200,
                                          color: Colors.grey[200],
                                          child: const Center(
                                            child: Icon(
                                              Icons.image_not_supported,
                                              size: 48,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        );
                                      },
                                    ),

                                  // Caption
                                  Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Text(post.caption),
                                  ),

                                  // Topics
                                  if (post.healthTopics.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12),
                                      child: Wrap(
                                        spacing: 8,
                                        children:
                                            post.healthTopics.map((topic) {
                                          return Chip(
                                            label: Text(
                                              topic,
                                              style:
                                                  const TextStyle(fontSize: 12),
                                            ),
                                            backgroundColor: Colors.pink[50],
                                            padding: EdgeInsets.zero,
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                          );
                                        }).toList(),
                                      ),
                                    ),

                                  // Actions
                                  Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceAround,
                                      children: [
                                        InkWell(
                                          onTap: () => _toggleLikePost(post),
                                          child: Row(
                                            children: [
                                              Icon(
                                                post.isLiked
                                                    ? Icons.favorite
                                                    : Icons.favorite_border,
                                                color: post.isLiked
                                                    ? Colors.pink
                                                    : null,
                                              ),
                                              const SizedBox(width: 4),
                                              Text('${post.likeCount}'),
                                            ],
                                          ),
                                        ),
                                        InkWell(
                                          onTap: () => _toggleSavePost(post),
                                          child: Row(
                                            children: [
                                              Icon(
                                                post.isSaved
                                                    ? Icons.bookmark
                                                    : Icons.bookmark_border,
                                                color: post.isSaved
                                                    ? Colors.blue
                                                    : null,
                                              ),
                                              const SizedBox(width: 4),
                                              Text('${post.saveCount}'),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),

                        // Filter button
                        Positioned(
                          bottom: 16,
                          right: 16,
                          child: FloatingActionButton(
                            onPressed: _showFilterBottomSheet,
                            backgroundColor: const Color(0xFFEC4899),
                            child: const Icon(Icons.filter_list),
                          ),
                        ),

                        // Loading indicator
                        if (_isLoading && _posts.isEmpty)
                          const Center(
                            child: CircularProgressIndicator(),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
