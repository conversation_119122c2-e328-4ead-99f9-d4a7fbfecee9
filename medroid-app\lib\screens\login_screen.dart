import 'package:flutter/material.dart';
import 'package:medroid_app/screens/auth_screen.dart';

class LoginScreen extends StatefulWidget {
  final Function(String)? onLoginSuccess;
  final String initialMode;

  const LoginScreen({
    Key? key,
    this.onLoginSuccess,
    this.initialMode = 'login',
  }) : super(key: key);

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  Widget build(BuildContext context) {
    return AuthScreen(
      initialMode: widget.initialMode,
      onLoginSuccess: widget.onLoginSuccess,
    );
  }
}
