import 'package:flutter/material.dart';
import 'package:medroid_app/screens/agora_video_consultation_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/auth_service.dart';

class MainVideoConsultationScreen extends StatefulWidget {
  final String appointmentId;

  const MainVideoConsultationScreen({
    Key? key,
    required this.appointmentId,
  }) : super(key: key);

  @override
  State<MainVideoConsultationScreen> createState() =>
      _MainVideoConsultationScreenState();
}

class _MainVideoConsultationScreenState
    extends State<MainVideoConsultationScreen> {
  final ApiService _apiService = ApiService();
  final AuthService _authService = AuthService();

  bool _isLoading = true;
  String _errorMessage = '';
  bool _isProvider = false;
  String _userName = '';

  @override
  void initState() {
    super.initState();
    _initializeVideoSession();
  }

  Future<void> _initializeVideoSession() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Check if user is a provider
      final userData = await _authService.getCurrentUser();
      _isProvider = userData?['role'] == 'provider';
      _userName = userData?['name'] ?? 'User';

      debugPrint(
          'Initializing video session for appointment: ${widget.appointmentId}');
      debugPrint('User is ${_isProvider ? 'Provider' : 'Patient'}: $_userName');

      // Initialize the video session
      final response =
          await _apiService.initializeVideoSession(widget.appointmentId);

      if (!response['success']) {
        debugPrint(
            'Failed to initialize video session: ${response['message']}');
        setState(() {
          _isLoading = false;
          _errorMessage =
              response['message'] ?? 'Failed to initialize video session';
        });
        return;
      }

      debugPrint('Video session initialized successfully');
      debugPrint('Room ID: ${response['room_id']}');

      // Get session data
      final sessionData =
          await _apiService.getVideoSessionData(widget.appointmentId);

      if (!sessionData['success']) {
        debugPrint('Failed to get session data: ${sessionData['message']}');
        setState(() {
          _isLoading = false;
          _errorMessage =
              sessionData['message'] ?? 'Failed to get session data';
        });
        return;
      }

      // Verify we have tokens for Agora session
      if (sessionData['tokens'] == null) {
        debugPrint('Missing Agora tokens for video consultation');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Missing Agora tokens for video consultation';
        });
        return;
      }

      debugPrint('Session data retrieved successfully');
      debugPrint('User role: ${sessionData['user_role']}');

      // Navigate to the Agora video consultation screen
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        debugPrint('Navigating to Agora video consultation screen');

        // Use Agora video consultation screen for all platforms
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => AgoraVideoConsultationScreen(
              appointmentId: widget.appointmentId,
              isProvider: _isProvider,
              userName: _userName,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error initializing video session: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Consultation'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? _buildLoadingView()
          : _errorMessage.isNotEmpty
              ? _buildErrorView()
              : const SizedBox(), // This should not be visible as we navigate away
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            Theme.of(context).brightness == Brightness.dark
                ? 'assets/images/medroid_icon_dark.png'
                : 'assets/images/medroid_icon.png',
            height: 100,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(Icons.video_call, size: 80);
            },
          ),
          const SizedBox(height: 24),
          const CircularProgressIndicator(),
          const SizedBox(height: 24),
          const Text(
            'Preparing your video consultation...',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'This may take a few moments',
            style: TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red,
            ),
            const SizedBox(height: 24),
            Text(
              'Unable to start video consultation',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _initializeVideoSession,
              child: const Text('Try Again'),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
