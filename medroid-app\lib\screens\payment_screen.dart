import 'package:flutter/material.dart';
import 'package:medroid_app/screens/unified_payment_screen.dart';

/// This class is an adapter to maintain backward compatibility
/// It simply forwards to the new UnifiedPaymentScreen implementation
class PaymentScreen extends StatelessWidget {
  final Map<String, dynamic> appointmentData;
  final Map<String, dynamic>? paymentData;
  final Function(bool success, String? paymentId) onPaymentComplete;

  const PaymentScreen({
    Key? key,
    required this.appointmentData,
    this.paymentData,
    required this.onPaymentComplete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use the new UnifiedPaymentScreen implementation
    return UnifiedPaymentScreen(
      appointmentData: appointmentData,
      paymentData: paymentData,
      onPaymentComplete: onPaymentComplete,
    );
  }
}