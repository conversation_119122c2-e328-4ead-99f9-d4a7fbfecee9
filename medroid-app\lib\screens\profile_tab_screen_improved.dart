import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image/image.dart' as img;

import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/widgets/appointment_card.dart';
import 'package:medroid_app/widgets/profile_option_tile.dart';
import 'package:medroid_app/widgets/profile_section_header.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';
import 'package:medroid_app/screens/provider_dashboard/provider_profile_edit_screen.dart';
import 'package:image_picker/image_picker.dart';

import 'package:medroid_app/widgets/appointment_preferences_form.dart';
import 'package:medroid_app/screens/change_password_screen.dart';
import 'package:medroid_app/utils/app_colors.dart';

class ProfileTabScreenImproved extends StatefulWidget {
  const ProfileTabScreenImproved({Key? key}) : super(key: key);

  @override
  State<ProfileTabScreenImproved> createState() =>
      _ProfileTabScreenImprovedState();
}

class _ProfileTabScreenImprovedState extends State<ProfileTabScreenImproved>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isUploadingImage = false;
  List<Appointment> _appointments = [];
  String _filter = 'upcoming'; // 'upcoming', 'past', 'all'

  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Image picker variables
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserData();
    _loadAppointments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData({bool forceRefresh = false}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);
      final userData =
          await authService.getCurrentUser(forceRefresh: forceRefresh);

      setState(() {
        _userData = userData;
        _nameController.text = userData?['name'] ?? '';
        _isLoading = false;
      });

      // Debug profile image URL
      debugPrint('User data loaded: ${userData?['name']}');
      debugPrint('Profile image URL: ${userData?['profile_image']}');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading user data: $e')),
        );
      }
    }
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/appointments/user');

      final List<Appointment> appointments = [];
      if (response != null) {
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }
      }

      setState(() {
        _appointments = appointments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading appointments: $e')),
        );
      }
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        return _appointments.where((appointment) {
          return appointment.date.isAfter(now) ||
              (appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
        }).toList();
      case 'past':
        return _appointments.where((appointment) {
          return appointment.date.isBefore(now) &&
              !(appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
        }).toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  Future<void> _logout() async {
    final authService = RepositoryProvider.of<AuthService>(context);
    await authService.logout();

    // Navigate to login screen
    if (mounted) {
      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    }
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80, // Compress to 80% quality
      maxWidth: 1024, // Limit width to 1024px
      maxHeight: 1024, // Limit height to 1024px
    );

    if (image != null) {
      setState(() {
        _isUploadingImage = true;
      });

      try {
        if (kIsWeb) {
          // For web, read the image as bytes and compress further if needed
          final bytes = await image.readAsBytes();
          final compressedBytes = await _compressImageBytes(bytes);
          await _uploadProfileImage(
              webImageBytes: compressedBytes, fileName: image.name);
        } else {
          // For mobile platforms, compress the image
          final compressedFile = await _compressImageFile(File(image.path));
          await _uploadProfileImage(imageFile: compressedFile);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error uploading image: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isUploadingImage = false;
          });
        }
      }
    }
  }

  Future<Uint8List> _compressImageBytes(Uint8List bytes) async {
    try {
      // Decode the image
      final image = img.decodeImage(bytes);
      if (image == null) return bytes;

      // First, crop to square (center crop)
      final size = image.width < image.height ? image.width : image.height;
      final x = (image.width - size) ~/ 2;
      final y = (image.height - size) ~/ 2;

      img.Image cropped =
          img.copyCrop(image, x: x, y: y, width: size, height: size);

      // Then resize to optimal size for profile pictures (512x512)
      const int profileSize = 512;
      img.Image resized =
          img.copyResize(cropped, width: profileSize, height: profileSize);

      // Compress with reducing quality until under 2MB
      int quality = 90;
      Uint8List compressed;
      do {
        compressed =
            Uint8List.fromList(img.encodeJpg(resized, quality: quality));
        quality -= 10;
      } while (compressed.length > 1.8 * 1024 * 1024 &&
          quality > 30); // 1.8MB to be safe

      debugPrint(
          'Image processed: Original ${bytes.length} bytes -> Final ${compressed.length} bytes (Quality: $quality%, Size: ${profileSize}x$profileSize)');
      return compressed;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return bytes;
    }
  }

  Future<File> _compressImageFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final compressedBytes = await _compressImageBytes(bytes);

      // Create a new temporary file with compressed data
      final tempDir = Directory.systemTemp;
      final tempFile = File(
          '${tempDir.path}/compressed_profile_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await tempFile.writeAsBytes(compressedBytes);

      return tempFile;
    } catch (e) {
      debugPrint('Error compressing image file: $e');
      return file;
    }
  }

  Future<void> _uploadProfileImage(
      {File? imageFile, Uint8List? webImageBytes, String? fileName}) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Create a multipart request
      Map<String, dynamic> result;

      if (kIsWeb && webImageBytes != null) {
        // For web
        result = await apiService.uploadProfileImageWeb(
          webImageBytes,
          fileName ?? 'profile.jpg',
        );
      } else if (imageFile != null) {
        // For mobile
        result = await apiService.uploadProfileImage(imageFile);
      } else {
        throw Exception('No image provided');
      }

      if (result['success']) {
        // Update user data with the response from the backend
        if (result['data'] != null && mounted) {
          final authService = RepositoryProvider.of<AuthService>(context);
          await authService.saveUserData(result['data']);
          debugPrint(
              'Updated user data with new profile image: ${result['data']['profile_image']}');
        }

        // Force refresh user data from API to ensure UI is updated
        if (mounted) {
          await _loadUserData(forceRefresh: true);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profile image updated successfully')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to update profile image: ${result['message']}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error uploading profile image: $e')),
        );
      }
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);

      final updateData = {
        'name': _nameController.text,
      };

      final success = await authService.updateProfile(updateData);

      if (!mounted) return;

      if (success) {
        await _loadUserData();

        if (!mounted) return;

        setState(() {
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully')),
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update profile')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating profile: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Scaffold(
      backgroundColor:
          AppColors.backgroundLight, // Match Discover screen background
      appBar: AppBar(
        backgroundColor:
            AppColors.backgroundLight, // Match Discover screen background
        elevation: 0,
        title: const Text(
          'My Account',
          style: TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (!_isEditing &&
              !_isLoading &&
              _userData != null &&
              (!isDesktop || _tabController.index == 1))
            IconButton(
              icon: const Icon(Icons.edit, color: AppColors.tealSurge),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      body: isDesktop
          // Desktop layout - Profile only
          ? _isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: AppColors.tealSurge))
              : _userData == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text('Failed to load user profile'),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadUserData,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.tealSurge,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  : _isEditing
                      ? _buildEditForm()
                      : _buildProfileView()
          // Mobile layout - Tabbed view with appointments and profile
          : Column(
              children: [
                TabBar(
                  controller: _tabController,
                  labelColor: AppColors.tealSurge,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: AppColors.tealSurge,
                  tabs: const [
                    Tab(text: 'Appointments'),
                    Tab(text: 'Profile'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Appointments Tab
                      _buildAppointmentsTab(),

                      // Profile Tab
                      _isLoading
                          ? const Center(
                              child: CircularProgressIndicator(
                                  color: AppColors.tealSurge))
                          : _userData == null
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Text('Failed to load user profile'),
                                      const SizedBox(height: 16),
                                      ElevatedButton(
                                        onPressed: _loadUserData,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.tealSurge,
                                          foregroundColor: Colors.white,
                                        ),
                                        child: const Text('Retry'),
                                      ),
                                    ],
                                  ),
                                )
                              : _isEditing
                                  ? _buildEditForm()
                                  : _buildProfileView(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildAppointmentsTab() {
    if (_isLoading) {
      return const Center(
          child: CircularProgressIndicator(color: AppColors.tealSurge));
    }

    if (_appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.calendar_today, size: 64, color: Colors.grey[300]),
            const SizedBox(height: 16),
            Text(
              'No appointments found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadAppointments,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.tealSurge,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Filter chips
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('upcoming', 'Upcoming'),
                const SizedBox(width: 8),
                _buildFilterChip('past', 'Past'),
                const SizedBox(width: 8),
                _buildFilterChip('all', 'All'),
              ],
            ),
          ),
        ),

        // Appointments list
        Expanded(
          child: _filteredAppointments.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.event_busy, size: 64, color: Colors.grey[300]),
                      const SizedBox(height: 16),
                      Text(
                        'No $_filter appointments',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadAppointments,
                  color: AppColors.tealSurge,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredAppointments.length,
                    itemBuilder: (context, index) {
                      return AppointmentCard(
                        appointment: _filteredAppointments[index],
                        onActionCompleted: _loadAppointments,
                      );
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _filter == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _filter = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.tealSurge.withAlpha(25)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.tealSurge : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? AppColors.tealSurge : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileView() {
    final isPatient = _userData!['role'] == 'patient';
    final isProvider = _userData!['role'] == 'provider';
    const themeColor = AppColors.tealSurge;
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    // Get the spacing variables from the parent context
    final parentContext = context;
    final verticalSpacing =
        MediaQuery.of(parentContext).size.width >= 768 ? 12.0 : 20.0;
    final horizontalPadding =
        MediaQuery.of(parentContext).size.width >= 768 ? 16.0 : 24.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(horizontalPadding),
      child: isDesktop
          // Desktop layout - Two column layout
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left column - Profile image and basic info
                Expanded(
                  flex: 1,
                  child: Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Profile image with upload button
                          Stack(
                            children: <Widget>[
                              // Profile image
                              CircleAvatar(
                                radius: 70,
                                backgroundColor: Colors.grey[100],
                                child: ClipOval(
                                  child: _userData!['profile_image'] != null &&
                                          _userData!['profile_image']
                                              .toString()
                                              .isNotEmpty &&
                                          _userData!['profile_image'] != 'null'
                                      ? Builder(
                                          builder: (context) {
                                            final imageUrl = _userData![
                                                        'profile_image']
                                                    .startsWith('http')
                                                ? _userData!['profile_image']
                                                : '${Constants.baseStorageUrl}${_userData!['profile_image']}';
                                            debugPrint(
                                                'Loading profile image from: $imageUrl');
                                            return PlatformAwareNetworkImage(
                                              imageUrl: imageUrl,
                                              width: 140,
                                              height: 140,
                                              fit: BoxFit.cover,
                                              placeholder: (context, url) =>
                                                  const CircularProgressIndicator(
                                                      color: themeColor),
                                              errorWidget:
                                                  (context, url, error) {
                                                debugPrint(
                                                    'Error loading profile image: $error for URL: $url');
                                                return Icon(
                                                  Icons.person,
                                                  size: 70,
                                                  color: Colors.grey[300],
                                                );
                                              },
                                            );
                                          },
                                        )
                                      : Icon(
                                          Icons.person,
                                          size: 70,
                                          color: Colors.grey[300],
                                        ),
                                ),
                              ),
                              // Camera icon
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: GestureDetector(
                                  onTap: _pickImage,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: const BoxDecoration(
                                      color: themeColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: _isUploadingImage
                                        ? const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                            ),
                                          )
                                        : const Icon(
                                            Icons.camera_alt,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 24),
                // Right column - Settings and other options
                Expanded(
                  flex: 2,
                  child: Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // User info section
                          Text(
                            _userData!['name'] ?? 'User',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _userData!['email'] ?? '',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: isPatient
                                  ? Colors.blue.withAlpha(26)
                                  : isProvider
                                      ? Colors.green.withAlpha(26)
                                      : Colors.grey.withAlpha(26),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              isPatient
                                  ? 'Patient'
                                  : isProvider
                                      ? 'Healthcare Provider'
                                      : _userData!['role'] ?? 'User',
                              style: TextStyle(
                                color: isPatient
                                    ? Colors.blue
                                    : isProvider
                                        ? Colors.green
                                        : Colors.grey[700],
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ),
                          SizedBox(height: verticalSpacing),
                          const Divider(),
                          SizedBox(height: verticalSpacing * 0.75),

                          // Account section
                          const ProfileSectionHeader(title: 'Account'),
                          ProfileOptionTile(
                            icon: Icons.person,
                            title: 'Personal Information',
                            onTap: () {
                              setState(() {
                                _isEditing = true;
                              });
                            },
                          ),
                          ProfileOptionTile(
                            icon: Icons.lock,
                            title: 'Change Password',
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const ChangePasswordScreen(),
                                ),
                              );
                            },
                          ),
                          if (isPatient) ...[
                            ProfileOptionTile(
                              icon: Icons.favorite,
                              title: 'Health Profile',
                              onTap: () {
                                // Navigate to health profile screen
                              },
                            ),
                            ProfileOptionTile(
                              icon: Icons.calendar_today,
                              title: 'Appointment Preferences',
                              onTap: () {
                                _showAppointmentPreferencesDialog();
                              },
                            ),
                          ],
                          if (isProvider) ...[
                            ProfileOptionTile(
                              icon: Icons.business,
                              title: 'Practice Information',
                              onTap: () {
                                // Navigate to provider dashboard with practice information tab selected
                                Navigator.pushNamed(
                                  context,
                                  '/provider-dashboard',
                                  arguments: {
                                    'initialTab': 3
                                  }, // Index of the Practice Info tab
                                );
                              },
                            ),
                            ProfileOptionTile(
                              icon: Icons.person_outline,
                              title: 'Edit Provider Profile',
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const ProviderProfileEditScreen(),
                                  ),
                                ).then((value) {
                                  if (value == true) {
                                    // Reload user data if profile was updated
                                    _loadUserData();
                                  }
                                });
                              },
                            ),
                          ],
                          ProfileOptionTile(
                            icon: Icons.notifications,
                            title: 'Notification Settings',
                            onTap: () {
                              // Navigate to notification settings screen
                            },
                          ),

                          // App section
                          const SizedBox(height: 8),
                          const ProfileSectionHeader(title: 'App'),
                          ProfileOptionTile(
                            icon: Icons.help,
                            title: 'Help & Support',
                            onTap: () {
                              // Navigate to help screen
                            },
                          ),
                          ProfileOptionTile(
                            icon: Icons.privacy_tip,
                            title: 'Privacy Policy',
                            onTap: () {
                              // Navigate to privacy policy screen
                            },
                          ),
                          ProfileOptionTile(
                            icon: Icons.description,
                            title: 'Terms of Service',
                            onTap: () {
                              // Navigate to terms of service screen
                            },
                          ),

                          // Logout button
                          const SizedBox(height: 24),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _logout,
                              icon: const Icon(Icons.exit_to_app, size: 18),
                              label: const Text('Logout'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red.shade400,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )
          // Mobile layout - Single column
          : Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Profile image and name section
                Center(
                  child: Stack(
                    children: <Widget>[
                      // Profile image
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.grey[100],
                        child: ClipOval(
                          child: _userData!['profile_image'] != null &&
                                  _userData!['profile_image']
                                      .toString()
                                      .isNotEmpty &&
                                  _userData!['profile_image'] != 'null'
                              ? Builder(
                                  builder: (context) {
                                    final imageUrl = _userData!['profile_image']
                                            .startsWith('http')
                                        ? _userData!['profile_image']
                                        : '${Constants.baseStorageUrl}${_userData!['profile_image']}';
                                    debugPrint(
                                        'Loading mobile profile image from: $imageUrl');
                                    return PlatformAwareNetworkImage(
                                      imageUrl: imageUrl,
                                      width: 100,
                                      height: 100,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) =>
                                          const CircularProgressIndicator(
                                              color: themeColor),
                                      errorWidget: (context, url, error) {
                                        debugPrint(
                                            'Error loading mobile profile image: $error for URL: $url');
                                        return Icon(
                                          Icons.person,
                                          size: 50,
                                          color: Colors.grey[300],
                                        );
                                      },
                                    );
                                  },
                                )
                              : Icon(
                                  Icons.person,
                                  size: 50,
                                  color: Colors.grey[300],
                                ),
                        ),
                      ),
                      // Camera icon
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: _pickImage,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: themeColor,
                              shape: BoxShape.circle,
                            ),
                            child: _isUploadingImage
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : const Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _userData!['name'] ?? 'User',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _userData!['email'] ?? '',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: isPatient
                        ? Colors.blue.withAlpha(26)
                        : isProvider
                            ? Colors.green.withAlpha(26)
                            : Colors.grey.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isPatient
                        ? 'Patient'
                        : isProvider
                            ? 'Healthcare Provider'
                            : _userData!['role'] ?? 'User',
                    style: TextStyle(
                      color: isPatient
                          ? Colors.blue
                          : isProvider
                              ? Colors.green
                              : Colors.grey[700],
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Account section
                const ProfileSectionHeader(title: 'Account'),
                ProfileOptionTile(
                  icon: Icons.person,
                  title: 'Personal Information',
                  onTap: () {
                    setState(() {
                      _isEditing = true;
                    });
                  },
                ),
                ProfileOptionTile(
                  icon: Icons.lock,
                  title: 'Change Password',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ChangePasswordScreen(),
                      ),
                    );
                  },
                ),
                if (isPatient) ...[
                  ProfileOptionTile(
                    icon: Icons.favorite,
                    title: 'Health Profile',
                    onTap: () {
                      // Navigate to health profile screen
                    },
                  ),
                  ProfileOptionTile(
                    icon: Icons.calendar_today,
                    title: 'Appointment Preferences',
                    onTap: () {
                      _showAppointmentPreferencesDialog();
                    },
                  ),
                ],
                if (isProvider) ...[
                  ProfileOptionTile(
                    icon: Icons.business,
                    title: 'Practice Information',
                    onTap: () {
                      // Navigate to provider dashboard with practice information tab selected
                      Navigator.pushNamed(
                        context,
                        '/provider-dashboard',
                        arguments: {
                          'initialTab': 3
                        }, // Index of the Practice Info tab
                      );
                    },
                  ),
                  ProfileOptionTile(
                    icon: Icons.person_outline,
                    title: 'Edit Provider Profile',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              const ProviderProfileEditScreen(),
                        ),
                      ).then((value) {
                        if (value == true) {
                          // Reload user data if profile was updated
                          _loadUserData();
                        }
                      });
                    },
                  ),
                ],
                ProfileOptionTile(
                  icon: Icons.notifications,
                  title: 'Notification Settings',
                  onTap: () {
                    // Navigate to notification settings screen
                  },
                ),

                // App section
                const SizedBox(height: 8),
                const ProfileSectionHeader(title: 'App'),
                ProfileOptionTile(
                  icon: Icons.help,
                  title: 'Help & Support',
                  onTap: () {
                    // Navigate to help screen
                  },
                ),
                ProfileOptionTile(
                  icon: Icons.privacy_tip,
                  title: 'Privacy Policy',
                  onTap: () {
                    // Navigate to privacy policy screen
                  },
                ),
                ProfileOptionTile(
                  icon: Icons.description,
                  title: 'Terms of Service',
                  onTap: () {
                    // Navigate to terms of service screen
                  },
                ),

                // Logout button
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _logout,
                    icon: const Icon(Icons.exit_to_app, size: 18),
                    label: const Text('Logout'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade400,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
              ],
            ),
    );
  }

  // Show appointment preferences dialog
  Future<void> _showAppointmentPreferencesDialog() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/patient/appointment-preferences');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      final appointmentPreferences = response?['appointment_preferences'];

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Appointment Preferences'),
          content: SizedBox(
            width: 500,
            child: AppointmentPreferencesForm(
              initialPreferences: appointmentPreferences,
              onPreferencesSaved: (updatedPreferences) {
                Navigator.of(context).pop(); // Close the dialog

                // Show confirmation
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Appointment preferences saved'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading appointment preferences: $e')),
        );
      }
    }
  }

  // Build the edit form for user profile
  Widget _buildEditForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile image
            Center(
              child: Stack(
                children: <Widget>[
                  // Profile image
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey[100],
                    child: ClipOval(
                      child: _userData!['profile_image'] != null
                          ? PlatformAwareNetworkImage(
                              imageUrl: _userData!['profile_image']
                                      .startsWith('http')
                                  ? _userData!['profile_image']
                                  : '${Constants.baseStorageUrl}${_userData!['profile_image']}',
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                              placeholder: (context, url) =>
                                  const CircularProgressIndicator(
                                      color: Color(0xFFEC4899)),
                              errorWidget: (context, url, error) {
                                debugPrint(
                                    'Error loading profile image: $error for URL: $url');
                                return Icon(
                                  Icons.person,
                                  size: 50,
                                  color: Colors.grey[300],
                                );
                              },
                            )
                          : Icon(
                              Icons.person,
                              size: 50,
                              color: Colors.grey[300],
                            ),
                    ),
                  ),
                  // Camera icon
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: _pickImage,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Color(0xFFEC4899),
                          shape: BoxShape.circle,
                        ),
                        child: _isUploadingImage
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 16,
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Name field
            const Text(
              'Name',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                hintText: 'Enter your name',
                filled: true,
                fillColor: Colors.grey.shade50,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFEC4899)),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Email field (read-only)
            const Text(
              'Email',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: _userData!['email'] ?? '',
              readOnly: true,
              decoration: InputDecoration(
                hintText: 'Your email',
                filled: true,
                fillColor: Colors.grey.shade100,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                suffixIcon: const Icon(Icons.lock_outline, size: 18),
              ),
            ),
            const SizedBox(height: 32),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _isEditing = false;
                        _nameController.text = _userData!['name'] ?? '';
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black87,
                      side: BorderSide(color: Colors.grey.shade300),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEC4899),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text('Save'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
