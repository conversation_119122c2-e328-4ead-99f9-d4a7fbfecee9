import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/screens/referral_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/payment_service.dart';
import 'package:medroid_app/widgets/appointment_card.dart';

class ProfileTabScreenUpdated extends StatefulWidget {
  const ProfileTabScreenUpdated({Key? key}) : super(key: key);

  @override
  _ProfileTabScreenUpdatedState createState() =>
      _ProfileTabScreenUpdatedState();
}

class _ProfileTabScreenUpdatedState extends State<ProfileTabScreenUpdated>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  bool _isEditing = false;
  List<Appointment> _appointments = [];
  String _filter = 'upcoming'; // 'upcoming', 'past', 'all'
  late PaymentService _paymentService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _paymentService = PaymentService();
    _loadUserData();
    _loadAppointments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/user');

      if (response != null) {
        setState(() {
          _userData = response;
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/appointments/user');

      if (response != null) {
        final List<Appointment> appointments = [];
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }

        setState(() {
          _appointments = appointments;
          _isLoading = false;
        });
      } else {
        setState(() {
          _appointments = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading appointments: $e');
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading appointments: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        return _appointments
            .where((appointment) =>
                appointment.date.isAfter(now) ||
                (appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'past':
        return _appointments
            .where((appointment) =>
                appointment.date.isBefore(now) &&
                !(appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title:
            const Text('My Account', style: TextStyle(color: Colors.black87)),
      ),
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            labelColor: const Color(0xFFEC4899),
            unselectedLabelColor: Colors.grey,
            indicatorColor: const Color(0xFFEC4899),
            tabs: const [
              Tab(text: 'Appointments'),
              Tab(text: 'Profile'),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Appointments Tab
                _buildAppointmentsTab(),

                // Profile Tab
                _isEditing ? _buildEditProfileForm() : _buildProfileTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsTab() {
    if (_isLoading) {
      return const Center(
          child: CircularProgressIndicator(color: Color(0xFFEC4899)));
    }

    if (_appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.calendar_today, size: 64, color: Colors.grey[300]),
            const SizedBox(height: 16),
            Text(
              'No appointments found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadAppointments,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFEC4899),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Filter chips
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('upcoming', 'Upcoming'),
                const SizedBox(width: 8),
                _buildFilterChip('past', 'Past'),
                const SizedBox(width: 8),
                _buildFilterChip('all', 'All'),
              ],
            ),
          ),
        ),

        // Appointments list
        Expanded(
          child: _filteredAppointments.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.event_busy, size: 64, color: Colors.grey[300]),
                      const SizedBox(height: 16),
                      Text(
                        'No $_filter appointments',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadAppointments,
                  color: const Color(0xFFEC4899),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredAppointments.length,
                    itemBuilder: (context, index) {
                      return AppointmentCard(
                        appointment: _filteredAppointments[index],
                        onActionCompleted: _loadAppointments,
                      );
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _filter == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _filter = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFFEC4899).withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFFEC4899) : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? const Color(0xFFEC4899) : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header
          Center(
            child: Column(
              children: [
                const CircleAvatar(
                  radius: 50,
                  backgroundColor: Color(0xFFEC4899),
                  child: Icon(Icons.person, size: 50, color: Colors.white),
                ),
                const SizedBox(height: 16),
                Text(
                  _userData?['name'] ?? 'User',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _userData?['email'] ?? '<EMAIL>',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 12),
                // Credit balance indicator
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D9488).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.card_giftcard,
                        size: 18,
                        color: Color(0xFF0D9488),
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Credits: \$0.00',
                        style: TextStyle(
                          color: Color(0xFF0D9488),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit Profile'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFEC4899),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () {
                    setState(() {
                      _isEditing = true;
                    });
                  },
                ),
              ],
            ),
          ),

          // Settings Section
          const SizedBox(height: 32),
          _buildSectionHeader('Settings'),
          const SizedBox(height: 16),

          _buildOptionTile(
            icon: Icons.card_giftcard,
            title: 'Referrals & Credits',
            subtitle: 'Invite friends and earn \$3 credit',
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ReferralScreen(),
                ),
              );
            },
          ),

          _buildOptionTile(
            icon: Icons.notifications_outlined,
            title: 'Notifications',
            subtitle: 'Manage your notification preferences',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Notification settings coming soon'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),

          _buildOptionTile(
            icon: Icons.help_outline,
            title: 'Help & Support',
            subtitle: 'Get assistance and contact support',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Help & Support coming soon'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),

          _buildOptionTile(
            icon: Icons.logout,
            title: 'Logout',
            subtitle: 'Sign out from your account',
            onTap: () async {
              // Show confirmation dialog
              final shouldLogout = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Logout'),
                  content: const Text('Are you sure you want to logout?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: const Text('Logout'),
                    ),
                  ],
                ),
              );

              if (shouldLogout == true) {
                final authService = RepositoryProvider.of<AuthService>(context);
                await authService.logout();
                if (mounted) {
                  Navigator.pushNamedAndRemoveUntil(
                      context, '/', (route) => false);
                }
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFFEC4899),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Divider(
              color: const Color(0xFFEC4899).withOpacity(0.2),
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFEC4899).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: const Color(0xFFEC4899), size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            )
          : null,
      trailing: const Icon(Icons.chevron_right, size: 20, color: Colors.grey),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildEditProfileForm() {
    // Placeholder for edit profile form
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Edit Profile Coming Soon',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isEditing = false;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEC4899),
              foregroundColor: Colors.white,
            ),
            child: const Text('Back to Profile'),
          ),
        ],
      ),
    );
  }
}
