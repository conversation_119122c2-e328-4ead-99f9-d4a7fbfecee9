import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/services/api_service.dart';

class PatientDetailsScreen extends StatefulWidget {
  final Appointment appointment;

  const PatientDetailsScreen({
    Key? key,
    required this.appointment,
  }) : super(key: key);

  @override
  State<PatientDetailsScreen> createState() => _PatientDetailsScreenState();
}

class _PatientDetailsScreenState extends State<PatientDetailsScreen> {
  bool _isLoading = true;
  Map<String, dynamic>? _patientData;
  List<Appointment> _patientAppointments = [];

  @override
  void initState() {
    super.initState();
    _loadPatientDetails();
  }

  Future<void> _loadPatientDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Get patient details from the appointment
      final patientData = widget.appointment.patient;

      // Get all appointments for this patient
      final response = await apiService
          .get('/appointments/patient/${widget.appointment.patientId}');

      if (mounted) {
        setState(() {
          _patientData = patientData;

          if (response != null && response is List) {
            _patientAppointments = response
                .map((appointmentData) => Appointment.fromJson(appointmentData))
                .toList();
          }

          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading patient details: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading patient details: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Details'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _patientData == null
              ? const Center(
                  child: Text('Patient information not available'),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPatientHeader(),
                      const SizedBox(height: 24),
                      _buildHealthInformation(),
                      const SizedBox(height: 24),
                      _buildAppointmentHistory(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildPatientHeader() {
    final user = _patientData?['user'];
    final name = user?['name'] ?? 'Unknown Patient';
    final email = user?['email'] ?? 'No email provided';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Colors.grey[200],
              child: Icon(
                Icons.person,
                size: 40,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    email,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(26), // 0.1 opacity = 26/255
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      'Patient',
                      style: TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthInformation() {
    final healthHistory =
        _patientData?['health_history'] as List<dynamic>? ?? [];
    final allergies = _patientData?['allergies'] as List<dynamic>? ?? [];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Health Information',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),

            // Health History
            const Text(
              'Health History',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            healthHistory.isEmpty
                ? const Text('No health history recorded')
                : Column(
                    children: healthHistory
                        .map((item) => _buildHealthHistoryItem(item))
                        .toList(),
                  ),
            const SizedBox(height: 16),

            // Allergies
            const Text(
              'Allergies',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            allergies.isEmpty
                ? const Text('No allergies recorded')
                : Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: allergies
                        .map((allergy) => Chip(
                              label: Text(allergy.toString()),
                              backgroundColor: Colors.red[50],
                              labelStyle: TextStyle(color: Colors.red[700]),
                            ))
                        .toList(),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthHistoryItem(Map<String, dynamic> item) {
    final condition = item['condition'] ?? 'Unknown condition';
    final diagnosedDate = item['diagnosed_date'] != null
        ? DateFormat('MMM d, y').format(DateTime.parse(item['diagnosed_date']))
        : 'Unknown date';
    final medications = item['medications'] as List<dynamic>? ?? [];

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            condition,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text('Diagnosed: $diagnosedDate'),
          if (medications.isNotEmpty)
            Text('Medications: ${medications.join(', ')}'),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildAppointmentHistory() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Appointment History',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),
            _patientAppointments.isEmpty
                ? const Text('No previous appointments')
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _patientAppointments.length,
                    itemBuilder: (context, index) {
                      final appointment = _patientAppointments[index];
                      return _buildAppointmentHistoryItem(appointment);
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentHistoryItem(Appointment appointment) {
    final dateFormat = DateFormat('MMM d, y');
    final formattedDate = dateFormat.format(appointment.date);

    return ListTile(
      title: Text(formattedDate),
      subtitle: Text(
        '${appointment.timeSlot['start_time']} - ${appointment.timeSlot['end_time']} | ${appointment.status.toUpperCase()}',
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: appointment.statusColor.isEmpty
              ? Colors.grey
              : Color(
                  int.parse(appointment.statusColor.replaceFirst('#', '0xFF'))),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          appointment.status.toUpperCase(),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
