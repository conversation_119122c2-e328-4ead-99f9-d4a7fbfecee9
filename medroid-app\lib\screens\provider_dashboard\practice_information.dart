import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/widgets/language_selector.dart';
import 'package:medroid_app/widgets/location_picker.dart';
import 'package:medroid_app/screens/provider_dashboard/provider_profile_edit_screen.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class PracticeInformationScreen extends StatefulWidget {
  const PracticeInformationScreen({Key? key}) : super(key: key);

  @override
  State<PracticeInformationScreen> createState() =>
      _PracticeInformationScreenState();
}

class _PracticeInformationScreenState extends State<PracticeInformationScreen> {
  bool _isLoading = true;
  bool _isSaving = false;
  bool _profileNotFound = false;
  Map<String, dynamic>? _providerData;

  // Form controllers and values
  final _formKey = GlobalKey<FormState>();
  String _selectedGender = 'male';
  List<String> _selectedLanguages = [];
  final List<Map<String, dynamic>> _practiceLocations = [];

  @override
  void initState() {
    super.initState();
    _loadProviderProfile();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadProviderProfile() async {
    setState(() {
      _isLoading = true;
      _profileNotFound = false;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final providerData = await apiService.getProviderProfile();

      if (mounted) {
        setState(() {
          _providerData = providerData;
          _isLoading = false;

          // Initialize form values from provider data
          if (providerData['gender'] != null) {
            _selectedGender = providerData['gender'];
          }

          if (providerData['languages'] != null &&
              providerData['languages'] is List) {
            _selectedLanguages = List<String>.from(providerData['languages']);
          }

          if (providerData['practice_locations'] != null &&
              providerData['practice_locations'] is List) {
            _practiceLocations.clear();
            for (var location in providerData['practice_locations']) {
              _practiceLocations.add(Map<String, dynamic>.from(location));
            }
          }
        });
      }
    } catch (e) {
      // Handle provider profile not found error
      if (e.toString().contains('Provider profile not found')) {
        if (mounted) {
          setState(() {
            _profileNotFound = true;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error loading provider profile: $e')),
          );
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _saveProviderProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Use the selected languages
      final languages = _selectedLanguages;

      // Get existing provider data to preserve other fields
      final specialization =
          _providerData?['specialization'] ?? 'General Practice';

      // Properly cast the weekly availability to the expected type
      List<Map<String, dynamic>> weeklyAvailability = [];
      if (_providerData?['weekly_availability'] != null) {
        final rawAvailability =
            _providerData!['weekly_availability'] as List<dynamic>;
        weeklyAvailability = rawAvailability.map((item) {
          // Convert each item to Map<String, dynamic>
          final Map<String, dynamic> availabilityMap =
              Map<String, dynamic>.from(item as Map);

          // Ensure slots is properly handled
          if (availabilityMap.containsKey('slots') &&
              availabilityMap['slots'] is List) {
            final List<dynamic> rawSlots =
                availabilityMap['slots'] as List<dynamic>;
            availabilityMap['slots'] = rawSlots
                .map((slot) => Map<String, dynamic>.from(slot as Map))
                .toList();
          } else {
            availabilityMap['slots'] = [];
          }

          return availabilityMap;
        }).toList();
      }

      // Properly cast the absences to the expected type
      List<Map<String, dynamic>> absences = [];
      if (_providerData?['absences'] != null) {
        final rawAbsences = _providerData!['absences'] as List<dynamic>;
        absences = rawAbsences
            .map((item) => Map<String, dynamic>.from(item as Map))
            .toList();
      }

      // Update provider profile
      final response = await apiService.createOrUpdateProviderProfile(
        specialization: specialization,
        weeklyAvailability: weeklyAvailability,
        absences: absences,
        gender: _selectedGender,
        languages: languages,
        practiceLocations: _practiceLocations,
      );

      if (mounted) {
        setState(() {
          _isSaving = false;
          _providerData = response['provider'];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Practice information updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating practice information: $e')),
        );
      }
    }
  }

  void _addPracticeLocation(Map<String, dynamic> location) {
    // Debug information is logged to console

    // Validate the location data
    if (!location.containsKey('address') ||
        !location.containsKey('coordinates')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('Invalid location data: missing address or coordinates')),
      );
      return;
    }

    if (location['coordinates'] == null ||
        location['coordinates'] is! List ||
        (location['coordinates'] as List).length != 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid coordinates format')),
      );
      return;
    }

    // Ensure coordinates are doubles
    try {
      final coordinates = location['coordinates'] as List;
      final validatedLocation = {
        'address': location['address'],
        'coordinates': [
          coordinates[0] is double
              ? coordinates[0]
              : double.parse(coordinates[0].toString()),
          coordinates[1] is double
              ? coordinates[1]
              : double.parse(coordinates[1].toString()),
        ],
      };

      setState(() {
        _practiceLocations.add(validatedLocation);
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error processing location data: $e')),
      );
    }
  }

  void _removePracticeLocation(int index) {
    setState(() {
      _practiceLocations.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    if (_isLoading) {
      // For desktop/tablet, no AppBar since it's handled by parent
      if (isDesktop || isTablet) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      return Scaffold(
        appBar: AppBar(
          title: const Text('Practice Information'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_profileNotFound) {
      // For desktop/tablet, no AppBar since it's handled by parent
      if (isDesktop || isTablet) {
        return _buildProfileNotFoundView(theme);
      }

      return Scaffold(
        appBar: AppBar(
          title: const Text('Practice Information'),
        ),
        body: _buildProfileNotFoundView(theme),
      );
    }

    // For desktop and tablet, we don't show the AppBar since it's handled by the parent
    if (isDesktop || isTablet) {
      return _buildResponsiveContent(context, isDesktop);
    }

    // Mobile layout with AppBar
    return Scaffold(
      appBar: AppBar(
        title: const Text('Practice Information'),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_outline),
            tooltip: 'Edit Profile',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ProviderProfileEditScreen(),
                ),
              ).then((value) {
                if (value == true) {
                  // Reload data if profile was updated
                  _loadProviderProfile();
                }
              });
            },
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Provider Details',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 16),

              // Gender Selection
              Text(
                'Gender',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              SegmentedButton<String>(
                segments: const [
                  ButtonSegment(
                    value: 'male',
                    label: Text('Male'),
                    icon: Icon(Icons.male),
                  ),
                  ButtonSegment(
                    value: 'female',
                    label: Text('Female'),
                    icon: Icon(Icons.female),
                  ),
                  ButtonSegment(
                    value: 'other',
                    label: Text('Other'),
                    icon: Icon(Icons.person),
                  ),
                ],
                selected: {_selectedGender},
                onSelectionChanged: (Set<String> selection) {
                  setState(() {
                    _selectedGender = selection.first;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Languages
              Text(
                'Languages',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              LanguageSelector(
                selectedLanguages: _selectedLanguages,
                onLanguagesChanged: (languages) {
                  setState(() {
                    _selectedLanguages = languages;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Practice Locations
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Practice Locations',
                    style: theme.textTheme.titleMedium,
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      _showAddLocationDialog();
                    },
                    icon: const Icon(Icons.add_location),
                    label: const Text('Add Location'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (_practiceLocations.isEmpty)
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: Text('No practice locations added yet'),
                    ),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _practiceLocations.length,
                  itemBuilder: (context, index) {
                    final location = _practiceLocations[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8.0),
                      child: ListTile(
                        title: Text(location['address'] ?? 'Unknown Address'),
                        subtitle: Text(
                          'Coordinates: ${location['coordinates']?[0]}, ${location['coordinates']?[1]}',
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _removePracticeLocation(index),
                        ),
                      ),
                    );
                  },
                ),
              const SizedBox(height: 32),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSaving ? null : _saveProviderProfile,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isSaving
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        )
                      : const Text('Save Practice Information'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddLocationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Practice Location'),
        content: SizedBox(
          width: double.maxFinite,
          child: LocationPicker(
            onLocationSelected: (location) {
              // Log the location data for debugging
              debugPrint('Location selected: $location');
              Navigator.pop(context);
              _addPracticeLocation(location);
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileNotFoundView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'Provider Profile Not Found',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Please set up your availability first.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveContent(BuildContext context, bool isDesktop) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main content area
          Expanded(
            flex: isDesktop ? 2 : 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and edit profile button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Practice Information',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const ProviderProfileEditScreen(),
                          ),
                        ).then((value) {
                          if (value == true) {
                            _loadProviderProfile();
                          }
                        });
                      },
                      icon: const Icon(Icons.person_outline, size: 18),
                      label: const Text('Edit Profile'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        elevation: 2,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Form content
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: _buildFormContent(theme, isDesktop),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormContent(ThemeData theme, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Provider Details Section
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.cardTheme.color,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Provider Details',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),

              // Gender Selection
              Text(
                'Gender',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 12),
              SegmentedButton<String>(
                segments: const [
                  ButtonSegment(
                    value: 'male',
                    label: Text('Male'),
                    icon: Icon(Icons.male),
                  ),
                  ButtonSegment(
                    value: 'female',
                    label: Text('Female'),
                    icon: Icon(Icons.female),
                  ),
                  ButtonSegment(
                    value: 'other',
                    label: Text('Other'),
                    icon: Icon(Icons.person),
                  ),
                ],
                selected: {_selectedGender},
                onSelectionChanged: (Set<String> selection) {
                  setState(() {
                    _selectedGender = selection.first;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Languages
              Text(
                'Languages',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 12),
              LanguageSelector(
                selectedLanguages: _selectedLanguages,
                onLanguagesChanged: (languages) {
                  setState(() {
                    _selectedLanguages = languages;
                  });
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // Practice Locations Section
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.cardTheme.color,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Practice Locations',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _showAddLocationDialog,
                    icon: const Icon(Icons.add_location, size: 18),
                    label: const Text('Add Location'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.surface,
                      foregroundColor: theme.colorScheme.primary,
                      elevation: 1,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              if (_practiceLocations.isEmpty)
                Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.dividerTheme.color ?? Colors.grey.shade300,
                      style: BorderStyle.solid,
                    ),
                  ),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.location_off,
                          size: 48,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No practice locations added yet',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _practiceLocations.length,
                  itemBuilder: (context, index) {
                    final location = _practiceLocations[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: theme.cardTheme.color,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              theme.dividerTheme.color ?? Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(16),
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withAlpha(25),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.location_on,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        title: Text(
                          location['address'] ?? 'Unknown Address',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            'Coordinates: ${location['coordinates']?[0]}, ${location['coordinates']?[1]}',
                            style: theme.textTheme.bodySmall,
                          ),
                        ),
                        trailing: IconButton(
                          icon: Icon(Icons.delete_outline,
                              color: theme.colorScheme.error),
                          onPressed: () => _removePracticeLocation(index),
                          tooltip: 'Remove location',
                        ),
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Save Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveProviderProfile,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              elevation: 2,
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text(
                    'Save Practice Information',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
          ),
        ),
      ],
    );
  }
}
