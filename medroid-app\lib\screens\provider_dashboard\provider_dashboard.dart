import 'package:flutter/material.dart';
import 'package:medroid_app/screens/provider_dashboard/availability_calendar.dart';
import 'package:medroid_app/screens/provider_dashboard/service_management.dart';
import 'package:medroid_app/screens/provider_dashboard/practice_information.dart';
import 'package:medroid_app/screens/provider_dashboard/provider_appointments_screen.dart';

class ProviderDashboardScreen extends StatefulWidget {
  const ProviderDashboardScreen({Key? key}) : super(key: key);

  @override
  State<ProviderDashboardScreen> createState() =>
      _ProviderDashboardScreenState();
}

class _ProviderDashboardScreenState extends State<ProviderDashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const AvailabilityCalendarScreen(),
    const ServiceManagementScreen(),
    const ProviderAppointmentsScreen(),
    const PracticeInformationScreen(),
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if we have arguments to set the initial tab
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null &&
        args is Map<String, dynamic> &&
        args.containsKey('initialTab')) {
      final initialTab = args['initialTab'] as int;
      if (initialTab >= 0 && initialTab < _screens.length) {
        setState(() {
          _selectedIndex = initialTab;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Provider Dashboard'),
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Availability',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.medical_services),
            label: 'Services',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.event_note),
            label: 'Appointments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.business),
            label: 'Practice Info',
          ),
        ],
      ),
    );
  }
}
