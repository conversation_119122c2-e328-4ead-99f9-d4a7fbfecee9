import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/provider.dart';
import 'package:medroid_app/models/service.dart';
import 'package:medroid_app/screens/appointment_booking_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class ProviderDetailScreen extends StatefulWidget {
  final HealthcareProvider provider;

  const ProviderDetailScreen({Key? key, required this.provider})
      : super(key: key);

  @override
  State<ProviderDetailScreen> createState() => ProviderDetailScreenState();
}

class ProviderDetailScreenState extends State<ProviderDetailScreen> {
  bool _isLoading = true;
  List<Service> _services = [];
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final data =
          await apiService.getProviderServices(widget.provider.id.toString());

      // Check if we got any services
      if (data.isEmpty) {
        if (mounted) {
          setState(() {
            _services = [];
            _isLoading = false;
          });
        }
        return;
      }

      // Try to parse the services
      try {
        final services = data.map((item) => Service.fromJson(item)).toList();

        if (mounted) {
          setState(() {
            _services = services;
            _isLoading = false;
          });
        }
      } catch (parseError) {
        debugPrint('Error parsing services: $parseError');
        if (mounted) {
          setState(() {
            _services = [];
            _isLoading = false;
            _errorMessage = 'Error loading services. Please try again later.';
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading services: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load services. Please try again later.';
        });
      }
    }
  }

  void _navigateToBookAppointment(Service service) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AppointmentBookingScreen(
          provider: widget.provider,
          service: service,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isLargeScreen = isDesktop || isTablet;

    return Scaffold(
      backgroundColor:
          AppColors.backgroundLight, // Match Discover screen background
      appBar: AppBar(
        backgroundColor:
            AppColors.backgroundLight, // Match Discover screen background
        elevation: 0,
        title: const Text(''), // Remove the title text
        iconTheme: const IconThemeData(
          color: AppColors.tealSurge, // Teal icon for back button
        ),
      ),
      body: isLargeScreen
          // Desktop/tablet layout - two columns
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left column - Provider info and location
                Expanded(
                  flex: 4,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildProviderHeader(),
                        const SizedBox(height: 24),
                        _buildLocationSection(),
                      ],
                    ),
                  ),
                ),

                // Right column - Availability and services
                Expanded(
                  flex: 6,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildAvailabilitySection(),
                        const SizedBox(height: 24),
                        _buildServicesSection(),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ],
            )
          // Mobile layout - single column
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProviderHeader(),
                  const SizedBox(height: 16),
                  _buildAvailabilitySection(),
                  const SizedBox(height: 16),
                  _buildLocationSection(),
                  const SizedBox(height: 24),
                  _buildServicesSection(),
                  const SizedBox(height: 30), // Add extra padding at the bottom
                ],
              ),
            ),
    );
  }

  Widget _buildProviderHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile image with subtle border
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.tealSurge.withAlpha(30),
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: SizedBox(
                      width: 110,
                      height: 110,
                      child: widget.provider.profileImage.startsWith('assets/')
                          ? Image.asset(
                              widget.provider.profileImage,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[100],
                                  child: Icon(
                                    Icons.person,
                                    size: 50,
                                    color: Colors.grey[400],
                                  ),
                                );
                              },
                            )
                          : CachedNetworkImage(
                              imageUrl: widget.provider.profileImage,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey[100],
                                child: Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.grey[300],
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey[100],
                                child: Icon(
                                  Icons.person,
                                  size: 50,
                                  color: Colors.grey[400],
                                ),
                              ),
                            ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.provider.displayName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 22,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey.withAlpha(40),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          widget.provider.specialization,
                          style: TextStyle(
                            color: Colors.grey[800],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (widget.provider.verificationStatus == 'verified')
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.tealSurge.withAlpha(30),
                              width: 1,
                            ),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.verified,
                                color: AppColors.tealSurge,
                                size: 16,
                              ),
                              SizedBox(width: 6),
                              Text(
                                'Verified Provider',
                                style: TextStyle(
                                  color: AppColors.tealSurge,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            // Add a bio section
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.withAlpha(30),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.grey[700],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'About',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: Colors.grey[800],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Experienced healthcare provider dedicated to delivering quality care and personalized treatment plans for patients.',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailabilitySection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: AppColors.tealSurge,
                  size: 18,
                ),
                const SizedBox(width: 12),
                Text(
                  'Available On',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                _buildDayChip(
                    'Monday', widget.provider.isAvailableOn('Monday')),
                _buildDayChip(
                    'Tuesday', widget.provider.isAvailableOn('Tuesday')),
                _buildDayChip(
                    'Wednesday', widget.provider.isAvailableOn('Wednesday')),
                _buildDayChip(
                    'Thursday', widget.provider.isAvailableOn('Thursday')),
                _buildDayChip(
                    'Friday', widget.provider.isAvailableOn('Friday')),
                _buildDayChip(
                    'Saturday', widget.provider.isAvailableOn('Saturday')),
                _buildDayChip(
                    'Sunday', widget.provider.isAvailableOn('Sunday')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    if (widget.provider.practiceLocations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  color: AppColors.tealSurge,
                  size: 18,
                ),
                const SizedBox(width: 12),
                Text(
                  'Location',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...widget.provider.practiceLocations.map((location) {
              return Container(
                margin: const EdgeInsets.only(bottom: 10),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.withAlpha(30)),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.location_on,
                      color: AppColors.tealSurge,
                      size: 18,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        location.address,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.medical_services,
                  color: AppColors.tealSurge,
                  size: 18,
                ),
                const SizedBox(width: 12),
                Text(
                  'Services',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _isLoading
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.grey[300],
                      ),
                    ),
                  )
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red[300],
                                size: 36,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Error: $_errorMessage',
                                style: TextStyle(color: Colors.red[300]),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      )
                    : _services.isEmpty
                        ? Center(
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.grey[400],
                                    size: 36,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No services available',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontStyle: FontStyle.italic,
                                      color: Colors.grey[600],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          )
                        : ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _services.length,
                            separatorBuilder: (context, index) => Divider(
                              color: Colors.grey.withAlpha(30),
                              height: 24,
                            ),
                            itemBuilder: (context, index) {
                              final service = _services[index];
                              return _buildServiceItem(service);
                            },
                          ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem(Service service) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withAlpha(30)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service.name,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      service.description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.tealSurge.withAlpha(30),
                    width: 1,
                  ),
                ),
                child: Text(
                  service.formattedPrice,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AppColors.tealSurge,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.grey.withAlpha(40),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey[700],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      service.formattedDuration,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              ElevatedButton(
                onPressed: () => _navigateToBookAppointment(service),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.tealSurge,
                  elevation: 0,
                  side: const BorderSide(color: AppColors.tealSurge),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.calendar_today, size: 16),
                    SizedBox(width: 6),
                    Text('Book Now'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDayChip(String day, bool isAvailable) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
      decoration: BoxDecoration(
        color: isAvailable ? Colors.white : Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isAvailable
              ? AppColors.tealSurge.withAlpha(30)
              : Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Text(
        day.substring(0, 3),
        style: TextStyle(
          color: isAvailable ? AppColors.tealSurge : Colors.grey[500],
          fontSize: 13,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
