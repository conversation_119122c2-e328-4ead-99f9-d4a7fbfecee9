import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/provider.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/widgets/chat_scaffold.dart';
import 'package:medroid_app/widgets/provider_card.dart';
import 'package:medroid_app/utils/gradient_colors.dart';
import 'package:medroid_app/utils/app_colors.dart';

class ProviderMarketplaceScreen extends StatefulWidget {
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const ProviderMarketplaceScreen({Key? key, this.onCreateNewChat})
      : super(key: key);

  @override
  ProviderMarketplaceScreenState createState() =>
      ProviderMarketplaceScreenState();
}

class ProviderMarketplaceScreenState extends State<ProviderMarketplaceScreen> {
  final List<HealthcareProvider> _providers = [];
  final List<String> _specializations = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  String? _selectedSpecialization;
  double _minRating = 0.0;
  String _sortBy = 'rating';

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _hasMoreProviders = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreProviders();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Always reload data when the widget is mounted
    _loadInitialData();
    _loadSpecializations();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      // Use getAvailableProviders instead of getProviders to allow unauthenticated access
      final data = await apiService.getAvailableProviders();

      final providers =
          data.map((item) => HealthcareProvider.fromJson(item)).toList();

      setState(() {
        _providers.clear();
        _providers.addAll(providers);
        _isLoading = false;
        _hasMoreProviders = providers.length >= 10; // Assuming page size is 10
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMoreProviders() async {
    if (_isLoading || !_hasMoreProviders) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Use getAvailableProviders instead of getProviders to allow unauthenticated access
      final data = await apiService.getAvailableProviders();

      final newProviders =
          data.map((item) => HealthcareProvider.fromJson(item)).toList();

      setState(() {
        _providers.addAll(newProviders);
        _isLoading = false;
        _hasMoreProviders =
            newProviders.length >= 10; // Assuming page size is 10
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadSpecializations() async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final specializations = await apiService.getProviderSpecializations();

      setState(() {
        _specializations.clear();
        _specializations.addAll(specializations);
      });
    } catch (e) {
      debugPrint('Error loading specializations: $e');

      // If we get a session expired error, just clear the specializations list
      // This prevents the error from being displayed to the user
      if (e.toString().contains('Your session has expired')) {
        setState(() {
          _specializations.clear();
        });
      }
    }
  }

  Future<void> _refreshProviders() async {
    await _loadInitialData();
  }

  Widget _buildSpecializationChip({
    required String label,
    required bool isSelected,
    required Function(bool) onSelected,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected ? AppColors.tealSurge : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppColors.tealSurge : Colors.grey.withAlpha(30),
          width: 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: AppColors.tealSurge.withAlpha(20),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onSelected(!isSelected),
          borderRadius: BorderRadius.circular(30),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isSelected)
                  const Padding(
                    padding: EdgeInsets.only(right: 6),
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Providers',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Specialization',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  SizedBox(
                    height: 50,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: const Text('All'),
                            selected: _selectedSpecialization == null,
                            onSelected: (selected) {
                              setState(() {
                                _selectedSpecialization =
                                    selected ? null : _selectedSpecialization;
                              });
                            },
                          ),
                        ),
                        ..._specializations.map((specialization) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(specialization),
                              selected:
                                  _selectedSpecialization == specialization,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedSpecialization =
                                      selected ? specialization : null;
                                });
                              },
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Minimum Rating',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Slider(
                          value: _minRating,
                          min: 0,
                          max: 5,
                          divisions: 10,
                          label: _minRating.toString(),
                          onChanged: (value) {
                            setState(() {
                              _minRating = value;
                            });
                          },
                        ),
                      ),
                      Container(
                        width: 40,
                        alignment: Alignment.center,
                        child: Text(
                          _minRating.toStringAsFixed(1),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Sort By',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'rating',
                        groupValue: _sortBy,
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      const Text('Rating'),
                      Radio<String>(
                        value: 'proximity',
                        groupValue: _sortBy,
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      const Text('Proximity'),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Cancel'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          _selectedSpecialization = _selectedSpecialization;
                          _minRating = _minRating;
                          _sortBy = _sortBy;
                          Navigator.pop(context);
                          _refreshProviders();
                        },
                        child: const Text('Apply Filters'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return ChatScaffold(
      backgroundColor:
          AppColors.backgroundLight, // Match Discover screen background
      appBar: AppBar(
        backgroundColor:
            AppColors.backgroundLight, // Match Discover screen background
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0, // Remove default spacing
        centerTitle: false, // Ensure title is left-aligned
        title: Padding(
          padding: EdgeInsets.only(
              left: isDesktop
                  ? 32
                  : 24), // Match the padding of the content below
          child: Text(
            'Find Your Healthcare Provider',
            style: TextStyle(
              color: AppColors.tealSurge,
              fontWeight: FontWeight.bold,
              fontSize: isDesktop ? 24 : 22,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.filter_list_rounded,
              color: AppColors.tealSurge,
              size: isDesktop ? 24 : 22,
            ),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      ),
      showChatInput: true,
      hintText: 'Ask about healthcare providers...',
      onCreateNewChat: widget.onCreateNewChat,
      body: Column(
        children: [
          // Header with search bar - Minimalistic and elegant design
          Container(
            padding: EdgeInsets.fromLTRB(isDesktop ? 32 : 24,
                isDesktop ? 24 : 16, isDesktop ? 32 : 24, isDesktop ? 24 : 16),
            decoration: const BoxDecoration(
              color: AppColors.backgroundLight,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: isDesktop ? 24 : 16),
                  child: Text(
                    'Connect with trusted medical professionals near you',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: isDesktop ? 16 : 14,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.withAlpha(40),
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search by name or specialty...',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontSize: isDesktop ? 16 : 14,
                      ),
                      prefixIcon: Icon(
                        Icons.search_rounded,
                        color: GradientColors.primaryGradient[0],
                        size: isDesktop ? 22 : 20,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                          vertical: isDesktop ? 16 : 14,
                          horizontal: isDesktop ? 20 : 16),
                    ),
                    onChanged: (value) {
                      // In a real app, implement search functionality
                    },
                  ),
                ),
              ],
            ),
          ),

          // Specialization chips
          if (_specializations.isNotEmpty)
            Container(
              height: isDesktop ? 50 : 44,
              margin: EdgeInsets.only(top: isDesktop ? 12 : 8),
              padding: EdgeInsets.symmetric(horizontal: isDesktop ? 32 : 24),
              color: AppColors.backgroundLight,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildSpecializationChip(
                    label: 'All',
                    isSelected: _selectedSpecialization == null,
                    onSelected: (selected) {
                      setState(() {
                        _selectedSpecialization = null;
                        _refreshProviders();
                      });
                    },
                  ),
                  SizedBox(width: isDesktop ? 14 : 10),
                  ..._specializations.map((specialization) {
                    return Padding(
                      padding: EdgeInsets.only(right: isDesktop ? 14 : 10),
                      child: _buildSpecializationChip(
                        label: specialization,
                        isSelected: _selectedSpecialization == specialization,
                        onSelected: (selected) {
                          setState(() {
                            _selectedSpecialization =
                                selected ? specialization : null;
                            _refreshProviders();
                          });
                        },
                      ),
                    );
                  }).toList(),
                ],
              ),
            ),

          // Provider list section
          Expanded(
            child: _hasError
                ? Container(
                    color: AppColors.backgroundLight,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline_rounded,
                            size: 48,
                            color: GradientColors.primaryGradient[0],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error Loading Providers',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? Colors.white
                                  : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _errorMessage,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(0xFFEC4899),
                                width: 1,
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: _refreshProviders,
                                borderRadius: BorderRadius.circular(12),
                                child: const Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.refresh_rounded,
                                        color: Color(0xFFEC4899),
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Try Again',
                                        style: TextStyle(
                                          color: Color(0xFFEC4899),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : Container(
                    color: AppColors.backgroundLight,
                    child: RefreshIndicator(
                      onRefresh: _refreshProviders,
                      color: GradientColors.primaryGradient[0],
                      child: _providers.isEmpty && _isLoading
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      GradientColors.primaryGradient[0],
                                    ),
                                  ),
                                  SizedBox(height: isDesktop ? 24 : 16),
                                  Text(
                                    'Loading providers...',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: isDesktop ? 16 : 14,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : _providers.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.search_off_rounded,
                                        size: isDesktop ? 64 : 48,
                                        color: Colors.grey[400],
                                      ),
                                      SizedBox(height: isDesktop ? 24 : 16),
                                      Text(
                                        'No providers found',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isDesktop ? 22 : 18,
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.white
                                              : Colors.black87,
                                        ),
                                      ),
                                      SizedBox(height: isDesktop ? 12 : 8),
                                      Text(
                                        'Try adjusting your filters or search terms',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: isDesktop ? 16 : 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Container(
                                  color: AppColors.backgroundLight,
                                  child: isDesktop
                                      // Grid view for desktop
                                      ? GridView.builder(
                                          controller: _scrollController,
                                          padding: const EdgeInsets.only(
                                              top: 16,
                                              bottom: 100,
                                              left: 24,
                                              right: 24),
                                          physics:
                                              const BouncingScrollPhysics(),
                                          gridDelegate:
                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 2,
                                            childAspectRatio: 2.2,
                                            crossAxisSpacing: 16,
                                            mainAxisSpacing: 16,
                                          ),
                                          itemCount: _providers.length +
                                              (_isLoading ? 1 : 0),
                                          itemBuilder: (context, index) {
                                            if (index == _providers.length) {
                                              return Center(
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                      16.0),
                                                  child:
                                                      CircularProgressIndicator(
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                            Color>(
                                                      GradientColors
                                                          .primaryGradient[0],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }

                                            final provider = _providers[index];
                                            return ProviderCard(
                                                provider: provider);
                                          },
                                        )
                                      // List view for mobile
                                      : ListView.builder(
                                          controller: _scrollController,
                                          padding: const EdgeInsets.only(
                                              top: 8, bottom: 100),
                                          physics:
                                              const BouncingScrollPhysics(),
                                          itemCount: _providers.length +
                                              (_isLoading ? 1 : 0),
                                          itemBuilder: (context, index) {
                                            if (index == _providers.length) {
                                              return Center(
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                      16.0),
                                                  child:
                                                      CircularProgressIndicator(
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                            Color>(
                                                      GradientColors
                                                          .primaryGradient[0],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }

                                            final provider = _providers[index];
                                            return ProviderCard(
                                                provider: provider);
                                          },
                                        ),
                                ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
