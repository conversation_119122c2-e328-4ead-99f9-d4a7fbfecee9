import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/widgets/responsive_centered_container.dart';
import 'package:medroid_app/screens/main_navigation.dart';
import 'package:share_plus/share_plus.dart';
import 'package:qr_flutter/qr_flutter.dart';

class ReferralScreen extends StatefulWidget {
  const ReferralScreen({Key? key}) : super(key: key);

  @override
  State<ReferralScreen> createState() => ReferralScreenState();
}

class ReferralScreenState extends State<ReferralScreen> {
  final _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = true;
  bool _isSendingInvite = false;
  String? _referralCode;
  String? _referralUrl;
  List<dynamic> _referrals = [];
  double _creditBalance = 0.0;

  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Don't call methods that use context here
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This is called after initState and is safe to use context
    if (!_isInitialized) {
      _isInitialized = true;
      // Force a refresh of the authentication state and load data
      _checkAuthAndLoadData();
    }
  }

  @override
  void didUpdateWidget(ReferralScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh data when widget is updated (e.g., after navigation)
    if (!_isLoading) {
      _checkAuthAndLoadData();
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  // Check authentication first, then load data if authenticated
  Future<void> _checkAuthAndLoadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);

      // Use the asynchronous method to get the current authentication state
      final isAuthenticated = await authService.isLoggedIn();

      // Check if user is authenticated
      if (!isAuthenticated) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        // Schedule the SnackBar to show after the current frame
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('Please log in to access referral features')),
            );

            // Navigate to the main screen with the login tab selected
            // This is safer than trying to use named routes
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const MainNavigation(initialTabIndex: 4),
              ),
              (route) => false, // Remove all previous routes
            );
          }
        });
        return;
      }

      // Force refresh the auth token to ensure it's valid
      await authService.getToken();

      // User is authenticated, load referral data
      _loadReferralData();
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Schedule the SnackBar to show after the current frame
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Authentication error: $e')),
          );
        }
      });
    }
  }

  Future<void> _loadReferralData() async {
    if (!mounted) return;

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      try {
        // Get referral code
        final codeResponse = await apiService.get('/referrals/code');

        // Get user referrals
        final referralsResponse = await apiService.get('/referrals/my');

        // Get credit balance
        final creditResponse = await apiService.get('/credits/balance');

        if (mounted) {
          setState(() {
            _referralCode = codeResponse['referral_code'];
            _referralUrl = codeResponse['referral_url'];
            _referrals = referralsResponse['referrals'] ?? [];

            // Handle credit balance which might be returned as a string
            var balance = creditResponse['balance'];
            if (balance is String) {
              _creditBalance = double.tryParse(balance) ?? 0.0;
            } else if (balance is num) {
              _creditBalance = balance.toDouble();
            } else {
              _creditBalance = 0.0;
            }

            debugPrint(
                'Credit balance: $balance (${balance.runtimeType}), converted to: $_creditBalance');

            _isLoading = false;
          });
        }
      } catch (apiError) {
        // Handle API errors specifically
        debugPrint('API Error: $apiError');

        // Check if it's an authentication error
        if (apiError.toString().contains('401') ||
            apiError.toString().contains('Unauthorized') ||
            apiError.toString().contains('Unauthenticated')) {
          // Only proceed if widget is still mounted
          if (!mounted) return;

          // Get auth service again to avoid using it across async gap
          final authService = RepositoryProvider.of<AuthService>(context);
          // Clear auth token and redirect to login
          await authService.logout();

          // Schedule the SnackBar to show after the current frame
          SchedulerBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                    content:
                        Text('Your session has expired. Please log in again.')),
              );

              // Navigate to the main screen with the login tab selected
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) =>
                      const MainNavigation(initialTabIndex: 4),
                ),
                (route) => false, // Remove all previous routes
              );
            }
          });
        } else {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });

            // Schedule the SnackBar to show after the current frame
            SchedulerBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text('Error loading referral data: $apiError')),
                );
              }
            });
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Schedule the SnackBar to show after the current frame
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error loading referral data: $e')),
            );
          }
        });
      }
    }
  }

  Future<void> _sendInvite() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSendingInvite = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      await apiService.post('/referrals/invite', {
        'email': _emailController.text,
      });

      _emailController.clear();

      if (mounted) {
        // Schedule the SnackBar to show after the current frame
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Invitation sent successfully!')),
            );
          }
        });

        // Refresh referrals list
        _loadReferralData();
      }
    } catch (e) {
      if (mounted) {
        // Schedule the SnackBar to show after the current frame
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error sending invitation: $e')),
            );
          }
        });
      }
    } finally {
      setState(() {
        _isSendingInvite = false;
      });
    }
  }

  void _shareReferralCode() async {
    if (_referralUrl != null) {
      try {
        // First try to use the Share API
        await Share.share(
          'Join me on Medroid! Use my referral code $_referralCode to get \$3 credit. $_referralUrl',
          subject: 'Join Medroid Health App',
        );
      } catch (e) {
        debugPrint('Share error: $e');

        // If sharing fails, fallback to clipboard
        await Clipboard.setData(ClipboardData(
          text:
              'Join me on Medroid! Use my referral code $_referralCode to get \$3 credit. $_referralUrl',
        ));

        // Show a snackbar to inform the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Sharing failed. Referral link copied to clipboard instead.'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  void _copyReferralCode() async {
    if (_referralCode != null) {
      try {
        // Copy both the code and URL for better user experience
        final textToCopy = _referralUrl != null
            ? 'Referral code: $_referralCode\nReferral link: $_referralUrl'
            : _referralCode!;

        await Clipboard.setData(ClipboardData(text: textToCopy));

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Referral information copied to clipboard'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        debugPrint('Copy error: $e');

        // Show error message if copying fails
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to copy to clipboard. Please try again.'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  // Desktop layout with two-column design for better space utilization
  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32.0),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1200),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Credit Balance Card - Premium Design
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF0D9488), Color(0xFF0F766E)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x330D9488), // 20% opacity teal
                      blurRadius: 16,
                      spreadRadius: 1,
                      offset: Offset(0, 6),
                    ),
                  ],
                  border: Border.all(
                    color: Colors.white.withAlpha(25), // 10% opacity white
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Your Credit Balance',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '\$',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _creditBalance.toStringAsFixed(2),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                                letterSpacing: -0.5,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(38), // 15% opacity white
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.card_giftcard,
                            color: Colors.white,
                            size: 18,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Earn \$3 per referral',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Two-column layout for desktop
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left column - Referral code and QR code
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Referral Code Section - Premium Design
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF0F9F8),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFF0D9488)
                                      .withAlpha(51), // 20% opacity
                                ),
                              ),
                              child: const Icon(
                                Icons.qr_code_rounded,
                                color: Color(0xFF0D9488),
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Your Referral Code',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // QR Code - Premium Design
                        if (_referralUrl != null)
                          Container(
                            padding: const EdgeInsets.all(32),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: const [
                                BoxShadow(
                                  color: Color(0x1A000000), // 10% opacity black
                                  blurRadius: 20,
                                  spreadRadius: 0,
                                  offset: Offset(0, 6),
                                ),
                              ],
                              border: Border.all(
                                color:
                                    const Color(0xFFE6F7F5), // Very light teal
                                width: 2,
                              ),
                            ),
                            child: Column(
                              children: [
                                // QR Code with embedded logo
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: const Color(0xFFE6F7F5),
                                      width: 1,
                                    ),
                                  ),
                                  child: QrImageView(
                                    data: _referralUrl!,
                                    version: QrVersions.auto,
                                    size: 250, // Larger for desktop
                                    backgroundColor: Colors.white,
                                    eyeStyle: const QrEyeStyle(
                                      eyeShape: QrEyeShape.square,
                                      color: Color(0xFF0D9488),
                                    ),
                                    dataModuleStyle: const QrDataModuleStyle(
                                      dataModuleShape: QrDataModuleShape.square,
                                      color: Color(0xFF0D9488),
                                    ),
                                    embeddedImage: const AssetImage(
                                        'assets/images/medroid_icon.png'),
                                    embeddedImageStyle:
                                        const QrEmbeddedImageStyle(
                                      size: Size(50, 50),
                                    ),
                                    errorStateBuilder: (context, error) {
                                      return const Center(
                                        child: Text(
                                          'Something went wrong!',
                                          style: TextStyle(color: Colors.red),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(height: 20),
                                // Scan instruction with icon
                                const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.qr_code_scanner,
                                      color: Color(0xFF0D9488),
                                      size: 18,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Scan to join Medroid',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Color(0xFF0D9488),
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 0.3,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 24),

                                // Referral code display
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF0F9F8),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: const Color(0xFFE6F7F5),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Your Referral Code:',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF666666),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            _referralCode ?? '',
                                            style: const TextStyle(
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                              letterSpacing: 1,
                                              color: Color(0xFF0D9488),
                                              fontFamily: 'monospace',
                                            ),
                                          ),
                                          IconButton(
                                            onPressed: _copyReferralCode,
                                            icon: const Icon(
                                              Icons.copy,
                                              color: Color(0xFF0D9488),
                                              size: 20,
                                            ),
                                            tooltip: 'Copy referral code',
                                            style: IconButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFFE6F7F5),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 24),

                                // Share buttons
                                Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton.icon(
                                        onPressed: _shareReferralCode,
                                        icon: const Icon(Icons.share, size: 18),
                                        label: const Text('Share'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              const Color(0xFF0D9488),
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: OutlinedButton.icon(
                                        onPressed: _copyReferralCode,
                                        icon: const Icon(Icons.copy, size: 18),
                                        label: const Text('Copy Link'),
                                        style: OutlinedButton.styleFrom(
                                          foregroundColor:
                                              const Color(0xFF0D9488),
                                          side: const BorderSide(
                                              color: Color(0xFF0D9488)),
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 32),

                  // Right column - Invite form and referral history
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Invite Form - Premium Design
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFDF2F8), // Light pink
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFFEC4899)
                                      .withAlpha(51), // 20% opacity pink
                                ),
                              ),
                              child: const Icon(
                                Icons.email_rounded,
                                color: Color(0xFFEC4899), // Pink
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Invite a Friend',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x0A000000), // 4% opacity black
                                blurRadius: 16,
                                spreadRadius: 0,
                                offset: Offset(0, 4),
                              ),
                            ],
                            border: Border.all(
                              color: const Color(0xFFFBE4F0), // Very light pink
                              width: 1,
                            ),
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Send an invitation email',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                TextFormField(
                                  controller: _emailController,
                                  decoration: InputDecoration(
                                    labelText: 'Friend\'s Email',
                                    hintText:
                                        'Enter your friend\'s email address',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: const BorderSide(
                                        color: Color(0xFFE0E0E0),
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: const BorderSide(
                                        color: Color(0xFFEC4899),
                                      ),
                                    ),
                                    prefixIcon: const Icon(
                                      Icons.email_outlined,
                                      color: Color(0xFF666666),
                                    ),
                                  ),
                                  keyboardType: TextInputType.emailAddress,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter an email address';
                                    }
                                    if (!RegExp(
                                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                        .hasMatch(value)) {
                                      return 'Please enter a valid email address';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 24),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    onPressed:
                                        _isSendingInvite ? null : _sendInvite,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFFEC4899),
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      disabledBackgroundColor:
                                          const Color(0xFFEC4899)
                                              .withAlpha(128),
                                    ),
                                    child: _isSendingInvite
                                        ? const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.white),
                                            ),
                                          )
                                        : const Text(
                                            'Send Invitation',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Referral History - Premium Design
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF0F9F8),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFF0D9488)
                                      .withAlpha(51), // 20% opacity
                                ),
                              ),
                              child: const Icon(
                                Icons.people_alt_outlined,
                                color: Color(0xFF0D9488),
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Your Referrals',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x0A000000), // 4% opacity black
                                blurRadius: 16,
                                spreadRadius: 0,
                                offset: Offset(0, 4),
                              ),
                            ],
                            border: Border.all(
                              color: const Color(0xFFE6F7F5), // Very light teal
                              width: 1,
                            ),
                          ),
                          child: _referrals.isEmpty
                              ? Center(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 40, horizontal: 20),
                                    child: Column(
                                      children: [
                                        Container(
                                          width: 80,
                                          height: 80,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFF0F9F8),
                                            borderRadius:
                                                BorderRadius.circular(40),
                                          ),
                                          child: const Icon(
                                            Icons.share_rounded,
                                            color: Color(0xFF0D9488),
                                            size: 36,
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        const Text(
                                          'No Referrals Yet',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xFF333333),
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        const Text(
                                          'Share your referral code with friends to start earning credits!',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: Color(0xFF666666),
                                            fontSize: 16,
                                            height: 1.5,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: _referrals.length,
                                  separatorBuilder: (context, index) =>
                                      const Divider(height: 1),
                                  itemBuilder: (context, index) {
                                    final referral = _referrals[index];
                                    final isCompleted =
                                        referral['status'] == 'completed';
                                    final referredName =
                                        referral['referred'] != null
                                            ? referral['referred']['name']
                                            : (referral['email'] ?? 'Someone');

                                    // Format date
                                    final createdAt = referral['created_at'] !=
                                            null
                                        ? DateTime.parse(referral['created_at'])
                                        : null;
                                    final formattedDate = createdAt != null
                                        ? '${createdAt.day}/${createdAt.month}/${createdAt.year}'
                                        : 'Unknown date';

                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12, horizontal: 8),
                                      child: Row(
                                        children: [
                                          // Status indicator
                                          Container(
                                            width: 40,
                                            height: 40,
                                            decoration: BoxDecoration(
                                              color: isCompleted
                                                  ? const Color(0xFFF0F9F8)
                                                  : const Color(0xFFF5F5F5),
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: isCompleted
                                                    ? const Color(0xFF0D9488)
                                                        .withAlpha(
                                                            51) // 20% opacity
                                                    : const Color(0xFFE0E0E0),
                                                width: 1,
                                              ),
                                            ),
                                            child: Icon(
                                              isCompleted
                                                  ? Icons.check_circle
                                                  : Icons.hourglass_empty,
                                              color: isCompleted
                                                  ? const Color(0xFF0D9488)
                                                  : const Color(0xFF9E9E9E),
                                              size: 20,
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          // Referral details
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  referredName,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 16,
                                                    color: Color(0xFF333333),
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  referral['email'] ??
                                                      'No email',
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Color(0xFF666666),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          // Status and date
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: isCompleted
                                                      ? const Color(0xFFE6F7F5)
                                                      : const Color(0xFFF5F5F5),
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                                child: Text(
                                                  isCompleted
                                                      ? 'Completed'
                                                      : 'Pending',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                    color: isCompleted
                                                        ? const Color(
                                                            0xFF0D9488)
                                                        : const Color(
                                                            0xFF9E9E9E),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                formattedDate,
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFF9E9E9E),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a desktop-sized screen
    final isDesktop = MediaQuery.of(context).size.width > 1024;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Referrals & Rewards',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            letterSpacing: 0.3,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        scrolledUnderElevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D9488)),
              ),
            )
          : isDesktop
              ? _buildDesktopLayout()
              : ResponsiveCenteredContainer(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Credit Balance Card - Premium Design
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(24.0),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF0D9488), Color(0xFF0F766E)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(16.0),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x330D9488), // 20% opacity teal
                                blurRadius: 16,
                                spreadRadius: 1,
                                offset: Offset(0, 6),
                              ),
                            ],
                            border: Border.all(
                              color: Colors.white.withOpacity(0.1),
                              width: 0.5,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Credit balance header with icon
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Icon(
                                      Icons.account_balance_wallet_outlined,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Your Credit Balance',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.3,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              // Credit amount
                              Text(
                                '\$${_creditBalance.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 40,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                              const SizedBox(height: 12),
                              // Referral incentive text
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.15),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.card_giftcard,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Earn \$3 for each friend who joins',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Referral Code Section - Premium Design
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF0F9F8),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color:
                                      const Color(0xFF0D9488).withOpacity(0.2),
                                ),
                              ),
                              child: const Icon(
                                Icons.qr_code_rounded,
                                color: Color(0xFF0D9488),
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Your Referral Code',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // QR Code - Premium Design
                        if (_referralUrl != null)
                          Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: const [
                                BoxShadow(
                                  color: Color(0x1A000000), // 10% opacity black
                                  blurRadius: 20,
                                  spreadRadius: 0,
                                  offset: Offset(0, 6),
                                ),
                              ],
                              border: Border.all(
                                color:
                                    const Color(0xFFE6F7F5), // Very light teal
                                width: 2,
                              ),
                            ),
                            child: Column(
                              children: [
                                // QR Code with embedded logo
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: const Color(0xFFE6F7F5),
                                      width: 1,
                                    ),
                                  ),
                                  child: QrImageView(
                                    data: _referralUrl!,
                                    version: QrVersions.auto,
                                    size: 200,
                                    backgroundColor: Colors.white,
                                    // Use QR style instead of deprecated foregroundColor
                                    eyeStyle: const QrEyeStyle(
                                      eyeShape: QrEyeShape.square,
                                      color: Color(0xFF0D9488),
                                    ),
                                    dataModuleStyle: const QrDataModuleStyle(
                                      dataModuleShape: QrDataModuleShape.square,
                                      color: Color(0xFF0D9488),
                                    ),
                                    embeddedImage: const AssetImage(
                                        'assets/images/medroid_icon.png'),
                                    embeddedImageStyle:
                                        const QrEmbeddedImageStyle(
                                      size: Size(40, 40),
                                    ),
                                    errorStateBuilder: (context, error) {
                                      return const Center(
                                        child: Text(
                                          'Something went wrong!',
                                          style: TextStyle(color: Colors.red),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(height: 20),
                                // Scan instruction with icon
                                const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.qr_code_scanner,
                                      color: Color(0xFF0D9488),
                                      size: 18,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Scan to join Medroid',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Color(0xFF0D9488),
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 0.3,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                        const SizedBox(height: 20),

                        // Referral Code Display - Premium Design
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x0A000000), // 4% opacity black
                                blurRadius: 10,
                                spreadRadius: 0,
                                offset: Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color: const Color(0xFFE6F7F5),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.code,
                                color: Color(0xFF0D9488),
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _referralCode ?? '',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 1.5,
                                    color: Color(0xFF333333),
                                  ),
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF0F9F8),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.copy_rounded,
                                    color: Color(0xFF0D9488),
                                    size: 20,
                                  ),
                                  onPressed: _copyReferralCode,
                                  tooltip: 'Copy code',
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Share Button - Premium Design
                        Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF0D9488), Color(0xFF0F766E)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x330D9488), // 20% opacity teal
                                blurRadius: 12,
                                spreadRadius: 0,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _shareReferralCode,
                              borderRadius: BorderRadius.circular(16),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.share_rounded,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Share Your Referral Code',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.3,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 40),

                        // Invite Form - Premium Design
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFDF2F8), // Light pink
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFFEC4899)
                                      .withOpacity(0.2), // Pink with opacity
                                ),
                              ),
                              child: const Icon(
                                Icons.email_rounded,
                                color: Color(0xFFEC4899), // Pink
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Invite a Friend',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x0A000000), // 4% opacity black
                                blurRadius: 16,
                                spreadRadius: 0,
                                offset: Offset(0, 4),
                              ),
                            ],
                            border: Border.all(
                              color: const Color(0xFFFBE4F0), // Very light pink
                              width: 1,
                            ),
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Send an invitation email',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                TextFormField(
                                  controller: _emailController,
                                  decoration: InputDecoration(
                                    labelText: 'Friend\'s Email',
                                    hintText: 'Enter your friend\'s email',
                                    labelStyle: const TextStyle(
                                      color: Color(0xFF666666),
                                      fontWeight: FontWeight.w500,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: Color(0xFFE6E6E6),
                                        width: 1,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: Color(0xFFE6E6E6),
                                        width: 1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: Color(0xFFEC4899),
                                        width: 2,
                                      ),
                                    ),
                                    prefixIcon: const Icon(
                                      Icons.email_outlined,
                                      color: Color(0xFF666666),
                                    ),
                                    filled: true,
                                    fillColor: const Color(0xFFFAFAFA),
                                  ),
                                  keyboardType: TextInputType.emailAddress,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter an email';
                                    }
                                    if (!RegExp(
                                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                        .hasMatch(value)) {
                                      return 'Please enter a valid email';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 20),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFFEC4899),
                                        Color(0xFFDB2777)
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: const [
                                      BoxShadow(
                                        color: Color(
                                            0x33EC4899), // 20% opacity pink
                                        blurRadius: 8,
                                        spreadRadius: 0,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap:
                                          _isSendingInvite ? null : _sendInvite,
                                      borderRadius: BorderRadius.circular(12),
                                      child: Center(
                                        child: _isSendingInvite
                                            ? const SizedBox(
                                                height: 24,
                                                width: 24,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  color: Colors.white,
                                                ),
                                              )
                                            : const Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.send_rounded,
                                                    color: Colors.white,
                                                    size: 18,
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text(
                                                    'Send Invitation',
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      letterSpacing: 0.3,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Referral History - Premium Design
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF0F9F8),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color:
                                      const Color(0xFF0D9488).withOpacity(0.2),
                                ),
                              ),
                              child: const Icon(
                                Icons.people_alt_outlined,
                                color: Color(0xFF0D9488),
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Your Referrals',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        _referrals.isEmpty
                            ? Container(
                                padding: const EdgeInsets.all(24),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: const [
                                    BoxShadow(
                                      color:
                                          Color(0x0A000000), // 4% opacity black
                                      blurRadius: 16,
                                      spreadRadius: 0,
                                      offset: Offset(0, 4),
                                    ),
                                  ],
                                  border: Border.all(
                                    color: const Color(
                                        0xFFE6F7F5), // Very light teal
                                    width: 1,
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFF0F9F8),
                                        borderRadius: BorderRadius.circular(40),
                                      ),
                                      child: const Icon(
                                        Icons.share_rounded,
                                        color: Color(0xFF0D9488),
                                        size: 36,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'No Referrals Yet',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF333333),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    const Text(
                                      'Share your referral code with friends to start earning credits!',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xFF666666),
                                        fontSize: 16,
                                        height: 1.5,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: const [
                                    BoxShadow(
                                      color:
                                          Color(0x0A000000), // 4% opacity black
                                      blurRadius: 16,
                                      spreadRadius: 0,
                                      offset: Offset(0, 4),
                                    ),
                                  ],
                                  border: Border.all(
                                    color: const Color(
                                        0xFFE6F7F5), // Very light teal
                                    width: 1,
                                  ),
                                ),
                                child: ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: _referrals.length,
                                  separatorBuilder: (context, index) =>
                                      const Divider(height: 1),
                                  itemBuilder: (context, index) {
                                    final referral = _referrals[index];
                                    final isCompleted =
                                        referral['status'] == 'completed';
                                    final referredName =
                                        referral['referred'] != null
                                            ? referral['referred']['name']
                                            : (referral['email'] ?? 'Someone');

                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 48,
                                            height: 48,
                                            decoration: BoxDecoration(
                                              color: isCompleted
                                                  ? const Color(0xFFF0F9F8)
                                                  : const Color(0xFFF5F5F5),
                                              borderRadius:
                                                  BorderRadius.circular(24),
                                              border: Border.all(
                                                color: isCompleted
                                                    ? const Color(0xFF0D9488)
                                                        .withOpacity(0.2)
                                                    : const Color(0xFFE0E0E0),
                                                width: 1,
                                              ),
                                            ),
                                            child: Center(
                                              child: Icon(
                                                isCompleted
                                                    ? Icons.check_circle
                                                    : Icons.pending,
                                                color: isCompleted
                                                    ? const Color(0xFF0D9488)
                                                    : const Color(0xFF9E9E9E),
                                                size: 24,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  referredName,
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w600,
                                                    color: Color(0xFF333333),
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Row(
                                                  children: [
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 8,
                                                        vertical: 2,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: isCompleted
                                                            ? const Color(
                                                                0xFFF0F9F8)
                                                            : const Color(
                                                                0xFFF5F5F5),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                      child: Text(
                                                        isCompleted
                                                            ? 'Completed'
                                                            : 'Pending',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color: isCompleted
                                                              ? const Color(
                                                                  0xFF0D9488)
                                                              : const Color(
                                                                  0xFF9E9E9E),
                                                        ),
                                                      ),
                                                    ),
                                                    if (isCompleted) ...[
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        '\$${referral['credit_amount']} earned',
                                                        style: const TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              Color(0xFF0D9488),
                                                        ),
                                                      ),
                                                    ],
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          Text(
                                            _formatDate(referral['created_at']),
                                            style: const TextStyle(
                                              color: Color(0xFF9E9E9E),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                      ],
                    ),
                  ),
                ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return '';
    }
  }
}
