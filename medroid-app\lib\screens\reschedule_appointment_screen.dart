import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/models/service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:medroid_app/utils/gradient_colors.dart';

class RescheduleAppointmentScreen extends StatefulWidget {
  final Appointment appointment;

  const RescheduleAppointmentScreen({
    Key? key,
    required this.appointment,
  }) : super(key: key);

  @override
  State<RescheduleAppointmentScreen> createState() =>
      _RescheduleAppointmentScreenState();
}

class _RescheduleAppointmentScreenState
    extends State<RescheduleAppointmentScreen> {
  bool _isLoading = false;
  bool _isSubmitting = false;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  Map<String, List<TimeSlot>> _availableSlots = {};
  TimeSlot? _selectedTimeSlot;
  Map<String, dynamic>? _provider;
  Service? _service;

  @override
  void initState() {
    super.initState();
    _selectedDay = widget.appointment.date;
    _focusedDay = widget.appointment.date;
    _loadProviderDetails();
  }

  Future<void> _loadProviderDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Get provider details
      final providerData = await apiService.getProviderDetails(
        widget.appointment.providerId.toString(),
      );

      _provider = providerData;

      // Get service details if available
      if (widget.appointment.serviceId != null) {
        final services = await apiService.getProviderServices(
          widget.appointment.providerId.toString(),
        );

        for (final serviceData in services) {
          if (serviceData['id'].toString() ==
              widget.appointment.serviceId.toString()) {
            _service = Service.fromJson(serviceData);
            break;
          }
        }
      }

      // Load available slots for the selected day
      await _loadAvailableSlots();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading provider details: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading provider details: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _loadAvailableSlots() async {
    if (_provider == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Format dates for API request
      final startDate = DateFormat('yyyy-MM-dd').format(_selectedDay);
      final endDate = DateFormat('yyyy-MM-dd').format(_selectedDay);

      final data = await apiService.getAvailableTimeSlots(
        providerId: _provider!['id'].toString(),
        startDate: startDate,
        endDate: endDate,
        serviceId: _service?.id,
      );

      // Parse available slots from API response
      final Map<String, List<TimeSlot>> slots = {};

      if (data != null && data['available_slots'] != null) {
        final availableSlotsData =
            data['available_slots'] as Map<String, dynamic>;

        availableSlotsData.forEach((date, slotsData) {
          final List<TimeSlot> dateSlots = [];

          for (final slotData in slotsData) {
            dateSlots.add(TimeSlot(
              startTime: slotData['start_time'],
              endTime: slotData['end_time'],
            ));
          }

          slots[date] = dateSlots;
        });
      }

      if (mounted) {
        setState(() {
          _availableSlots = slots;
          _selectedTimeSlot = null;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading available slots: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading available slots: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _rescheduleAppointment() async {
    if (_selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a time slot'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      await apiService.updateAppointment(
        appointmentId: widget.appointment.id.toString(),
        date: DateFormat('yyyy-MM-dd').format(_selectedDay),
        timeSlot: {
          'start_time': _selectedTimeSlot!.startTime,
          'end_time': _selectedTimeSlot!.endTime,
        },
      );

      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment rescheduled successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      debugPrint('Error rescheduling appointment: $e');
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rescheduling appointment: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = GradientColors.primaryGradient[0];

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Reschedule Appointment',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: themeColor,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(themeColor),
              ),
            )
          : _provider == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Provider information not available',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : SafeArea(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildProviderInfo(),
                        const SizedBox(height: 20),
                        _buildCalendar(),
                        const SizedBox(height: 20),
                        _buildTimeSlots(),
                        const SizedBox(height: 24),
                        _buildSubmitButton(),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildProviderInfo() {
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _provider!['user'] != null
                ? _provider!['user']['name']
                : 'Provider',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: themeColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _provider!['specialization'] ?? 'Specialist',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: themeColor,
              ),
            ),
          ),
          if (_service != null) ...[
            const SizedBox(height: 20),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.medical_services_outlined,
                        size: 18,
                        color: Colors.black.withOpacity(0.7),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _service!.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.timer_outlined,
                              size: 16,
                              color: Colors.black.withOpacity(0.6),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              '${_service!.duration} minutes',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.black.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.attach_money,
                              size: 16,
                              color: Colors.black.withOpacity(0.6),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              '\$${_service!.price.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCalendar() {
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 8, bottom: 16),
            child: Text(
              'Select New Date',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          TableCalendar(
            firstDay: DateTime.now(),
            lastDay: DateTime.now().add(const Duration(days: 90)),
            focusedDay: _focusedDay,
            selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
            calendarFormat: CalendarFormat.month,
            availableCalendarFormats: const {CalendarFormat.month: 'Month'},
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              leftChevronIcon: Icon(
                Icons.chevron_left,
                color: themeColor,
                size: 28,
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right,
                color: themeColor,
                size: 28,
              ),
            ),
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay;
              });
              _loadAvailableSlots();
            },
            calendarStyle: CalendarStyle(
              selectedDecoration: BoxDecoration(
                color: themeColor,
                shape: BoxShape.circle,
              ),
              todayDecoration: BoxDecoration(
                color: themeColor.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              weekendTextStyle: const TextStyle(color: Colors.red),
              outsideDaysVisible: false,
              cellMargin: const EdgeInsets.all(6),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black.withOpacity(0.7),
              ),
              weekendStyle: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.red.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlots() {
    final dateKey = DateFormat('yyyy-MM-dd').format(_selectedDay);
    final slots = _availableSlots[dateKey] ?? [];
    final themeColor = GradientColors.primaryGradient[0];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 20,
                color: themeColor,
              ),
              const SizedBox(width: 8),
              const Text(
                'Available Time Slots',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          slots.isEmpty
              ? Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.event_busy,
                        color: Colors.grey.shade400,
                        size: 40,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'No available time slots for this day',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : Wrap(
                  spacing: 10,
                  runSpacing: 12,
                  children: slots.map((slot) {
                    final isSelected = _selectedTimeSlot == slot;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedTimeSlot = isSelected ? null : slot;
                        });
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected ? themeColor : Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                isSelected ? themeColor : Colors.grey.shade300,
                            width: 1.5,
                          ),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: themeColor.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                              : null,
                        ),
                        child: Text(
                          '${slot.startTime} - ${slot.endTime}',
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    final themeColor = GradientColors.primaryGradient[0];
    final isButtonEnabled = !_isSubmitting && _selectedTimeSlot != null;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 8),
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: isButtonEnabled ? GradientColors.getPrimaryGradient() : null,
        color: isButtonEnabled ? null : Colors.grey.shade300,
        boxShadow: isButtonEnabled
            ? [
                BoxShadow(
                  color: themeColor.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isButtonEnabled ? _rescheduleAppointment : null,
          borderRadius: BorderRadius.circular(16),
          child: Center(
            child: _isSubmitting
                ? const SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_month,
                        color: Colors.white,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Reschedule Appointment',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}

class TimeSlot {
  final String startTime;
  final String endTime;

  TimeSlot({
    required this.startTime,
    required this.endTime,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeSlot &&
          runtimeType == other.runtimeType &&
          startTime == other.startTime &&
          endTime == other.endTime;

  @override
  int get hashCode => startTime.hashCode ^ endTime.hashCode;
}
