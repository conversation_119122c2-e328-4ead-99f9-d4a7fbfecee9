import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:medroid_app/services/sso_service.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/gradient_button.dart';
import 'package:medroid_app/widgets/app_logo.dart';
import 'package:intl/intl.dart';

class SSOAdditionalDetailsScreen extends StatefulWidget {
  final String tempToken;
  final Map<String, dynamic> userData;

  const SSOAdditionalDetailsScreen({
    Key? key,
    required this.tempToken,
    required this.userData,
  }) : super(key: key);

  @override
  SSOAdditionalDetailsScreenState createState() =>
      SSOAdditionalDetailsScreenState();
}

class SSOAdditionalDetailsScreenState extends State<SSOAdditionalDetailsScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _dobController = TextEditingController();
  final _referralCodeController = TextEditingController();

  final SSOService _ssoService = SSOService();
  final AuthService _authService = AuthService();
  final ApiService _apiService = ApiService();

  String? _selectedGender;
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _dobController.dispose();
    _referralCodeController.dispose();
    super.dispose();
  }

  Future<void> _selectDateOfBirth() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.coralPop,
                  onPrimary: Colors.white,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dobController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<void> _completeRegistration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _ssoService.completeRegistration(
        tempToken: widget.tempToken,
        gender: _selectedGender,
        dateOfBirth:
            _dobController.text.isNotEmpty ? _dobController.text : null,
        referralCode: _referralCodeController.text.isNotEmpty
            ? _referralCodeController.text
            : null,
      );

      if (result['success'] && mounted) {
        // Save the token and user data
        await _apiService.setAuthToken(result['token']);
        await _authService.saveUserData(result['user']);

        // Navigate to main app
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/');
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['error'] ?? 'Failed to complete registration'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate responsive padding and constraints
    final padding = isDesktop
        ? 32.0
        : isTablet
            ? 28.0
            : 24.0;

    final maxWidth = isDesktop
        ? 500.0
        : isTablet
            ? (screenWidth * 0.8).clamp(400.0, 600.0)
            : double.infinity;

    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(padding),
            child: Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: maxWidth,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: isDesktop ? 40 : 60),

                    // Logo
                    _buildHeader(context, isDarkMode, isDesktop ? 100 : 120),

                    SizedBox(height: isDesktop ? 32 : 40),

                    // Welcome message
                    _buildWelcomeMessage(isDarkMode),

                    SizedBox(height: isDesktop ? 24 : 32),

                    // Form
                    _buildForm(isDarkMode, isDesktop),

                    SizedBox(height: isDesktop ? 24 : 32),

                    // Complete button
                    _buildCompleteButton(isDesktop),

                    SizedBox(height: isDesktop ? 16 : 24),

                    // Skip button
                    _buildSkipButton(isDarkMode),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDarkMode, double logoSize) {
    return Column(
      children: [
        Image.asset(
          'assets/images/medroid_icon.png',
          width: logoSize,
          height: logoSize,
          errorBuilder: (context, error, stackTrace) {
            return AppLogo(
              size: logoSize,
              animate: true,
              showShadow: true,
            );
          },
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildWelcomeMessage(bool isDarkMode) {
    return Column(
      children: [
        Text(
          'Welcome to Medroid!',
          style: GoogleFonts.inter(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: isDarkMode
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          'Help us personalize your experience by providing a few additional details.',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: isDarkMode
                ? AppColors.textSecondaryDark
                : AppColors.textSecondaryLight,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildForm(bool isDarkMode, bool isDesktop) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Gender selection
          _buildGenderSelection(isDarkMode),

          const SizedBox(height: 20),

          // Date of birth
          _buildDateOfBirthField(isDarkMode),

          const SizedBox(height: 20),

          // Referral code
          _buildReferralCodeField(isDarkMode),
        ],
      ),
    );
  }

  Widget _buildGenderSelection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gender (Optional)',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDarkMode
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildGenderOption(
              label: 'Male',
              value: 'male',
              isSelected: _selectedGender == 'male',
              isDarkMode: isDarkMode,
            ),
            const SizedBox(width: 12),
            _buildGenderOption(
              label: 'Female',
              value: 'female',
              isSelected: _selectedGender == 'female',
              isDarkMode: isDarkMode,
            ),
            const SizedBox(width: 12),
            _buildGenderOption(
              label: 'Other',
              value: 'other',
              isSelected: _selectedGender == 'other',
              isDarkMode: isDarkMode,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGenderOption({
    required String label,
    required String value,
    required bool isSelected,
    required bool isDarkMode,
  }) {
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedGender = value;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.coralPop.withValues(alpha: 0.1)
                : (isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight),
            border: Border.all(
              color: isSelected
                  ? AppColors.coralPop
                  : (isDarkMode ? Colors.grey[600]! : Colors.grey[300]!),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              color: isSelected
                  ? AppColors.coralPop
                  : (isDarkMode
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildDateOfBirthField(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date of Birth (Optional)',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDarkMode
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _dobController,
          readOnly: true,
          onTap: _selectDateOfBirth,
          decoration: InputDecoration(
            hintText: 'Select your date of birth',
            hintStyle: GoogleFonts.inter(
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontSize: 14,
            ),
            prefixIcon: Icon(
              Icons.calendar_today_outlined,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              size: 20,
            ),
            filled: true,
            fillColor:
                isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: AppColors.coralPop,
                width: 2,
              ),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          style: GoogleFonts.inter(
            color: isDarkMode
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildReferralCodeField(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Referral Code (Optional)',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDarkMode
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _referralCodeController,
          decoration: InputDecoration(
            hintText: 'Enter referral code',
            hintStyle: GoogleFonts.inter(
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontSize: 14,
            ),
            prefixIcon: Icon(
              Icons.card_giftcard_outlined,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              size: 20,
            ),
            filled: true,
            fillColor:
                isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: AppColors.coralPop,
                width: 2,
              ),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          style: GoogleFonts.inter(
            color: isDarkMode
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildCompleteButton(bool isDesktop) {
    return SizedBox(
      width: double.infinity,
      child: GradientButton(
        onPressed: _isLoading ? null : _completeRegistration,
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: isDesktop ? 14 : 16),
        borderRadius: BorderRadius.circular(16),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Complete Setup',
                    style: TextStyle(
                      fontSize: isDesktop ? 15 : 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_rounded,
                    size: isDesktop ? 18 : 20,
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSkipButton(bool isDarkMode) {
    return TextButton(
      onPressed: _isLoading
          ? null
          : () async {
              // Complete registration without additional details
              await _completeRegistration();
            },
      child: Text(
        'Skip for now',
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDarkMode
              ? AppColors.textSecondaryDark
              : AppColors.textSecondaryLight,
        ),
      ),
    );
  }
}
