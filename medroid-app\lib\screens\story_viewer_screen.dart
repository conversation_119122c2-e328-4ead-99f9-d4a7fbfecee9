import 'package:flutter/material.dart';
import 'package:medroid_app/models/story.dart';
import 'package:medroid_app/services/story_service.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';

class StoryViewerScreen extends StatefulWidget {
  final StoryGroup storyGroup;
  final StoryService storyService;

  const StoryViewerScreen({
    Key? key,
    required this.storyGroup,
    required this.storyService,
  }) : super(key: key);

  @override
  State<StoryViewerScreen> createState() => _StoryViewerScreenState();
}

class _StoryViewerScreenState extends State<StoryViewerScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _progressController;
  int _currentStoryIndex = 0;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _progressController = AnimationController(
      duration: const Duration(seconds: 5), // 5 seconds per story
      vsync: this,
    );

    _startStoryTimer();
    _markCurrentStoryAsViewed();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _startStoryTimer() {
    _progressController.reset();
    _progressController.forward().then((_) {
      if (!_isPaused && mounted) {
        _nextStory();
      }
    });
  }

  void _pauseStory() {
    setState(() {
      _isPaused = true;
    });
    _progressController.stop();
  }

  void _resumeStory() {
    setState(() {
      _isPaused = false;
    });
    _progressController.forward();
  }

  void _nextStory() {
    if (_currentStoryIndex < widget.storyGroup.stories.length - 1) {
      setState(() {
        _currentStoryIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startStoryTimer();
      _markCurrentStoryAsViewed();
    } else {
      // All stories viewed, close the viewer
      Navigator.pop(context);
    }
  }

  void _previousStory() {
    if (_currentStoryIndex > 0) {
      setState(() {
        _currentStoryIndex--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startStoryTimer();
    }
  }

  void _markCurrentStoryAsViewed() {
    if (_currentStoryIndex < widget.storyGroup.stories.length) {
      final story = widget.storyGroup.stories[_currentStoryIndex];
      if (!story.isViewed) {
        widget.storyService.markStoryAsViewed(story.id).catchError((e) {
          debugPrint('Error marking story as viewed: $e');
        });
      }
    }
  }

  void _showStoryOptions() {
    _pauseStory();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text(
                  'Delete Story',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _deleteCurrentStory();
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel, color: Colors.white),
                title: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _resumeStory();
                },
              ),
            ],
          ),
        );
      },
    ).then((_) {
      // Resume story if modal was dismissed
      if (!_isPaused) {
        _resumeStory();
      }
    });
  }

  void _deleteCurrentStory() async {
    if (_currentStoryIndex < widget.storyGroup.stories.length) {
      final story = widget.storyGroup.stories[_currentStoryIndex];

      try {
        await widget.storyService.deleteStory(story.id);

        // Remove the story from the list
        widget.storyGroup.stories.removeAt(_currentStoryIndex);

        if (widget.storyGroup.stories.isEmpty) {
          // No more stories, close viewer
          if (mounted) {
            Navigator.pop(
                context, true); // Return true to indicate refresh needed
          }
        } else {
          // Adjust current index if needed
          if (_currentStoryIndex >= widget.storyGroup.stories.length) {
            _currentStoryIndex = widget.storyGroup.stories.length - 1;
          }

          // Continue with next story
          if (mounted) {
            setState(() {});
            _startStoryTimer();
            _markCurrentStoryAsViewed();
          }
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Story deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting story: $e')),
          );
        }
        _resumeStory();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    if (widget.storyGroup.stories.isEmpty) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Text(
            'No stories available',
            style: TextStyle(
              color: Colors.white,
              fontSize: isDesktop ? 24 : 18,
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: isDesktop || isTablet
          ? _buildDesktopStoryViewer()
          : _buildMobileStoryViewer(),
    );
  }

  Widget _buildDesktopStoryViewer() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final maxStoryWidth = isDesktop ? 400.0 : 350.0;

    return Center(
      child: Container(
        width: maxStoryWidth,
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: _buildMobileStoryViewer(),
      ),
    );
  }

  Widget _buildMobileStoryViewer() {
    return GestureDetector(
      onTapDown: (details) {
        final screenWidth = MediaQuery.of(context).size.width;
        final tapPosition = details.globalPosition.dx;

        if (tapPosition < screenWidth / 3) {
          // Tap on left third - previous story
          _previousStory();
        } else if (tapPosition > screenWidth * 2 / 3) {
          // Tap on right third - next story
          _nextStory();
        } else {
          // Tap on middle third - pause/resume
          if (_isPaused) {
            _resumeStory();
          } else {
            _pauseStory();
          }
        }
      },
      child: Stack(
        children: [
          // Story content
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentStoryIndex = index;
              });
              _startStoryTimer();
              _markCurrentStoryAsViewed();
            },
            itemCount: widget.storyGroup.stories.length,
            itemBuilder: (context, index) {
              final story = widget.storyGroup.stories[index];
              return _buildStoryContent(story);
            },
          ),

          // Progress indicators
          SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: List.generate(
                  widget.storyGroup.stories.length,
                  (index) => Expanded(
                    child: Container(
                      height: 3,
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      child: LinearProgressIndicator(
                        value: index < _currentStoryIndex
                            ? 1.0
                            : index == _currentStoryIndex
                                ? _progressController.value
                                : 0.0,
                        backgroundColor: Colors.white.withValues(alpha: 0.3),
                        valueColor:
                            const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Header with user info and close button
          SafeArea(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // User avatar
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.grey,
                    backgroundImage: widget.storyGroup.userAvatar != null &&
                            widget.storyGroup.userAvatar!.isNotEmpty
                        ? NetworkImage(widget.storyGroup.userAvatar!)
                        : null,
                    child: widget.storyGroup.userAvatar == null ||
                            widget.storyGroup.userAvatar!.isEmpty
                        ? Text(
                            widget.storyGroup.username.isNotEmpty
                                ? widget.storyGroup.username[0].toUpperCase()
                                : '?',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  // Username and timestamp
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.storyGroup.username,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        if (_currentStoryIndex <
                            widget.storyGroup.stories.length)
                          Text(
                            _formatTimestamp(widget.storyGroup
                                .stories[_currentStoryIndex].createdAt),
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                  // More options button (for current user's stories)
                  if (widget.storyGroup.isCurrentUser)
                    IconButton(
                      onPressed: _showStoryOptions,
                      icon: const Icon(
                        Icons.more_vert,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  // Close button
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Pause indicator
          if (_isPaused)
            const Center(
              child: Icon(
                Icons.pause_circle_filled,
                color: Colors.white,
                size: 64,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStoryContent(Story story) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Story image
          if (story.mediaUrl.isNotEmpty)
            PlatformAwareNetworkImage(
              imageUrl: story.mediaUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Center(
                child: CircularProgressIndicator(
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: isDesktop ? 3 : 2,
                ),
              ),
              errorWidget: (context, url, error) => Center(
                child: Icon(
                  Icons.error,
                  color: Colors.white,
                  size: isDesktop
                      ? 80
                      : isTablet
                          ? 72
                          : 64,
                ),
              ),
            ),

          // Caption overlay
          if (story.caption != null && story.caption!.isNotEmpty)
            Positioned(
              bottom: isDesktop
                  ? 120
                  : isTablet
                      ? 110
                      : 100,
              left: isDesktop ? 24 : 16,
              right: isDesktop ? 24 : 16,
              child: Container(
                padding: EdgeInsets.all(isDesktop ? 16 : 12),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(isDesktop ? 12 : 8),
                ),
                child: Text(
                  story.caption!,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isDesktop
                        ? 18
                        : isTablet
                            ? 17
                            : 16,
                    height: 1.3,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
