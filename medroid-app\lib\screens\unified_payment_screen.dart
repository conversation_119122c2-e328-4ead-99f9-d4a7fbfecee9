import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart' hide Card;
import 'package:medroid_app/services/unified_payment_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/unified_payment_form.dart';

class UnifiedPaymentScreen extends StatefulWidget {
  final Map<String, dynamic> appointmentData;
  final Map<String, dynamic>? paymentData;
  final Function(bool success, String? paymentId) onPaymentComplete;

  const UnifiedPaymentScreen({
    Key? key,
    required this.appointmentData,
    this.paymentData,
    required this.onPaymentComplete,
  }) : super(key: key);

  @override
  UnifiedPaymentScreenState createState() => UnifiedPaymentScreenState();
}

class UnifiedPaymentScreenState extends State<UnifiedPaymentScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  bool _paymentSuccess = false;

  // Payment details
  late final String _appointmentId;
  late final String _providerId;
  late final String? _serviceId;
  late final double _amount;
  late final String _providerName;
  late final String _appointmentDate;
  late final String _appointmentTime;

  // Payment intent details
  String? _paymentIntentId;
  String? _clientSecret;
  String? _publishableKey;

  // Payment service
  final _paymentService = UnifiedPaymentService();

  @override
  void initState() {
    super.initState();
    _extractAppointmentDetails();
    _initializePayment();
  }

  void _extractAppointmentDetails() {
    final appointment = widget.appointmentData;

    // Debug the appointment data structure
    debugPrint('Appointment data: $appointment');

    // Extract appointment ID
    _appointmentId = appointment['id']?.toString() ?? '';

    // Handle provider ID - check if it's a nested object or direct field
    if (appointment['provider'] is Map) {
      _providerId = appointment['provider']['id']?.toString() ?? '';
    } else {
      _providerId = appointment['provider_id']?.toString() ?? '';
    }

    // Handle service ID - check if it's a nested object or direct field
    if (appointment['service'] is Map) {
      _serviceId = appointment['service']?['id']?.toString();
    } else {
      _serviceId = appointment['service_id']?.toString();
    }

    // Extract amount - first try direct amount field, then nested objects
    if (appointment['amount'] != null) {
      _amount = double.tryParse(appointment['amount'].toString()) ?? 50.0;
    } else if (appointment['service'] is Map &&
        appointment['service']['price'] != null) {
      _amount =
          double.tryParse(appointment['service']['price'].toString()) ?? 50.0;
    } else if (appointment['provider'] is Map &&
        appointment['provider']['pricing'] is Map &&
        appointment['provider']['pricing']['consultation'] != null) {
      _amount = double.tryParse(
              appointment['provider']['pricing']['consultation'].toString()) ??
          50.0;
    } else {
      _amount = 50.0; // Default amount
    }

    // Extract provider name from different possible structures
    String providerName = 'Healthcare Provider';

    if (appointment['provider'] is Map &&
        appointment['provider']['name'] != null) {
      providerName = appointment['provider']['name'];
    } else if (appointment['provider'] is Map &&
        appointment['provider']['user'] is Map &&
        appointment['provider']['user']['name'] != null) {
      providerName = appointment['provider']['user']['name'];
    } else if (appointment['provider_name'] != null) {
      providerName = appointment['provider_name'];
    } else {
      // Try to use provider ID as a fallback
      final providerId = _providerId;
      if (providerId.isNotEmpty) {
        providerName = 'Provider #$providerId';
      }
    }

    _providerName = providerName;

    // Extract appointment date and time
    final date = appointment['date'] ?? 'Unknown date';
    final timeSlot = appointment['time_slot'] ??
        {'start_time': 'Unknown', 'end_time': 'Unknown'};
    _appointmentDate = date;
    _appointmentTime =
        '${timeSlot['start_time'] ?? 'Unknown'} - ${timeSlot['end_time'] ?? 'Unknown'}';
  }

  Future<void> _initializePayment() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the Stripe publishable key
      _publishableKey = await _paymentService.getStripePublishableKey();

      // Initialize Stripe on mobile
      if (!kIsWeb) {
        Stripe.publishableKey = _publishableKey!;
        await Stripe.instance.applySettings();
      }

      // Check if payment data was provided (from createAppointmentWithPayment)
      if (widget.paymentData != null) {
        setState(() {
          _paymentIntentId = widget.paymentData!['id']?.toString();
          _clientSecret = widget.paymentData!['client_secret']?.toString();
          _isLoading = false;
        });

        // Validate that we have the required payment data
        if (_paymentIntentId == null || _clientSecret == null) {
          setState(() {
            _errorMessage = 'Missing payment information. Please try again.';
          });
        }
      } else {
        // Validate appointment ID before creating payment intent
        if (_appointmentId.isEmpty) {
          setState(() {
            _isLoading = false;
            _errorMessage =
                'Invalid appointment information. Please try again.';
          });
          return;
        }

        // Create payment intent if no payment data was provided
        final paymentIntent = await _paymentService.createPaymentIntent(
          appointmentId: _appointmentId,
          amount: _amount,
          currency: 'usd',
          providerId: _providerId,
          serviceId: _serviceId,
        );

        setState(() {
          _paymentIntentId = paymentIntent['id']?.toString();
          _clientSecret = paymentIntent['client_secret']?.toString();
          _isLoading = false;
        });

        // Validate that we have the required payment data
        if (_paymentIntentId == null || _clientSecret == null) {
          setState(() {
            _errorMessage = 'Failed to get payment details. Please try again.';
          });
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to initialize payment: ${e.toString()}';
      });
    }
  }

  void _handlePaymentResult(Map<String, dynamic> result) async {
    debugPrint('Payment result: $result');

    if (result['success'] == true) {
      try {
        // Extract payment intent ID from result or use the stored one
        final paymentIntentId = result['payment_intent_id'] ?? _paymentIntentId;

        if (paymentIntentId == null) {
          setState(() {
            _errorMessage = 'Missing payment intent ID. Please try again.';
          });
          widget.onPaymentComplete(false, null);
          return;
        }

        debugPrint('Confirming payment with intent ID: $paymentIntentId');

        // Confirm the payment with the server
        final confirmationResult = await _paymentService.confirmElementsPayment(
          paymentIntentId: paymentIntentId,
          appointmentId: _appointmentId,
        );

        debugPrint('Payment confirmation result: $confirmationResult');

        if (confirmationResult['success'] == true) {
          debugPrint('Payment successfully confirmed with the server');
          setState(() {
            _paymentSuccess = true;
            _errorMessage = null;
          });
          widget.onPaymentComplete(true, paymentIntentId);
        } else {
          debugPrint(
              'Server confirmation failed: ${confirmationResult['message']}');

          // Even if server confirmation fails, the payment might have succeeded
          // with Stripe, so we treat this as a successful payment
          setState(() {
            _paymentSuccess = true;
            _errorMessage = null;
          });
          widget.onPaymentComplete(true, paymentIntentId);
        }
      } catch (e) {
        debugPrint('Exception during payment confirmation: $e');

        // Even if there's an error confirming with our server,
        // the payment may have succeeded with Stripe
        setState(() {
          _paymentSuccess = true;
          _errorMessage = null;
        });
        widget.onPaymentComplete(true, _paymentIntentId);
      }
    } else {
      debugPrint('Payment failed: ${result['error']}');

      // Check if it's a technical error or a payment error
      final errorMsg = result['error'] ?? 'Payment failed';
      final errorType = result['type'] ?? '';
      final errorCode = result['code'] ?? '';

      debugPrint(
          'Payment error details: code=$errorCode, type=$errorType, message=$errorMsg');

      // Handle common Stripe errors with friendly messages
      String userFriendlyMessage;

      // Special handling for test/live mode mismatch
      if (errorMsg.contains('test card') && errorMsg.contains('live mode')) {
        debugPrint('Detected test card in live mode error');
        userFriendlyMessage =
            'You cannot use test cards in live mode. Please use a real credit card or contact support.';
      } else if (errorMsg.contains('test mode') &&
          errorMsg.contains('live mode')) {
        debugPrint('Detected test/live mode mismatch');
        userFriendlyMessage =
            'There is a mode mismatch between test and live environments. Please refresh and try again.';
      } else if (errorCode == 'card_declined') {
        userFriendlyMessage =
            'Your card was declined. Please try a different card.';
      } else if (errorCode == 'expired_card') {
        userFriendlyMessage =
            'Your card has expired. Please try a different card.';
      } else if (errorCode == 'incorrect_cvc') {
        userFriendlyMessage =
            'The card\'s security code is incorrect. Please check and try again.';
      } else if (errorCode == 'processing_error') {
        userFriendlyMessage =
            'An error occurred while processing your card. Please try again.';
      } else if (errorCode == 'insufficient_funds') {
        userFriendlyMessage =
            'Your card has insufficient funds. Please try a different card.';
      } else {
        userFriendlyMessage = errorMsg;
      }

      setState(() {
        _errorMessage = userFriendlyMessage;
      });
      widget.onPaymentComplete(false, null);
    }
  }

  Future<void> _processMobilePayment() async {
    if (_clientSecret == null || _paymentIntentId == null) {
      setState(() {
        _errorMessage = 'Payment initialization failed. Please try again.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Use Stripe SDK for mobile payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: _clientSecret!,
          merchantDisplayName: 'Medroid Health',
          style: ThemeMode.system,
        ),
      );

      await Stripe.instance.presentPaymentSheet();

      // If we get here, the payment was successful
      try {
        await _paymentService.confirmPayment(
          paymentIntentId: _paymentIntentId!,
          appointmentId: _appointmentId,
        );

        setState(() {
          _isLoading = false;
          _paymentSuccess = true;
        });
        widget.onPaymentComplete(true, _paymentIntentId);
      } catch (confirmError) {
        setState(() {
          _isLoading = false;
          _errorMessage =
              'Payment processed but confirmation failed. Please contact support: ${confirmError.toString()}';
        });
        widget.onPaymentComplete(true, _paymentIntentId);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Payment processing error: ${e.toString()}';
      });
      widget.onPaymentComplete(false, null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: _isLoading
          ? _buildLoadingView()
          : _paymentSuccess
              ? _buildSuccessView()
              : _buildPaymentForm(),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      decoration: BoxDecoration(
        gradient: AppColors.getPrimaryGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
            SizedBox(height: 24),
            Text(
              'Preparing your payment...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentForm() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isDesktop = constraints.maxWidth > 768;
        final isTablet =
            constraints.maxWidth > 600 && constraints.maxWidth <= 768;
        final maxWidth =
            isDesktop ? 700.0 : (isTablet ? 550.0 : double.infinity);

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.tealSurge.withAlpha(26), // 10% opacity
                AppColors.backgroundLight,
                AppColors.backgroundLight,
              ],
              stops: const [0.0, 0.3, 1.0],
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Custom App Bar
                _buildCustomAppBar(context, isDesktop),

                // Main Content
                Expanded(
                  child: Center(
                    child: Container(
                      constraints: BoxConstraints(maxWidth: maxWidth),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(
                          horizontal: isDesktop ? 24.0 : 20.0,
                          vertical: isDesktop ? 16.0 : 20.0,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Header Section
                            _buildHeaderSection(context, isDesktop),

                            SizedBox(height: isDesktop ? 24 : 32),

                            // Appointment Details Card
                            _buildAppointmentCard(context, isDesktop),

                            SizedBox(height: isDesktop ? 20 : 24),

                            // Payment Section
                            _buildPaymentSection(context, isDesktop),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomAppBar(BuildContext context, bool isDesktop) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isDesktop ? 24.0 : 20.0,
        vertical: isDesktop ? 12.0 : 16.0,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0A000000), // 4% opacity black
            offset: Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: AppColors.textPrimaryLight,
              size: 20,
            ),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.backgroundLight,
              padding: const EdgeInsets.all(12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Payment',
            style: TextStyle(
              fontSize: isDesktop ? 22 : 24,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimaryLight,
              letterSpacing: -0.5,
            ),
          ),
          const Spacer(),
          // Security badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.tealSurge.withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.security,
                  size: 16,
                  color: AppColors.tealSurge,
                ),
                const SizedBox(width: 4),
                const Text(
                  'Secure',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.tealSurge,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Payment icon
        Container(
          width: isDesktop ? 64 : 64,
          height: isDesktop ? 64 : 64,
          decoration: BoxDecoration(
            gradient: AppColors.getPrimaryGradient(),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppColors.tealSurge.withAlpha(51), // 20% opacity
                offset: const Offset(0, 8),
                blurRadius: 24,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Icon(
            Icons.payment,
            size: isDesktop ? 32 : 32,
            color: Colors.white,
          ),
        ),

        SizedBox(height: isDesktop ? 16 : 16),

        Text(
          'Complete Your Payment',
          style: TextStyle(
            fontSize: isDesktop ? 26 : 28,
            fontWeight: FontWeight.w700,
            color: AppColors.textPrimaryLight,
            letterSpacing: -1.0,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: isDesktop ? 8 : 8),

        Text(
          'Secure payment powered by Stripe',
          style: TextStyle(
            fontSize: isDesktop ? 14 : 14,
            color: AppColors.textSecondaryLight,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAppointmentCard(BuildContext context, bool isDesktop) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isDesktop ? 20 : 20),
        boxShadow: [
          BoxShadow(
            color: AppColors.tealSurge.withAlpha(13), // 5% opacity
            offset: const Offset(0, 8),
            blurRadius: 32,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isDesktop ? 20.0 : 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon
            Row(
              children: [
                Container(
                  width: isDesktop ? 48 : 48,
                  height: isDesktop ? 48 : 48,
                  decoration: BoxDecoration(
                    gradient: AppColors.getAccentGradient(),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.calendar_today,
                    size: isDesktop ? 24 : 24,
                    color: Colors.white,
                  ),
                ),
                SizedBox(width: isDesktop ? 16 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Appointment Details',
                        style: TextStyle(
                          fontSize: isDesktop ? 20 : 20,
                          fontWeight: FontWeight.w700,
                          color: AppColors.textPrimaryLight,
                          letterSpacing: -0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Review your booking information',
                        style: TextStyle(
                          fontSize: isDesktop ? 14 : 14,
                          color: AppColors.textSecondaryLight,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: isDesktop ? 20 : 24),

            // Appointment details
            _buildDetailRow('Provider', _providerName, isDesktop: isDesktop),
            _buildDetailRow('Date', _appointmentDate, isDesktop: isDesktop),
            _buildDetailRow('Time', _appointmentTime, isDesktop: isDesktop),

            // Divider
            Container(
              margin: EdgeInsets.symmetric(vertical: isDesktop ? 16 : 20),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    AppColors.textSecondaryLight.withAlpha(51), // 20% opacity
                    Colors.transparent,
                  ],
                ),
              ),
            ),

            // Total amount
            Container(
              padding: EdgeInsets.all(isDesktop ? 16 : 16),
              decoration: BoxDecoration(
                gradient: AppColors.getPrimaryGradient(),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Amount',
                    style: TextStyle(
                      fontSize: isDesktop ? 16 : 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    '\$${_amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: isDesktop ? 20 : 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection(BuildContext context, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Payment Information Header
        Row(
          children: [
            Container(
              width: isDesktop ? 48 : 48,
              height: isDesktop ? 48 : 48,
              decoration: BoxDecoration(
                gradient: AppColors.getPrimaryGradient(),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.credit_card,
                size: isDesktop ? 24 : 24,
                color: Colors.white,
              ),
            ),
            SizedBox(width: isDesktop ? 16 : 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Payment Information',
                    style: TextStyle(
                      fontSize: isDesktop ? 20 : 20,
                      fontWeight: FontWeight.w700,
                      color: AppColors.textPrimaryLight,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Secure payment powered by Stripe',
                    style: TextStyle(
                      fontSize: isDesktop ? 14 : 14,
                      color: AppColors.textSecondaryLight,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: isDesktop ? 20 : 24),

        // Payment Method Info Card
        Container(
          padding: EdgeInsets.all(isDesktop ? 16 : 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(isDesktop ? 16 : 16),
            border: Border.all(
              color: AppColors.tealSurge.withAlpha(51), // 20% opacity
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.tealSurge.withAlpha(13), // 5% opacity
                offset: const Offset(0, 4),
                blurRadius: 16,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.security,
                    color: AppColors.tealSurge,
                    size: isDesktop ? 20 : 20,
                  ),
                  SizedBox(width: isDesktop ? 8 : 8),
                  Expanded(
                    child: Text(
                      kIsWeb
                          ? 'You will be prompted to enter your payment details securely through Stripe\'s payment form.'
                          : 'Tap the payment button below to securely enter your payment details.',
                      style: TextStyle(
                        fontSize: isDesktop ? 14 : 14,
                        color: AppColors.textPrimaryLight,
                        fontWeight: FontWeight.w500,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: isDesktop ? 12 : 12),

              // Accepted cards
              Row(
                children: [
                  Icon(
                    Icons.credit_card,
                    color: AppColors.textSecondaryLight,
                    size: isDesktop ? 16 : 16,
                  ),
                  SizedBox(width: isDesktop ? 6 : 6),
                  Text(
                    'We accept all major credit and debit cards',
                    style: TextStyle(
                      fontSize: isDesktop ? 12 : 12,
                      color: AppColors.textSecondaryLight,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        if (_errorMessage != null) ...[
          SizedBox(height: isDesktop ? 16 : 16),
          Container(
            padding: EdgeInsets.all(isDesktop ? 12 : 12),
            decoration: BoxDecoration(
              color: AppColors.error.withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.error.withAlpha(51), // 20% opacity
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: AppColors.error,
                  size: isDesktop ? 20 : 20,
                ),
                SizedBox(width: isDesktop ? 8 : 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(
                      color: AppColors.error,
                      fontSize: isDesktop ? 12 : 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        SizedBox(height: isDesktop ? 20 : 24),

        // Payment Form or Button
        if (kIsWeb) ...[
          if (_clientSecret != null && _publishableKey != null) ...[
            Container(
              padding: EdgeInsets.all(isDesktop ? 16 : 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(isDesktop ? 16 : 16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.tealSurge.withAlpha(13), // 5% opacity
                    offset: const Offset(0, 8),
                    blurRadius: 32,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: UnifiedPaymentForm(
                clientSecret: _clientSecret!,
                publishableKey: _publishableKey!,
                onPaymentResult: _handlePaymentResult,
              ),
            ),
          ] else ...[
            _buildLoadingPaymentForm(isDesktop),
          ],
        ] else ...[
          _buildMobilePaymentButton(isDesktop),
        ],

        SizedBox(height: isDesktop ? 16 : 16),

        // Security footer
        _buildSecurityFooter(isDesktop),
      ],
    );
  }

  Widget _buildLoadingPaymentForm(bool isDesktop) {
    return Container(
      padding: EdgeInsets.all(isDesktop ? 20 : 24),
      decoration: BoxDecoration(
        color: AppColors.backgroundLight,
        borderRadius: BorderRadius.circular(isDesktop ? 16 : 16),
        border: Border.all(
          color: AppColors.tealSurge.withAlpha(51), // 20% opacity
          width: 1,
        ),
      ),
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.tealSurge),
            strokeWidth: 3,
          ),
          SizedBox(height: isDesktop ? 16 : 16),
          Text(
            'Preparing secure payment form...',
            style: TextStyle(
              color: AppColors.textSecondaryLight,
              fontSize: isDesktop ? 14 : 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMobilePaymentButton(bool isDesktop) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppColors.getPrimaryGradient(),
        borderRadius: BorderRadius.circular(isDesktop ? 16 : 16),
        boxShadow: [
          BoxShadow(
            color: AppColors.tealSurge.withAlpha(77), // 30% opacity
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _processMobilePayment,
          borderRadius: BorderRadius.circular(isDesktop ? 16 : 16),
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: isDesktop ? 18 : 20,
              horizontal: isDesktop ? 24 : 24,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment,
                  color: Colors.white,
                  size: isDesktop ? 20 : 20,
                ),
                SizedBox(width: isDesktop ? 8 : 8),
                Text(
                  'Pay \$${_amount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: isDesktop ? 18 : 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    letterSpacing: -0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityFooter(bool isDesktop) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock,
              size: isDesktop ? 14 : 14,
              color: AppColors.textSecondaryLight,
            ),
            SizedBox(width: isDesktop ? 6 : 6),
            Text(
              'Your payment is secure and encrypted',
              style: TextStyle(
                color: AppColors.textSecondaryLight,
                fontSize: isDesktop ? 12 : 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: isDesktop ? 8 : 8),
        Text(
          'Powered by Stripe',
          style: TextStyle(
            color: AppColors.textSecondaryLight,
            fontSize: isDesktop ? 10 : 10,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuccessView() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isDesktop = constraints.maxWidth > 768;
        final isTablet =
            constraints.maxWidth > 600 && constraints.maxWidth <= 768;
        final maxWidth =
            isDesktop ? 500.0 : (isTablet ? 450.0 : double.infinity);

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.tealSurge.withAlpha(26), // 10% opacity
                AppColors.backgroundLight,
              ],
            ),
          ),
          child: SafeArea(
            child: Center(
              child: Container(
                constraints: BoxConstraints(maxWidth: maxWidth),
                padding: EdgeInsets.all(isDesktop ? 24.0 : 20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Success animation container
                    Container(
                      width: isDesktop ? 80 : 100,
                      height: isDesktop ? 80 : 100,
                      decoration: BoxDecoration(
                        gradient: AppColors.getPrimaryGradient(),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.tealSurge
                                .withAlpha(77), // 30% opacity
                            offset: const Offset(0, 12),
                            blurRadius: 32,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.check,
                        color: Colors.white,
                        size: isDesktop ? 40 : 50,
                      ),
                    ),

                    SizedBox(height: isDesktop ? 24 : 32),

                    // Success message
                    Text(
                      'Payment Successful!',
                      style: TextStyle(
                        fontSize: isDesktop ? 28 : 32,
                        fontWeight: FontWeight.w700,
                        color: AppColors.textPrimaryLight,
                        letterSpacing: -1.0,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: isDesktop ? 12 : 12),

                    Text(
                      'Your appointment has been confirmed and you will receive a confirmation email shortly.',
                      style: TextStyle(
                        fontSize: isDesktop ? 16 : 16,
                        color: AppColors.textSecondaryLight,
                        fontWeight: FontWeight.w500,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: isDesktop ? 32 : 40),

                    // Success details card
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(isDesktop ? 16 : 20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.circular(isDesktop ? 16 : 16),
                        boxShadow: [
                          BoxShadow(
                            color:
                                AppColors.tealSurge.withAlpha(13), // 5% opacity
                            offset: const Offset(0, 8),
                            blurRadius: 32,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: isDesktop ? 40 : 40,
                                height: isDesktop ? 40 : 40,
                                decoration: BoxDecoration(
                                  color: AppColors.success
                                      .withAlpha(26), // 10% opacity
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.calendar_today,
                                  color: AppColors.success,
                                  size: isDesktop ? 20 : 20,
                                ),
                              ),
                              SizedBox(width: isDesktop ? 12 : 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Appointment Confirmed',
                                      style: TextStyle(
                                        fontSize: isDesktop ? 16 : 16,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.textPrimaryLight,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Check your email for details',
                                      style: TextStyle(
                                        fontSize: isDesktop ? 12 : 12,
                                        color: AppColors.textSecondaryLight,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: isDesktop ? 24 : 32),

                    // Return button
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: AppColors.getPrimaryGradient(),
                        borderRadius:
                            BorderRadius.circular(isDesktop ? 16 : 16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.tealSurge
                                .withAlpha(77), // 30% opacity
                            offset: const Offset(0, 8),
                            blurRadius: 24,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => Navigator.of(context).pop(),
                          borderRadius:
                              BorderRadius.circular(isDesktop ? 16 : 16),
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: isDesktop ? 16 : 16,
                              horizontal: isDesktop ? 24 : 24,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.arrow_back,
                                  color: Colors.white,
                                  size: isDesktop ? 20 : 20,
                                ),
                                SizedBox(width: isDesktop ? 8 : 8),
                                Text(
                                  'Return to Chat',
                                  style: TextStyle(
                                    fontSize: isDesktop ? 16 : 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value,
      {bool isBold = false, required bool isDesktop}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isDesktop ? 8 : 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: AppColors.textSecondaryLight,
                fontSize: isDesktop ? 14 : 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: isBold ? FontWeight.w700 : FontWeight.w600,
                fontSize: isDesktop ? 14 : 14,
                color: AppColors.textPrimaryLight,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
