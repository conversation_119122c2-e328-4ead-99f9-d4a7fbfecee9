import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';
import 'package:medroid_app/widgets/appointment_preferences_form.dart';
import 'package:medroid_app/screens/provider_dashboard/provider_profile_edit_screen.dart';
import 'package:medroid_app/screens/appointments_screen.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({Key? key}) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  bool _isEditing = false;

  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);
      final userData = await authService.getCurrentUser();

      setState(() {
        _userData = userData;
        _nameController.text = userData?['name'] ?? '';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading user data: $e')),
      );
    }
  }

  Future<void> _logout() async {
    final authService = RepositoryProvider.of<AuthService>(context);
    await authService.logout();

    // Navigate to login screen
    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);

      final updateData = {
        'name': _nameController.text,
      };

      final success = await authService.updateProfile(updateData);

      if (success) {
        await _loadUserData();
        setState(() {
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update profile')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating profile: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundLight,
        title: const Text('Profile'),
        actions: [
          if (!_isEditing && !_isLoading && _userData != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: isDesktop ? 600 : double.infinity,
          ),
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: AppColors.tealSurge))
              : _userData == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text('Failed to load user profile'),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadUserData,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.coralPop,
                            ),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  : _isEditing
                      ? _buildEditForm()
                      : _buildProfileView(),
        ),
      ),
    );
  }

  Widget _buildProfileView() {
    final isPatient = _userData!['role'] == 'patient';
    final isProvider = _userData!['role'] == 'provider';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Center(
            child: Stack(
              children: [
                CircleAvatar(
                  radius: 60,
                  backgroundColor: Colors.grey[200],
                  child: ClipOval(
                    child: _userData!['profile_image'] != null
                        ? PlatformAwareNetworkImage(
                            imageUrl: _userData!['profile_image'],
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CircularProgressIndicator(),
                            errorWidget: (context, url, error) => Icon(
                              Icons.person,
                              size: 60,
                              color: Colors.grey[400],
                            ),
                          )
                        : Icon(
                            Icons.person,
                            size: 60,
                            color: Colors.grey[400],
                          ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            _userData!['name'] ?? 'User',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _userData!['email'] ?? '',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isPatient
                  ? Colors.blue.withAlpha(26) // 0.1 opacity = 26/255
                  : isProvider
                      ? Colors.green.withAlpha(26) // 0.1 opacity = 26/255
                      : Colors.grey.withAlpha(26), // 0.1 opacity = 26/255
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              isPatient
                  ? 'Patient'
                  : isProvider
                      ? 'Healthcare Provider'
                      : _userData!['role'] ?? 'User',
              style: TextStyle(
                color: isPatient
                    ? Colors.blue
                    : isProvider
                        ? Colors.green
                        : Colors.grey[700],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 32),
          _buildSectionHeader('Account'),
          _buildOptionTile(
            icon: Icons.person,
            title: 'Personal Information',
            onTap: () {
              // Navigate to personal information screen
            },
          ),
          if (isPatient) ...[
            _buildOptionTile(
              icon: Icons.favorite,
              title: 'Health Profile',
              onTap: () {
                // Navigate to health profile screen
              },
            ),
            _buildOptionTile(
              icon: Icons.event_note,
              title: 'Appointments',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AppointmentsScreen(),
                  ),
                );
              },
            ),
            _buildOptionTile(
              icon: Icons.calendar_today,
              title: 'Appointment Preferences',
              onTap: () {
                _showAppointmentPreferencesDialog();
              },
            ),
          ],
          if (isProvider) ...[
            _buildOptionTile(
              icon: Icons.business,
              title: 'Practice Information',
              onTap: () {
                // Navigate to provider dashboard with practice information tab selected
                Navigator.pushNamed(
                  context,
                  '/provider-dashboard',
                  arguments: {
                    'initialTab': 3
                  }, // Index of the Practice Info tab
                );
              },
            ),
            _buildOptionTile(
              icon: Icons.person_outline,
              title: 'Edit Provider Profile',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProviderProfileEditScreen(),
                  ),
                ).then((value) {
                  if (value == true) {
                    // Reload user data if profile was updated
                    _loadUserData();
                  }
                });
              },
            ),
          ],
          _buildOptionTile(
            icon: Icons.notifications,
            title: 'Notification Settings',
            onTap: () {
              // Navigate to notification settings screen
            },
          ),
          const SizedBox(height: 16),
          _buildSectionHeader('App'),
          _buildOptionTile(
            icon: Icons.help,
            title: 'Help & Support',
            onTap: () {
              // Navigate to help screen
            },
          ),
          _buildOptionTile(
            icon: Icons.privacy_tip,
            title: 'Privacy Policy',
            onTap: () {
              // Navigate to privacy policy screen
            },
          ),
          _buildOptionTile(
            icon: Icons.description,
            title: 'Terms of Service',
            onTap: () {
              // Navigate to terms of service screen
            },
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _logout,
            icon: const Icon(Icons.exit_to_app),
            label: const Text('Logout'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildEditForm() {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(isDesktop ? 32 : 16),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Center(
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: Colors.grey[200],
                    child: ClipOval(
                      child: _userData!['profile_image'] != null
                          ? CachedNetworkImage(
                              imageUrl: _userData!['profile_image'],
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                              placeholder: (context, url) =>
                                  const CircularProgressIndicator(),
                              errorWidget: (context, url, error) => Icon(
                                Icons.person,
                                size: 60,
                                color: Colors.grey[400],
                              ),
                            )
                          : Icon(
                              Icons.person,
                              size: 60,
                              color: Colors.grey[400],
                            ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: isDesktop ? 40 : 32),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isDesktop ? 12 : 8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isDesktop ? 12 : 8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isDesktop ? 12 : 8),
                  borderSide: const BorderSide(color: AppColors.tealSurge),
                ),
                prefixIcon: const Icon(Icons.person),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: isDesktop ? 16 : 12,
                  vertical: isDesktop ? 16 : 12,
                ),
              ),
              style: TextStyle(fontSize: isDesktop ? 16 : 14),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            SizedBox(height: isDesktop ? 40 : 32),

            // Responsive button layout
            isDesktop
                ? Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _isEditing = false;
                              _nameController.text = _userData!['name'] ?? '';
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Cancel',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _updateProfile,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.coralPop,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Save',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _updateProfile,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.coralPop,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Save',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _isEditing = false;
                              _nameController.text = _userData!['name'] ?? '';
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Cancel',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Divider(
              color:
                  AppTheme.primaryColor.withAlpha(51), // 0.2 opacity = 51/255
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
    );
  }

  Future<void> _showAppointmentPreferencesDialog() async {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the API service
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Get the current appointment preferences
      final preferencesResponse = await apiService.getAppointmentPreferences();

      if (!mounted) return;

      // Hide loading indicator
      setState(() {
        _isLoading = false;
      });

      // Extract preferences
      final appointmentPreferences =
          preferencesResponse['appointment_preferences'] ??
              {
                'preferred_location': null,
                'preferred_gender': null,
                'preferred_language': null
              };

      // Show dialog with appointment preferences form
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Appointment Preferences'),
          content: SizedBox(
            width: double.maxFinite,
            child: AppointmentPreferencesForm(
              initialPreferences: appointmentPreferences,
              onPreferencesSaved: (updatedPreferences) {
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      // Hide loading indicator
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading appointment preferences: $e')),
        );
      }
    }
  }
}
