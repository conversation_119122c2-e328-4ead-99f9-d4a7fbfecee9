import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:medroid_app/services/api_service.dart';

/// Simplified Agora service for video consultations
class AgoraService {
  final ApiService _apiService;

  // Agora engine instance
  RtcEngine? engine;

  // Session data
  String? channelName;
  int? uid;
  String? token;
  String? appId;

  // Audio/Video state
  bool _isAudioEnabled = true;
  bool _isVideoEnabled = true;

  // Callbacks
  Function(int uid)? onUserJoined;
  Function(int uid)? onUserLeft;
  Function(ConnectionStateType state)? onConnectionStateChanged;
  Function(String error)? onError;

  // Constructor
  AgoraService(this._apiService);

  /// Initialize the Agora engine
  Future<void> initialize() async {
    try {
      // Check if we're running on web
      if (kIsWeb) {
        debugPrint('Running on web platform - Agora mobile SDK not supported');
        // For web, we'll handle video calls differently
        // The web implementation should use the existing web service
        throw UnsupportedError(
            'Agora mobile SDK not supported on web platform. Please use the web interface.');
      }

      // Create RTC engine instance (mobile only)
      engine = createAgoraRtcEngine();

      // Use hardcoded App ID that matches backend
      appId = '93f074e1f7a342a4a01e0b25e6be7d02';

      debugPrint('Initializing Agora engine with App ID: $appId');

      // Initialize the engine
      await engine!.initialize(RtcEngineContext(
        appId: appId!,
        channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
      ));

      // Register event handlers
      engine!.registerEventHandler(RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          debugPrint(
              'Local user joined: ${connection.channelId} with UID: $uid');
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          debugPrint('Remote user joined: $remoteUid (Local UID: $uid)');
          if (remoteUid != uid) {
            onUserJoined?.call(remoteUid);
          }
        },
        onUserOffline: (RtcConnection connection, int remoteUid,
            UserOfflineReasonType reason) {
          debugPrint('Remote user left: $remoteUid, reason: $reason');
          if (remoteUid != uid) {
            onUserLeft?.call(remoteUid);
          }
        },
        onConnectionStateChanged: (RtcConnection connection,
            ConnectionStateType state, ConnectionChangedReasonType reason) {
          debugPrint('Connection state changed: $state, reason: $reason');
          onConnectionStateChanged?.call(state);
        },
        onError: (ErrorCodeType err, String msg) {
          debugPrint('Agora error: $err - $msg');
          onError?.call('$err: $msg');
        },
      ));

      // Enable audio and video
      await engine!.enableVideo();
      await engine!.enableAudio();
      await engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);

      debugPrint('Agora service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Agora engine: $e');
      rethrow;
    }
  }

  /// Join a video consultation channel
  Future<bool> joinChannel({
    required String appointmentId,
    required bool isProvider,
  }) async {
    try {
      // Check if we're running on web
      if (kIsWeb) {
        debugPrint(
            'Web platform detected - redirecting to web video consultation');
        // For web, we should redirect to the web video consultation interface
        // This method should not be called on web platform
        throw UnsupportedError(
            'Use web video consultation interface for web platform');
      }

      // Request permissions (mobile only)
      Map<Permission, PermissionStatus> statuses =
          await [Permission.camera, Permission.microphone].request();

      debugPrint('Permission status: $statuses');

      // Get session data from our new simplified API
      final sessionData = await _apiService.getVideoSessionData(appointmentId);

      if (!sessionData['success']) {
        debugPrint('Failed to get session data: ${sessionData['message']}');
        return false;
      }

      final sessionInfo = sessionData['session_data'];

      // Extract session information
      channelName = sessionInfo['channel'];
      uid = sessionInfo['uid'];
      token = sessionInfo['token'];

      debugPrint('=== JOINING AGORA CHANNEL ===');
      debugPrint('Channel: $channelName');
      debugPrint('UID: $uid');
      debugPrint('Token length: ${token?.length ?? 0}');
      debugPrint('User role: ${isProvider ? 'Provider' : 'Patient'}');
      debugPrint('============================');

      // Join the channel
      await engine!.joinChannel(
        token: token!,
        channelId: channelName!,
        uid: uid!,
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
        ),
      );

      debugPrint('Successfully joined Agora channel');
      return true;
    } catch (e) {
      debugPrint('Error joining channel: $e');
      return false;
    }
  }

  /// Leave the channel
  Future<void> leaveChannel() async {
    try {
      debugPrint('Leaving Agora channel');
      if (engine != null) {
        await engine!.leaveChannel();
        debugPrint('Successfully left channel');
      }

      // Reset session data
      channelName = null;
      uid = null;
      token = null;
    } catch (e) {
      debugPrint('Error leaving channel: $e');
    }
  }

  /// Toggle local audio (mute/unmute)
  Future<bool> toggleLocalAudio() async {
    try {
      if (engine == null) {
        debugPrint('Engine is null, cannot toggle audio');
        return false;
      }

      _isAudioEnabled = !_isAudioEnabled;
      await engine!.enableLocalAudio(_isAudioEnabled);
      debugPrint('Audio ${_isAudioEnabled ? 'enabled' : 'disabled'}');

      return _isAudioEnabled;
    } catch (e) {
      debugPrint('Error toggling audio: $e');
      return false;
    }
  }

  /// Toggle local video (enable/disable)
  Future<bool> toggleLocalVideo() async {
    try {
      if (engine == null) {
        debugPrint('Engine is null, cannot toggle video');
        return false;
      }

      _isVideoEnabled = !_isVideoEnabled;
      await engine!.enableLocalVideo(_isVideoEnabled);
      debugPrint('Video ${_isVideoEnabled ? 'enabled' : 'disabled'}');

      return _isVideoEnabled;
    } catch (e) {
      debugPrint('Error toggling video: $e');
      return false;
    }
  }

  /// Switch camera
  Future<void> switchCamera() async {
    try {
      if (engine != null) {
        await engine!.switchCamera();
        debugPrint('Camera switched');
      }
    } catch (e) {
      debugPrint('Error switching camera: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    try {
      leaveChannel();
    } catch (e) {
      debugPrint('Error leaving channel during dispose: $e');
    }

    try {
      engine?.release();
      engine = null;
    } catch (e) {
      debugPrint('Error disposing engine: $e');
    }

    // Reset all state
    channelName = null;
    uid = null;
    token = null;
    _isAudioEnabled = true;
    _isVideoEnabled = true;

    debugPrint('Agora service disposed');
  }

  // Getters for current state
  bool get isAudioEnabled => _isAudioEnabled;
  bool get isVideoEnabled => _isVideoEnabled;
  bool get isConnected => channelName != null && uid != null;
}
