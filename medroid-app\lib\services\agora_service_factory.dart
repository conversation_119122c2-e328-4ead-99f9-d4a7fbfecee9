import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/agora_service.dart';
import 'package:medroid_app/services/agora_web_service_export.dart';

/// Factory for creating the appropriate Agora service based on the platform
class AgoraServiceFactory {
  /// Create an Agora service based on the platform
  static dynamic createAgoraService(ApiService apiService) {
    if (kIsWeb) {
      // Return web service for web platform
      return AgoraWebService(apiService);
    } else {
      // Return mobile service for mobile platforms
      return AgoraService(apiService);
    }
  }
}
