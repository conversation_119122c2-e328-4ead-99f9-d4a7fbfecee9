import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

/// A service for generating Agora tokens
class AgoraTokenService {
  // Constants for token versions
  static const int _tokenVersion = 1;
  static const int _appIdLength = 32;

  // Role types
  static const int _roleAttendee = 0;
  static const int _rolePublisher = 1;
  static const int _roleSubscriber = 2;
  static const int _roleAdmin = 101;

  // Privilege types
  static const int _privilegeJoinChannel = 1;
  static const int _privilegePublishAudioStream = 2;
  static const int _privilegePublishVideoStream = 3;
  static const int _privilegePublishDataStream = 4;

  /// Generate a token for Agora RTC
  ///
  /// [appId] - The App ID issued by Agora
  /// [appCertificate] - The App Certificate issued by Agora
  /// [channelName] - The channel name
  /// [uid] - The user ID
  /// [role] - The role of the user (publisher, subscriber, etc.)
  /// [expireTimestamp] - The expiration time of the token in seconds since epoch
  static String generateToken({
    required String appId,
    required String appCertificate,
    required String channelName,
    required int uid,
    int role = _rolePublisher,
    int expireTimestamp = 0,
  }) {
    try {
      // For development mode, we can use a simpler token format
      if (kDebugMode) {
        debugPrint('Generating simple token for development');
        return ''; // Empty token works in development mode
      }

      // For production, we need to generate a proper token
      // This is a simplified implementation of the Agora token generation algorithm

      // 1. Generate a random 16-byte salt
      final random = Random.secure();
      final salt = List<int>.generate(16, (_) => random.nextInt(256));

      // 2. Generate message digest
      final message = _generateMessage(
        appId: appId,
        appCertificate: appCertificate,
        channelName: channelName,
        uid: uid,
        salt: salt,
        expireTimestamp: expireTimestamp,
        role: role,
      );

      // 3. Generate signature
      final signature =
          _generateSignature(appCertificate, channelName, uid, message);

      // 4. Encode token
      final token = _encodeToken(
        signature: signature,
        appId: appId,
        channelName: channelName,
        uid: uid,
        expireTimestamp: expireTimestamp,
      );

      return token;
    } catch (e) {
      debugPrint('Error generating token: $e');
      return ''; // Return empty token on error
    }
  }

  /// Generate message for token
  static List<int> _generateMessage({
    required String appId,
    required String appCertificate,
    required String channelName,
    required int uid,
    required List<int> salt,
    required int expireTimestamp,
    required int role,
  }) {
    // This is a simplified implementation
    final message = <int>[];

    // Add version
    message.add(_tokenVersion);

    // Add app ID
    message.addAll(utf8.encode(appId));

    // Add channel name
    message.addAll(utf8.encode(channelName));

    // Add UID
    message.addAll(_packUint32(uid));

    // Add timestamp
    message.addAll(_packUint32(expireTimestamp));

    // Add salt
    message.addAll(salt);

    return message;
  }

  /// Generate signature for token
  static String _generateSignature(
    String appCertificate,
    String channelName,
    int uid,
    List<int> message,
  ) {
    // Create HMAC-SHA256 signature
    final key = utf8.encode(appCertificate);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(message);

    return base64.encode(digest.bytes);
  }

  /// Encode token
  static String _encodeToken({
    required String signature,
    required String appId,
    required String channelName,
    required int uid,
    required int expireTimestamp,
  }) {
    // For simplicity, we'll just return an empty token for now
    // In a real implementation, this would properly encode the token
    return '';
  }

  /// Pack uint32 to bytes
  static List<int> _packUint32(int value) {
    final bytes = Uint8List(4);
    bytes[0] = value & 0xff;
    bytes[1] = (value >> 8) & 0xff;
    bytes[2] = (value >> 16) & 0xff;
    bytes[3] = (value >> 24) & 0xff;
    return bytes;
  }
}
