// This file is only used on web platforms
// ignore: avoid_web_libraries_in_flutter
import 'dart:async';
import 'dart:convert';
import 'dart:html' as html; // Only available on web
import 'package:flutter/material.dart';
import 'package:medroid_app/services/api_service.dart';

/// A service to handle Agora video consultations for web platforms
class AgoraWebService {
  final ApiService _apiService;

  // Channel name for the current session
  String? channelName;

  // User ID for the current user
  int? uid;

  // Callbacks - using dynamic types for web compatibility
  Function(int uid)? onUserJoined;
  Function(int uid)? onUserLeft;
  Function(dynamic connection, dynamic state)? onConnectionStateChanged;
  Function(dynamic err, String msg)? onError;

  // HTML elements for Agora web
  html.DivElement? _localVideoContainer;
  html.DivElement? _remoteVideoContainer;
  bool _isInitialized = false;
  Timer? _pollingTimer; // Add timer reference for cleanup

  // Constructor
  AgoraWebService(this._apiService);

  /// Clean up any existing video elements
  void _cleanupExistingVideoElements() {
    try {
      // Generate unique variable names to avoid conflicts
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Clean up all video containers with our naming pattern
      final cleanupScript = html.ScriptElement()
        ..id = 'cleanup-script-$timestamp'
        ..text = '''
          (function() {
            // Use immediately invoked function expression to avoid variable conflicts
            try {
              // Find and remove all video containers with our naming pattern
              var localContainers_$timestamp = document.querySelectorAll('div[id^="local-video-container-"]');
              var remoteContainers_$timestamp = document.querySelectorAll('div[id^="remote-video-container-"]');

              console.log('Found ' + localContainers_$timestamp.length + ' local video containers to clean up');
              console.log('Found ' + remoteContainers_$timestamp.length + ' remote video containers to clean up');

              // Remove local containers
              localContainers_$timestamp.forEach(function(container) {
                console.log('Removing local container: ' + container.id);
                container.remove();
              });

              // Remove remote containers
              remoteContainers_$timestamp.forEach(function(container) {
                console.log('Removing remote container: ' + container.id);
                container.remove();
              });

              // Clean up any existing script elements from previous sessions
              var oldScripts = document.querySelectorAll('script[id^="cleanup-script-"], script[id^="agora-web-script"], script[id^="remote-user-check-"]');
              oldScripts.forEach(function(script) {
                if (script.id !== 'cleanup-script-$timestamp') {
                  script.remove();
                }
              });
            } catch (e) {
              console.error('Error in cleanup script:', e);
            }
          })();
        '''
        ..type = 'text/javascript';
      html.document.body?.append(cleanupScript);

      // Clean up the script element after a short delay
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          cleanupScript.remove();
        } catch (e) {
          debugPrint('Error removing cleanup script: $e');
        }
      });

      // Also try to remove the standard container IDs for backward compatibility
      final existingLocalContainer =
          html.document.getElementById('local-video-container');
      final existingRemoteContainer =
          html.document.getElementById('remote-video-container');

      if (existingLocalContainer != null) {
        existingLocalContainer.remove();
        debugPrint('Removed existing local video container');
      }

      if (existingRemoteContainer != null) {
        existingRemoteContainer.remove();
        debugPrint('Removed existing remote video container');
      }

      // Execute JavaScript to clean up any existing Agora resources
      final script = html.ScriptElement()
        ..text = '''
        try {
          // Clean up any existing Agora clients
          if (window.AgoraRTC) {
            const clients = AgoraRTC.instances;
            if (clients && clients.length > 0) {
              console.log('Found ' + clients.length + ' existing Agora clients, cleaning up...');
              for (let i = 0; i < clients.length; i++) {
                try {
                  clients[i].leave();
                  console.log('Left channel for client ' + i);
                } catch (e) {
                  console.error('Error leaving channel for client ' + i + ':', e);
                }
              }
            }
          }
        } catch (e) {
          console.error('Error cleaning up existing Agora resources:', e);
        }
      ''';
      html.document.body?.append(script);
      script.remove();

      debugPrint('Cleaned up existing video elements');
    } catch (e) {
      debugPrint('Error cleaning up existing video elements: $e');
    }
  }

  /// Initialize the Agora engine for web
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      // Generate unique IDs for this session to avoid conflicts with multiple tabs
      final sessionUniqueId = DateTime.now().millisecondsSinceEpoch.toString();
      final localContainerId = 'local-video-container-$sessionUniqueId';
      final remoteContainerId = 'remote-video-container-$sessionUniqueId';

      // Create container elements for videos with unique IDs
      _localVideoContainer = html.DivElement()
        ..id = localContainerId
        ..style.position = 'absolute'
        ..style.right = '20px'
        ..style.bottom = '120px'
        ..style.width = '120px'
        ..style.height = '160px'
        ..style.zIndex = '10'
        ..style.borderRadius = '10px'
        ..style.overflow = 'hidden'
        ..style.backgroundColor =
            '#333333'; // Dark background instead of border

      _remoteVideoContainer = html.DivElement()
        ..id = remoteContainerId
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.position = 'absolute'
        ..style.left = '0'
        ..style.top = '0'
        ..style.zIndex = '5';

      // Add containers to the body
      html.document.body?.append(_localVideoContainer!);
      html.document.body?.append(_remoteVideoContainer!);

      // Store the container IDs in window for JavaScript to access
      final storeIdsScript = html.ScriptElement()
        ..text = '''
          window.currentLocalVideoContainerId = '$localContainerId';
          window.currentRemoteVideoContainerId = '$remoteContainerId';
          console.log('Set video container IDs:', '$localContainerId', '$remoteContainerId');
        '''
        ..type = 'text/javascript';
      html.document.body?.append(storeIdsScript);
      storeIdsScript.remove();

      // Check if Agora Web SDK is already loaded
      if (html.document.getElementById('agora-web-sdk') == null) {
        // Load Agora Web SDK
        final scriptElement = html.ScriptElement()
          ..id = 'agora-web-sdk'
          ..src = 'https://download.agora.io/sdk/release/AgoraRTC_N-4.23.3.js'
          ..type = 'text/javascript';

        html.document.head?.append(scriptElement);

        // Wait for script to load
        final completer = Completer<void>();
        scriptElement.onLoad.listen((event) {
          completer.complete();
        });
        scriptElement.onError.listen((event) {
          completer.completeError('Failed to load Agora Web SDK');
        });

        // Wait for script to load with a timeout
        await completer.future.timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            debugPrint('Timeout waiting for Agora Web SDK to load');
            return;
          },
        );
      }

      _isInitialized = true;
      debugPrint('Agora Web SDK initialized');
    } catch (e) {
      debugPrint('Error initializing Agora Web SDK: $e');
      rethrow;
    }
  }

  /// Join a video consultation channel
  Future<bool> joinChannel({
    required String appointmentId,
    required bool isProvider,
  }) async {
    try {
      // Clean up any existing video elements first
      _cleanupExistingVideoElements();

      // Get session data
      final sessionData = await _apiService.getVideoSessionData(appointmentId);

      if (!sessionData['success']) {
        debugPrint('Failed to get session data: ${sessionData['message']}');
        return false;
      }

      // Parse tokens - handle both string and object formats
      final tokens = sessionData['tokens'] is String
          ? jsonDecode(sessionData['tokens'])
          : sessionData['tokens'];

      if (tokens == null) {
        debugPrint('Error: No tokens found in session data');
        return false;
      }

      // Log the tokens for debugging
      debugPrint('=== AGORA WEB SESSION DATA ===');
      debugPrint('Channel: ${tokens['channel']}');
      debugPrint('Patient UID: ${tokens['patient_uid']}');
      debugPrint('Provider UID: ${tokens['provider_uid']}');
      debugPrint('User is ${isProvider ? 'Provider' : 'Patient'}');
      debugPrint('========================');

      // Set channel name and token
      channelName = tokens['channel'];

      // Make sure we're using the correct RTC token for the user role
      final token = isProvider
          ? tokens['provider_token_rtc']
          : tokens['patient_token_rtc'];
      uid = isProvider ? tokens['provider_uid'] : tokens['patient_uid'];

      // Validate token and uid
      if (token == null || token.isEmpty) {
        debugPrint(
            'Error: Invalid token for ${isProvider ? 'provider' : 'patient'}');
        return false;
      }

      if (uid == null) {
        debugPrint(
            'Error: Invalid UID for ${isProvider ? 'provider' : 'patient'}');
        return false;
      }

      debugPrint('Joining Agora channel with:');
      debugPrint('Channel Name: $channelName');
      debugPrint('UID: $uid');
      debugPrint('Token: ${token.substring(0, 20)}...');

      // Generate a unique ID for this session
      final sessionId = DateTime.now().millisecondsSinceEpoch.toString();

      // Initialize Agora Web client with unique variable names
      // IMPORTANT: Use the exact App ID from the backend - this must match exactly
      const appId = '93f074e1f7a342a4a01e0b25e6be7d02';

      // Debug log the values we're using
      debugPrint('Using Agora Web SDK with:');
      debugPrint('App ID: $appId');
      debugPrint('Channel: $channelName');
      debugPrint('UID: $uid');
      debugPrint('Token length: ${token.length}');

      // Properly escape the token for JavaScript
      final escapedToken = token.replaceAll("'", "\\'").replaceAll(r'\', r'\\');

      final initScript = '''
        // Initialize Agora client with unique variable names for session $sessionId
        window.appId_$sessionId = '$appId';
        window.channel_$sessionId = '$channelName';
        window.token_$sessionId = '$escapedToken';
        window.uid_$sessionId = $uid;

        // Create Agora client
        const client_$sessionId = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });

        // Initialize local tracks
        let localTracks_$sessionId = {
          audioTrack: null,
          videoTrack: null
        };

        // Initialize remote tracks
        let remoteTracks_$sessionId = {};

        // Create a global control interface for Flutter to interact with
        window.agoraWebRTC = {
          // Toggle audio mute/unmute
          toggleAudio: function() {
            try {
              if (localTracks_$sessionId.audioTrack) {
                const muted = localTracks_$sessionId.audioTrack.muted;
                console.log('Current audio muted state:', muted);

                if (muted) {
                  localTracks_$sessionId.audioTrack.setMuted(false);
                  console.log('Audio unmuted');
                } else {
                  localTracks_$sessionId.audioTrack.setMuted(true);
                  console.log('Audio muted');
                }
                return !muted; // Return the new state
              } else {
                console.error('Audio track not found');
                return false;
              }
            } catch (e) {
              console.error('Error toggling audio:', e);
              return false;
            }
          },

          // Toggle video enable/disable
          toggleVideo: function() {
            try {
              if (localTracks_$sessionId.videoTrack) {
                const muted = localTracks_$sessionId.videoTrack.muted;
                console.log('Current video muted state:', muted);

                if (muted) {
                  localTracks_$sessionId.videoTrack.setMuted(false);
                  console.log('Video enabled');
                } else {
                  localTracks_$sessionId.videoTrack.setMuted(true);
                  console.log('Video disabled');
                }
                return !muted; // Return the new state
              } else {
                console.error('Video track not found');
                return false;
              }
            } catch (e) {
              console.error('Error toggling video:', e);
              return false;
            }
          },

          // Switch camera
          switchCamera: async function() {
            try {
              if (localTracks_$sessionId.videoTrack) {
                // Get all video devices
                const devices = await AgoraRTC.getDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');

                if (videoDevices.length <= 1) {
                  console.log('Only one camera found, cannot switch');
                  return false;
                }

                // Get current device ID
                const currentDeviceId = localTracks_$sessionId.videoTrack.getMediaStreamTrack().getSettings().deviceId;
                console.log('Current device ID:', currentDeviceId);

                // Find the next device ID
                let nextDeviceId = null;
                for (let i = 0; i < videoDevices.length; i++) {
                  if (videoDevices[i].deviceId === currentDeviceId) {
                    nextDeviceId = videoDevices[(i + 1) % videoDevices.length].deviceId;
                    break;
                  }
                }

                if (!nextDeviceId) {
                  nextDeviceId = videoDevices[0].deviceId;
                }

                console.log('Switching to device ID:', nextDeviceId);

                // Close the current track
                await localTracks_$sessionId.videoTrack.close();

                // Create a new track with the next device
                localTracks_$sessionId.videoTrack = await AgoraRTC.createCameraVideoTrack({ deviceId: nextDeviceId });

                // Play the new track
                localTracks_$sessionId.videoTrack.play('local-video-container');

                // Publish the new track
                await client_$sessionId.publish(localTracks_$sessionId.videoTrack);

                console.log('Camera switched successfully');
                return true;
              } else {
                console.error('Video track not found');
                return false;
              }
            } catch (e) {
              console.error('Error switching camera:', e);
              return false;
            }
          }
        };

        // Join the channel
        async function joinChannel_$sessionId() {
          try {
            // Get the variables from the window object
            const appId = window.appId_$sessionId;
            const channel = window.channel_$sessionId;
            const token = window.token_$sessionId;
            const uid = window.uid_$sessionId;

            console.log('Attempting to join channel with:');
            console.log('App ID:', appId);
            console.log('Channel:', channel);
            console.log('UID:', uid);
            console.log('Token length:', token ? token.length : 0);

            // Validate inputs before joining
            if (!appId) {
              console.error('App ID is empty or invalid');
              alert('Error: App ID is missing or invalid');
              return false;
            }

            if (!channel) {
              console.error('Channel name is empty or invalid');
              alert('Error: Channel name is missing or invalid');
              return false;
            }

            if (!token) {
              console.error('Token is empty or invalid');
              alert('Error: Token is missing or invalid');
              return false;
            }

            if (!uid) {
              console.error('UID is empty or invalid');
              alert('Error: UID is missing or invalid');
              return false;
            }

            console.log('All parameters validated, joining channel...');

            // Join the channel
            await client_$sessionId.join(appId, channel, token, uid);
            console.log('Joined channel successfully with UID:', uid);

            // Create local audio and video tracks
            try {
              console.log('Creating audio track...');
              localTracks_$sessionId.audioTrack = await AgoraRTC.createMicrophoneAudioTrack();
              console.log('Audio track created successfully');

              console.log('Creating video track...');
              localTracks_$sessionId.videoTrack = await AgoraRTC.createCameraVideoTrack();
              console.log('Video track created successfully');

              // Play local video track
              console.log('Playing local video track...');
              // Use the unique container ID stored in the window object
              const localContainerId = window.currentLocalVideoContainerId || 'local-video-container-$sessionId';
              console.log('Using local container ID:', localContainerId);

              const localContainer = document.getElementById(localContainerId);
              if (!localContainer) {
                console.error('Local video container not found, creating it...');
                // Create the container if it doesn't exist
                const newContainer = document.createElement('div');
                newContainer.id = localContainerId;
                newContainer.style.width = '120px';
                newContainer.style.height = '160px';
                newContainer.style.position = 'absolute';
                newContainer.style.right = '20px';
                newContainer.style.bottom = '120px';
                newContainer.style.zIndex = '10';
                newContainer.style.borderRadius = '10px';
                newContainer.style.overflow = 'hidden';
                newContainer.style.backgroundColor = '#333333';
                document.body.appendChild(newContainer);
              }

              localTracks_$sessionId.videoTrack.play(localContainerId);
              console.log('Local video track played successfully');

              // Publish local tracks
              console.log('Publishing local tracks...');
              const tracksToPublish = [];

              if (localTracks_$sessionId.audioTrack) {
                tracksToPublish.push(localTracks_$sessionId.audioTrack);
              }

              if (localTracks_$sessionId.videoTrack) {
                tracksToPublish.push(localTracks_$sessionId.videoTrack);
              }

              if (tracksToPublish.length > 0) {
                await client_$sessionId.publish(tracksToPublish);
                console.log('Published local tracks successfully');
              } else {
                console.warn('No local tracks to publish');
              }
            } catch (trackError) {
              console.error('Error creating or publishing local tracks:', trackError);
              // Continue even if we can't create tracks - might be permission issues

              // Try to get more detailed error information
              if (trackError.code) {
                console.error('Error code:', trackError.code, 'Error message:', trackError.message);
              }

              // Show a browser alert for permission issues
              if (trackError.name === 'NotAllowedError' || trackError.name === 'PermissionDeniedError') {
                alert('Camera or microphone permission denied. Please allow access to use video call features.');
              }
            }

            // Listen for remote users
            client_$sessionId.on('user-published', async (user, mediaType) => {
              console.log('Remote user published:', user.uid, mediaType);

              try {
                // Subscribe to remote user
                await client_$sessionId.subscribe(user, mediaType);
                console.log('Subscribed to remote user:', user.uid);

                // If the user publishes a video track
                if (mediaType === 'video') {
                  // Get the remote video track
                  const remoteVideoTrack = user.videoTrack;
                  if (remoteVideoTrack) {
                    // Check if remote container exists
                    console.log('Playing remote video track...');
                    // Use the unique container ID stored in the window object
                    const remoteContainerId = window.currentRemoteVideoContainerId || 'remote-video-container-$sessionId';
                    console.log('Using remote container ID:', remoteContainerId);

                    const remoteContainer = document.getElementById(remoteContainerId);
                    if (!remoteContainer) {
                      console.error('Remote video container not found, creating it...');
                      // Create the container if it doesn't exist
                      const newContainer = document.createElement('div');
                      newContainer.id = remoteContainerId;
                      newContainer.style.width = '100%';
                      newContainer.style.height = '100%';
                      newContainer.style.position = 'absolute';
                      newContainer.style.left = '0';
                      newContainer.style.top = '0';
                      newContainer.style.zIndex = '5';
                      document.body.appendChild(newContainer);
                    }

                    // Play the remote video track
                    remoteVideoTrack.play(remoteContainerId);
                    console.log('Remote video track played');

                    // Store remote track reference
                    if (!remoteTracks_$sessionId[user.uid]) {
                      remoteTracks_$sessionId[user.uid] = { audio: null, video: null };
                    }
                    remoteTracks_$sessionId[user.uid].video = user.videoTrack;

                    // Notify Flutter that a remote user has joined
                    try {
                      console.log('Notifying Flutter about remote user joined:', user.uid);

                      // Try multiple ways to notify Flutter
                      console.log('🚨 ATTEMPTING TO NOTIFY FLUTTER - USER JOINED:', user.uid);

                      // Method 1: Direct window postMessage (for same window)
                      window.postMessage({type: 'userJoined', uid: user.uid}, '*');

                      // Method 2: Parent window postMessage (for iframes)
                      if (window.parent !== window) {
                        window.parent.postMessage({type: 'userJoined', uid: user.uid}, '*');
                      }

                      // Method 3: Top window postMessage (for nested iframes)
                      if (window.top !== window) {
                        window.top.postMessage({type: 'userJoined', uid: user.uid}, '*');
                      }

                      // Also try the callHandler method as backup
                      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                        window.flutter_inappwebview.callHandler('onUserJoined', user.uid);
                      }

                      // Set global variables that Flutter can check
                      window.lastRemoteUserJoined = user.uid;
                      window.lastRemoteUserJoinedTime = Date.now();

                      // 🚨 CRITICAL FIX: Set immediate detection flag
                      window.agoraRemoteUserDetected = {
                        uid: user.uid,
                        timestamp: Date.now(),
                        processed: false
                      };

                      console.log('🚨 IMMEDIATE FLAG SET:', window.agoraRemoteUserDetected);

                      // Store in localStorage to share between tabs
                      try {
                        const userJoinData = {
                          uid: user.uid,
                          channel: window.channel_$sessionId,
                          timestamp: Date.now(),
                          type: 'userJoined'
                        };
                        localStorage.setItem('lastRemoteUserJoined', user.uid.toString());
                        localStorage.setItem('lastRemoteUserJoinedTime', Date.now().toString());
                        localStorage.setItem('agoraActiveChannel', window.channel_$sessionId);
                        localStorage.setItem('agoraUserJoinEvent', JSON.stringify(userJoinData));
                        console.log('✅ Stored remote user info in localStorage:', userJoinData);

                        // Trigger storage event manually for same-origin tabs
                        window.dispatchEvent(new StorageEvent('storage', {
                          key: 'agoraUserJoinEvent',
                          newValue: JSON.stringify(userJoinData),
                          oldValue: null,
                          storageArea: localStorage
                        }));

                      } catch (storageError) {
                        console.error('Error storing in localStorage:', storageError);
                      }

                      // Broadcast to all tabs using multiple methods
                      try {
                        const broadcastData = {
                          type: 'userJoined',
                          uid: user.uid,
                          channel: window.channel_$sessionId,
                          timestamp: Date.now()
                        };

                        // Method 1: BroadcastChannel
                        if (typeof BroadcastChannel !== 'undefined') {
                          const broadcastChannel = new BroadcastChannel('agora_video_channel');
                          broadcastChannel.postMessage(broadcastData);
                          console.log('📡 Broadcast via BroadcastChannel:', broadcastData);
                        }

                        // Method 2: SharedWorker (if available)
                        if (typeof SharedWorker !== 'undefined') {
                          try {
                            const worker = new SharedWorker('data:application/javascript,self.onconnect=function(e){var port=e.ports[0];port.onmessage=function(e){self.ports.forEach(p=>p.postMessage(e.data))};self.ports=self.ports||[];self.ports.push(port);}');
                            worker.port.postMessage(broadcastData);
                            console.log('👷 Broadcast via SharedWorker:', broadcastData);
                          } catch (workerError) {
                            console.log('SharedWorker not available:', workerError);
                          }
                        }

                      } catch (broadcastError) {
                        console.error('Error broadcasting event:', broadcastError);
                      }
                    } catch (e) {
                      console.error('Error notifying Flutter:', e);
                    }
                  }
                }

                // If the user publishes an audio track
                if (mediaType === 'audio') {
                  // Get the remote audio track
                  const remoteAudioTrack = user.audioTrack;
                  if (remoteAudioTrack) {
                    // Play the remote audio track
                    remoteAudioTrack.play();
                    console.log('Remote audio track played');

                    // Store remote track reference
                    if (!remoteTracks_$sessionId[user.uid]) {
                      remoteTracks_$sessionId[user.uid] = { audio: null, video: null };
                    }
                    remoteTracks_$sessionId[user.uid].audio = user.audioTrack;
                  }
                }
              } catch (subscribeError) {
                console.error('Error subscribing to remote user:', subscribeError);
              }
            });

            // Listen for remote user leaving
            client_$sessionId.on('user-unpublished', (user, mediaType) => {
              console.log('Remote user unpublished:', user.uid, mediaType);

              // Notify Flutter that a remote user has left
              try {
                console.log('Notifying Flutter about remote user left:', user.uid);
                // First try direct postMessage which is more reliable
                window.parent.postMessage({type: 'userLeft', uid: user.uid}, '*');

                // Also try the callHandler method as backup
                if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                  window.flutter_inappwebview.callHandler('onUserLeft', user.uid);
                }
              } catch (e) {
                console.error('Error notifying Flutter:', e);
                // Last resort - try to set a global variable that Flutter can check
                try {
                  window.lastRemoteUserLeft = user.uid;
                  window.lastRemoteUserLeftTime = Date.now();
                } catch (e2) {
                  console.error('Failed to set fallback variable:', e2);
                }
              }

              // Clean up remote tracks
              if (remoteTracks_$sessionId[user.uid]) {
                if (mediaType === 'audio') {
                  remoteTracks_$sessionId[user.uid].audio = null;
                } else if (mediaType === 'video') {
                  remoteTracks_$sessionId[user.uid].video = null;
                }

                if (!remoteTracks_$sessionId[user.uid].audio && !remoteTracks_$sessionId[user.uid].video) {
                  delete remoteTracks_$sessionId[user.uid];
                }
              }
            });

            // Add error event handler
            client_$sessionId.on('exception', (event) => {
              console.error('Agora client exception:', event);
            });

            return true;
          } catch (error) {
            console.error('Error joining channel:', error);

            // Try to provide more detailed error information
            if (error.code) {
              console.error('Error code:', error.code, 'Error message:', error.message);
            }

            // Show a browser alert for common errors
            if (error.code === 'INVALID_PARAMS') {
              alert('Invalid parameters for video call. Please try again.');
            } else if (error.code === 'INVALID_OPERATION') {
              alert('Invalid operation. You might already be in a call.');
            } else if (error.code === 'OPERATION_ABORTED') {
              alert('Operation aborted. Please try again.');
            } else if (error.code === 'PERMISSION_DENIED') {
              alert('Permission denied. Please allow camera and microphone access.');
            } else if (error.code === 'NOT_SUPPORTED') {
              alert('Your browser does not support some features required for video calls.');
            } else if (error.code === 'NETWORK_ERROR') {
              alert('Network error. Please check your connection and try again.');
            } else if (error.code === 'TIMEOUT') {
              alert('Connection timeout. Please try again.');
            } else {
              // Generic error message for other errors
              alert('Error joining video call: ' + (error.message || 'Unknown error'));
            }

            return false;
          }
        }

        // Call the join function
        joinChannel_$sessionId();

        // Store references in window for debugging and cleanup
        window.agoraSession_$sessionId = {
          client: client_$sessionId,
          localTracks: localTracks_$sessionId,
          remoteTracks: remoteTracks_$sessionId,
          channel: window.channel_$sessionId,
          uid: window.uid_$sessionId,
          appId: window.appId_$sessionId
        };
      ''';

      // Execute the script
      html.window.console.log('Executing Agora Web initialization script');

      // Remove any existing script with the same ID to avoid conflicts
      final existingScripts =
          html.document.querySelectorAll('script[id^="agora-web-script"]');
      for (final script in existingScripts) {
        script.remove();
      }

      html.ScriptElement script = html.ScriptElement()
        ..id = 'agora-web-script-$sessionId'
        ..text = initScript
        ..type = 'text/javascript';
      html.document.body?.append(script);

      // Set up multiple listeners for cross-tab communication

      // Method 1: PostMessage listener
      html.window.addEventListener('message', (event) {
        final data = event as html.MessageEvent;
        debugPrint('📨 Web service received postMessage: ${data.data}');
        if (data.data is Map) {
          if (data.data['type'] == 'userJoined') {
            final remoteUid = data.data['uid'] as int;
            debugPrint('🎯 PROCESSING userJoined message for UID: $remoteUid');
            debugPrint('🎯 Current local UID: $uid');
            debugPrint('🎯 Calling onUserJoined callback...');
            onUserJoined?.call(remoteUid);
            debugPrint('🎯 onUserJoined callback completed');
          } else if (data.data['type'] == 'userLeft') {
            final remoteUid = data.data['uid'] as int;
            debugPrint('🎯 PROCESSING userLeft message for UID: $remoteUid');
            onUserLeft?.call(remoteUid);
          }
        }
      });

      // Method 2: Storage event listener (for cross-tab communication)
      html.window.addEventListener('storage', (event) {
        final storageEvent = event as html.StorageEvent;
        debugPrint(
            '💾 Storage event received: ${storageEvent.key} = ${storageEvent.newValue}');

        if (storageEvent.key == 'agoraUserJoinEvent' &&
            storageEvent.newValue != null) {
          try {
            final data = jsonDecode(storageEvent.newValue!);
            if (data['type'] == 'userJoined') {
              final remoteUid = data['uid'] as int;
              final timestamp = data['timestamp'] as int;
              final now = DateTime.now().millisecondsSinceEpoch;

              // Only process recent events (within 30 seconds)
              if (now - timestamp < 30000) {
                debugPrint(
                    '🎯 PROCESSING storage userJoined for UID: $remoteUid');
                onUserJoined?.call(remoteUid);
              }
            }
          } catch (e) {
            debugPrint('Error parsing storage event: $e');
          }
        }
      });

      // Method 3: BroadcastChannel listener
      const broadcastScript = '''
        try {
          if (typeof BroadcastChannel !== 'undefined') {
            const channel = new BroadcastChannel('agora_video_channel');
            channel.onmessage = function(event) {
              console.log('📡 BroadcastChannel received:', event.data);
              if (event.data.type === 'userJoined') {
                window.postMessage({type: 'userJoined', uid: event.data.uid}, '*');
              }
            };
            console.log('📡 BroadcastChannel listener set up');
          }
        } catch (e) {
          console.error('Error setting up BroadcastChannel:', e);
        }
      ''';

      final broadcastScriptElement = html.ScriptElement()
        ..text = broadcastScript
        ..type = 'text/javascript';
      html.document.body?.append(broadcastScriptElement);

      // Set up a more aggressive polling mechanism to check for remote users
      // Cancel any existing timer first
      _pollingTimer?.cancel();
      _pollingTimer =
          Timer.periodic(const Duration(milliseconds: 500), (timer) {
        // More frequent polling
        try {
          // Generate unique timestamp for this polling check
          final pollingTimestamp = DateTime.now().millisecondsSinceEpoch;

          final checkScript = '''
            (function() {
              // Use immediately invoked function expression to avoid variable conflicts
              try {
                // 🚨 FIRST: Check immediate detection flag
                if (window.agoraRemoteUserDetected && !window.agoraRemoteUserDetected.processed) {
                  console.log('🚨 IMMEDIATE DETECTION FLAG FOUND:', window.agoraRemoteUserDetected);
                  var uid = window.agoraRemoteUserDetected.uid;
                  window.agoraRemoteUserDetected.processed = true;

                  // Notify Flutter immediately
                  console.log('🚨 NOTIFYING FLUTTER IMMEDIATELY for UID:', uid);
                  window.postMessage({type: 'userJoined', uid: uid, source: 'immediate'}, '*');
                  if (window.parent !== window) {
                    window.parent.postMessage({type: 'userJoined', uid: uid, source: 'immediate'}, '*');
                  }
                  return; // Exit early since we found what we need
                }
                // Check if we have any remote users
                var sessions_$pollingTimestamp = Object.keys(window).filter(function(key) {
                  return key.startsWith('agoraSession_');
                });

                for (var i = 0; i < sessions_$pollingTimestamp.length; i++) {
                  var sessionKey = sessions_$pollingTimestamp[i];
                  var session = window[sessionKey];
                  if (session && session.remoteTracks) {
                    var remoteUids = Object.keys(session.remoteTracks);
                    if (remoteUids.length > 0) {
                      console.log('Found remote users in polling:', remoteUids);
                      for (var j = 0; j < remoteUids.length; j++) {
                        var uid = remoteUids[j];
                        window.parent.postMessage({type: 'userJoined', uid: parseInt(uid)}, '*');
                      }
                    }
                  }
                }

                // Also check the fallback variables
                if (window.lastRemoteUserJoined && window.lastRemoteUserJoinedTime) {
                  var timeSinceJoin = Date.now() - window.lastRemoteUserJoinedTime;
                  if (timeSinceJoin < 30000) { // Extended to 30 seconds
                    console.log('🔄 POLLING: Found recent remote user:', window.lastRemoteUserJoined);
                    // Try all notification methods
                    window.postMessage({type: 'userJoined', uid: window.lastRemoteUserJoined}, '*');
                    if (window.parent !== window) {
                      window.parent.postMessage({type: 'userJoined', uid: window.lastRemoteUserJoined}, '*');
                    }
                    if (window.top !== window) {
                      window.top.postMessage({type: 'userJoined', uid: window.lastRemoteUserJoined}, '*');
                    }
                  }
                }

                // Force check all Agora clients for remote users
                if (typeof AgoraRTC !== 'undefined') {
                  console.log('🔄 POLLING: Checking all Agora clients...');
                  // This is a more aggressive check
                }
              } catch (e) {
                console.error('Error in remote user polling:', e);
              }
            })();
          ''';

          final scriptElement = html.ScriptElement()
            ..id = 'remote-user-check-$pollingTimestamp'
            ..text = checkScript
            ..type = 'text/javascript';
          html.document.body?.append(scriptElement);

          // Clean up the script element immediately after execution
          Future.delayed(const Duration(milliseconds: 50), () {
            try {
              scriptElement.remove();
            } catch (e) {
              debugPrint('Error removing polling script: $e');
            }
          });
        } catch (e) {
          debugPrint('Error in remote user polling: $e');
        }
      });

      return true;
    } catch (e) {
      debugPrint('Error joining channel: $e');
      return false;
    }
  }

  /// Leave the channel
  Future<void> leaveChannel() async {
    try {
      // No need for a session ID in the leave method as we're cleaning up all sessions
      debugPrint('Leaving Agora Web channel');

      const leaveScript = '''
        // Find all Agora sessions in the window object
        const sessions = Object.keys(window).filter(key => key.startsWith('agoraSession_'));
        console.log('Found ' + sessions.length + ' Agora sessions to clean up');

        // Leave all active channels
        for (const sessionKey of sessions) {
          const session = window[sessionKey];
          if (session && session.client) {
            try {
              console.log('Cleaning up session:', sessionKey);

              // Destroy local tracks
              if (session.localTracks) {
                if (session.localTracks.audioTrack) {
                  console.log('Closing audio track');
                  try {
                    session.localTracks.audioTrack.close();
                  } catch (e) {
                    console.error('Error closing audio track:', e);
                  }
                }
                if (session.localTracks.videoTrack) {
                  console.log('Closing video track');
                  try {
                    session.localTracks.videoTrack.close();
                  } catch (e) {
                    console.error('Error closing video track:', e);
                  }
                }
              }

              // Unpublish all local tracks before leaving
              try {
                if (session.client.localTracks && session.client.localTracks.length > 0) {
                  console.log('Unpublishing local tracks');
                  session.client.unpublish(session.client.localTracks);
                }
              } catch (unpublishError) {
                console.error('Error unpublishing tracks:', unpublishError);
              }

              // Leave the channel
              console.log('Leaving channel for session:', sessionKey);
              try {
                session.client.leave().then(() => {
                  console.log('Left channel successfully for session:', sessionKey);

                  // Notify Flutter that the call has ended
                  try {
                    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                      window.flutter_inappwebview.callHandler('onCallEnded');
                    } else {
                      console.log('Flutter handler not available, posting message instead');
                      window.postMessage({type: 'callEnded'}, '*');
                    }
                  } catch (e) {
                    console.error('Error notifying Flutter about call end:', e);
                  }

                }).catch(error => {
                  console.error('Error leaving channel for session:', sessionKey, error);
                });
              } catch (leaveError) {
                console.error('Error calling leave() method:', leaveError);

                // Still try to notify Flutter even if leave() failed
                try {
                  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                    window.flutter_inappwebview.callHandler('onCallEnded');
                  } else {
                    console.log('Flutter handler not available, posting message instead');
                    window.postMessage({type: 'callEnded'}, '*');
                  }
                } catch (e) {
                  console.error('Error notifying Flutter about call end:', e);
                }
              }
            } catch (error) {
              console.error('Error cleaning up session:', sessionKey, error);
            }

            // Clean up the session reference
            console.log('Deleting session reference:', sessionKey);
            delete window[sessionKey];
          }
        }

        // Clean up any remaining event listeners
        try {
          window.removeEventListener('beforeunload', null);
        } catch (e) {
          console.error('Error removing event listeners:', e);
        }

        console.log('Agora Web cleanup completed');
      ''';

      // Execute the script
      html.window.console.log('Executing Agora Web leave script');

      // Remove any existing script with the same ID to avoid conflicts
      final existingScript =
          html.document.getElementById('agora-web-leave-script');
      if (existingScript != null) {
        existingScript.remove();
      }

      html.ScriptElement script = html.ScriptElement()
        ..id = 'agora-web-leave-script'
        ..text = leaveScript
        ..type = 'text/javascript';
      html.document.body?.append(script);

      // Set up a listener for call ended event
      html.window.addEventListener('message', (event) {
        final data = event as html.MessageEvent;
        if (data.data is Map && data.data['type'] == 'callEnded') {
          debugPrint('Received callEnded event from JavaScript');
          // This will help us know if the event is being received
        }
      });

      // Remove the video containers
      if (_localVideoContainer != null) {
        debugPrint('Removing local video container');
        _localVideoContainer?.remove();
        _localVideoContainer = null;
      }

      if (_remoteVideoContainer != null) {
        debugPrint('Removing remote video container');
        _remoteVideoContainer?.remove();
        _remoteVideoContainer = null;
      }

      // Cancel polling timer
      _pollingTimer?.cancel();
      _pollingTimer = null;

      // Reset state
      channelName = null;
      uid = null;

      debugPrint('Agora Web channel left successfully');
    } catch (e) {
      debugPrint('Error leaving channel: $e');
    }
  }

  // Track audio state for web
  bool _webAudioMuted = false;

  /// Toggle local audio (mute/unmute)
  Future<bool> toggleLocalAudio() async {
    try {
      debugPrint('Toggling audio in Agora Web');

      // Create a completer to get the result from JavaScript
      final completer = Completer<bool>();

      // Execute JavaScript to toggle audio directly
      const toggleScript = '''
        try {
          if (window.agoraWebRTC && typeof window.agoraWebRTC.toggleAudio === 'function') {
            const result = window.agoraWebRTC.toggleAudio();
            window.parent.postMessage({ type: 'audioToggleResult', muted: !result }, '*');
            console.log('Audio toggle result:', result);
          } else {
            console.error('agoraWebRTC.toggleAudio function not found');
            window.parent.postMessage({ type: 'audioToggleResult', muted: true, error: 'Function not found' }, '*');
          }
        } catch (e) {
          console.error('Error in audio toggle script:', e);
          window.parent.postMessage({ type: 'audioToggleResult', muted: true, error: e.toString() }, '*');
        }
      ''';

      // Set up a listener for the result
      final listener = html.window.onMessage.listen((event) {
        final data = event;
        if (data.data is Map && data.data['type'] == 'audioToggleResult') {
          _webAudioMuted = data.data['muted'] as bool;
          if (!completer.isCompleted) {
            completer.complete(!_webAudioMuted);
          }
        }
      });

      // Execute the script
      final scriptElement = html.ScriptElement()
        ..id = 'audio-toggle-script-${DateTime.now().millisecondsSinceEpoch}'
        ..text = toggleScript
        ..type = 'text/javascript';
      html.document.body?.append(scriptElement);

      // Clean up the script element
      Future.delayed(Duration.zero, () {
        scriptElement.remove();
      });

      // Add a visual indicator
      final debugElement = html.DivElement()
        ..id = 'audio-toggle-debug'
        ..style.position = 'fixed'
        ..style.bottom = '80px'
        ..style.left = '10px'
        ..style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
        ..style.color = 'white'
        ..style.padding = '5px'
        ..style.borderRadius = '5px'
        ..style.zIndex = '9999'
        ..style.fontSize = '12px'
        ..text = 'Toggling audio...';
      html.document.body?.append(debugElement);

      // Wait for the result with a timeout
      bool result;
      try {
        result = await completer.future.timeout(const Duration(seconds: 2),
            onTimeout: () {
          debugPrint('Timeout waiting for audio toggle result');
          return !_webAudioMuted; // Return the toggled state as a fallback
        });
      } finally {
        // Clean up the listener
        listener.cancel();

        // Update the debug element
        final debugEl = html.document.getElementById('audio-toggle-debug');
        if (debugEl != null) {
          debugEl.text = 'Audio ${_webAudioMuted ? "muted" : "unmuted"}';

          // Remove after a short delay
          Future.delayed(const Duration(milliseconds: 1500), () {
            debugEl.remove();
          });
        }
      }

      return result;
    } catch (e) {
      debugPrint('Error toggling audio: $e');
      return false;
    }
  }

  // Track video state for web
  bool _webVideoMuted = false;

  /// Toggle local video (enable/disable)
  Future<bool> toggleLocalVideo() async {
    try {
      debugPrint('Toggling video in Agora Web');

      // Create a completer to get the result from JavaScript
      final completer = Completer<bool>();

      // Execute JavaScript to toggle video directly
      const toggleScript = '''
        try {
          if (window.agoraWebRTC && typeof window.agoraWebRTC.toggleVideo === 'function') {
            const result = window.agoraWebRTC.toggleVideo();
            window.parent.postMessage({ type: 'videoToggleResult', muted: !result }, '*');
            console.log('Video toggle result:', result);
          } else {
            console.error('agoraWebRTC.toggleVideo function not found');
            window.parent.postMessage({ type: 'videoToggleResult', muted: true, error: 'Function not found' }, '*');
          }
        } catch (e) {
          console.error('Error in video toggle script:', e);
          window.parent.postMessage({ type: 'videoToggleResult', muted: true, error: e.toString() }, '*');
        }
      ''';

      // Set up a listener for the result
      final listener = html.window.onMessage.listen((event) {
        final data = event;
        if (data.data is Map && data.data['type'] == 'videoToggleResult') {
          _webVideoMuted = data.data['muted'] as bool;
          if (!completer.isCompleted) {
            completer.complete(!_webVideoMuted);
          }
        }
      });

      // Execute the script
      final scriptElement = html.ScriptElement()
        ..id = 'video-toggle-script-${DateTime.now().millisecondsSinceEpoch}'
        ..text = toggleScript
        ..type = 'text/javascript';
      html.document.body?.append(scriptElement);

      // Clean up the script element
      Future.delayed(Duration.zero, () {
        scriptElement.remove();
      });

      // Add a visual indicator
      final debugElement = html.DivElement()
        ..id = 'video-toggle-debug'
        ..style.position = 'fixed'
        ..style.bottom = '110px'
        ..style.left = '10px'
        ..style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
        ..style.color = 'white'
        ..style.padding = '5px'
        ..style.borderRadius = '5px'
        ..style.zIndex = '9999'
        ..style.fontSize = '12px'
        ..text = 'Toggling video...';
      html.document.body?.append(debugElement);

      // Wait for the result with a timeout
      bool result;
      try {
        result = await completer.future.timeout(const Duration(seconds: 2),
            onTimeout: () {
          debugPrint('Timeout waiting for video toggle result');
          _webVideoMuted = !_webVideoMuted; // Toggle the state as a fallback
          return !_webVideoMuted;
        });
      } finally {
        // Clean up the listener
        listener.cancel();

        // Update the debug element
        final debugEl = html.document.getElementById('video-toggle-debug');
        if (debugEl != null) {
          debugEl.text = 'Video ${_webVideoMuted ? "disabled" : "enabled"}';

          // Remove after a short delay
          Future.delayed(const Duration(milliseconds: 1500), () {
            debugEl.remove();
          });
        }
      }

      return result;
    } catch (e) {
      debugPrint('Error toggling video: $e');
      return false;
    }
  }

  /// Switch camera
  Future<void> switchCamera() async {
    try {
      debugPrint('Switching camera in Agora Web');

      // Create a completer to get the result from JavaScript
      final completer = Completer<bool>();

      // Execute JavaScript to switch camera directly
      const switchScript = '''
        try {
          if (window.agoraWebRTC && typeof window.agoraWebRTC.switchCamera === 'function') {
            window.agoraWebRTC.switchCamera()
              .then(result => {
                window.parent.postMessage({ type: 'cameraSwitchResult', success: true }, '*');
                console.log('Camera switched successfully');
              })
              .catch(error => {
                window.parent.postMessage({ type: 'cameraSwitchResult', success: false, error: error.toString() }, '*');
                console.error('Error switching camera:', error);
              });
          } else {
            console.error('agoraWebRTC.switchCamera function not found');
            window.parent.postMessage({ type: 'cameraSwitchResult', success: false, error: 'Function not found' }, '*');
          }
        } catch (e) {
          console.error('Error in camera switch script:', e);
          window.parent.postMessage({ type: 'cameraSwitchResult', success: false, error: e.toString() }, '*');
        }
      ''';

      // Set up a listener for the result
      final listener = html.window.onMessage.listen((event) {
        final data = event;
        if (data.data is Map && data.data['type'] == 'cameraSwitchResult') {
          final success = data.data['success'] as bool? ?? false;
          if (!completer.isCompleted) {
            completer.complete(success);
          }
        }
      });

      // Execute the script
      final scriptElement = html.ScriptElement()
        ..id = 'camera-switch-script-${DateTime.now().millisecondsSinceEpoch}'
        ..text = switchScript
        ..type = 'text/javascript';
      html.document.body?.append(scriptElement);

      // Clean up the script element
      Future.delayed(Duration.zero, () {
        scriptElement.remove();
      });

      // Add a visual indicator
      final debugElement = html.DivElement()
        ..id = 'camera-switch-debug'
        ..style.position = 'fixed'
        ..style.bottom = '140px'
        ..style.left = '10px'
        ..style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
        ..style.color = 'white'
        ..style.padding = '5px'
        ..style.borderRadius = '5px'
        ..style.zIndex = '9999'
        ..style.fontSize = '12px'
        ..text = 'Switching camera...';
      html.document.body?.append(debugElement);

      // Wait for the result with a timeout
      try {
        await completer.future.timeout(const Duration(seconds: 5),
            onTimeout: () {
          debugPrint('Timeout waiting for camera switch result');
          return false;
        });
      } finally {
        // Clean up the listener
        listener.cancel();

        // Update and remove the debug element after a short delay
        Future.delayed(const Duration(milliseconds: 2000), () {
          final debugEl = html.document.getElementById('camera-switch-debug');
          if (debugEl != null) {
            debugEl.remove();
          }
        });
      }
    } catch (e) {
      debugPrint('Error switching camera: $e');
    }
  }

  /// Start audio recording (provider only)
  Future<bool> startRecording(String appointmentId) async {
    try {
      debugPrint('Starting recording for appointment: $appointmentId');

      // Call the API to start recording
      final response = await _apiService.startRecording(appointmentId);

      // Check if the recording was started successfully
      final success = response['success'] ?? false;

      if (success) {
        debugPrint('Recording started successfully');

        // Execute JavaScript to show recording indicator
        const recordingScript = '''
          // Create or show recording indicator
          let recordingIndicator = document.getElementById('recording-indicator');

          if (!recordingIndicator) {
            // Create the indicator if it doesn't exist
            recordingIndicator = document.createElement('div');
            recordingIndicator.id = 'recording-indicator';
            recordingIndicator.style.position = 'absolute';
            recordingIndicator.style.top = '20px';
            recordingIndicator.style.left = '20px';
            recordingIndicator.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
            recordingIndicator.style.color = 'white';
            recordingIndicator.style.padding = '5px 10px';
            recordingIndicator.style.borderRadius = '5px';
            recordingIndicator.style.display = 'flex';
            recordingIndicator.style.alignItems = 'center';
            recordingIndicator.style.zIndex = '100';

            // Create the recording dot
            const dot = document.createElement('div');
            dot.style.width = '10px';
            dot.style.height = '10px';
            dot.style.borderRadius = '50%';
            dot.style.backgroundColor = 'red';
            dot.style.marginRight = '5px';
            dot.style.animation = 'pulse 1.5s infinite';

            // Add the animation
            const style = document.createElement('style');
            style.textContent = `
              @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
              }
            `;
            document.head.appendChild(style);

            recordingIndicator.appendChild(dot);

            // Add the text
            const text = document.createElement('span');
            text.textContent = 'Recording';
            recordingIndicator.appendChild(text);

            // Add to the document
            document.body.appendChild(recordingIndicator);
          } else {
            // Show the indicator if it exists
            recordingIndicator.style.display = 'flex';
          }
        ''';

        // Execute the script
        html.window.console.log('Executing recording indicator script');

        // Create a unique ID for this script
        final scriptId =
            'agora-recording-start-${DateTime.now().millisecondsSinceEpoch}';

        // Add a debug element to show the status
        final debugElement = html.DivElement()
          ..id = 'recording-start-debug'
          ..style.position = 'fixed'
          ..style.bottom = '170px'
          ..style.left = '10px'
          ..style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          ..style.color = 'white'
          ..style.padding = '5px'
          ..style.borderRadius = '5px'
          ..style.zIndex = '9999'
          ..style.fontSize = '12px'
          ..text = 'Starting recording...';
        html.document.body?.append(debugElement);

        html.ScriptElement script = html.ScriptElement()
          ..id = scriptId
          ..text = recordingScript
          ..type = 'text/javascript';
        html.document.body?.append(script);

        // Clean up the script after execution
        Future.delayed(const Duration(milliseconds: 1500), () {
          final scriptElement = html.document.getElementById(scriptId);
          if (scriptElement != null) {
            scriptElement.remove();
          }

          final debugEl = html.document.getElementById('recording-start-debug');
          if (debugEl != null) {
            debugEl.remove();
          }
        });
      } else {
        debugPrint('Failed to start recording: ${response['message']}');
      }

      return success;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      return false;
    }
  }

  /// Stop audio recording (provider only)
  Future<bool> stopRecording(String appointmentId) async {
    try {
      debugPrint('Stopping recording for appointment: $appointmentId');

      // Call the API to stop recording
      final response = await _apiService.stopRecording(appointmentId);

      // Check if the recording was stopped successfully
      final success = response['success'] ?? false;

      if (success) {
        debugPrint('Recording stopped successfully');

        // Execute JavaScript to hide recording indicator
        const recordingScript = '''
          // Hide recording indicator
          const recordingIndicator = document.getElementById('recording-indicator');

          if (recordingIndicator) {
            recordingIndicator.style.display = 'none';
          }
        ''';

        // Execute the script
        html.window.console.log('Executing recording indicator hide script');

        // Create a unique ID for this script
        final scriptId =
            'agora-recording-stop-${DateTime.now().millisecondsSinceEpoch}';

        // Add a debug element to show the status
        final debugElement = html.DivElement()
          ..id = 'recording-stop-debug'
          ..style.position = 'fixed'
          ..style.bottom = '170px'
          ..style.left = '10px'
          ..style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          ..style.color = 'white'
          ..style.padding = '5px'
          ..style.borderRadius = '5px'
          ..style.zIndex = '9999'
          ..style.fontSize = '12px'
          ..text = 'Stopping recording...';
        html.document.body?.append(debugElement);

        html.ScriptElement script = html.ScriptElement()
          ..id = scriptId
          ..text = recordingScript
          ..type = 'text/javascript';
        html.document.body?.append(script);

        // Clean up the script after execution
        Future.delayed(const Duration(milliseconds: 1500), () {
          final scriptElement = html.document.getElementById(scriptId);
          if (scriptElement != null) {
            scriptElement.remove();
          }

          final debugEl = html.document.getElementById('recording-stop-debug');
          if (debugEl != null) {
            debugEl.remove();
          }
        });
      } else {
        debugPrint('Failed to stop recording: ${response['message']}');
      }

      return success;
    } catch (e) {
      debugPrint('Error stopping recording: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    debugPrint('Disposing Agora Web service');

    // Cancel polling timer first
    _pollingTimer?.cancel();
    _pollingTimer = null;

    leaveChannel();

    // These might already be removed in leaveChannel, but let's make sure
    if (_localVideoContainer != null) {
      _localVideoContainer?.remove();
      _localVideoContainer = null;
    }

    if (_remoteVideoContainer != null) {
      _remoteVideoContainer?.remove();
      _remoteVideoContainer = null;
    }

    // Reset callbacks
    onUserJoined = null;
    onUserLeft = null;
    onConnectionStateChanged = null;
    onError = null;

    // Reset state
    channelName = null;
    uid = null;
    _isInitialized = false;

    debugPrint('Agora Web service disposed');
  }
}
