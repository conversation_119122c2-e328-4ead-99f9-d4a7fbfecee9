// This is a stub implementation for non-web platforms
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:medroid_app/services/api_service.dart';

/// A stub service for non-web platforms
class AgoraWebService {
  final ApiService _apiService;

  // Channel name for the current session
  String? channelName;

  // User ID for the current user
  int? uid;

  // Callbacks - using dynamic types for web compatibility
  Function(int uid)? onUserJoined;
  Function(int uid)? onUserLeft;
  Function(dynamic connection, dynamic state)? onConnectionStateChanged;
  Function(dynamic err, String msg)? onError;

  // Constructor
  AgoraWebService(this._apiService);

  /// Initialize the Agora engine for web (stub implementation)
  Future<void> initialize() async {
    debugPrint('AgoraWebService stub: initialize() called');
    // This is a stub implementation, so we don't do anything
    return;
  }

  /// Join a video consultation channel (stub implementation)
  Future<bool> joinChannel({
    required String appointmentId,
    required bool isProvider,
  }) async {
    debugPrint('AgoraWebService stub: joinChannel() called');
    // This is a stub implementation, so we always return false
    return false;
  }

  /// Leave the channel (stub implementation)
  Future<void> leaveChannel() async {
    debugPrint('AgoraWebService stub: leaveChannel() called');
    // This is a stub implementation, so we don't do anything
    return;
  }

  /// Toggle local audio (mute/unmute) (stub implementation)
  Future<bool> toggleLocalAudio() async {
    debugPrint('AgoraWebService stub: toggleLocalAudio() called');
    // This is a stub implementation, so we always return false
    return false;
  }

  /// Toggle local video (enable/disable) (stub implementation)
  Future<bool> toggleLocalVideo() async {
    debugPrint('AgoraWebService stub: toggleLocalVideo() called');
    // This is a stub implementation, so we always return false
    return false;
  }

  /// Switch camera (stub implementation)
  Future<void> switchCamera() async {
    debugPrint('AgoraWebService stub: switchCamera() called');
    // This is a stub implementation, so we don't do anything
    return;
  }

  /// Start audio recording (provider only) (stub implementation)
  Future<bool> startRecording(String appointmentId) async {
    debugPrint('AgoraWebService stub: startRecording() called');
    // This is a stub implementation, so we always return false
    return false;
  }

  /// Stop audio recording (provider only) (stub implementation)
  Future<bool> stopRecording(String appointmentId) async {
    debugPrint('AgoraWebService stub: stopRecording() called');
    // This is a stub implementation, so we always return false
    return false;
  }

  /// Dispose resources (stub implementation)
  void dispose() {
    debugPrint('AgoraWebService stub: dispose() called');
    // This is a stub implementation, so we don't do anything
    return;
  }
}
