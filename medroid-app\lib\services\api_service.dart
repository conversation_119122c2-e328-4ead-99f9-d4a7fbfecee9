import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/models/feed_models.dart';

class ApiService {
  String? _authToken;

  ApiService() {
    _loadToken();
  }

  Future<void> _loadToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(Constants.tokenKey);

      // Only update if token is different or not set
      if (token != _authToken) {
        _authToken = token;
        debugPrint(
            'Auth token loaded${_authToken != null ? ' (valid)' : ' (none)'}');
      }
    } catch (e) {
      debugPrint('Error loading auth token: $e');
    }
  }

  Future<void> setAuthToken(String token) async {
    if (token.isEmpty) {
      debugPrint('Warning: Attempted to save empty token');
      return;
    }

    try {
      _authToken = token;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(Constants.tokenKey, token);

      // Also save the token timestamp for expiration checking
      await prefs.setString(
          'auth_token_timestamp', DateTime.now().toIso8601String());

      debugPrint('Auth token saved successfully');
    } catch (e) {
      debugPrint('Error saving auth token: $e');
    }
  }

  Future<void> clearAuthToken() async {
    try {
      _authToken = null;
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(Constants.tokenKey);
      await prefs.remove('auth_token_timestamp');
      debugPrint('Auth token cleared');
    } catch (e) {
      debugPrint('Error clearing auth token: $e');
    }
  }

  // Check if token is valid and not expired
  Future<bool> isTokenValid() async {
    await _loadToken();

    if (_authToken == null || _authToken!.isEmpty) {
      return false;
    }

    try {
      // Check token timestamp if available
      final prefs = await SharedPreferences.getInstance();
      final timestampStr = prefs.getString('auth_token_timestamp');

      if (timestampStr != null) {
        final timestamp = DateTime.parse(timestampStr);
        final now = DateTime.now();

        // If token is older than 7 days, consider it potentially expired
        if (now.difference(timestamp).inDays > 7) {
          debugPrint('Token is older than 7 days, validating with server');

          // Validate with server by making a simple request
          try {
            await get(Constants.userEndpoint);
            // If we get here, token is still valid
            // Update the timestamp
            await prefs.setString(
                'auth_token_timestamp', now.toIso8601String());
            return true;
          } catch (e) {
            // If we get an unauthorized error, token is invalid
            if (e.toString().contains('unauthorized') ||
                e.toString().contains('Unauthorized') ||
                e.toString().contains('401')) {
              debugPrint('Token validation failed: $e');
              await clearAuthToken();
              return false;
            }
            // For other errors, assume token is still valid
            return true;
          }
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error checking token validity: $e');
      return false;
    }
  }

  Map<String, String> _headers({bool requiresAuth = true}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (requiresAuth && _authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  Future<dynamic> get(String endpoint, {bool requiresAuth = true}) async {
    try {
      // Always reload token to ensure it's the latest
      if (requiresAuth) {
        await _loadToken();

        // Check if token exists
        if (_authToken == null || _authToken!.isEmpty) {
          debugPrint('No auth token available for GET request to $endpoint');
          throw Exception('Authentication required for this operation');
        }

        // Validate token if required
        if (!await isTokenValid()) {
          debugPrint('Invalid auth token for GET request to $endpoint');
          throw Exception('Authentication required for this operation');
        }
      }

      final headers = _headers(requiresAuth: requiresAuth);
      debugPrint('GET request headers: $headers');
      debugPrint('GET request URL: ${Constants.baseUrl}$endpoint');

      // Add a timeout to the request
      final response = await http
          .get(
        Uri.parse('${Constants.baseUrl}$endpoint'),
        headers: headers,
      )
          .timeout(
        const Duration(seconds: Constants.connectionTimeoutSeconds),
        onTimeout: () {
          debugPrint(
              'GET request timed out for: ${Constants.baseUrl}$endpoint');
          throw Exception(
              'Request timed out. Please check your connection and try again.');
        },
      );

      debugPrint('GET response status: ${response.statusCode}');
      if (response.body.length < 1000) {
        debugPrint('GET response body: ${response.body}');
      } else {
        debugPrint(
            'GET response body: (large response, length: ${response.body.length})');
      }

      return _handleResponse(response);
    } on SocketException catch (e) {
      debugPrint('SocketException during GET request: $e');
      debugPrint('Request URL: ${Constants.baseUrl}$endpoint');
      debugPrint('Base URL configured as: ${Constants.baseUrl}');
      throw Exception('${Constants.networkErrorMessage} (${e.message})');
    } catch (e) {
      debugPrint('Error during GET request: $e');
      debugPrint('Request URL: ${Constants.baseUrl}$endpoint');
      debugPrint('Error type: ${e.runtimeType}');

      // Check if it's a network-related error
      if (e.toString().contains('XMLHttpRequest') ||
          e.toString().contains('Network') ||
          e.toString().contains('Connection') ||
          e.toString().contains('timeout')) {
        throw Exception(
            'Network error: Unable to connect to server. Please check your internet connection and try again.');
      }

      throw Exception(e.toString());
    }
  }

  Future<dynamic> post(String endpoint, dynamic data,
      {bool requiresAuth = true, int? timeoutSeconds}) async {
    try {
      // Always reload token to ensure it's the latest
      if (requiresAuth) {
        await _loadToken();

        // Validate token if required
        if (requiresAuth && !await isTokenValid()) {
          throw Exception('Authentication required for this operation');
        }
      }

      // Log request for debugging
      debugPrint('Sending POST request to: ${Constants.baseUrl}$endpoint');
      if (data != null) {
        try {
          debugPrint('Request data: ${json.encode(data)}');
        } catch (e) {
          debugPrint('Request data: (could not encode)');
        }
      }

      final headers = _headers(requiresAuth: requiresAuth);
      debugPrint('Headers: $headers');

      // Use the real backend API for all endpoints
      String url = '${Constants.baseUrl}$endpoint';

      final response = await http
          .post(
        Uri.parse(url),
        body: json.encode(data),
        headers: headers,
      )
          .timeout(
        Duration(seconds: timeoutSeconds ?? Constants.connectionTimeoutSeconds),
        onTimeout: () {
          throw Exception(
              'Request timed out. Please check your connection and try again.');
        },
      );

      // Log response for debugging
      debugPrint('Response status: ${response.statusCode}');
      if (response.body.length < 1000) {
        debugPrint('Response body: ${response.body}');
      } else {
        debugPrint(
            'Response body: (large response, length: ${response.body.length})');
      }

      if (response.statusCode >= 400) {
        debugPrint('Error response received: ${response.statusCode}');
        if (response.body.isNotEmpty) {
          try {
            final errorData = json.decode(response.body);
            debugPrint('Error data: $errorData');
          } catch (e) {
            debugPrint('Could not parse error response: $e');
          }
        }
      }

      return _handleResponse(response);
    } on SocketException {
      debugPrint('Network error: Could not connect to server');
      throw Exception(Constants.networkErrorMessage);
    } catch (e) {
      debugPrint('Error during POST request: $e');
      throw Exception(e.toString());
    }
  }

  Future<dynamic> put(String endpoint, dynamic data,
      {bool requiresAuth = true}) async {
    try {
      // Always reload token to ensure it's the latest
      if (requiresAuth) {
        await _loadToken();

        // Validate token if required
        if (requiresAuth && !await isTokenValid()) {
          throw Exception('Authentication required for this operation');
        }
      }

      final headers = _headers(requiresAuth: requiresAuth);
      debugPrint('PUT request headers: $headers');

      final response = await http.put(
        Uri.parse('${Constants.baseUrl}$endpoint'),
        body: json.encode(data),
        headers: headers,
      );

      return _handleResponse(response);
    } on SocketException {
      throw Exception(Constants.networkErrorMessage);
    } catch (e) {
      debugPrint('Error during PUT request: $e');
      throw Exception(e.toString());
    }
  }

  Future<dynamic> delete(String endpoint, {bool requiresAuth = true}) async {
    try {
      // Always reload token to ensure it's the latest
      if (requiresAuth) {
        await _loadToken();

        // Validate token if required
        if (!await isTokenValid()) {
          debugPrint(
              'Error: No valid auth token available for DELETE request to $endpoint');
          throw Exception('Authentication required for this operation');
        }
      }

      final headers = _headers(requiresAuth: requiresAuth);
      debugPrint('DELETE request to: ${Constants.baseUrl}$endpoint');
      debugPrint('DELETE request headers: $headers');

      final response = await http
          .delete(
        Uri.parse('${Constants.baseUrl}$endpoint'),
        headers: headers,
      )
          .timeout(
        const Duration(seconds: Constants.connectionTimeoutSeconds),
        onTimeout: () {
          debugPrint(
              'DELETE request timed out for: ${Constants.baseUrl}$endpoint');
          throw Exception(
              'Request timed out. Please check your connection and try again.');
        },
      );

      // Log response status for debugging
      debugPrint('DELETE response status: ${response.statusCode}');
      if (response.statusCode >= 400) {
        debugPrint('DELETE response body: ${response.body}');
      }

      return _handleResponse(response);
    } on SocketException {
      debugPrint('Network error during DELETE request');
      throw Exception(Constants.networkErrorMessage);
    } catch (e) {
      debugPrint('Error during DELETE request: $e');
      throw Exception(e.toString());
    }
  }

  dynamic _handleResponse(http.Response response) {
    // Check if the response is HTML instead of JSON
    bool isHtmlResponse = _isHtmlResponse(response.body);
    if (isHtmlResponse) {
      debugPrint('Received HTML response instead of JSON');
      String errorMessage = _extractErrorFromHtml(response.body);
      throw Exception('Server returned HTML instead of JSON. $errorMessage');
    }

    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) return null;

      try {
        return json.decode(response.body);
      } catch (e) {
        debugPrint('Error parsing JSON response: $e');
        throw Exception(
            'Invalid JSON response from server. Please check your connection and try again.');
      }
    } else if (response.statusCode == 401) {
      // Clear the token on unauthorized response
      clearAuthToken();
      debugPrint('Unauthorized response (401): ${response.body}');
      throw Exception(Constants.unauthorizedErrorMessage);
    } else if (response.statusCode == 403) {
      // Handle forbidden errors (authorization issues)
      debugPrint('Forbidden response (403): ${response.body}');

      if (response.body.isNotEmpty) {
        try {
          final errorData = json.decode(response.body);
          if (errorData.containsKey('message')) {
            throw Exception(errorData['message']);
          }
        } catch (e) {
          // If we can't parse the JSON, just use the raw body
          if (e is! FormatException) rethrow;
        }
      }

      throw Exception('You do not have permission to perform this action');
    } else if (response.statusCode == 422) {
      // Handle validation errors specifically
      debugPrint('Validation error response (422): ${response.body}');

      if (response.body.isNotEmpty) {
        try {
          final errorData = json.decode(response.body);
          debugPrint('Validation error data: $errorData');

          if (errorData.containsKey('errors')) {
            final errors = errorData['errors'];
            if (errors is Map) {
              // Handle validation errors
              final errorMessages = errors.values
                  .map((e) => e is List ? e.join(', ') : e.toString())
                  .join(', ');
              throw Exception('Validation error: $errorMessages');
            } else {
              throw Exception('Validation error: ${errors.toString()}');
            }
          } else if (errorData.containsKey('message')) {
            throw Exception('Validation error: ${errorData['message']}');
          }

          // If we can't extract a specific error message, throw a generic validation error
          throw Exception('Validation error: Please check your input data');
        } catch (e) {
          if (e is Exception && e is! FormatException) rethrow;
          throw Exception('Validation error occurred');
        }
      }
      throw Exception('Validation error: Please check your input data');
    } else {
      debugPrint('Error response (${response.statusCode}): ${response.body}');

      if (response.body.isNotEmpty) {
        try {
          final errorData = json.decode(response.body);
          debugPrint('Error response data: $errorData');

          if (errorData.containsKey('message')) {
            // Check if the error is related to token expiration
            String errorMessage = errorData['message'];
            if (errorMessage.contains('token') &&
                (errorMessage.contains('expired') ||
                    errorMessage.contains('invalid') ||
                    errorMessage.contains('denied'))) {
              // Clear the token if it's expired or invalid
              clearAuthToken();
            }
            throw Exception(errorMessage);
          } else if (errorData.containsKey('errors')) {
            final errors = errorData['errors'];
            if (errors is Map) {
              // Handle validation errors
              final errorMessages = errors.values
                  .map((e) => e is List ? e.join(', ') : e.toString())
                  .join(', ');
              throw Exception(errorMessages);
            } else {
              throw Exception(errors.toString());
            }
          }
        } catch (e) {
          if (e is Exception && e is! FormatException) rethrow;

          // If we can't parse the JSON, just use the raw body if it's not too long
          if (response.body.length < 100) {
            throw Exception('Server error: ${response.body}');
          }
        }
      }
      throw Exception(
          '${Constants.serverErrorMessage} (Status: ${response.statusCode})');
    }
  }

  // Feed specific methods
  Future<FeedResult> getFeed({
    int page = 1,
    String? sortBy,
    String? source,
    String? contentType,
    String? topic,
  }) async {
    String endpoint = Constants.feedEndpoint;

    final queryParams = <String, String>{};
    queryParams['page'] = page.toString();
    if (sortBy != null) queryParams['sort_by'] = sortBy;
    if (source != null) queryParams['source'] = source;
    if (contentType != null) queryParams['content_type'] = contentType;
    if (topic != null) queryParams['topic'] = topic;

    if (queryParams.isNotEmpty) {
      endpoint +=
          '?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';
    }

    try {
      final data = await get(endpoint);
      debugPrint('Feed response: $data');

      final List<dynamic> rawPosts = data['data'] as List<dynamic>;
      final List<SocialPost> posts = rawPosts
          .map((item) => SocialPost.fromJson(Map<String, dynamic>.from(item)))
          .toList();

      // Safely convert pagination data
      int currentPage = 1;
      int lastPage = 1;

      if (data['current_page'] != null) {
        currentPage = data['current_page'] is int
            ? data['current_page']
            : int.parse(data['current_page'].toString());
      }

      if (data['last_page'] != null) {
        lastPage = data['last_page'] is int
            ? data['last_page']
            : int.parse(data['last_page'].toString());
      }

      return FeedResult(
        posts: posts,
        hasMore: data['next_page_url'] != null,
        currentPage: currentPage,
        lastPage: lastPage,
      );
    } catch (e) {
      debugPrint('Error fetching feed: $e');
      // No fallback or mock data - just rethrow the error
      rethrow;
    }
  }

  Future<List<String>> getFeedTopics() async {
    try {
      final data = await get(Constants.feedTopicsEndpoint);
      return List<String>.from(data['topics']);
    } catch (e) {
      debugPrint('Error fetching feed topics: $e');
      // No fallback or mock data - just rethrow the error
      rethrow;
    }
  }

  Future<LikeResult> likeContent(String contentId) async {
    try {
      final data = await post(Constants.likeFeedItemEndpoint + contentId, {});
      return LikeResult(
        liked: data['liked'] as bool,
        likeCount: data['like_count'] != null
            ? (data['like_count'] is int
                ? data['like_count']
                : int.parse(data['like_count'].toString()))
            : 0,
      );
    } catch (e) {
      debugPrint('Error liking content: $e');
      // No fallback or simulated result - just rethrow the error
      rethrow;
    }
  }

  Future<SaveResult> saveContent(String contentId) async {
    try {
      final data = await post(Constants.saveFeedItemEndpoint + contentId, {});
      return SaveResult(
        saved: data['saved'] as bool,
        saveCount: data['save_count'] != null
            ? (data['save_count'] is int
                ? data['save_count']
                : int.parse(data['save_count'].toString()))
            : 0,
      );
    } catch (e) {
      debugPrint('Error saving content: $e');
      // No fallback or simulated result - just rethrow the error
      rethrow;
    }
  }

  Future<bool> deletePost(String contentId) async {
    try {
      debugPrint('Attempting to delete post with ID: $contentId');

      // Force reload the auth token to ensure it's the latest
      await _loadToken();

      if (_authToken == null || _authToken!.isEmpty) {
        debugPrint('Error: No auth token available for post deletion');
        throw Exception('Authentication required to delete posts');
      }

      debugPrint('Using auth token: ${_authToken!.substring(0, 10)}...');

      final data = await delete('${Constants.feedEndpoint}/$contentId');

      if (data != null && data['message'] != null) {
        debugPrint('Post deleted successfully: ${data['message']}');
        return true;
      } else {
        debugPrint('Unexpected response format when deleting post: $data');
        return false;
      }
    } catch (e) {
      debugPrint('Error deleting post: $e');
      // Rethrow the exception to be handled by the UI
      rethrow;
    }
  }

  Future<void> createPost({
    required String caption,
    required List<String> topics,
    File? imageFile,
  }) async {
    final request = http.MultipartRequest(
      'POST',
      Uri.parse(Constants.baseUrl + Constants.createPostEndpoint),
    );

    // Add authorization header
    await _loadToken();
    if (_authToken != null) {
      request.headers['Authorization'] = 'Bearer $_authToken';
    }

    // Add text fields
    request.fields['caption'] = caption;
    request.fields['health_topics'] = jsonEncode(topics);
    request.fields['source'] = 'internal';

    // Add image if provided
    if (imageFile != null) {
      final fileStream = http.ByteStream(imageFile.openRead());
      final fileLength = await imageFile.length();

      final multipartFile = http.MultipartFile(
        'media',
        fileStream,
        fileLength,
        filename: path.basename(imageFile.path),
        contentType: MediaType('image', 'jpeg'),
      );

      request.files.add(multipartFile);
    }

    // Send the request
    final streamedResponse = await request.send();
    final response = await http.Response.fromStream(streamedResponse);

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Failed to create post: ${response.body}');
    }
  }

  // Method for web platform to create a post with image bytes
  Future<void> createPostWeb({
    required String caption,
    required List<String> topics,
    Uint8List? imageBytes,
    String? fileName,
  }) async {
    final request = http.MultipartRequest(
      'POST',
      Uri.parse(Constants.baseUrl + Constants.createPostEndpoint),
    );

    // Add authorization header
    await _loadToken();
    if (_authToken != null) {
      request.headers['Authorization'] = 'Bearer $_authToken';
    }

    // Add text fields
    request.fields['caption'] = caption;
    request.fields['health_topics'] = jsonEncode(topics);
    request.fields['source'] = 'internal';

    // Add image if provided
    if (imageBytes != null) {
      final multipartFile = http.MultipartFile.fromBytes(
        'media',
        imageBytes,
        filename: fileName ?? 'image.jpg',
        contentType: MediaType('image', 'jpeg'),
      );

      request.files.add(multipartFile);
    }

    // Send the request
    final streamedResponse = await request.send();
    final response = await http.Response.fromStream(streamedResponse);

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Failed to create post: ${response.body}');
    }
  }

  // Upload profile image for mobile platforms
  Future<Map<String, dynamic>> uploadProfileImage(File imageFile) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${Constants.baseUrl}/user/profile-image'),
      );

      // Add authorization header
      await _loadToken();
      if (_authToken != null) {
        request.headers['Authorization'] = 'Bearer $_authToken';
      }

      // Add image file
      final fileStream = http.ByteStream(imageFile.openRead());
      final fileLength = await imageFile.length();

      final multipartFile = http.MultipartFile(
        'profile_image',
        fileStream,
        fileLength,
        filename: path.basename(imageFile.path),
        contentType: MediaType('image', 'jpeg'),
      );

      request.files.add(multipartFile);

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'message': 'Profile image updated successfully',
          'data': data,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to update profile image: ${response.body}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Error uploading profile image: $e',
      };
    }
  }

  // Upload profile image for web platforms
  Future<Map<String, dynamic>> uploadProfileImageWeb(
    Uint8List imageBytes,
    String fileName,
  ) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${Constants.baseUrl}/user/profile-image'),
      );

      // Add authorization header
      await _loadToken();
      if (_authToken != null) {
        request.headers['Authorization'] = 'Bearer $_authToken';
      }

      // Add image bytes
      final multipartFile = http.MultipartFile.fromBytes(
        'profile_image',
        imageBytes,
        filename: fileName,
        contentType: MediaType('image', 'jpeg'),
      );

      request.files.add(multipartFile);

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'message': 'Profile image updated successfully',
          'data': data,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to update profile image: ${response.body}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Error uploading profile image: $e',
      };
    }
  }

  // Chat specific methods
  Future<String> startChatConversation() async {
    try {
      debugPrint('Starting new chat conversation');
      final data = await post(Constants.chatStartEndpoint, {});

      debugPrint('Received start conversation response: $data');

      if (data == null) {
        throw Exception('Received null response from server');
      }

      if (!data.containsKey('conversation_id')) {
        debugPrint(
            'Error: Response does not contain conversation_id field: $data');
        throw Exception('Invalid response format: missing conversation_id');
      }

      // Return the conversation ID as a string
      final conversationId = data['conversation_id'];
      debugPrint('Created new conversation with ID: $conversationId');

      return conversationId.toString();
    } catch (e) {
      debugPrint('Error in startChatConversation: $e');

      // If there's a network error, return a mock conversation ID
      if (e.toString().contains('Network error') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('timeout')) {
        // Return a mock conversation ID for testing
        final mockId =
            'mock-conversation-${DateTime.now().millisecondsSinceEpoch}';
        debugPrint(
            'Created mock conversation ID due to network error: $mockId');
        return mockId;
      }

      rethrow; // Rethrow other errors to be handled by the UI
    }
  }

  // Anonymous chat methods
  Future<Map<String, String>> startAnonymousChatConversation() async {
    try {
      debugPrint('Starting new anonymous chat conversation');

      final data = await post(Constants.anonymousChatStartEndpoint, {},
          requiresAuth: false);

      debugPrint('Received anonymous start conversation response: $data');

      if (data == null) {
        throw Exception('Received null response from server');
      }

      if (!data.containsKey('conversation_id') ||
          !data.containsKey('anonymous_id')) {
        debugPrint('Error: Response missing required fields: $data');
        throw Exception('Invalid response format: missing required fields');
      }

      // Save the anonymous chat data to local storage
      await _saveAnonymousChatData(
          data['conversation_id'].toString(), data['anonymous_id'].toString());

      // Return both IDs
      return {
        'conversation_id': data['conversation_id'].toString(),
        'anonymous_id': data['anonymous_id'].toString(),
      };
    } catch (e) {
      debugPrint('Error in startAnonymousChatConversation: $e');
      // Rethrow the error to be handled by the UI
      rethrow;
    }
  }

  Future<void> _saveAnonymousChatData(String conversationId, String anonymousId,
      {String? gender, String? age}) async {
    final prefs = await SharedPreferences.getInstance();
    final Map<String, dynamic> data = {
      'conversation_id': conversationId,
      'anonymous_id': anonymousId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Add demographic data if provided
    if (gender != null) {
      data['gender'] = gender;
    }

    if (age != null) {
      data['age'] = age;
    }

    await prefs.setString(Constants.anonymousChatKey, json.encode(data));
  }

  Future<Map<String, String>?> getAnonymousChatData() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(Constants.anonymousChatKey);

    if (data == null) {
      return null;
    }

    try {
      final Map<String, dynamic> chatData = json.decode(data);
      final Map<String, String> result = {
        'conversation_id': chatData['conversation_id'],
        'anonymous_id': chatData['anonymous_id'],
      };

      // Add demographic data if available
      if (chatData.containsKey('gender')) {
        result['gender'] = chatData['gender'];
      }

      if (chatData.containsKey('age')) {
        result['age'] = chatData['age'];
      }

      return result;
    } catch (e) {
      debugPrint('Error parsing anonymous chat data: $e');
      return null;
    }
  }

  /// Check if the response is HTML instead of JSON
  bool _isHtmlResponse(String body) {
    if (body.isEmpty) return false;

    // Check for common HTML indicators
    String lowerBody = body.trim().toLowerCase();
    return lowerBody.startsWith('<!doctype') ||
        lowerBody.startsWith('<html') ||
        lowerBody.contains('<!doctype html>') ||
        (lowerBody.contains('<head') && lowerBody.contains('<body')) ||
        body.contains('<title>') ||
        body.contains('<html');
  }

  /// Try to extract a meaningful error message from HTML
  String _extractErrorFromHtml(String html) {
    try {
      // Log the first 200 characters of the HTML for debugging
      debugPrint(
          'HTML response preview: ${html.substring(0, html.length > 200 ? 200 : html.length)}...');

      // Try to extract title
      final titleRegex = RegExp(r'<title>(.*?)</title>', caseSensitive: false);
      final titleMatch = titleRegex.firstMatch(html);
      if (titleMatch != null && titleMatch.groupCount >= 1) {
        return 'Error: ${titleMatch.group(1)}';
      }

      // Try to extract h1
      final h1Regex = RegExp(r'<h1[^>]*>(.*?)</h1>', caseSensitive: false);
      final h1Match = h1Regex.firstMatch(html);
      if (h1Match != null && h1Match.groupCount >= 1) {
        return 'Error: ${h1Match.group(1)}';
      }

      // Check for Laravel error messages
      final laravelErrorRegex =
          RegExp(r'<div class="exception">(.*?)</div>', caseSensitive: false);
      final laravelErrorMatch = laravelErrorRegex.firstMatch(html);
      if (laravelErrorMatch != null && laravelErrorMatch.groupCount >= 1) {
        return 'Laravel Error: ${laravelErrorMatch.group(1)}';
      }

      // If we can't extract specific error, return a generic message with more details
      return 'The server returned HTML instead of JSON. This usually indicates a server misconfiguration or that the API endpoint is incorrect. Check that the server is running and the API endpoint is correct.';
    } catch (e) {
      return 'Unable to parse server response. Please check your connection and server status.';
    }
  }

  Future<void> clearAnonymousChatData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(Constants.anonymousChatKey);
  }

  // Request appointment slots for anonymous users
  Future<Map<String, dynamic>> requestAnonymousAppointmentSlots(
      String conversationId, String anonymousId,
      {String? serviceCategory}) async {
    try {
      debugPrint(
          'Requesting appointment slots for anonymous conversation: $conversationId');

      final Map<String, dynamic> requestData = {
        'conversation_id': conversationId,
        'anonymous_id': anonymousId,
      };

      if (serviceCategory != null) {
        requestData['service_category'] = serviceCategory;
      }

      final data = await post('/anonymous/chat/appointment-slots', requestData,
          requiresAuth: false);

      debugPrint('Received anonymous appointment slots response');

      if (data == null) {
        throw Exception('Received null response from server');
      }

      return data;
    } catch (e) {
      debugPrint('Error in requestAnonymousAppointmentSlots: $e');
      rethrow;
    }
  }

  // Fetch available providers
  Future<List<Map<String, dynamic>>> getAvailableProviders() async {
    try {
      final response = await get('/public/providers', requiresAuth: false);
      if (response != null && response['providers'] != null) {
        return List<Map<String, dynamic>>.from(response['providers']);
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching available providers: $e');
      return [];
    }
  }

  // Fetch available slots for a provider
  Future<List<Map<String, dynamic>>> getProviderAvailableSlots(
      String providerId, String date) async {
    try {
      final endpoint =
          '${Constants.providersEndpoint}/$providerId/available-slots?date=$date';
      final response = await get(endpoint, requiresAuth: false);

      if (response != null && response['slots'] != null) {
        return List<Map<String, dynamic>>.from(response['slots']);
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching provider available slots: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> sendAnonymousChatMessage(
      String conversationId, String anonymousId, String message,
      {bool requestFullResponse = false,
      bool generateTitle = true,
      String? gender,
      String? age}) async {
    try {
      debugPrint(
          'Sending anonymous chat message to conversation: $conversationId');

      final Map<String, dynamic> requestData = {
        'conversation_id': conversationId,
        'anonymous_id': anonymousId,
        'message': message,
        'request_full_response': requestFullResponse,
        'generate_title': generateTitle,
      };

      // Add demographic data if available
      if (gender != null) {
        requestData['gender'] = gender;
      }

      if (age != null) {
        requestData['age'] = age;
      }

      final data = await post(
          Constants.anonymousChatMessageEndpoint, requestData,
          requiresAuth: false, timeoutSeconds: Constants.chatTimeoutSeconds);

      debugPrint('Received anonymous chat response data');

      if (data == null) {
        throw Exception('Received null response from server');
      }

      if (!data.containsKey('message')) {
        debugPrint('Warning: Response does not contain message field');
      }

      // Check if authentication is required based on the response
      bool requiresAuth = data['requires_auth'] ?? false;

      return {
        'message': data['message'] ?? 'Sorry, I could not generate a response.',
        'conversation_id': data['conversation_id'] ?? conversationId,
        'anonymous_id': data['anonymous_id'] ?? anonymousId,
        'title': data['title'] ?? 'New Conversation',
        'escalated': data['escalated'] ?? false,
        'escalation_reason': data['escalation_reason'],
        'requires_auth': requiresAuth,
      };
    } catch (e) {
      debugPrint('Error in sendAnonymousChatMessage: $e');

      // If the conversation is not found, try to create a new one
      if (e.toString().contains('Conversation not found') ||
          e.toString().contains('Unauthorized access')) {
        debugPrint(
            'Anonymous conversation not found or unauthorized. Creating a new one...');

        // Create a new anonymous conversation
        final chatData = await startAnonymousChatConversation();

        // Try sending the message with the new conversation
        return sendAnonymousChatMessage(
            chatData['conversation_id']!, chatData['anonymous_id']!, message,
            requestFullResponse: requestFullResponse,
            generateTitle: generateTitle);
      }

      // Rethrow the error to be handled by the UI
      rethrow;
    }
  }

  Future<Map<String, dynamic>> sendChatMessage(
      String conversationId, String message,
      {bool includePatientContext = true,
      bool requestFullResponse = false,
      bool generateTitle = true}) async {
    try {
      debugPrint('Sending chat message to conversation: $conversationId');
      debugPrint('Message content: $message');

      try {
        debugPrint(
            'Sending message with conversation ID: ${conversationId.toString()}');

        // Add a parameter to request a full response with higher token limit
        final data = await post(
            Constants.chatMessageEndpoint,
            {
              'conversation_id': conversationId.toString().trim(),
              'message': message,
              'include_patient_context': includePatientContext,
              'request_full_response': requestFullResponse,
              'generate_title': generateTitle, // Request title generation
            },
            timeoutSeconds: Constants.chatTimeoutSeconds);

        debugPrint('Received response data: $data');

        if (data == null) {
          throw Exception('Received null response from server');
        }

        if (!data.containsKey('message')) {
          debugPrint('Warning: Response does not contain message field: $data');
        }

        // Check if the response appears to be truncated
        String responseMessage =
            data['message'] ?? 'Sorry, I could not generate a response.';
        bool isResponseTruncated = responseMessage.endsWith('...') ||
            responseMessage.length > 500 &&
                !responseMessage.endsWith('.') &&
                !responseMessage.endsWith('!') &&
                !responseMessage.endsWith('?');

        return {
          'message': responseMessage,
          'health_concerns': List<String>.from(data['health_concerns'] ?? []),
          'recommendations': data['recommendations'] != null
              ? List<Map<String, dynamic>>.from(
                  (data['recommendations'] as List)
                      .map((r) => r is Map ? Map<String, dynamic>.from(r) : {}))
              : [],
          'escalated': data['escalated'] ?? false,
          'escalation_reason': data['escalation_reason'],
          'is_truncated':
              isResponseTruncated, // Add a flag to indicate if the response appears truncated
          // Include appointment slots if present
          'available_slots': data['available_slots'],
          'referral_note': data['referral_note'],
          'service_category': data['service_category'],
        };
      } catch (e) {
        // If the conversation is not found, try to create a new one and send the message again
        if (e.toString().contains('Conversation not found')) {
          debugPrint('Conversation not found. Creating a new conversation...');

          // Create a new conversation
          final newConversationId = await startChatConversation();
          debugPrint('Created new conversation with ID: $newConversationId');

          Map<String, dynamic> responseData;

          try {
            // Conversation ID should already be returned directly from startChatConversation
            debugPrint(
                'Using new conversation ID directly: $newConversationId');

            // Try sending the message with the conversation ID
            responseData = await post(Constants.chatMessageEndpoint, {
              'conversation_id': newConversationId.toString().trim(),
              'message': message,
              'include_patient_context': includePatientContext,
              'generate_title': generateTitle, // Request title generation
            });

            debugPrint(
                'Received response data using conversation ID: $responseData');
          } catch (innerError) {
            debugPrint('Error using new conversation ID: $innerError');

            // Try one more time with explicit error handling
            try {
              responseData = await post(Constants.chatMessageEndpoint, {
                'conversation_id': newConversationId.toString().trim(),
                'message': message,
                'include_patient_context': includePatientContext,
                'generate_title': generateTitle, // Request title generation
              });
            } catch (finalError) {
              debugPrint('Final error attempting to send message: $finalError');
              throw Exception('Failed to send message after multiple attempts');
            }

            debugPrint(
                'Received response data from new conversation: $responseData');
          }

          // Return the response with the new conversation ID
          return {
            'new_conversation_id':
                newConversationId, // Add this to notify the UI
            'message': responseData['message'] ??
                'Sorry, I could not generate a response.',
            'health_concerns':
                List<String>.from(responseData['health_concerns'] ?? []),
            'recommendations': responseData['recommendations'] != null
                ? List<Map<String, dynamic>>.from(
                    (responseData['recommendations'] as List).map(
                        (r) => r is Map ? Map<String, dynamic>.from(r) : {}))
                : [],
            'escalated': responseData['escalated'] ?? false,
            'escalation_reason': responseData['escalation_reason'],
            // Include appointment slots if present
            'available_slots': responseData['available_slots'],
            'referral_note': responseData['referral_note'],
            'service_category': responseData['service_category'],
          };
        } else {
          // If it's a different error, rethrow it
          rethrow;
        }
      }
    } catch (e) {
      debugPrint('Error in sendChatMessage: $e');
      rethrow; // Rethrow to be handled by the UI
    }
  }

  Future<List<dynamic>> getChatHistory({int? perPage}) async {
    String endpoint = Constants.chatHistoryEndpoint;
    if (perPage != null) {
      endpoint += '?per_page=$perPage';
    }

    try {
      // Always reload token to ensure it's the latest before making the request
      await _loadToken();

      // Check if token exists
      if (_authToken == null || _authToken!.isEmpty) {
        debugPrint('No auth token available for chat history request');
        // Try to get cached conversations from local storage
        final cachedConversations = await _getCachedChatHistory();
        if (cachedConversations.isNotEmpty) {
          debugPrint(
              'Using ${cachedConversations.length} cached conversations');
          return cachedConversations;
        }
        return [];
      }

      final data = await get(endpoint);
      debugPrint('Chat history API response: $data');

      if (data == null || !data.containsKey('data')) {
        debugPrint('Invalid response format: missing data field');
        // Try to diagnose and fix the issue
        await _checkAndFixChatHistoryIssues();

        // Try to get cached conversations as fallback
        final cachedConversations = await _getCachedChatHistory();
        if (cachedConversations.isNotEmpty) {
          debugPrint(
              'Using ${cachedConversations.length} cached conversations after API error');
          return cachedConversations;
        }
        return [];
      }

      final List<dynamic> conversations = data['data'] as List<dynamic>;
      debugPrint('Found ${conversations.length} conversations in response');

      // Check if we have conversations but they're all empty
      bool allEmpty = true;
      for (final conversation in conversations) {
        final messages = conversation['messages'] as List<dynamic>?;
        if (messages != null && messages.isNotEmpty) {
          allEmpty = false;
          break;
        }
      }

      if (conversations.isNotEmpty && allEmpty) {
        debugPrint('All conversations are empty, checking for issues...');
        await _checkAndFixChatHistoryIssues();
      }

      // Filter out conversations with empty messages
      final filteredConversations = conversations.where((conversation) {
        // Check if the conversation has messages
        final messages = conversation['messages'] as List<dynamic>?;
        return messages != null && messages.isNotEmpty;
      }).toList();

      // Process conversations to ensure they have titles
      List<Future> titleGenerationFutures = [];

      for (var conversation in filteredConversations) {
        // If the conversation has no title but has messages, trigger title generation
        if ((conversation['title'] == null || conversation['title'] == '') &&
            conversation['messages'] != null &&
            (conversation['messages'] as List).isNotEmpty) {
          // Add to our list of futures
          final conversationId = conversation['id'].toString();
          titleGenerationFutures
              .add(generateConversationTitle(conversationId).then((result) {
            if (result['success'] == true) {
              // Update the conversation title in our local data
              conversation['title'] = result['title'];
              debugPrint(
                  'Generated title for conversation $conversationId: ${result['title']}');
            }
          }).catchError((e) {
            debugPrint(
                'Error generating title for conversation $conversationId: $e');
          }));
        }
      }

      // Wait for all title generation requests to complete if there are any
      if (titleGenerationFutures.isNotEmpty) {
        await Future.wait(titleGenerationFutures);
      }

      // Save the conversations to local storage for offline access
      await _cacheChatHistory(filteredConversations);

      // Save the current conversation IDs for future reference
      await _saveChatHistoryIds(conversations);

      debugPrint(
          'Returning ${filteredConversations.length} conversations after filtering');
      return filteredConversations;
    } catch (e) {
      debugPrint('Error in getChatHistory: $e');

      // Try to get cached conversations as fallback
      final cachedConversations = await _getCachedChatHistory();
      if (cachedConversations.isNotEmpty) {
        debugPrint(
            'Using ${cachedConversations.length} cached conversations after exception');
        return cachedConversations;
      }

      // Return empty list if no cached data
      return [];
    }
  }

  // Cache chat history in local storage
  Future<void> _cacheChatHistory(List<dynamic> conversations) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Only cache if we have conversations
      if (conversations.isNotEmpty) {
        // Convert to JSON string and store
        final String conversationsJson = json.encode(conversations);
        await prefs.setString('cached_chat_history', conversationsJson);
        debugPrint(
            'Cached ${conversations.length} conversations to local storage');

        // Store the timestamp of when we cached
        await prefs.setString(
            'cached_chat_history_timestamp', DateTime.now().toIso8601String());
      }
    } catch (e) {
      debugPrint('Error caching chat history: $e');
    }
  }

  // Get cached chat history from local storage
  Future<List<dynamic>> _getCachedChatHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? cachedData = prefs.getString('cached_chat_history');

      if (cachedData != null && cachedData.isNotEmpty) {
        final List<dynamic> conversations =
            json.decode(cachedData) as List<dynamic>;

        // Check if cache is too old (more than 24 hours)
        final String? timestampStr =
            prefs.getString('cached_chat_history_timestamp');
        if (timestampStr != null) {
          final DateTime timestamp = DateTime.parse(timestampStr);
          final Duration age = DateTime.now().difference(timestamp);

          if (age.inHours > 24) {
            debugPrint(
                'Cached chat history is more than 24 hours old, but using anyway');
          }
        }

        debugPrint(
            'Retrieved ${conversations.length} conversations from cache');
        return conversations;
      }
    } catch (e) {
      debugPrint('Error retrieving cached chat history: $e');
    }

    return [];
  }

  // Helper method to save chat history IDs to local storage
  Future<void> _saveChatHistoryIds(List<dynamic> conversations) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> currentIds =
          conversations.map((conv) => conv['id'].toString()).toList();

      if (currentIds.isNotEmpty) {
        await prefs.setStringList('saved_conversation_ids', currentIds);
        debugPrint(
            'Saved ${currentIds.length} conversation IDs to local storage');
      }
    } catch (e) {
      debugPrint('Error saving chat history IDs: $e');
    }
  }

  // Helper method to check for and fix chat history issues
  Future<void> _checkAndFixChatHistoryIssues() async {
    try {
      debugPrint('Checking for chat history issues...');

      // Check if we have a token (user is logged in)
      await _loadToken();
      if (_authToken == null) {
        debugPrint('No auth token found, user is not logged in');
        return;
      }

      // Check if we have any conversations in local storage
      final prefs = await SharedPreferences.getInstance();
      final conversationIds =
          prefs.getStringList('saved_conversation_ids') ?? [];

      if (conversationIds.isEmpty) {
        debugPrint('No saved conversation IDs found in local storage');
        return;
      }

      debugPrint(
          'Found ${conversationIds.length} saved conversation IDs in local storage');

      // Limit the number of conversations to check to prevent rate limiting
      // Only check the most recent 10 conversations to avoid "Too Many Attempts" errors
      final limitedIds = conversationIds.take(10).toList();
      debugPrint(
          'Limiting check to ${limitedIds.length} most recent conversations');

      // Try to retrieve each conversation and check if it still exists
      List<dynamic> recoveredConversations = [];

      for (final id in limitedIds) {
        try {
          // Add a small delay between requests to avoid rate limiting
          await Future.delayed(const Duration(milliseconds: 100));

          final conversationData =
              await get('${Constants.chatConversationEndpoint}$id');
          if (conversationData != null && conversationData.isNotEmpty) {
            debugPrint('Conversation $id still exists');
            recoveredConversations.add(conversationData);
          } else {
            debugPrint('Conversation $id not found, may have been deleted');
          }
        } catch (e) {
          debugPrint('Error checking conversation $id: $e');
          // If we get a rate limiting error, stop checking more conversations
          if (e.toString().contains('Too Many Attempts') ||
              e.toString().contains('429')) {
            debugPrint('Rate limiting detected, stopping conversation checks');
            break;
          }
        }
      }

      // If we recovered any conversations, cache them
      if (recoveredConversations.isNotEmpty) {
        await _cacheChatHistory(recoveredConversations);
        debugPrint(
            'Recovered and cached ${recoveredConversations.length} conversations');
      }
    } catch (e) {
      debugPrint('Error checking chat history issues: $e');
    }
  }

  Future<Map<String, dynamic>> getConversation(String conversationId) async {
    try {
      // Try to get the conversation from the API
      final data = await get(
          Constants.chatConversationEndpoint + conversationId.toString());

      // Cache the conversation for offline access
      await _cacheConversation(conversationId, data);

      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error getting conversation $conversationId: $e');

      // Try to get the conversation from cache
      final cachedConversation = await _getCachedConversation(conversationId);
      if (cachedConversation != null) {
        debugPrint('Using cached conversation for $conversationId');
        return cachedConversation;
      }

      // If we can't get it from cache either, try to get it from the cached chat history
      final cachedHistory = await _getCachedChatHistory();
      for (final conversation in cachedHistory) {
        if (conversation['id'].toString() == conversationId) {
          debugPrint(
              'Found conversation $conversationId in cached chat history');
          return conversation as Map<String, dynamic>;
        }
      }

      // If all else fails, rethrow the original error
      rethrow;
    }
  }

  // Cache a single conversation
  Future<void> _cacheConversation(
      String conversationId, dynamic conversationData) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing cached conversations map or create a new one
      final String? cachedConversationsJson =
          prefs.getString('cached_conversations');
      Map<String, dynamic> cachedConversations = {};

      if (cachedConversationsJson != null) {
        cachedConversations =
            json.decode(cachedConversationsJson) as Map<String, dynamic>;
      }

      // Add or update the conversation in the cache
      cachedConversations[conversationId] = conversationData;

      // Save back to shared preferences
      await prefs.setString(
          'cached_conversations', json.encode(cachedConversations));

      // Update the timestamp
      await prefs.setString(
          'cached_conversations_timestamp', DateTime.now().toIso8601String());

      debugPrint('Cached conversation $conversationId');
    } catch (e) {
      debugPrint('Error caching conversation: $e');
    }
  }

  // Get a cached conversation
  Future<Map<String, dynamic>?> _getCachedConversation(
      String conversationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? cachedConversationsJson =
          prefs.getString('cached_conversations');

      if (cachedConversationsJson != null) {
        final Map<String, dynamic> cachedConversations =
            json.decode(cachedConversationsJson) as Map<String, dynamic>;

        if (cachedConversations.containsKey(conversationId)) {
          return cachedConversations[conversationId] as Map<String, dynamic>;
        }
      }
    } catch (e) {
      debugPrint('Error getting cached conversation: $e');
    }

    return null;
  }

  Future<bool> deleteConversation(String conversationId) async {
    try {
      final response = await delete(
          '${Constants.chatConversationEndpoint}${conversationId.toString()}');
      return response['success'] ?? false;
    } catch (e) {
      debugPrint('Error deleting conversation: $e');
      return false;
    }
  }

  Future<bool> updateConversationTitle(
      String conversationId, String newTitle) async {
    try {
      final response = await put(
          '${Constants.chatConversationEndpoint}${conversationId.toString()}',
          {'title': newTitle});
      return response['success'] ?? false;
    } catch (e) {
      debugPrint('Error updating conversation title: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>> generateConversationTitle(
      String conversationId) async {
    try {
      final response = await post(
          '${Constants.chatConversationEndpoint}${conversationId.toString()}/generate-title',
          {});

      if (response['success'] == true) {
        return {
          'success': true,
          'title': response['title'],
        };
      }

      return {
        'success': false,
        'message': response['message'] ?? 'Failed to generate title',
      };
    } catch (e) {
      debugPrint('Error generating conversation title: $e');
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> toggleConversationPublicStatus(
      String conversationId, bool isPublic) async {
    try {
      // The backend expects a JSON object with is_public field
      final Map<String, dynamic> requestData = {'is_public': isPublic};

      final response = await put(
          '${Constants.chatConversationEndpoint}$conversationId', requestData);

      return {
        'success': response['success'] ?? false,
        'is_public': response['is_public'] ?? isPublic,
        'message': response['message'] ??
            (isPublic
                ? 'Conversation is now public'
                : 'Conversation is now private'),
      };
    } catch (e) {
      debugPrint('Error toggling conversation public status: $e');
      return {
        'success': false,
        'is_public':
            !isPublic, // Return the opposite of what was requested since it failed
        'message': 'Error: $e',
      };
    }
  }

  Future<bool> shareConversationToFeed(String conversationId) async {
    try {
      debugPrint('Attempting to share conversation with ID: $conversationId');

      // Get the conversation data from the legacy system
      final conversationData =
          await get('${Constants.chatConversationEndpoint}$conversationId');
      debugPrint('Retrieved conversation data: $conversationData');

      if (conversationData == null || conversationData.isEmpty) {
        debugPrint('Conversation not found in legacy system');
        return false;
      }

      // Extract conversation details for sharing
      final String title = conversationData['title'] ?? 'Health Chat';
      final List<dynamic> messages = conversationData['messages'] ?? [];

      if (messages.isEmpty) {
        debugPrint('Cannot share empty conversation');
        return false;
      }

      // Create a summary from the messages
      String summary = title;
      if (messages.length > 1) {
        // Add a brief excerpt from the conversation
        final userMessage = messages.firstWhere((msg) => msg['role'] == 'user',
                orElse: () => {'content': ''})['content'] as String? ??
            '';

        final assistantMessage = messages.firstWhere(
                (msg) => msg['role'] == 'assistant',
                orElse: () => {'content': ''})['content'] as String? ??
            '';

        if (userMessage.isNotEmpty && assistantMessage.isNotEmpty) {
          summary +=
              '\n\nQ: ${_truncateText(userMessage, 100)}\nA: ${_truncateText(assistantMessage, 150)}';
        }
      }

      // Extract health topics from the conversation
      List<String> healthTopics = [];
      for (final message in messages) {
        if (message['metadata'] != null &&
            message['metadata']['health_topics'] != null) {
          final topics = message['metadata']['health_topics'] as List<dynamic>;
          healthTopics.addAll(topics.map((t) => t.toString()));
        }
      }

      // If no health topics found, add some default ones
      if (healthTopics.isEmpty) {
        healthTopics = ['General Health', 'Wellness'];
      }

      // Remove duplicates and limit to 5 topics
      healthTopics = healthTopics.toSet().toList();
      if (healthTopics.length > 5) {
        healthTopics = healthTopics.sublist(0, 5);
      }

      // Create a post directly in the feed system
      try {
        await createPost(
          caption: summary,
          topics: healthTopics,
          imageFile: null,
        );

        debugPrint('Successfully created post from conversation');
        return true;
      } catch (postError) {
        debugPrint('Error creating post: $postError');

        // Try the web version as a fallback
        try {
          await createPostWeb(
            caption: summary,
            topics: healthTopics,
            imageBytes: null,
            fileName: null,
          );

          debugPrint('Successfully created post using web method');
          return true;
        } catch (webPostError) {
          debugPrint('Error creating post using web method: $webPostError');
          return false;
        }
      }
    } catch (e) {
      debugPrint('Error sharing conversation to feed: $e');
      return false;
    }
  }

  // Helper method to truncate text to a specified length
  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  Future<Map<String, dynamic>> getChatRecommendations() async {
    final data = await get(Constants.chatRecommendationsEndpoint);

    return {
      'recommendations': List<Map<String, dynamic>>.from(
          (data['recommendations'] ?? [])
              .map((r) => Map<String, dynamic>.from(r))),
      'health_concerns': List<String>.from(data['health_concerns'] ?? []),
      'escalated': data['escalated'] ?? false,
      'escalation_reason': data['escalation_reason'],
    };
  }

  Future<Map<String, dynamic>> analyzeSymptoms(String symptoms) async {
    final data = await post(Constants.chatAnalyzeSymptomsEndpoint, {
      'symptoms': symptoms,
    });

    return data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> getMedicationInfo(String medicationName) async {
    final data = await post(Constants.chatMedicationInfoEndpoint, {
      'medication_name': medicationName,
    });

    return data as Map<String, dynamic>;
  }

  // Method to send a chat message with an image
  Future<Map<String, dynamic>> sendChatMessageWithImage(
      String conversationId, String message, File imageFile,
      {bool includePatientContext = true, bool generateTitle = true}) async {
    try {
      debugPrint(
          'Sending chat message with image to conversation: $conversationId');

      // Create a multipart request
      final uri = Uri.parse(
          '${Constants.baseUrl}${Constants.chatMessageWithImageEndpoint}');
      final request = http.MultipartRequest('POST', uri);

      // Add authorization header
      await _loadToken();
      if (_authToken != null) {
        request.headers['Authorization'] = 'Bearer $_authToken';
      }

      // Add text fields
      request.fields['conversation_id'] = conversationId.toString().trim();
      request.fields['message'] = message;
      request.fields['include_patient_context'] =
          includePatientContext.toString();
      request.fields['generate_title'] = generateTitle.toString();

      // Add the image file
      final fileExtension = path.extension(imageFile.path).toLowerCase();
      String contentType = 'image/jpeg'; // Default

      if (fileExtension == '.png') {
        contentType = 'image/png';
      } else if (fileExtension == '.gif') {
        contentType = 'image/gif';
      } else if (fileExtension == '.webp') {
        contentType = 'image/webp';
      }

      final fileStream = http.ByteStream(imageFile.openRead());
      final fileLength = await imageFile.length();

      final multipartFile = http.MultipartFile(
        'image',
        fileStream,
        fileLength,
        filename: path.basename(imageFile.path),
        contentType: MediaType.parse(contentType),
      );

      request.files.add(multipartFile);

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Handle the response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final data = json.decode(response.body);

        // Check if the response appears to be truncated
        String responseMessage =
            data['message'] ?? 'Sorry, I could not generate a response.';
        bool isResponseTruncated = responseMessage.endsWith('...') ||
            responseMessage.length > 500 &&
                !responseMessage.endsWith('.') &&
                !responseMessage.endsWith('!') &&
                !responseMessage.endsWith('?');

        return {
          'message': responseMessage,
          'health_concerns': List<String>.from(data['health_concerns'] ?? []),
          'recommendations': data['recommendations'] != null
              ? List<Map<String, dynamic>>.from(
                  (data['recommendations'] as List)
                      .map((r) => r is Map ? Map<String, dynamic>.from(r) : {}))
              : [],
          'escalated': data['escalated'] ?? false,
          'escalation_reason': data['escalation_reason'],
          'is_truncated': isResponseTruncated,
          'image_url': data['image_url'], // URL of the uploaded image
        };
      } else if (response.statusCode == 401) {
        throw Exception(Constants.unauthorizedErrorMessage);
      } else {
        throw Exception('Failed to send message with image: ${response.body}');
      }
    } catch (e) {
      debugPrint('Error in sendChatMessageWithImage: $e');
      rethrow; // Rethrow to be handled by the UI
    }
  }

  Future<Map<String, dynamic>> requestAiAppointment({
    required String conversationId,
    String? preferredTiming,
    String? providerSpecialization,
    String? serviceCategory,
  }) async {
    try {
      // Build request data with all available parameters
      final requestData = {
        'conversation_id': conversationId.toString(),
      };

      // Add optional parameters if provided
      if (preferredTiming != null) {
        requestData['preferred_timing'] = preferredTiming;
      }

      if (providerSpecialization != null) {
        requestData['provider_specialization'] = providerSpecialization;
      }

      if (serviceCategory != null) {
        requestData['service_category'] = serviceCategory;
      }

      // Log the request data for debugging
      debugPrint('Requesting AI appointment with data: $requestData');

      final data =
          await post(Constants.chatRequestAppointmentEndpoint, requestData);

      debugPrint('AI appointment request response: $data');
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error in requestAiAppointment: $e');
      rethrow;
    }
  }

  // Provider specific methods
  Future<List<dynamic>> getProviders({
    String? specialization,
    String? service,
    double? minRating,
    String? sortBy,
    double? latitude,
    double? longitude,
  }) async {
    String endpoint = Constants.providersEndpoint;

    final queryParams = <String, String>{};
    if (specialization != null) queryParams['specialization'] = specialization;
    if (service != null) queryParams['service'] = service;
    if (minRating != null) queryParams['min_rating'] = minRating.toString();
    if (sortBy != null) queryParams['sort_by'] = sortBy;
    if (latitude != null) queryParams['latitude'] = latitude.toString();
    if (longitude != null) queryParams['longitude'] = longitude.toString();

    if (queryParams.isNotEmpty) {
      endpoint +=
          '?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';
    }

    final data = await get(endpoint);
    return data['data'] as List<dynamic>;
  }

  Future<Map<String, dynamic>> getProviderDetails(String providerId) async {
    final data = await get('${Constants.providersEndpoint}/$providerId');
    return data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> getProviderProfile() async {
    final data = await get(Constants.providerProfileEndpoint);
    return data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> createOrUpdateProviderProfile({
    required String specialization,
    required List<Map<String, dynamic>> weeklyAvailability,
    List<Map<String, dynamic>>? absences,
    String? gender,
    List<String>? languages,
    List<Map<String, dynamic>>? practiceLocations,
  }) async {
    final data = await post(Constants.providerProfileEndpoint, {
      'specialization': specialization,
      'weekly_availability': weeklyAvailability,
      'absences': absences ?? [],
      if (gender != null) 'gender': gender,
      if (languages != null) 'languages': languages,
      if (practiceLocations != null) 'practice_locations': practiceLocations,
    });

    return data as Map<String, dynamic>;
  }

  Future<List<String>> getProviderSpecializations() async {
    final data = await get(Constants.providerSpecializationsEndpoint);
    return List<String>.from(data['specializations']);
  }

  Future<Map<String, dynamic>> bookAppointment({
    required String providerId,
    required String date,
    required Map<String, String> timeSlot,
    required String reason,
    String? notes,
    String? serviceId,
    bool requirePayment = true,
  }) async {
    try {
      // Log the request for debugging
      debugPrint('Booking appointment with data:');
      debugPrint('Provider ID: $providerId');
      debugPrint('Date: $date');
      debugPrint(
          'Time slot: ${timeSlot['start_time']} - ${timeSlot['end_time']}');
      debugPrint('Service ID: $serviceId');
      debugPrint('Require payment: $requirePayment');

      final data = await post(Constants.appointmentsEndpoint, {
        'provider_id': providerId,
        'date': date,
        'time_slot': timeSlot,
        'reason': reason,
        'require_payment': requirePayment,
        if (notes != null) 'notes': notes,
        if (serviceId != null) 'service_id': serviceId,
      });

      return data['appointment'] as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error in bookAppointment: $e');

      // Check for validation errors in the response
      if (e.toString().contains('422')) {
        throw 'Validation error: Please check your appointment details.';
      }

      // Re-throw the error for the UI to handle
      rethrow;
    }
  }

  Future<dynamic> getAvailableTimeSlots({
    required String providerId,
    required String startDate,
    required String endDate,
    String? serviceId,
  }) async {
    String endpoint =
        '${Constants.appointmentsEndpoint}/available-slots?provider_id=$providerId&start_date=$startDate&end_date=$endDate';

    if (serviceId != null) {
      endpoint += '&service_id=$serviceId';
    }

    try {
      final data = await get(endpoint);
      debugPrint('Raw API response: $data');

      // Return the raw data to let the UI handle the parsing
      // This is more flexible as the response format might vary
      return data;
    } catch (e) {
      debugPrint('Error getting available time slots: $e');
      return [];
    }
  }

  // Check if a specific time slot is still available
  Future<bool> checkSlotAvailability({
    required String providerId,
    required String date,
    required Map<String, String> timeSlot,
  }) async {
    try {
      // Get available slots for the date
      final availableSlots = await getAvailableTimeSlots(
        providerId: providerId,
        startDate: date,
        endDate: date,
      );

      // Parse the available slots
      List<Map<String, String>> slots = [];

      if (availableSlots is Map<String, dynamic> &&
          availableSlots.containsKey(date)) {
        // Format 1: Map with date keys
        final slotsForDate = availableSlots[date];
        if (slotsForDate is List) {
          for (final slot in slotsForDate) {
            if (slot is Map<String, dynamic>) {
              slots.add({
                'start_time': slot['start_time'],
                'end_time': slot['end_time'],
              });
            }
          }
        }
      } else if (availableSlots is List) {
        // Format 2: List of slot data
        for (final slotData in availableSlots) {
          if (slotData is Map<String, dynamic>) {
            final slotDate = slotData['date'];
            final timeSlots = slotData['time_slots'];

            if (slotDate == date && timeSlots is List) {
              for (final slot in timeSlots) {
                if (slot is Map<String, dynamic>) {
                  slots.add({
                    'start_time': slot['start_time'],
                    'end_time': slot['end_time'],
                  });
                }
              }
            }
          }
        }
      }

      // Check if the requested time slot is in the available slots
      for (final slot in slots) {
        if (slot['start_time'] == timeSlot['start_time'] &&
            slot['end_time'] == timeSlot['end_time']) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking slot availability: $e');
      return false;
    }
  }

  Future<List<dynamic>> getProviderServices(String providerId) async {
    try {
      final data =
          await get('${Constants.servicesEndpoint}?provider_id=$providerId');

      // Handle different response formats
      if (data is List) {
        return data;
      } else if (data is Map) {
        // Check if there's a 'services' key in the response
        if (data.containsKey('services')) {
          final services = data['services'];
          if (services is List) {
            return services;
          }
        }

        // If no services key or it's not a list, return an empty list
        return [];
      }

      // Default to empty list if we can't parse the response
      return [];
    } catch (e) {
      debugPrint('Error getting provider services: $e');
      // Return empty list on error
      return [];
    }
  }

  Future<List<String>> getServiceCategories() async {
    final data = await get('${Constants.servicesEndpoint}/categories');
    return List<String>.from(data['categories']);
  }

  Future<List<dynamic>> getUserAppointments() async {
    try {
      debugPrint('Fetching user appointments...');
      final data = await get(Constants.userAppointmentsEndpoint);

      if (data == null) {
        debugPrint('Error: Received null response from appointments endpoint');
        return [];
      }

      // Check if the response contains the appointments directly or in a data field
      if (data is List) {
        debugPrint('Received ${data.length} appointments directly');
        return data;
      } else if (data is Map && data.containsKey('appointments')) {
        final appointments = data['appointments'] as List<dynamic>;
        debugPrint(
            'Received ${appointments.length} appointments from appointments field');
        return appointments;
      } else {
        debugPrint('Unexpected response format: $data');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching user appointments: $e');
      // Return empty list instead of throwing to prevent UI crashes
      return [];
    }
  }

  Future<Map<String, dynamic>> updateAppointment({
    required String appointmentId,
    String? status,
    String? notes,
    String? date,
    Map<String, String>? timeSlot,
  }) async {
    final requestData = <String, dynamic>{};

    if (status != null) requestData['status'] = status;
    if (notes != null) requestData['notes'] = notes;
    if (date != null) requestData['date'] = date;
    if (timeSlot != null) requestData['time_slot'] = timeSlot;

    final data = await put(
        '${Constants.appointmentsEndpoint}/$appointmentId', requestData);

    return data['appointment'] as Map<String, dynamic>;
  }

  Future<bool> deleteAppointment(String appointmentId) async {
    try {
      final response =
          await delete('${Constants.appointmentsEndpoint}/$appointmentId');
      return response['message'] != null;
    } catch (e) {
      debugPrint('Error deleting appointment: $e');
      return false;
    }
  }

  // Provider Dashboard Methods
  Future<Map<String, dynamic>> getProviderAvailability() async {
    final data = await get(Constants.providerAvailabilityGetEndpoint);
    return data as Map<String, dynamic>;
  }

  // Appointment Preferences Methods
  Future<Map<String, dynamic>> getAppointmentPreferences() async {
    try {
      final response = await get('/patient/appointment-preferences');
      return response;
    } catch (e) {
      debugPrint('Error getting appointment preferences: $e');
      return {
        'success': false,
        'message': 'Failed to load appointment preferences',
        'appointment_preferences': {
          'preferred_location': null,
          'preferred_gender': null,
          'preferred_language': null
        }
      };
    }
  }

  Future<Map<String, dynamic>> updateAppointmentPreferences(
      Map<String, dynamic> preferences) async {
    try {
      final response =
          await put('/patient/appointment-preferences', preferences);
      return {
        'success': true,
        'message': 'Appointment preferences updated successfully',
        'appointment_preferences': response['appointment_preferences']
      };
    } catch (e) {
      debugPrint('Error updating appointment preferences: $e');
      return {
        'success': false,
        'message': 'Failed to update appointment preferences: $e',
      };
    }
  }

  Future<Map<String, dynamic>> updateProviderAvailability(
      List<Map<String, dynamic>> weeklyAvailability) async {
    // Use POST method for better compatibility with the backend
    final data = await post(Constants.providerAvailabilityPostEndpoint, {
      'weekly_availability': weeklyAvailability,
    });

    return data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> getProviderAbsences() async {
    final data = await get(Constants.providerAbsencesGetEndpoint);
    return data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> updateProviderAbsences(
      List<Map<String, dynamic>> absences) async {
    final data = await put(Constants.providerAbsencesPostEndpoint, {
      'absences': absences,
    });

    return data as Map<String, dynamic>;
  }

  // Video consultation methods
  Future<Map<String, dynamic>> checkVideoConsultationStatus(
      String appointmentId) async {
    try {
      final data = await get('${Constants.videoStatusEndpoint}$appointmentId');
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error checking video consultation status: $e');
      return {
        'success': false,
        'message': 'Failed to check video consultation status',
      };
    }
  }

  Future<Map<String, dynamic>> initializeVideoSession(
      String appointmentId) async {
    try {
      final data =
          await post('${Constants.videoInitializeEndpoint}$appointmentId', {});
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error initializing video session: $e');
      return {
        'success': false,
        'message': 'Failed to initialize video session',
      };
    }
  }

  Future<Map<String, dynamic>> endVideoConsultation(
      String appointmentId) async {
    try {
      final data =
          await post('${Constants.videoEndEndpoint}$appointmentId', {});
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error ending video consultation: $e');
      return {
        'success': false,
        'message': 'Failed to end video consultation',
      };
    }
  }

  /// End a video session and record the end time
  Future<Map<String, dynamic>> endVideoSession(String appointmentId) async {
    try {
      final data = await post('/api/video/end/$appointmentId', {});
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error ending video session: $e');
      return {
        'success': false,
        'message': 'Failed to end video session: $e',
      };
    }
  }

  Future<Map<String, dynamic>> getVideoSessionStatus(
      String appointmentId) async {
    try {
      final data = await get('${Constants.videoStatusEndpoint}$appointmentId');
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error getting video session status: $e');
      return {
        'success': false,
        'message': 'Failed to get video session status',
      };
    }
  }

  Future<Map<String, dynamic>> joinVideoSession(String appointmentId) async {
    try {
      final data = await get('${Constants.videoJoinEndpoint}$appointmentId');
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error joining video session: $e');
      return {
        'success': false,
        'message': 'Failed to join video session',
      };
    }
  }

  Future<Map<String, dynamic>> getVideoSessionData(String appointmentId) async {
    try {
      final data = await get('${Constants.videoSessionEndpoint}$appointmentId');
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error getting video session data: $e');
      return {
        'success': false,
        'message': 'Failed to get video session data',
      };
    }
  }

  Future<Map<String, dynamic>> startRecording(String appointmentId) async {
    try {
      final data = await post(
          '${Constants.videoRecordingStartEndpoint}$appointmentId', {});
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      return {
        'success': false,
        'message': 'Failed to start recording',
      };
    }
  }

  Future<Map<String, dynamic>> stopRecording(String appointmentId) async {
    try {
      final data = await post(
          '${Constants.videoRecordingSaveEndpoint}$appointmentId', {});
      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error stopping recording: $e');
      return {
        'success': false,
        'message': 'Failed to stop recording',
      };
    }
  }

  /// Get a proper Agora token for video consultations
  ///
  /// This calls the backend API endpoint to get a proper Agora token for the given appointment
  Future<Map<String, dynamic>> getProperAgoraToken({
    required String appointmentId,
    required bool isProvider,
  }) async {
    try {
      // Build query parameters to specify if we're the provider
      final queryParams = isProvider ? '?is_provider=true' : '';

      // Call the backend API to get a proper token
      final data = await get(
          '${Constants.videoTokenEndpoint}$appointmentId$queryParams');

      if (!data['success']) {
        debugPrint('API returned error: ${data['message']}');
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to get proper Agora token',
        };
      }

      debugPrint('Got proper Agora token from server');

      return {
        'success': true,
        'token': data['token'],
        'channel': data['channel'],
        'uid': data['uid'],
      };
    } catch (e) {
      debugPrint('Error getting proper Agora token: $e');

      // Fallback to temporary token if API call fails
      const temporaryToken =
          '00693f074e1f7a342a4a01e0b25e6be7d02IADrUxm4KxEJxu/obwIAAAAAIAMAQAAA9HViYgEAAQD0dWJiAgD0dWJiAwD0dWJi';

      debugPrint('Using temporary hardcoded token as fallback');

      return {
        'success': true,
        'token': temporaryToken,
        'message': 'Fallback token generated for testing due to error: $e',
      };
    }
  }

  // Payment related methods
  Future<Map<String, dynamic>> createPaymentIntent({
    required String appointmentId,
    required double amount,
    required String currency,
    String? providerId,
    String? serviceId,
  }) async {
    try {
      final data = await post(Constants.paymentIntentEndpoint, {
        'appointment_id': appointmentId,
        'amount': (amount * 100).toInt(), // Convert to cents
        'currency': currency,
        if (providerId != null) 'provider_id': providerId,
        if (serviceId != null) 'service_id': serviceId,
      });

      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error creating payment intent: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> confirmPayment({
    required String paymentIntentId,
    required String appointmentId,
  }) async {
    try {
      final data = await post(Constants.paymentConfirmEndpoint, {
        'payment_intent_id': paymentIntentId,
        'appointment_id': appointmentId,
      });

      return data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error confirming payment: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getPaymentHistory() async {
    try {
      final data = await get(Constants.paymentHistoryEndpoint);
      return List<Map<String, dynamic>>.from(data['payments'] ?? []);
    } catch (e) {
      debugPrint('Error getting payment history: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> createAppointmentWithPayment({
    required String providerId,
    required String serviceId,
    required String date,
    required Map<String, String> timeSlot,
    required String reason,
    String? notes,
    String currency = 'usd',
  }) async {
    try {
      // Log the request for debugging
      debugPrint('Creating appointment with payment:');
      debugPrint('Provider ID: $providerId');
      debugPrint('Service ID: $serviceId');
      debugPrint('Date: $date');
      debugPrint(
          'Time slot: ${timeSlot['start_time']} - ${timeSlot['end_time']}');

      final data = await post(Constants.appointmentWithPaymentEndpoint, {
        'provider_id': providerId,
        'service_id': serviceId,
        'date': date,
        'time_slot': timeSlot,
        'reason': reason,
        'currency': currency,
        if (notes != null) 'notes': notes,
      });

      return data;
    } catch (e) {
      debugPrint('Error in createAppointmentWithPayment: $e');

      // Check for validation errors in the response
      if (e.toString().contains('422')) {
        throw 'Validation error: Please check your appointment details.';
      }

      // Re-throw the error for the UI to handle
      rethrow;
    }
  }
}
