import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';

class AuthService {
  final ApiService _apiService = ApiService();

  // Cached authentication state
  bool _cachedAuthState = false;

  // Getter for synchronous authentication check
  bool get isAuthenticated => _cachedAuthState;

  // Constructor to initialize the cached auth state
  AuthService() {
    // Initialize the cached auth state
    isLoggedIn().then((value) {
      _cachedAuthState = value;
    });
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(Constants.tokenKey);
    _cachedAuthState = token != null;
    return _cachedAuthState;
  }

  Future<Map<String, dynamic>> login(
      {required String email, required String password}) async {
    try {
      print('Attempting to login user: $email');

      final data = await _apiService.post(
        Constants.loginEndpoint,
        {'email': email, 'password': password},
        requiresAuth: false,
      );

      final token = data['access_token'];
      await _apiService.setAuthToken(token);

      // Save user data
      await _saveUserData(data['user']);

      // Update cached auth state
      _cachedAuthState = true;

      // Add a small delay to ensure token is saved before making other requests
      await Future.delayed(const Duration(milliseconds: 500));

      print('Login successful through API');

      // Return success with user role information
      return {
        'success': true,
        'role': data['user']['role'] ?? 'patient',
      };
    } catch (e) {
      print('Login error: $e');
      return {'success': false, 'role': 'patient'};
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String role,
    String? gender,
    String? dateOfBirth,
    String? referralCode,
  }) async {
    try {
      print('Attempting to register user: $email');

      final Map<String, dynamic> registrationData = {
        'name': name,
        'email': email,
        'password': password,
        'password_confirmation': password,
        'role': role,
      };

      // Add optional fields if provided
      if (gender != null) {
        registrationData['gender'] = gender;
      }

      if (dateOfBirth != null) {
        registrationData['date_of_birth'] = dateOfBirth;
      }

      // Add referral code if provided
      if (referralCode != null && referralCode.isNotEmpty) {
        registrationData['referral_code'] = referralCode;
        print('Including referral code in registration: $referralCode');
      }

      final data = await _apiService.post(
        Constants.registerEndpoint,
        registrationData,
        requiresAuth: false,
      );

      final token = data['access_token'];
      await _apiService.setAuthToken(token);

      // Save user data
      await _saveUserData(data['user']);

      // Update cached auth state
      _cachedAuthState = true;

      // Add a small delay to ensure token is saved before making other requests
      await Future.delayed(const Duration(milliseconds: 500));

      print('Registration successful through API');
      return true;
    } catch (e) {
      print('Registration error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      await _apiService.post(Constants.logoutEndpoint, {});
    } catch (e) {
      print('Logout error: $e');
    } finally {
      await _apiService.clearAuthToken();
      await _clearUserData();
      _cachedAuthState = false; // Update cached auth state
    }
  }

  Future<Map<String, dynamic>?> getCurrentUser(
      {bool forceRefresh = false}) async {
    try {
      // If force refresh is requested, skip local storage and fetch from API
      if (!forceRefresh) {
        // Try to get user from local storage first
        final userData = await _getUserData();
        if (userData != null) {
          return userData;
        }
      }

      // Fetch from API
      final data = await _apiService.get(Constants.userEndpoint);

      // Save the fetched user data
      await _saveUserData(data);

      return data;
    } catch (e) {
      print('Get user error: $e');
      return null;
    }
  }

  Future<void> _saveUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(Constants.userDataKey, json.encode(userData));
  }

  Future<Map<String, dynamic>?> _getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(Constants.userDataKey);

    if (userDataString == null) {
      return null;
    }

    try {
      return json.decode(userDataString) as Map<String, dynamic>;
    } catch (e) {
      await _clearUserData();
      return null;
    }
  }

  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(Constants.userDataKey);
  }

  // Get the current auth token
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(Constants.tokenKey);
  }

  // Public method to save user data (for SSO)
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    await _saveUserData(userData);
    _cachedAuthState = true; // Update cached auth state
  }

  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final data = await _apiService.put(Constants.userEndpoint, profileData);

      // Update local user data
      await _saveUserData(data);

      return true;
    } catch (e) {
      print('Update profile error: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>> sendPasswordResetEmail(String email) async {
    try {
      final response = await _apiService.post(
        Constants.passwordResetEmailEndpoint,
        {'email': email},
        requiresAuth: false,
      );

      return {
        'success': true,
        'message':
            response['message'] ?? 'Password reset email sent successfully.',
      };
    } catch (e) {
      print('Send password reset email error: $e');
      return {
        'success': false,
        'message':
            'Failed to send password reset email. Please try again later.',
      };
    }
  }

  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      final response = await _apiService.post(
        Constants.passwordResetEndpoint,
        {
          'token': token,
          'email': email,
          'password': password,
          'password_confirmation': passwordConfirmation,
        },
        requiresAuth: false,
      );

      return {
        'success': true,
        'message': response['message'] ?? 'Password reset successfully.',
      };
    } catch (e) {
      print('Reset password error: $e');
      return {
        'success': false,
        'message': 'Failed to reset password. Please try again later.',
      };
    }
  }

  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      final response = await _apiService.post(
        Constants.passwordChangeEndpoint,
        {
          'current_password': currentPassword,
          'password': newPassword,
          'password_confirmation': newPasswordConfirmation,
        },
      );

      return {
        'success': true,
        'message': response['message'] ?? 'Password changed successfully.',
      };
    } catch (e) {
      print('Change password error: $e');

      // Check if it's a validation error
      if (e is Map && e.containsKey('errors')) {
        final errors = e['errors'];
        if (errors is Map && errors.containsKey('current_password')) {
          return {
            'success': false,
            'message': errors['current_password'][0] ??
                'Current password is incorrect.',
          };
        }
      }

      return {
        'success': false,
        'message': 'Failed to change password. Please try again later.',
      };
    }
  }
}
