import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:medroid_app/models/comment.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/services/auth_service.dart';

class CommentService {
  final AuthService _authService;

  CommentService(this._authService);

  Future<List<Comment>> getComments(String socialContentId,
      {int page = 1}) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse(
            '${Constants.baseUrl}${Constants.commentsEndpoint}/$socialContentId/comments?page=$page'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> commentsJson = data['comments'];
          return commentsJson.map((json) => Comment.fromJson(json)).toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load comments');
        }
      } else {
        throw Exception('Failed to load comments: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading comments: $e');
    }
  }

  Future<Comment> postComment(String socialContentId, String content,
      {String? parentId}) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final body = {
        'content': content,
        if (parentId != null) 'parent_id': parentId,
      };

      final response = await http.post(
        Uri.parse(
            '${Constants.baseUrl}${Constants.commentsEndpoint}/$socialContentId/comments'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return Comment.fromJson(data['comment']);
        } else {
          throw Exception(data['message'] ?? 'Failed to post comment');
        }
      } else {
        throw Exception('Failed to post comment: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error posting comment: $e');
    }
  }

  Future<Comment> updateComment(
      String socialContentId, String commentId, String content) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final body = {
        'content': content,
      };

      final response = await http.put(
        Uri.parse(
            '${Constants.baseUrl}${Constants.commentsEndpoint}/$socialContentId/comments/$commentId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return Comment.fromJson(data['comment']);
        } else {
          throw Exception(data['message'] ?? 'Failed to update comment');
        }
      } else {
        throw Exception('Failed to update comment: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating comment: $e');
    }
  }

  Future<void> deleteComment(String socialContentId, String commentId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse(
            '${Constants.baseUrl}${Constants.commentsEndpoint}/$socialContentId/comments/$commentId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] != true) {
          throw Exception(data['message'] ?? 'Failed to delete comment');
        }
      } else {
        throw Exception('Failed to delete comment: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error deleting comment: $e');
    }
  }

  Future<List<Comment>> getReplies(
      String socialContentId, String commentId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse(
            '${Constants.baseUrl}${Constants.commentsEndpoint}/$socialContentId/comments/$commentId${Constants.commentRepliesEndpoint}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> repliesJson = data['replies'];
          return repliesJson.map((json) => Comment.fromJson(json)).toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load replies');
        }
      } else {
        throw Exception('Failed to load replies: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading replies: $e');
    }
  }

  Future<Map<String, dynamic>> reactToComment(
      String socialContentId, String commentId, String reactionType) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final body = {
        'reaction_type': reactionType,
      };

      final response = await http.post(
        Uri.parse(
            '${Constants.baseUrl}${Constants.commentsEndpoint}/$socialContentId/comments/$commentId/react'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data;
      }

      throw Exception('Failed to react to comment: ${response.statusCode}');
    } catch (e) {
      throw Exception('Failed to react to comment: $e');
    }
  }

  Future<int> getCommentCount(String socialContentId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse(
            '${Constants.baseUrl}/social-content/$socialContentId/comment-count'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['comment_count'] ?? 0;
      }

      throw Exception('Failed to get comment count: ${response.statusCode}');
    } catch (e) {
      throw Exception('Failed to get comment count: $e');
    }
  }
}
