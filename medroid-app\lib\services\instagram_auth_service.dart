import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class InstagramAuthService {
  // Replace with your Instagram app credentials
  // These should be stored securely in a config file or environment variables
  static const String clientId = 'YOUR_INSTAGRAM_CLIENT_ID';
  static const String clientSecret = 'YOUR_INSTAGRAM_CLIENT_SECRET';
  static const String redirectUri = 'medroid://auth/instagram';
  
  // Scopes needed for basic profile and media access
  static const String scope = 'user_profile,user_media';
  
  // Authentication endpoints
  static const String authUrl = 'https://api.instagram.com/oauth/authorize';
  static const String tokenUrl = 'https://api.instagram.com/oauth/access_token';
  static const String longLivedTokenUrl = 'https://graph.instagram.com/access_token';
  
  // Storage keys
  static const String tokenKey = 'instagram_access_token';
  static const String tokenExpiryKey = 'instagram_token_expiry';
  static const String userIdKey = 'instagram_user_id';
  
  // Method to initiate Instagram login
  Future<String?> login() async {
    final Uri authUri = Uri.parse(authUrl).replace(queryParameters: {
      'client_id': clientId,
      'redirect_uri': redirectUri,
      'scope': scope,
      'response_type': 'code',
      'state': _generateRandomState(),
    });
    
    try {
      // Launch the auth URL in a web view
      final result = await FlutterWebAuth.authenticate(
        url: authUri.toString(),
        callbackUrlScheme: redirectUri.split('://')[0],
      );
      
      // Extract the authorization code from the redirect URL
      final code = Uri.parse(result).queryParameters['code'];
      
      // Exchange the code for an access token
      if (code != null) {
        return await _getAccessToken(code);
      }
      return null;
    } catch (e) {
      debugPrint('Instagram login error: $e');
      return null;
    }
  }
  
  // Generate a random state parameter for security
  String _generateRandomState() {
    final random = Random.secure();
    return List.generate(32, (_) => random.nextInt(16).toRadixString(16)).join();
  }
  
  // Exchange authorization code for access token
  Future<String?> _getAccessToken(String code) async {
    try {
      final response = await http.post(
        Uri.parse(tokenUrl),
        body: {
          'client_id': clientId,
          'client_secret': clientSecret,
          'grant_type': 'authorization_code',
          'redirect_uri': redirectUri,
          'code': code,
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final accessToken = data['access_token'];
        final userId = data['user_id'];
        
        // Get a long-lived token
        final longLivedToken = await _getLongLivedToken(accessToken);
        
        if (longLivedToken != null) {
          // Save the token and user ID
          await _saveTokenData(longLivedToken, userId);
          return longLivedToken;
        }
        
        // If we couldn't get a long-lived token, save the short-lived one
        await _saveTokenData(accessToken, userId);
        return accessToken;
      }
      return null;
    } catch (e) {
      debugPrint('Error exchanging code for token: $e');
      return null;
    }
  }
  
  // Exchange short-lived token for a long-lived one (60 days validity)
  Future<String?> _getLongLivedToken(String shortLivedToken) async {
    try {
      final response = await http.get(
        Uri.parse('$longLivedTokenUrl?grant_type=ig_exchange_token&client_secret=$clientSecret&access_token=$shortLivedToken'),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['access_token'];
      }
      return null;
    } catch (e) {
      debugPrint('Error getting long-lived token: $e');
      return null;
    }
  }
  
  // Save the access token and user ID securely
  Future<void> _saveTokenData(String token, String userId) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Calculate expiry date (60 days from now for long-lived tokens)
    final expiryDate = DateTime.now().add(const Duration(days: 60)).toIso8601String();
    
    await prefs.setString(tokenKey, token);
    await prefs.setString(tokenExpiryKey, expiryDate);
    await prefs.setString(userIdKey, userId);
  }
  
  // Get the saved access token
  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(tokenKey);
    final expiryDateStr = prefs.getString(tokenExpiryKey);
    
    // Check if token is expired
    if (token != null && expiryDateStr != null) {
      final expiryDate = DateTime.parse(expiryDateStr);
      if (expiryDate.isAfter(DateTime.now())) {
        return token;
      } else {
        // Token is expired, clear it
        await logout();
      }
    }
    
    return null;
  }
  
  // Get the saved user ID
  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(userIdKey);
  }
  
  // Logout and clear saved data
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(tokenKey);
    await prefs.remove(tokenExpiryKey);
    await prefs.remove(userIdKey);
  }
  
  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    return token != null;
  }
}
