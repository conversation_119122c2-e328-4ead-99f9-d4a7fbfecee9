import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/services/instagram_auth_service.dart';
import 'package:medroid_app/utils/health_content_filter.dart';

class InstagramContentService {
  static const String apiBaseUrl = 'https://graph.instagram.com';
  
  final InstagramAuthService _authService = InstagramAuthService();
  final HealthContentFilter _healthFilter = HealthContentFilter();
  
  // Fetch user's media from Instagram
  Future<List<SocialPost>> getUserMedia() async {
    final accessToken = await _authService.getAccessToken();
    if (accessToken == null) {
      return [];
    }
    
    try {
      // First get the user's media IDs
      final mediaResponse = await http.get(
        Uri.parse('$apiBaseUrl/me/media?fields=id,caption&access_token=$accessToken'),
      );
      
      if (mediaResponse.statusCode != 200) {
        debugPrint('Error fetching Instagram media: ${mediaResponse.body}');
        return [];
      }
      
      final mediaData = json.decode(mediaResponse.body);
      final mediaItems = mediaData['data'] as List;
      
      // Process each media item to create SocialPost objects
      List<SocialPost> posts = [];
      for (var item in mediaItems) {
        // Get detailed media information
        final detailResponse = await http.get(
          Uri.parse('$apiBaseUrl/${item['id']}?fields=id,caption,media_type,media_url,permalink,timestamp&access_token=$accessToken'),
        );
        
        if (detailResponse.statusCode == 200) {
          final mediaDetail = json.decode(detailResponse.body);
          final caption = mediaDetail['caption'] ?? '';
          
          // Filter for health-related content
          if (_healthFilter.isHealthRelated(caption)) {
            posts.add(SocialPost(
              id: 'instagram_${mediaDetail['id']}',
              source: 'instagram',
              sourceId: mediaDetail['id'],
              contentType: _mapMediaType(mediaDetail['media_type']),
              mediaUrl: mediaDetail['media_url'] ?? mediaDetail['permalink'] ?? '',
              caption: caption,
              healthTopics: _healthFilter.extractHealthTopics(caption),
              relevanceScore: _healthFilter.calculateRelevanceScore(caption),
              engagementMetrics: {'likes': 0, 'shares': 0, 'saves': 0},
              filteredStatus: 'approved',
              createdAt: DateTime.parse(mediaDetail['timestamp']),
            ));
          }
        }
      }
      
      return posts;
    } catch (e) {
      debugPrint('Error fetching Instagram media: $e');
      return [];
    }
  }
  
  // Map Instagram media type to our app's content type
  String _mapMediaType(String instagramType) {
    switch (instagramType.toLowerCase()) {
      case 'image':
        return 'image';
      case 'video':
        return 'video';
      case 'carousel_album':
        return 'image'; // Default to image for carousel
      default:
        return 'text';
    }
  }
}
