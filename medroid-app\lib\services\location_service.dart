import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

class LocationService {
  // API key for location services (if needed)
  // final String _apiKey = 'YOUR_API_KEY';

  // Cache for location suggestions to reduce API calls
  final Map<String, List<Map<String, dynamic>>> _suggestionsCache = {};

  /// Get the current device location
  Future<Position?> getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled
      return null;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are permanently denied
      return null;
    }

    // Get the current position
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      debugPrint('Error getting current location: $e');
      return null;
    }
  }

  /// Get location suggestions based on input text
  Future<List<Map<String, dynamic>>> getLocationSuggestions(
      String query) async {
    if (query.isEmpty) {
      return [];
    }

    // Check cache first
    if (_suggestionsCache.containsKey(query)) {
      return _suggestionsCache[query]!;
    }

    try {
      // Use a free geocoding API (you might want to replace this with a more robust solution)
      final response = await http.get(
        Uri.parse(
            'https://nominatim.openstreetmap.org/search?format=json&q=$query&limit=5'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final suggestions = data
            .map((item) => {
                  'display_name': item['display_name'],
                  'lat': double.parse(item['lat']),
                  'lon': double.parse(item['lon']),
                  // Add a formatted address field for consistency
                  'address': item['display_name'],
                })
            .toList();

        // Cache the results
        _suggestionsCache[query] = suggestions;

        return suggestions;
      }

      return [];
    } catch (e) {
      debugPrint('Error getting location suggestions: $e');
      return [];
    }
  }

  /// Get coordinates from an address string
  Future<Map<String, double>?> getCoordinatesFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        return {
          'latitude': locations.first.latitude,
          'longitude': locations.first.longitude,
        };
      }
      return null;
    } catch (e) {
      debugPrint('Error getting coordinates from address: $e');
      return null;
    }
  }

  /// Calculate distance between two coordinates in kilometers
  double calculateDistance(double startLatitude, double startLongitude,
      double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(
            startLatitude, startLongitude, endLatitude, endLongitude) /
        1000; // Convert meters to kilometers
  }

  /// Format distance for display
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      // If less than 1 km, show in meters
      return '${(distanceInKm * 1000).toStringAsFixed(0)} m';
    } else if (distanceInKm < 10) {
      // If less than 10 km, show with one decimal place
      return '${distanceInKm.toStringAsFixed(1)} km';
    } else {
      // Otherwise, round to nearest km
      return '${distanceInKm.round()} km';
    }
  }
}
