import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:medroid_app/models/notification_model.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';

class NotificationService {
  final ApiService _apiService = ApiService();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // Stream controller for notification count
  final ValueNotifier<int> unreadCountNotifier = ValueNotifier<int>(0);

  // Stream for handling notification taps
  final Stream<RemoteMessage> onMessageOpenedApp =
      FirebaseMessaging.onMessageOpenedApp;

  // Initialize the notification service
  Future<void> initialize() async {
    // Skip initialization on web platform
    if (kIsWeb) {
      debugPrint('Skipping notification initialization on web platform');
      return;
    }

    // Firebase should already be initialized in main.dart

    // Request permission for iOS
    if (Platform.isIOS) {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
    }

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // We're not showing local notifications in this simplified version
      // but we still refresh the unread count
      _showLocalNotification(message);
      _refreshUnreadCount();
    });

    // Get the token and register it with the backend
    _getTokenAndRegister();

    // Refresh unread count
    _refreshUnreadCount();
  }

  // Get the FCM token and register it with the backend
  Future<void> _getTokenAndRegister() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _registerDeviceToken(token);
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _registerDeviceToken(newToken);
      });
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  // Register the device token with the backend
  Future<void> _registerDeviceToken(String token) async {
    try {
      final deviceType = Platform.isAndroid
          ? 'android'
          : Platform.isIOS
              ? 'ios'
              : 'web';

      await _apiService.post(
        Constants.deviceTokenEndpoint,
        {
          'token': token,
          'device_type': deviceType,
        },
      );
    } catch (e) {
      debugPrint('Error registering device token: $e');
    }
  }

  // Show a local notification - simplified version without flutter_local_notifications
  void _showLocalNotification(RemoteMessage message) {
    // In a real app, we would show a local notification here
    // But for this simplified version, we'll just log the notification
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      debugPrint(
          'Received notification: ${notification.title} - ${notification.body}');
      debugPrint('Notification data: $data');
    }
  }

  // Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> data) {
    final String type = data['type'] ?? '';
    debugPrint('Handling notification tap for type: $type');

    // Handle different notification types
    switch (type) {
      case 'appointment_reminder':
        // Navigate to appointment details
        break;
      case 'appointment_booked':
        // Navigate to appointment details
        break;
      case 'appointment_cancelled':
        // Navigate to appointment list
        break;
      default:
        // Navigate to notification list
        break;
    }
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications(
      {int limit = 20, int offset = 0}) async {
    try {
      final response = await _apiService.get(
        '${Constants.notificationsEndpoint}?limit=$limit&offset=$offset',
      );

      final List<NotificationModel> notifications = [];
      if (response != null && response['notifications'] != null) {
        for (final item in response['notifications']) {
          notifications.add(NotificationModel.fromJson(item));
        }
      }

      return notifications;
    } catch (e) {
      debugPrint('Error getting notifications: $e');
      return [];
    }
  }

  // Get unread notification count
  Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.get(Constants.notificationsUnreadCountEndpoint);
      final int count = response != null ? response['unread_count'] ?? 0 : 0;
      unreadCountNotifier.value = count;
      return count;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Refresh unread count
  Future<void> _refreshUnreadCount() async {
    await getUnreadCount();
  }

  // Mark a notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService
          .post('${Constants.notificationsEndpoint}/$notificationId/read', {});
      await _refreshUnreadCount();
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      await _apiService.post('${Constants.notificationsEndpoint}/read-all', {});
      unreadCountNotifier.value = 0;
      return true;
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      return false;
    }
  }
}
