import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PaymentService {
  String? _authToken;

  PaymentService();

  // Initialize Stripe with publishable key
  Future<void> initializeStripe(String publishableKey) async {
    Stripe.publishableKey = publishableKey;
    await Stripe.instance.applySettings();
  }

  // Fetch Stripe configuration from backend
  Future<String> getStripePublishableKey() async {
    try {
      final response = await http.get(
        Uri.parse('${Constants.baseUrl}/stripe/config'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['publishable_key'];
      } else {
        throw Exception('Failed to fetch Strip<PERSON> configuration');
      }
    } catch (e) {
      // Fallback to hardcoded live key if backend is unavailable
      return 'pk_live_51Piw4yP2u2ZJLXn8ly84ngVtPfCxxP62eRmVJWErUrZCWTP3MZ20fXeVCTOgBwmnEULUFeDrzuYU4uDJiJwWBLy700Wq3pO4vy';
    }
  }

  // Load auth token from shared preferences
  Future<void> _loadToken() async {
    if (_authToken != null) return;

    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(Constants.tokenKey);
  }

  // Create a payment intent for an appointment
  Future<Map<String, dynamic>> createPaymentIntent({
    required String appointmentId,
    required double amount,
    required String currency,
    String? providerId,
    String? serviceId,
  }) async {
    await _loadToken();

    final response = await http.post(
      Uri.parse('${Constants.baseUrl}${Constants.paymentIntentEndpoint}'),
      headers: {
        'Content-Type': 'application/json',
        if (_authToken != null) 'Authorization': 'Bearer $_authToken',
      },
      body: jsonEncode({
        'appointment_id': appointmentId,
        'amount': (amount * 100).toInt(), // Convert to cents
        'currency': currency,
        if (providerId != null) 'provider_id': providerId,
        if (serviceId != null) 'service_id': serviceId,
      }),
    );

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create payment intent: ${response.body}');
    }
  }

  // Process payment with card details
  Future<Map<String, dynamic>> processPayment({
    required String paymentIntentId,
    required String paymentIntentClientSecret,
  }) async {
    try {
      if (kIsWeb) {
        // For web, use Stripe Checkout
        return await _processWebPayment(paymentIntentClientSecret);
      } else {
        // For mobile, use Payment Sheet
        await Stripe.instance.initPaymentSheet(
          paymentSheetParameters: SetupPaymentSheetParameters(
            paymentIntentClientSecret: paymentIntentClientSecret,
            merchantDisplayName: 'Medroid Health',
            style: ThemeMode.system,
          ),
        );

        await Stripe.instance.presentPaymentSheet();

        // If we get here, the payment was successful
        return {
          'success': true,
          'payment_intent_id': paymentIntentId,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Process web payment - this will be handled by the WebPaymentForm widget
  Future<Map<String, dynamic>> _processWebPayment(String clientSecret) async {
    // This method is called but the actual payment processing
    // is handled by the WebPaymentForm widget in the UI
    // Return success to indicate the form should be shown
    return {
      'success': true,
      'requires_form': true,
      'message': 'Please complete payment using the form below',
    };
  }

  // Confirm payment on the server
  Future<Map<String, dynamic>> confirmPayment({
    required String paymentIntentId,
    required String appointmentId,
    bool isWebSimulation = false,
  }) async {
    await _loadToken();

    debugPrint(
        'Confirming payment: paymentIntentId=$paymentIntentId, appointmentId=$appointmentId, isWebSimulation=$isWebSimulation');

    // Prepare request body
    final Map<String, dynamic> requestBody = {
      'payment_intent_id': paymentIntentId,
      'appointment_id': appointmentId,
    };

    // For web simulation, add a flag to indicate this is a simulated payment
    if (isWebSimulation) {
      requestBody['is_web_simulation'] = true;
    }

    debugPrint('Payment confirmation request: $requestBody');
    debugPrint(
        'Payment confirmation URL: ${Constants.baseUrl}${Constants.paymentConfirmEndpoint}');
    debugPrint('Auth token available: ${_authToken != null}');

    final response = await http.post(
      Uri.parse('${Constants.baseUrl}${Constants.paymentConfirmEndpoint}'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (_authToken != null) 'Authorization': 'Bearer $_authToken',
      },
      body: jsonEncode(requestBody),
    );

    debugPrint('Payment confirmation response status: ${response.statusCode}');
    debugPrint('Payment confirmation response body: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      final responseData = jsonDecode(response.body);
      return responseData;
    } else {
      final errorMessage = 'Failed to confirm payment: ${response.body}';
      debugPrint(errorMessage);
      throw Exception(errorMessage);
    }
  }
}
