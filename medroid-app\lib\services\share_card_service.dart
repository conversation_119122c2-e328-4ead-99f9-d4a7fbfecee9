import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:image/image.dart' as img;
import '../utils/app_colors.dart';

class ShareCardService {
  static const double cardSize = 1080.0;
  static const double maxFileSizeKB = 200.0;

  /// Generate a share card for any content
  static Future<String?> generateShareCard({
    required String title,
    required String content,
    String? subtitle,
    String? qrCodeData,
    Color? accentColor,
  }) async {
    try {
      // Create the share card widget
      final shareCardWidget = _ShareCardWidget(
        title: title,
        content: content,
        subtitle: subtitle,
        qrCodeData: qrCodeData ?? 'https://medroid.ai',
        accentColor: accentColor ?? AppColors.coralPop,
      );

      // Convert widget to image
      final imageBytes = await _widgetToImage(shareCardWidget);

      // Optimize file size
      final optimizedBytes = await _optimizeImageSize(imageBytes);

      // Save to temporary file
      final filePath = await _saveToTempFile(optimizedBytes);

      return filePath;
    } catch (e) {
      debugPrint('Error generating share card: $e');
      return null;
    }
  }

  /// Convert widget to PNG bytes
  static Future<Uint8List> _widgetToImage(Widget widget) async {
    final repaintBoundary = RenderRepaintBoundary();

    final renderView = RenderView(
      view: WidgetsBinding.instance.platformDispatcher.views.first,
      child: RenderPositionedBox(
        alignment: Alignment.center,
        child: repaintBoundary,
      ),
      configuration: ViewConfiguration.fromView(
        WidgetsBinding.instance.platformDispatcher.views.first,
      ),
    );

    final pipelineOwner = PipelineOwner();
    final buildOwner = BuildOwner(focusManager: FocusManager());

    pipelineOwner.rootNode = renderView;
    renderView.prepareInitialFrame();

    final rootElement = RenderObjectToWidgetAdapter<RenderBox>(
      container: repaintBoundary,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: MediaQuery(
          data: const MediaQueryData(
            size: Size(cardSize, cardSize),
            devicePixelRatio: 1.0,
          ),
          child: widget,
        ),
      ),
    ).attachToRenderTree(buildOwner);

    buildOwner.buildScope(rootElement);
    buildOwner.finalizeTree();

    pipelineOwner.flushLayout();
    pipelineOwner.flushCompositingBits();
    pipelineOwner.flushPaint();

    final image = await repaintBoundary.toImage(pixelRatio: 1.0);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  /// Optimize image size to be under 200KB
  static Future<Uint8List> _optimizeImageSize(Uint8List originalBytes) async {
    if (originalBytes.lengthInBytes <= maxFileSizeKB * 1024) {
      return originalBytes;
    }

    // Decode the image
    img.Image? image = img.decodeImage(originalBytes);
    if (image == null) return originalBytes;

    // Start with quality 90 and reduce until under size limit
    int quality = 90;
    Uint8List? optimizedBytes;

    while (quality > 30) {
      optimizedBytes = Uint8List.fromList(
        img.encodePng(image, level: quality ~/ 10),
      );

      if (optimizedBytes.lengthInBytes <= maxFileSizeKB * 1024) {
        break;
      }

      quality -= 10;
    }

    return optimizedBytes ?? originalBytes;
  }

  /// Save image bytes to temporary file
  static Future<String> _saveToTempFile(Uint8List bytes) async {
    final tempDir = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${tempDir.path}/medroid_share_card_$timestamp.png';

    final file = File(filePath);
    await file.writeAsBytes(bytes);

    return filePath;
  }

  /// Share the generated card
  static Future<void> shareCard(String filePath, {String? text}) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: text ?? 'Shared from Medroid Health App',
      );
    } catch (e) {
      debugPrint('Error sharing card: $e');
    }
  }

  /// Generate and share a milestone card
  static Future<void> shareMilestone({
    required String milestone,
    required String description,
    String? additionalText,
  }) async {
    final filePath = await generateShareCard(
      title: 'Health Milestone',
      content: milestone,
      subtitle: description,
      qrCodeData: 'https://medroid.ai/milestones',
    );

    if (filePath != null) {
      await shareCard(
        filePath,
        text: 'I achieved a health milestone! $milestone\n\n$additionalText',
      );
    }
  }

  /// Generate and share a post card
  static Future<void> sharePost({
    required String postContent,
    String? authorName,
  }) async {
    final filePath = await generateShareCard(
      title: 'Health Insight',
      content: postContent,
      subtitle: authorName != null ? 'by $authorName' : null,
      qrCodeData: 'https://medroid.ai/feed',
    );

    if (filePath != null) {
      await shareCard(
        filePath,
        text: 'Check out this health insight from Medroid!',
      );
    }
  }
}

/// Internal widget for rendering the share card
class _ShareCardWidget extends StatelessWidget {
  final String title;
  final String content;
  final String? subtitle;
  final String qrCodeData;
  final Color accentColor;

  const _ShareCardWidget({
    required this.title,
    required this.content,
    this.subtitle,
    required this.qrCodeData,
    required this.accentColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ShareCardService.cardSize,
      height: ShareCardService.cardSize,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.tealSurge,
            AppColors.mintGlow,
            accentColor.withValues(alpha: 0.8),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // Gradient frame border
          Container(
            margin: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(32),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 0,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(60),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: AppColors.midnightNavy,
                      height: 1.2,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Subtitle if provided
                  if (subtitle != null) ...[
                    Text(
                      subtitle!,
                      style: const TextStyle(
                        fontSize: 28,
                        color: AppColors.slateGrey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 30),
                  ],

                  // Main content
                  Expanded(
                    child: Center(
                      child: Text(
                        content,
                        style: const TextStyle(
                          fontSize: 36,
                          color: AppColors.midnightNavy,
                          fontWeight: FontWeight.w600,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),

                  // Footer with QR code and branding
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Made with Medroid text
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Made with',
                            style: TextStyle(
                              fontSize: 24,
                              color: AppColors.slateGrey,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Medroid',
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: accentColor,
                            ),
                          ),
                        ],
                      ),

                      // QR Code
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.backgroundLight,
                            width: 2,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: QrImageView(
                            data: qrCodeData,
                            version: QrVersions.auto,
                            backgroundColor: Colors.white,
                            eyeStyle: QrEyeStyle(
                              eyeShape: QrEyeShape.square,
                              color: accentColor,
                            ),
                            dataModuleStyle: QrDataModuleStyle(
                              dataModuleShape: QrDataModuleShape.square,
                              color: accentColor,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
