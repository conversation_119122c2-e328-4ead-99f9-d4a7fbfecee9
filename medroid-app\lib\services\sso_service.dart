import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:crypto/crypto.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';

class SSOService {
  final ApiService _apiService = ApiService();

  // Google Sign-In configuration - platform specific
  static const String _googleClientIdWeb = 'YOUR_GOOGLE_WEB_CLIENT_ID';
  static const String _googleClientIdIOS = 'YOUR_GOOGLE_IOS_CLIENT_ID';
  static const String _googleClientIdAndroid = 'YOUR_GOOGLE_ANDROID_CLIENT_ID';

  late final GoogleSignIn _googleSignIn;

  SSOService() {
    _googleSignIn = GoogleSignIn(
      clientId: _getGoogleClientId(),
      scopes: [
        'email',
        'profile',
      ],
    );
  }

  /// Get platform-specific Google Client ID
  String _getGoogleClientId() {
    if (kIsWeb) {
      return _googleClientIdWeb;
    } else if (Platform.isIOS) {
      return _googleClientIdIOS;
    } else if (Platform.isAndroid) {
      return _googleClientIdAndroid;
    } else {
      // Fallback for other platforms
      return _googleClientIdAndroid;
    }
  }

  /// Generate a cryptographically secure nonce for Apple Sign-In
  String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = List.generate(length,
        (i) => charset[DateTime.now().millisecondsSinceEpoch % charset.length]);
    return random.join();
  }

  /// Generate SHA256 hash of the nonce for Apple Sign-In
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Sign in with Google
  Future<Map<String, dynamic>?> signInWithGoogle() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User cancelled the sign-in
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Extract user information
      final userData = {
        'provider': 'google',
        'provider_id': googleUser.id,
        'email': googleUser.email,
        'name': googleUser.displayName ?? '',
        'profile_picture': googleUser.photoUrl,
        'access_token': googleAuth.accessToken,
        'id_token': googleAuth.idToken,
      };

      return userData;
    } catch (error) {
      debugPrint('Google Sign-In error: $error');
      return null;
    }
  }

  /// Sign in with Apple
  Future<Map<String, dynamic>?> signInWithApple() async {
    try {
      // Check if Apple Sign-In is available (not available on web)
      if (kIsWeb) {
        debugPrint('Apple Sign-In is not available on web platform');
        // Return a special response to show user-friendly message
        throw Exception(
            'Apple Sign-In is not available on web. Please use the mobile app or try Google Sign-In.');
      }

      if (!await SignInWithApple.isAvailable()) {
        debugPrint('Apple Sign-In is not available on this device');
        throw Exception('Apple Sign-In is not available on this device.');
      }

      // Generate nonce for security
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      // Request credential from Apple
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );

      // Extract user information
      final userData = {
        'provider': 'apple',
        'provider_id': credential.userIdentifier,
        'email': credential.email ?? '',
        'name': _buildFullName(credential.givenName, credential.familyName),
        'authorization_code': credential.authorizationCode,
        'identity_token': credential.identityToken,
        'nonce': rawNonce,
      };

      return userData;
    } catch (error) {
      debugPrint('Apple Sign-In error: $error');
      return null;
    }
  }

  /// Build full name from given and family names
  String _buildFullName(String? givenName, String? familyName) {
    final parts = <String>[];
    if (givenName != null && givenName.isNotEmpty) parts.add(givenName);
    if (familyName != null && familyName.isNotEmpty) parts.add(familyName);
    return parts.join(' ');
  }

  /// Authenticate with backend using SSO data
  Future<Map<String, dynamic>> authenticateWithBackend(
      Map<String, dynamic> ssoData) async {
    try {
      final response = await _apiService.post(
        Constants.ssoLoginEndpoint,
        ssoData,
        requiresAuth: false,
      );

      return {
        'success': true,
        'user': response['user'],
        'token': response['access_token'],
        'needs_additional_info': response['needs_additional_info'] ?? false,
      };
    } catch (e) {
      debugPrint('SSO backend authentication error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Complete user registration with additional details
  Future<Map<String, dynamic>> completeRegistration({
    required String tempToken,
    String? gender,
    String? dateOfBirth,
    String? referralCode,
  }) async {
    try {
      final data = <String, dynamic>{
        'temp_token': tempToken,
      };

      if (gender != null) data['gender'] = gender;
      if (dateOfBirth != null) data['date_of_birth'] = dateOfBirth;
      if (referralCode != null && referralCode.isNotEmpty) {
        data['referral_code'] = referralCode;
      }

      final response = await _apiService.post(
        Constants.ssoCompleteRegistrationEndpoint,
        data,
        requiresAuth: false,
      );

      return {
        'success': true,
        'user': response['user'],
        'token': response['access_token'],
      };
    } catch (e) {
      debugPrint('SSO complete registration error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Sign out from Google
  Future<void> signOutGoogle() async {
    try {
      await _googleSignIn.signOut();
    } catch (error) {
      debugPrint('Google Sign-Out error: $error');
    }
  }

  /// Check if user is signed in with Google
  Future<bool> isSignedInWithGoogle() async {
    try {
      return await _googleSignIn.isSignedIn();
    } catch (error) {
      debugPrint('Google Sign-In check error: $error');
      return false;
    }
  }
}
