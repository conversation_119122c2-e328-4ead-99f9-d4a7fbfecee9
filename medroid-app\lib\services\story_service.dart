import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:medroid_app/models/story.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/services/auth_service.dart';

class StoryService {
  final AuthService _authService;

  StoryService(this._authService);

  Future<List<StoryGroup>> getStories() async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('${Constants.baseUrl}${Constants.storiesEndpoint}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> storiesJson = data['stories'];
          return storiesJson.map((json) => StoryGroup.fromJson(json)).toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load stories');
        }
      } else {
        throw Exception('Failed to load stories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading stories: $e');
    }
  }

  Future<Story> createStory(File mediaFile,
      {String? caption, String? mediaType}) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${Constants.baseUrl}${Constants.storiesEndpoint}'),
      );

      request.headers['Authorization'] = 'Bearer $token';

      // Add the media file
      request.files
          .add(await http.MultipartFile.fromPath('media', mediaFile.path));

      // Add optional fields
      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }
      if (mediaType != null) {
        request.fields['media_type'] = mediaType;
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return Story.fromJson(data['story']);
        } else {
          throw Exception(data['message'] ?? 'Failed to create story');
        }
      } else {
        throw Exception('Failed to create story: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error creating story: $e');
    }
  }

  Future<Story> createStoryFromBytes(Uint8List bytes, String filename,
      {String? caption, String? mediaType}) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${Constants.baseUrl}${Constants.storiesEndpoint}'),
      );

      request.headers['Authorization'] = 'Bearer $token';

      // Add the media file from bytes
      request.files.add(http.MultipartFile.fromBytes(
        'media',
        bytes,
        filename: filename,
      ));

      // Add optional fields
      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }
      if (mediaType != null) {
        request.fields['media_type'] = mediaType;
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return Story.fromJson(data['story']);
        } else {
          throw Exception(data['message'] ?? 'Failed to create story');
        }
      } else {
        throw Exception('Failed to create story: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error creating story: $e');
    }
  }

  Future<List<Story>> getUserStories(String userId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse(
            '${Constants.baseUrl}${Constants.storiesEndpoint}/user/$userId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> storiesJson = data['stories'];
          return storiesJson.map((json) => Story.fromJson(json)).toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load user stories');
        }
      } else {
        throw Exception('Failed to load user stories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading user stories: $e');
    }
  }

  Future<void> markStoryAsViewed(String storyId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse(
            '${Constants.baseUrl}${Constants.storiesEndpoint}/$storyId${Constants.storyViewEndpoint}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] != true) {
          throw Exception(data['message'] ?? 'Failed to mark story as viewed');
        }
      } else {
        throw Exception(
            'Failed to mark story as viewed: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error marking story as viewed: $e');
    }
  }

  Future<void> deleteStory(String storyId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse('${Constants.baseUrl}${Constants.storiesEndpoint}/$storyId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] != true) {
          throw Exception(data['message'] ?? 'Failed to delete story');
        }
      } else {
        throw Exception('Failed to delete story: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error deleting story: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getStoryViewers(String storyId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse(
            '${Constants.baseUrl}${Constants.storiesEndpoint}/$storyId${Constants.storyViewersEndpoint}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['viewers']);
        } else {
          throw Exception(data['message'] ?? 'Failed to load story viewers');
        }
      } else {
        throw Exception('Failed to load story viewers: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading story viewers: $e');
    }
  }
}
