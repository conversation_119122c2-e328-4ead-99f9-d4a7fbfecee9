import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TikTokAuthService {
  // Replace with your TikTok app credentials
  // These should be stored securely in a config file or environment variables
  static const String clientKey = 'YOUR_TIKTOK_CLIENT_KEY';
  static const String clientSecret = 'YOUR_TIKTOK_CLIENT_SECRET';
  static const String redirectUri = 'medroid://auth/tiktok';
  
  // Scopes needed for video access
  static const String scope = 'user.info.basic,video.list';
  
  // Authentication endpoints
  static const String authUrl = 'https://www.tiktok.com/auth/authorize/';
  static const String tokenUrl = 'https://open-api.tiktok.com/oauth/access_token/';
  static const String refreshTokenUrl = 'https://open-api.tiktok.com/oauth/refresh_token/';
  
  // Storage keys
  static const String tokenKey = 'tiktok_access_token';
  static const String refreshKey = 'tiktok_refresh_token';
  static const String tokenExpiryKey = 'tiktok_token_expiry';
  static const String openIdKey = 'tiktok_open_id';
  
  // Method to initiate TikTok login
  Future<String?> login() async {
    final Uri authUri = Uri.parse(authUrl).replace(queryParameters: {
      'client_key': clientKey,
      'redirect_uri': redirectUri,
      'scope': scope,
      'response_type': 'code',
      'state': _generateRandomState(),
    });
    
    try {
      // Launch the auth URL in a web view
      final result = await FlutterWebAuth.authenticate(
        url: authUri.toString(),
        callbackUrlScheme: redirectUri.split('://')[0],
      );
      
      // Extract the authorization code from the redirect URL
      final code = Uri.parse(result).queryParameters['code'];
      
      // Exchange the code for an access token
      if (code != null) {
        return await _getAccessToken(code);
      }
      return null;
    } catch (e) {
      debugPrint('TikTok login error: $e');
      return null;
    }
  }
  
  // Generate a random state parameter for security
  String _generateRandomState() {
    final random = Random.secure();
    return List.generate(32, (_) => random.nextInt(16).toRadixString(16)).join();
  }
  
  // Exchange authorization code for access token
  Future<String?> _getAccessToken(String code) async {
    try {
      final response = await http.post(
        Uri.parse(tokenUrl),
        body: {
          'client_key': clientKey,
          'client_secret': clientSecret,
          'code': code,
          'grant_type': 'authorization_code',
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['data'] != null) {
          final accessToken = data['data']['access_token'];
          final refreshToken = data['data']['refresh_token'];
          final expiresIn = data['data']['expires_in'];
          final openId = data['data']['open_id'];
          
          // Save the tokens and expiry
          await _saveTokenData(
            accessToken, 
            refreshToken, 
            DateTime.now().add(Duration(seconds: expiresIn)),
            openId,
          );
          
          return accessToken;
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error exchanging code for token: $e');
      return null;
    }
  }
  
  // Refresh the access token when it expires
  Future<String?> _refreshAccessToken(String refreshToken) async {
    try {
      final response = await http.post(
        Uri.parse(refreshTokenUrl),
        body: {
          'client_key': clientKey,
          'client_secret': clientSecret,
          'grant_type': 'refresh_token',
          'refresh_token': refreshToken,
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['data'] != null) {
          final accessToken = data['data']['access_token'];
          final newRefreshToken = data['data']['refresh_token'];
          final expiresIn = data['data']['expires_in'];
          final openId = data['data']['open_id'];
          
          // Save the new tokens
          await _saveTokenData(
            accessToken, 
            newRefreshToken, 
            DateTime.now().add(Duration(seconds: expiresIn)),
            openId,
          );
          
          return accessToken;
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      return null;
    }
  }
  
  // Save the token data securely
  Future<void> _saveTokenData(
    String accessToken, 
    String refreshToken, 
    DateTime expiryDate,
    String openId,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setString(tokenKey, accessToken);
    await prefs.setString(refreshKey, refreshToken);
    await prefs.setString(tokenExpiryKey, expiryDate.toIso8601String());
    await prefs.setString(openIdKey, openId);
  }
  
  // Get the saved access token (refreshing if needed)
  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(tokenKey);
    final refreshToken = prefs.getString(refreshKey);
    final expiryDateStr = prefs.getString(tokenExpiryKey);
    
    // Check if token is expired
    if (token != null && expiryDateStr != null && refreshToken != null) {
      final expiryDate = DateTime.parse(expiryDateStr);
      
      // If token is expired, try to refresh it
      if (expiryDate.isBefore(DateTime.now())) {
        return await _refreshAccessToken(refreshToken);
      }
      
      return token;
    }
    
    return null;
  }
  
  // Get the saved open ID
  Future<String?> getOpenId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(openIdKey);
  }
  
  // Logout and clear saved data
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(tokenKey);
    await prefs.remove(refreshKey);
    await prefs.remove(tokenExpiryKey);
    await prefs.remove(openIdKey);
  }
  
  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    return token != null;
  }
}
