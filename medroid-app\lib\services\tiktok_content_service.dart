import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/services/tiktok_auth_service.dart';
import 'package:medroid_app/utils/health_content_filter.dart';

class TikTokContentService {
  static const String apiBaseUrl = 'https://open-api.tiktok.com/api/v2';
  
  final TikTokAuthService _authService = TikTokAuthService();
  final HealthContentFilter _healthFilter = HealthContentFilter();
  
  // Fetch user's videos from TikTok
  Future<List<SocialPost>> getUserVideos() async {
    final accessToken = await _authService.getAccessToken();
    final openId = await _authService.getOpenId();
    
    if (accessToken == null || openId == null) {
      return [];
    }
    
    try {
      // Get user's videos
      final videoResponse = await http.get(
        Uri.parse('$apiBaseUrl/video/list/?fields=id,video_description,embed_link,thumbnail_url,create_time&access_token=$accessToken&open_id=$openId'),
      );
      
      if (videoResponse.statusCode != 200) {
        debugPrint('Error fetching TikTok videos: ${videoResponse.body}');
        return [];
      }
      
      final videoData = json.decode(videoResponse.body);
      
      // Check if the response has the expected structure
      if (!videoData.containsKey('data') || 
          !videoData['data'].containsKey('videos')) {
        debugPrint('Unexpected TikTok API response format: $videoData');
        return [];
      }
      
      final videoItems = videoData['data']['videos'] as List;
      
      // Process each video to create SocialPost objects
      List<SocialPost> posts = [];
      for (var video in videoItems) {
        final description = video['video_description'] ?? '';
        
        // Filter for health-related content
        if (_healthFilter.isHealthRelated(description)) {
          posts.add(SocialPost(
            id: 'tiktok_${video['id']}',
            source: 'tiktok',
            sourceId: video['id'],
            contentType: 'video',
            mediaUrl: video['embed_link'] ?? video['share_url'] ?? '',
            caption: description,
            healthTopics: _healthFilter.extractHealthTopics(description),
            relevanceScore: _healthFilter.calculateRelevanceScore(description),
            engagementMetrics: {'likes': 0, 'shares': 0, 'saves': 0},
            filteredStatus: 'approved',
            createdAt: DateTime.parse(video['create_time']),
          ));
        }
      }
      
      return posts;
    } catch (e) {
      debugPrint('Error fetching TikTok videos: $e');
      return [];
    }
  }
}
