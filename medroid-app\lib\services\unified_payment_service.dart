import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Unified payment service that handles payment processing for both mobile and web platforms
class UnifiedPaymentService {
  String? _authToken;
  
  /// Initialize the service
  UnifiedPaymentService();
  
  /// Create a payment intent for an appointment
  Future<Map<String, dynamic>> createPaymentIntent({
    required String appointmentId,
    required double amount,
    required String currency,
    String? providerId,
    String? serviceId,
  }) async {
    await _loadToken();
    
    final response = await http.post(
      Uri.parse('${Constants.baseUrl}${Constants.paymentIntentEndpoint}'),
      headers: {
        'Content-Type': 'application/json',
        if (_authToken != null) 'Authorization': 'Bearer $_authToken',
      },
      body: jsonEncode({
        'appointment_id': appointmentId,
        'amount': (amount * 100).toInt(), // Convert to cents
        'currency': currency,
        if (providerId != null) 'provider_id': providerId,
        if (serviceId != null) 'service_id': serviceId,
      }),
    );
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create payment intent: ${response.body}');
    }
  }
  
  /// Confirm a payment with the server
  Future<Map<String, dynamic>> confirmPayment({
    required String paymentIntentId,
    required String appointmentId,
  }) async {
    await _loadToken();
    
    debugPrint('Confirming payment: intent=$paymentIntentId, appointment=$appointmentId');
    
    final response = await http.post(
      Uri.parse('${Constants.baseUrl}${Constants.paymentConfirmEndpoint}'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (_authToken != null) 'Authorization': 'Bearer $_authToken',
      },
      body: jsonEncode({
        'payment_intent_id': paymentIntentId,
        'appointment_id': appointmentId,
      }),
    );
    
    debugPrint('Payment confirmation response: ${response.statusCode}');
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final responseData = jsonDecode(response.body);
      return responseData;
    } else {
      final errorMessage = 'Failed to confirm payment: ${response.body}';
      debugPrint(errorMessage);
      throw Exception(errorMessage);
    }
  }
  
  /// Confirm a payment processed by Stripe Elements on web
  Future<Map<String, dynamic>> confirmElementsPayment({
    required String paymentIntentId,
    required String appointmentId,
  }) async {
    await _loadToken();
    
    final response = await http.post(
      Uri.parse('${Constants.baseUrl}/payments/confirm-elements-payment'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (_authToken != null) 'Authorization': 'Bearer $_authToken',
      },
      body: jsonEncode({
        'payment_intent_id': paymentIntentId,
        'appointment_id': appointmentId,
      }),
    );
    
    final responseData = jsonDecode(response.body);
    
    if (response.statusCode == 200) {
      return responseData;
    } else {
      return {
        'success': false,
        'message': responseData['message'] ?? 'Payment confirmation failed',
      };
    }
  }
  
  /// Get the Stripe publishable key from the server
  Future<String> getStripePublishableKey() async {
    try {
      final response = await http.get(
        Uri.parse('${Constants.baseUrl}/stripe/config'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['publishable_key'];
      } else {
        throw Exception('Failed to fetch Stripe configuration');
      }
    } catch (e) {
      // Fallback to test key for development (kDebugMode) or live key for production
      if (kDebugMode) {
        debugPrint('Using test Stripe key for development');
        return 'pk_test_51Piw4yP2u2ZJLXn8MsD6o2CkSNurqM7lbBVySFQQVHX2eZFWIXhvj5zpADBCHGW9EHKX7IvmfOsJnkMbMc0TBq1v00pfRbw8aM';
      } else {
        debugPrint('Using live Stripe key for production');
        return 'pk_live_51Piw4yP2u2ZJLXn8ly84ngVtPfCxxP62eRmVJWErUrZCWTP3MZ20fXeVCTOgBwmnEULUFeDrzuYU4uDJiJwWBLy700Wq3pO4vy';
      }
    }
  }
  
  /// Load the auth token from shared preferences
  Future<void> _loadToken() async {
    if (_authToken != null) return;
    
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(Constants.tokenKey);
  }
}