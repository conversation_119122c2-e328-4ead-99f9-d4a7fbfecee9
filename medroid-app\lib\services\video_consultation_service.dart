import 'package:flutter/material.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/agora_service.dart';
import 'package:medroid_app/services/agora_service_factory.dart';

/// A service to handle video consultations using Agora
class VideoConsultationService {
  final ApiService _apiService;
  late final AgoraService _agoraService;

  VideoConsultationService(this._apiService) {
    _agoraService = AgoraServiceFactory.createAgoraService(_apiService);
  }

  /// Initialize a video consultation for an appointment
  Future<Map<String, dynamic>> initializeVideoSession(
      String appointmentId) async {
    try {
      final response = await _apiService.initializeVideoSession(appointmentId);
      return response;
    } catch (e) {
      debugPrint('Error initializing video session: $e');
      return {
        'success': false,
        'message': 'Failed to initialize video session: $e',
      };
    }
  }

  /// Join a video consultation using Agora
  Future<bool> joinVideoConsultation({
    required String appointmentId,
    required bool isProvider,
    required String userName,
  }) async {
    try {
      debugPrint('VideoConsultationService: Joining Agora video consultation');
      debugPrint('Appointment ID: $appointmentId');
      debugPrint('Is Provider: $isProvider');
      debugPrint('User Name: $userName');

      // Initialize Agora
      await _agoraService.initialize();

      // Join the channel
      final success = await _agoraService.joinChannel(
        appointmentId: appointmentId,
        isProvider: isProvider,
      );

      return success;
    } catch (e) {
      debugPrint('Error joining video consultation: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _agoraService.dispose();
  }
}
