import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:medroid_app/models/notification_model.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';

/// A web-compatible version of the NotificationService
/// This is used when running on web platforms to avoid Firebase dependencies
class WebNotificationService {
  final ApiService _apiService = ApiService();

  // Stream controller for notification count
  final ValueNotifier<int> unreadCountNotifier = ValueNotifier<int>(0);

  // Stream controller for notification events
  final StreamController<Map<String, dynamic>> _notificationStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Stream for handling notification clicks
  Stream<Map<String, dynamic>> get onNotificationClick =>
      _notificationStreamController.stream;

  // Timer for polling
  Timer? _pollingTimer;

  // Initialize the notification service
  Future<void> initialize() async {
    debugPrint('Web notification service initialized');

    // Refresh unread count
    _refreshUnreadCount();

    // Set up polling for new notifications
    _setupPolling();
  }

  // Set up polling for new notifications
  void _setupPolling() {
    // Cancel any existing timer
    _pollingTimer?.cancel();

    // Poll every 30 seconds for new notifications
    _pollingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkForNewNotifications();
    });
  }

  // Check for new notifications
  Future<void> _checkForNewNotifications() async {
    try {
      final currentCount = unreadCountNotifier.value;
      final newCount = await getUnreadCount();

      if (newCount > currentCount) {
        // We have new notifications, fetch the latest ones
        final notifications =
            await getNotifications(limit: newCount - currentCount, offset: 0);

        // Update the UI to show a notification indicator
        if (notifications.isNotEmpty) {
          debugPrint('New notifications available: ${notifications.length}');

          // Emit the latest notification to the stream
          if (_notificationStreamController.hasListener) {
            _notificationStreamController.add({
              'type': 'new_notification',
              'count': notifications.length,
              'latest': notifications.first.toJson(),
            });
          }
        }
      }
    } catch (e) {
      debugPrint('Error checking for new notifications: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _pollingTimer?.cancel();
    _notificationStreamController.close();
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications(
      {int limit = 20, int offset = 0}) async {
    try {
      final response = await _apiService.get(
        '${Constants.notificationsEndpoint}?limit=$limit&offset=$offset',
      );

      final List<NotificationModel> notifications = [];
      if (response != null && response['notifications'] != null) {
        for (final item in response['notifications']) {
          notifications.add(NotificationModel.fromJson(item));
        }
      }

      return notifications;
    } catch (e) {
      debugPrint('Error getting notifications: $e');
      return [];
    }
  }

  // Get unread notification count
  Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.get(Constants.notificationsUnreadCountEndpoint);
      final int count = response != null ? response['unread_count'] ?? 0 : 0;
      unreadCountNotifier.value = count;
      return count;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Refresh unread count
  Future<void> _refreshUnreadCount() async {
    await getUnreadCount();
  }

  // Mark a notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService
          .post('${Constants.notificationsEndpoint}/$notificationId/read', {});
      await _refreshUnreadCount();
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      await _apiService.post('${Constants.notificationsEndpoint}/read-all', {});
      unreadCountNotifier.value = 0;
      return true;
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      return false;
    }
  }
}
