import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// This is a utility class to create simple icon files
class IconCreator {
  static Future<void> createIcons() async {
    // Create a simple pink circle with "M" for Medroid
    final medroidIcon = await _createCircleIcon(
      size: 64,
      backgroundColor: const Color(0xFFEC4899),
      text: 'M',
      textColor: Colors.white,
    );
    await _saveIconToFile('assets/icons/medroid.png', medroidIcon);

    // Create a simple orange-pink gradient circle with camera icon for Instagram
    final instagramIcon = await _createCircleIcon(
      size: 64,
      backgroundColor: const Color(0xFFF56040),
      text: 'IG',
      textColor: Colors.white,
    );
    await _saveIconToFile('assets/icons/instagram.png', instagramIcon);

    // Create a simple black circle with "TT" for TikTok
    final tiktokIcon = await _createCircleIcon(
      size: 64,
      backgroundColor: Colors.black,
      text: 'TT',
      textColor: Colors.white,
    );
    await _saveIconToFile('assets/icons/tiktok.png', tiktokIcon);

    // Create a simple blue circle with "S" for Social
    final socialIcon = await _createCircleIcon(
      size: 64,
      backgroundColor: const Color(0xFF4267B2),
      text: 'S',
      textColor: Colors.white,
    );
    await _saveIconToFile('assets/icons/social.png', socialIcon);
  }

  static Future<Uint8List> _createCircleIcon({
    required double size,
    required Color backgroundColor,
    required String text,
    required Color textColor,
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    // Draw circle
    canvas.drawCircle(Offset(size / 2, size / 2), size / 2, paint);

    // Add text
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          color: textColor,
          fontSize: size * 0.4,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        size / 2 - textPainter.width / 2,
        size / 2 - textPainter.height / 2,
      ),
    );

    // Convert to image
    final picture = recorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  static Future<void> _saveIconToFile(String path, Uint8List bytes) async {
    final file = File(path);
    await file.writeAsBytes(bytes);
    print('Created icon: $path');
  }
}
