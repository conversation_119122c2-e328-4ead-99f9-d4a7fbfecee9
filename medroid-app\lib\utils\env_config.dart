import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Environment configuration class to manage environment variables
class EnvConfig {
  /// Initialize environment configuration
  /// For web, we use hardcoded values
  /// For mobile, we try to load from .env files with fallback to hardcoded values
  static Future<void> init({String env = 'production'}) async {
    // For web, we'll always use hardcoded production values
    if (kIsWeb) {
      debugPrint('Web platform detected, using hardcoded production URLs');
      debugPrint('API Base URL: $apiBaseUrl');
      debugPrint('API Storage URL: $apiStorageUrl');
      return;
    }

    // For mobile, try to load from .env files
    try {
      String fileName;

      // Determine which environment file to load
      if (env == 'development') {
        fileName = '.env.development';
      } else if (env == 'production') {
        fileName = '.env.production';
      } else {
        fileName = '.env';
      }

      // Load the environment file
      await dotenv.load(fileName: fileName);

      // Print loaded environment variables for debugging
      debugPrint('Environment loaded: $env');
      debugPrint('API Base URL: $apiBaseUrl');
      debugPrint('API Storage URL: $apiStorageUrl');
    } catch (e) {
      debugPrint('Error loading environment: $e');
      // If we can't load the env file, we'll use default values in the constants
      debugPrint('Using default values');
    }
  }

  /// Get API base URL from environment or fallback to default
  static String get apiBaseUrl {
    // Check if we're in debug mode (development)
    if (kDebugMode) {
      // Use localhost for development
      // Note: The backend CORS is configured to accept requests from any localhost port,
      // so Flutter web development server can use any random port
      return 'http://localhost:8000/api';
    } else {
      // Use production URL for builds (web/APK/iOS)
      return 'https://backend.medroid.ai/api';
    }
  }

  /// Get API storage URL from environment or fallback to default
  static String get apiStorageUrl {
    // Check if we're in debug mode (development)
    if (kDebugMode) {
      // Use localhost for development
      return 'http://localhost:8000';
    } else {
      // Use production URL for builds (web/APK/iOS)
      return 'https://backend.medroid.ai';
    }
  }
}
