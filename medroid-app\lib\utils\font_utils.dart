import 'package:flutter/material.dart';

class FontUtils {
  /// Creates a TextStyle with fallback fonts to ensure all characters are displayed
  static TextStyle withFallbackFonts(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontFamilyFallback: const [
        'NotoSansCJK',
        'NotoSansSymbols',
      ],
    );
  }

  /// Wraps a Text widget with fallback fonts
  static Widget textWithFallback(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    TextOverflow? overflow,
    int? maxLines,
    TextDirection? textDirection,
    bool? softWrap,
  }) {
    final effectiveStyle = style ?? const TextStyle();

    return Text(
      text,
      style: withFallbackFonts(effectiveStyle),
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      textDirection: textDirection,
      softWrap: softWrap,
    );
  }
}
