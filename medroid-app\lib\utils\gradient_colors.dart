import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

/// Defines the gradient colors used throughout the app
class GradientColors {
  /// The main gradient used for primary buttons and important UI elements
  static const List<Color> primaryGradient = [
    AppColors.tealSurge,
    AppColors.mintGlow,
  ];

  /// Stops for the primary gradient
  static const List<double> primaryGradientStops = [0.0, 1.0];

  /// Creates a linear gradient with the primary colors
  static LinearGradient getPrimaryGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: primaryGradient,
      stops: primaryGradientStops,
      begin: begin,
      end: end,
    );
  }

  /// The accent gradient used for buttons with coral pop
  static const List<Color> accentGradient = [
    AppColors.coralPop,
    Color(0xFFFF8A65), // Lighter coral
  ];

  /// Creates a linear gradient with the accent colors
  static LinearGradient getAccentGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: accentGradient,
      stops: primaryGradientStops,
      begin: begin,
      end: end,
    );
  }

  /// Creates a box decoration with the primary gradient
  static BoxDecoration getPrimaryGradientDecoration({
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(16)),
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      gradient: getPrimaryGradient(),
      borderRadius: borderRadius,
      boxShadow: boxShadow,
    );
  }

  /// Creates a box decoration with the accent gradient
  static BoxDecoration getAccentGradientDecoration({
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(16)),
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      gradient: getAccentGradient(),
      borderRadius: borderRadius,
      boxShadow: boxShadow,
    );
  }
}
