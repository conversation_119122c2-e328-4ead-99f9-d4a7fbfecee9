import 'package:flutter/material.dart';

/// Helper class for managing gradients throughout the app
class GradientHelper {
  /// The main gradient used throughout the app
  /// Pink to purple gradient
  static const LinearGradient mainGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFEC4899), // pink-500
      Color(0xFFA855F7), // purple-500
      Color(0xFF6B21A8), // purple-800
    ],
    stops: [0.0, 0.5, 1.0],
  );

  /// A lighter version of the main gradient for subtle UI elements
  static const LinearGradient lightGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFCE7F3), // pink-100
      Color(0xFFF5D0FE), // purple-200
      Color(0xFFE9D5FF), // purple-200
    ],
    stops: [0.0, 0.5, 1.0],
  );

  /// A darker version of the main gradient for dark mode
  static const LinearGradient darkGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFBE185D), // pink-700
      Color(0xFF7E22CE), // purple-700
      Color(0xFF581C87), // purple-900
    ],
    stops: [0.0, 0.5, 1.0],
  );

  /// Creates a gradient shader for text
  static Shader getTextGradient(Rect bounds) {
    return mainGradient.createShader(bounds);
  }

  /// Creates a gradient decoration for containers
  static BoxDecoration getGradientDecoration({
    BorderRadius? borderRadius,
    bool isDark = false,
  }) {
    return BoxDecoration(
      gradient: isDark ? darkGradient : mainGradient,
      borderRadius: borderRadius,
    );
  }

  /// Creates a light gradient decoration for containers
  static BoxDecoration getLightGradientDecoration({
    BorderRadius? borderRadius,
  }) {
    return BoxDecoration(
      gradient: lightGradient,
      borderRadius: borderRadius,
    );
  }
}
