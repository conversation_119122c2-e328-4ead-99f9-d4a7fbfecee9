class HealthContentFilter {
  // List of health-related keywords for filtering content
  final List<String> _healthKeywords = [
    'health', 'wellness', 'fitness', 'nutrition', 'diet',
    'exercise', 'workout', 'medical', 'medicine', 'doctor',
    'hospital', 'clinic', 'therapy', 'mental health', 'wellbeing',
    'healthcare', 'healthy', 'patient', 'treatment', 'diagnosis',
    'symptom', 'disease', 'condition', 'recovery', 'healing',
    'prevention', 'vitamin', 'supplement', 'protein', 'carb',
    'fat', 'calorie', 'weight', 'muscle', 'strength',
    'cardio', 'yoga', 'meditation', 'mindfulness', 'stress',
    'anxiety', 'depression', 'sleep', 'rest', 'hydration',
    'water', 'immune', 'immunity', 'vaccine', 'vaccination',
    'drug', 'medication', 'prescription', 'pharmacy', 'pharmacist',
    'nurse', 'physician', 'specialist', 'surgeon', 'surgery',
    'procedure', 'checkup', 'exam', 'test', 'scan',
    'x-ray', 'mri', 'ct scan', 'ultrasound', 'blood test',
    'urine test', 'lab', 'laboratory', 'research', 'study',
    'clinical', 'trial', 'experiment', 'data', 'evidence',
    'science', 'scientific', 'biology', 'anatomy', 'physiology',
    'psychology', 'psychiatry', 'therapy', 'therapist', 'counseling',
    'counselor', 'mental', 'brain', 'heart', 'lung',
    'liver', 'kidney', 'stomach', 'intestine', 'bone',
    'joint', 'muscle', 'tendon', 'ligament', 'skin',
    'hair', 'nail', 'eye', 'ear', 'nose',
    'throat', 'mouth', 'teeth', 'dental', 'dentist',
    'orthodontist', 'dermatologist', 'cardiologist', 'neurologist', 'oncologist',
    'pediatrician', 'gynecologist', 'obstetrician', 'urologist', 'endocrinologist',
    'rheumatologist', 'gastroenterologist', 'pulmonologist', 'nephrologist', 'hematologist',
  ];

  // Map of health topics to related keywords
  final Map<String, List<String>> _topicKeywords = {
    'fitness': [
      'workout', 'exercise', 'gym', 'training', 'fitness',
      'cardio', 'strength', 'muscle', 'weight', 'run',
      'jog', 'swim', 'bike', 'cycle', 'yoga',
      'pilates', 'stretch', 'flexibility', 'mobility', 'endurance',
    ],
    'nutrition': [
      'diet', 'food', 'nutrition', 'eating', 'meal',
      'protein', 'carb', 'fat', 'vitamin', 'mineral',
      'supplement', 'calorie', 'weight', 'healthy eating', 'vegetable',
      'fruit', 'grain', 'dairy', 'meat', 'plant-based',
    ],
    'mental health': [
      'mental', 'anxiety', 'depression', 'stress', 'therapy',
      'counseling', 'psychiatry', 'psychology', 'mindfulness', 'meditation',
      'brain', 'mood', 'emotion', 'feeling', 'wellbeing',
      'self-care', 'trauma', 'disorder', 'adhd', 'bipolar',
    ],
    'medical': [
      'doctor', 'hospital', 'clinic', 'treatment', 'medicine',
      'medication', 'drug', 'prescription', 'symptom', 'disease',
      'condition', 'diagnosis', 'patient', 'procedure', 'surgery',
      'specialist', 'physician', 'nurse', 'healthcare', 'medical',
    ],
    'preventive care': [
      'prevention', 'vaccine', 'vaccination', 'checkup', 'screening',
      'exam', 'test', 'immune', 'immunity', 'hygiene',
      'clean', 'sanitize', 'wash', 'protect', 'safety',
      'precaution', 'risk', 'factor', 'avoid', 'reduce',
    ],
    'sleep': [
      'sleep', 'rest', 'insomnia', 'apnea', 'snore',
      'dream', 'nap', 'tired', 'fatigue', 'exhaustion',
      'bed', 'pillow', 'mattress', 'night', 'circadian',
      'rhythm', 'cycle', 'rem', 'deep sleep', 'quality sleep',
    ],
  };

  // Check if content is health-related
  bool isHealthRelated(String text) {
    final lowerText = text.toLowerCase();
    return _healthKeywords.any((keyword) => lowerText.contains(keyword));
  }
  
  // Extract health topics from text
  List<String> extractHealthTopics(String text) {
    final lowerText = text.toLowerCase();
    final Set<String> topics = {};
    
    _topicKeywords.forEach((topic, keywords) {
      for (final keyword in keywords) {
        if (lowerText.contains(keyword)) {
          topics.add(topic);
          break;
        }
      }
    });
    
    return topics.isEmpty ? ['general health'] : topics.toList();
  }
  
  // Calculate relevance score based on health keywords
  double calculateRelevanceScore(String text) {
    final lowerText = text.toLowerCase();
    int matchCount = 0;
    final Set<String> matchedKeywords = {};
    
    // Count unique keyword matches
    for (final keyword in _healthKeywords) {
      if (lowerText.contains(keyword) && !matchedKeywords.contains(keyword)) {
        matchCount++;
        matchedKeywords.add(keyword);
      }
    }
    
    // Calculate score based on keyword density
    // More matches = higher relevance, but with diminishing returns
    final wordCount = text.split(' ').length;
    if (wordCount == 0) return 0.0;
    
    final keywordDensity = matchCount / wordCount;
    final baseScore = matchCount / 10; // 10 matches would give a score of 1.0
    
    // Combine density and match count for final score
    final score = (baseScore * 0.7) + (keywordDensity * 5 * 0.3);
    
    // Clamp score between 0.0 and 1.0
    return score.clamp(0.0, 1.0);
  }
}
