// This is a stub implementation of the dart:html library for non-web platforms
// It provides empty implementations of the classes and methods used in the web code

class MessageEvent {
  final dynamic data;
  MessageEvent(this.data);
}

class MessagePort {
  void postMessage(dynamic message) {}
}

class MessageStream {
  void listen(Function(MessageEvent) callback) {}
}

class Window {
  // Add any other methods or properties used in the web code
  Document document = Document();

  // Event listener stub
  void addEventListener(String event, Function(dynamic) callback) {}

  // Operator to access properties
  dynamic operator [](String key) => null;

  // Add onMessage property
  MessageStream get onMessage => MessageStream();
}

class Document {
  dynamic getElementById(String id) => null;
  dynamic querySelector(String selector) => null;
  Body get body => Body();
}

class Body {
  void append(dynamic element) {}
}

class ScriptElement {
  String id = '';
  String text = '';
  String type = '';

  void remove() {}
}

class DivElement {
  String id = '';
  dynamic style = _ElementStyle();
  String? text;

  void remove() {}
}

class _ElementStyle {
  String display = '';
}

class JsObject {
  static JsObject fromBrowserObject(dynamic object) => JsObject();

  dynamic operator [](String key) => null;
}

// Stub for the global window object
final Window window = Window();
