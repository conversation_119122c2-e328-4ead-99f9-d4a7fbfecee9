import 'platform_html_web.dart' if (dart.library.io) 'platform_html_stub.dart';
import 'dart:async';

/// A class that provides platform-specific HTML operations
class PlatformHtml {
  /// Appends an element to the document body
  /// This is a no-op on non-web platforms
  static void appendToBody(dynamic element) {
    PlatformHtmlWeb.appendToBody(element);
  }

  /// Executes JavaScript code
  /// This is a no-op on non-web platforms
  static void executeJavaScript(String script) {
    PlatformHtmlWeb.executeJavaScript(script);
  }
  
  /// Check if Stripe.js is available
  /// Returns false on non-web platforms
  static bool hasStripeJs() {
    return PlatformHtmlWeb.hasStripeJs();
  }
  
  /// Initialize Stripe with the publishable key
  /// Returns false on non-web platforms
  static bool initializeStripe(String publishableKey) {
    return PlatformHtmlWeb.initializeStripe(publishableKey);
  }
  
  /// Create a card element
  /// Returns false on non-web platforms
  static bool createCardElement() {
    return PlatformHtmlWeb.createCardElement();
  }
  
  /// Register a card element for Flutter to use
  /// This is a no-op on non-web platforms
  static void registerCardElement(String elementId) {
    PlatformHtmlWeb.registerCardElement(elementId);
  }
  
  /// Mount the card element to a DOM element
  /// Returns false on non-web platforms
  static bool mountCardElement(String elementId) {
    return PlatformHtmlWeb.mountCardElement(elementId);
  }
  
  /// Confirm a card payment
  /// Returns an error map on non-web platforms
  static Future<Map<String, dynamic>?> confirmCardPayment(
    String clientSecret,
    String cardholderName,
  ) {
    return PlatformHtmlWeb.confirmCardPayment(clientSecret, cardholderName);
  }
  
  /// Clean up Stripe elements
  /// This is a no-op on non-web platforms
  static void cleanupStripe() {
    PlatformHtmlWeb.cleanupStripe();
  }
}