import 'platform_html.dart';
import 'dart:async';

/// Stub implementation of PlatformHtmlWeb
/// This file is used on non-web platforms
class PlatformHtmlWeb implements PlatformHtml {
  /// Stub implementation of appendToBody
  static void appendToBody(dynamic element) {
    // No-op on non-web platforms
  }

  /// Stub implementation of executeJavaScript
  static void executeJavaScript(String script) {
    // No-op on non-web platforms
  }
  
  /// Stub implementation of hasStripeJs
  static bool hasStripeJs() {
    return false;
  }
  
  /// Stub implementation of initializeStripe
  static bool initializeStripe(String publishableKey) {
    return false;
  }
  
  /// Stub implementation of createCardElement
  static bool createCardElement() {
    return false;
  }
  
  /// Stub implementation of registerCardElement
  static void registerCardElement(String elementId) {
    // No-op on non-web platforms
  }
  
  /// Stub implementation of mountCardElement
  static bool mountCardElement(String elementId) {
    return false;
  }
  
  /// Stub implementation of confirmCardPayment
  static Future<Map<String, dynamic>?> confirmCardPayment(
    String clientSecret,
    String cardholderName,
  ) {
    return Future.value({
      'success': false,
      'error': 'Stripe payments are not supported on this platform'
    });
  }
  
  /// Stub implementation of cleanupStripe
  static void cleanupStripe() {
    // No-op on non-web platforms
  }
}