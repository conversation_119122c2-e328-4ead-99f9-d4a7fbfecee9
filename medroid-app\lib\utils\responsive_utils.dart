import 'package:flutter/material.dart';

/// A utility class for responsive design
///
/// This class provides methods and constants for responsive design
/// across different screen sizes and devices.
class ResponsiveUtils {
  // Screen size breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;

  /// Returns true if the screen width is less than or equal to the mobile breakpoint
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width <= mobileBreakpoint;
  }

  /// Returns true if the screen width is between the mobile and tablet breakpoints
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width > mobileBreakpoint && width <= tabletBreakpoint;
  }

  /// Returns true if the screen width is greater than the tablet breakpoint
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width > tabletBreakpoint;
  }

  /// Returns the appropriate value based on the screen size
  ///
  /// Example:
  /// ```dart
  /// final padding = ResponsiveUtils.value(
  ///   context,
  ///   mobile: 8.0,
  ///   tablet: 16.0,
  ///   desktop: 24.0,
  /// );
  /// ```
  static T value<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    required T desktop,
  }) {
    if (isDesktop(context)) {
      return desktop;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  /// Returns a responsive padding based on screen size
  static EdgeInsets responsivePadding(BuildContext context) {
    return value(
      context: context,
      mobile: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      tablet: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      desktop: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 24.0),
    );
  }

  /// Returns a responsive horizontal padding based on screen size
  static EdgeInsets responsiveHorizontalPadding(BuildContext context) {
    return value(
      context: context,
      mobile: const EdgeInsets.symmetric(horizontal: 16.0),
      tablet: const EdgeInsets.symmetric(horizontal: 24.0),
      desktop: const EdgeInsets.symmetric(horizontal: 32.0),
    );
  }

  /// Returns a responsive vertical padding based on screen size
  static EdgeInsets responsiveVerticalPadding(BuildContext context) {
    return value(
      context: context,
      mobile: const EdgeInsets.symmetric(vertical: 8.0),
      tablet: const EdgeInsets.symmetric(vertical: 16.0),
      desktop: const EdgeInsets.symmetric(vertical: 24.0),
    );
  }

  /// Returns a responsive font size based on screen size
  static double responsiveFontSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    required double desktop,
  }) {
    return value(
      context: context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Returns a responsive width based on screen size and percentage
  static double responsiveWidth(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.width * percentage;
  }

  /// Returns a responsive height based on screen size and percentage
  static double responsiveHeight(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.height * percentage;
  }

  /// Returns a responsive width constraint based on screen size
  static BoxConstraints responsiveWidthConstraint(BuildContext context) {
    return value(
      context: context,
      mobile: const BoxConstraints(maxWidth: 600),
      tablet: const BoxConstraints(maxWidth: 800),
      desktop: const BoxConstraints(maxWidth: 1200),
    );
  }

  /// Returns a responsive container width based on screen size
  static double responsiveContainerWidth(BuildContext context) {
    return value(
      context: context,
      mobile: MediaQuery.of(context).size.width * 0.9,
      tablet: MediaQuery.of(context).size.width * 0.8,
      desktop: MediaQuery.of(context).size.width * 0.7,
    );
  }

  /// Returns a responsive grid column count based on screen size
  static int responsiveGridCount(BuildContext context) {
    return value(
      context: context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
    );
  }
}

/// A responsive widget that adapts to different screen sizes
///
/// Example:
/// ```dart
/// ResponsiveBuilder(
///   mobile: MobileView(),
///   tablet: TabletView(),
///   desktop: DesktopView(),
/// )
/// ```
class ResponsiveBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const ResponsiveBuilder({
    Key? key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (ResponsiveUtils.isDesktop(context)) {
      return desktop;
    } else if (ResponsiveUtils.isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
}

/// A responsive layout widget that adapts to different screen sizes
///
/// Example:
/// ```dart
/// ResponsiveLayout(
///   mobileBody: MobileView(),
///   tabletBody: TabletView(),
///   desktopBody: DesktopView(),
/// )
/// ```
class ResponsiveLayout extends StatelessWidget {
  final Widget mobileBody;
  final Widget? tabletBody;
  final Widget desktopBody;

  const ResponsiveLayout({
    Key? key,
    required this.mobileBody,
    this.tabletBody,
    required this.desktopBody,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > ResponsiveUtils.tabletBreakpoint) {
          return desktopBody;
        } else if (constraints.maxWidth > ResponsiveUtils.mobileBreakpoint) {
          return tabletBody ?? mobileBody;
        } else {
          return mobileBody;
        }
      },
    );
  }
}
