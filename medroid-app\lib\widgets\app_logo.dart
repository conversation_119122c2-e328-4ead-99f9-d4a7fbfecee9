import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

/// A widget that displays the app logo
///
/// This widget can be used in various places throughout the app
/// to maintain a consistent branding.
class AppLogo extends StatelessWidget {
  /// The size of the logo
  final double size;

  /// Whether to show shadow under the logo
  final bool showShadow;

  /// Whether to animate the logo
  final bool animate;

  /// Custom padding around the logo
  final EdgeInsetsGeometry? padding;

  /// Creates an AppLogo widget
  ///
  /// The [size] parameter controls both width and height.
  const AppLogo({
    Key? key,
    this.size = 80.0,
    this.showShadow = true,
    this.animate = false,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget logo = Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Container(
        height: size,
        width: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            colors: [
              AppColors.tealSurge,
              AppColors.mintGlow,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: showShadow
              ? [
                  BoxShadow(
                    color: const Color.fromRGBO(23, 195, 178, 0.3),
                    blurRadius: size * 0.15,
                    spreadRadius: size * 0.05,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Center(
          child: Icon(
            Icons.medical_services_rounded,
            size: size * 0.5,
            color: Colors.white,
          ),
        ),
      ),
    );

    if (!animate) {
      return logo;
    }

    // Add animation if requested
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: const Duration(milliseconds: 1500),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      child: logo,
    );
  }
}

/// A widget that displays the app logo with text
class AppLogoWithText extends StatelessWidget {
  final double logoSize;
  final double fontSize;
  final bool showShadow;
  final bool animate;
  final Color? textColor;
  final EdgeInsetsGeometry? padding;

  const AppLogoWithText({
    Key? key,
    this.logoSize = 60.0,
    this.fontSize = 24.0,
    this.showShadow = true,
    this.animate = false,
    this.textColor,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final defaultTextColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : AppColors.midnightNavy;

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppLogo(
            size: logoSize,
            showShadow: showShadow,
            animate: animate,
          ),
          const SizedBox(height: 12),
          Text(
            'Medroid',
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: textColor ?? defaultTextColor,
              letterSpacing: 0.5,
            ),
          ),
          Text(
            'Your AI Dcotor',
            style: TextStyle(
              fontSize: fontSize * 0.5,
              color: textColor != null
                  ? const Color.fromRGBO(
                      110, 122, 138, 1.0) // Use slateGrey with opacity
                  : AppColors.slateGrey,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }
}
