import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/location_service.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/widgets/language_selector.dart';

class AppointmentPreferencesForm extends StatefulWidget {
  final Map<String, dynamic>? initialPreferences;
  final Function(Map<String, dynamic>) onPreferencesSaved;

  const AppointmentPreferencesForm({
    Key? key,
    this.initialPreferences,
    required this.onPreferencesSaved,
  }) : super(key: key);

  @override
  State<AppointmentPreferencesForm> createState() =>
      _AppointmentPreferencesFormState();
}

class _AppointmentPreferencesFormState
    extends State<AppointmentPreferencesForm> {
  final _formKey = GlobalKey<FormState>();
  final _locationController = TextEditingController();
  String _selectedGender = 'any';
  List<String> _selectedLanguages = [];
  bool _isLoading = false;

  // Location related fields
  double? _latitude;
  double? _longitude;
  int _searchRadius = 25; // Default 25 km
  final LocationService _locationService = LocationService();

  @override
  void initState() {
    super.initState();
    _initializeFormValues();
  }

  void _initializeFormValues() {
    if (widget.initialPreferences != null) {
      _locationController.text =
          widget.initialPreferences!['preferred_location'] ?? '';
      _selectedGender = widget.initialPreferences!['preferred_gender'] ?? 'any';

      // Initialize selected languages
      if (widget.initialPreferences!['preferred_language'] != null &&
          widget.initialPreferences!['preferred_language'].isNotEmpty) {
        _selectedLanguages = [widget.initialPreferences!['preferred_language']];
      }

      // Initialize location coordinates if available
      if (widget.initialPreferences!['latitude'] != null) {
        _latitude =
            double.tryParse(widget.initialPreferences!['latitude'].toString());
      }
      if (widget.initialPreferences!['longitude'] != null) {
        _longitude =
            double.tryParse(widget.initialPreferences!['longitude'].toString());
      }

      // Initialize search radius if available
      if (widget.initialPreferences!['search_radius'] != null) {
        _searchRadius = int.tryParse(
                widget.initialPreferences!['search_radius'].toString()) ??
            25;
      }
    }
  }

  // Method to get current location and update the form
  Future<void> _getCurrentLocation() async {
    final position = await _locationService.getCurrentLocation();
    if (position != null && mounted) {
      setState(() {
        _latitude = position.latitude;
        _longitude = position.longitude;
      });

      // Get address from coordinates using reverse geocoding
      try {
        final List<Placemark> placemarks = await placemarkFromCoordinates(
            position.latitude, position.longitude);

        if (placemarks.isNotEmpty && mounted) {
          final place = placemarks.first;
          final address =
              '${place.locality ?? ""}, ${place.administrativeArea ?? ""}, ${place.country ?? ""}'
                  .replaceAll(RegExp(r'^,\s*'), '');
          setState(() {
            _locationController.text = address;
          });
        }
      } catch (e) {
        debugPrint('Error getting address from coordinates: $e');
      }
    } else if (mounted) {
      // Show error message
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text(
              'Could not get current location. Please check your location permissions.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _savePreferences() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final apiService = RepositoryProvider.of<ApiService>(context);

        // If we have a location but no coordinates, try to get coordinates
        if (_locationController.text.isNotEmpty &&
            (_latitude == null || _longitude == null)) {
          final coordinates = await _locationService
              .getCoordinatesFromAddress(_locationController.text);
          if (coordinates != null) {
            _latitude = coordinates['latitude'];
            _longitude = coordinates['longitude'];
          }
        }

        final preferences = {
          'preferred_location': _locationController.text.trim(),
          'preferred_gender': _selectedGender,
          'preferred_language':
              _selectedLanguages.isNotEmpty ? _selectedLanguages.first : '',
          'latitude': _latitude?.toString(),
          'longitude': _longitude?.toString(),
          'search_radius': _searchRadius.toString(),
        };

        final response =
            await apiService.updateAppointmentPreferences(preferences);

        if (response['success'] && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Appointment preferences saved successfully'),
              backgroundColor: Colors.green,
            ),
          );

          widget.onPreferencesSaved(preferences);
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text(response['message'] ?? 'Failed to save preferences'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Simple header with pink accent
            Container(
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(30),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.calendar_today_outlined,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Text(
                    'Appointment Preferences',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Preferred Location with auto-suggestion
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Preferred Location',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  FormField<String>(
                    validator: (value) {
                      if (_locationController.text.isEmpty) {
                        return 'Please enter your preferred location';
                      }
                      return null;
                    },
                    builder: (FormFieldState<String> state) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextField(
                            controller: _locationController,
                            decoration: InputDecoration(
                              hintText: 'Enter your city or location',
                              hintStyle: TextStyle(
                                color: Colors.grey.shade400,
                                fontSize: 13,
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade50,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: const BorderSide(
                                    color: AppTheme.primaryColor, width: 1),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                              prefixIcon: const Icon(Icons.location_on,
                                  size: 16, color: AppTheme.primaryColor),
                              suffixIcon: Icon(Icons.search,
                                  size: 16, color: Colors.grey.shade600),
                              isDense: true,
                              errorText:
                                  state.hasError ? state.errorText : null,
                            ),
                            onChanged: (value) {
                              state.didChange(value);
                            },
                          ),
                        ],
                      );
                    },
                  ),

                  // Current location button
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton.icon(
                      icon: const Icon(Icons.my_location,
                          size: 12, color: AppTheme.primaryColor),
                      label: const Text(
                        'Use current location',
                        style: TextStyle(
                          fontSize: 11,
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      onPressed: _getCurrentLocation,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Search radius section - simplified
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.explore_outlined,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Search Radius',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '$_searchRadius km',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      SliderTheme(
                        data: SliderThemeData(
                          trackHeight: 3,
                          thumbShape: const RoundSliderThumbShape(
                              enabledThumbRadius: 6),
                          overlayShape:
                              const RoundSliderOverlayShape(overlayRadius: 12),
                          activeTrackColor: AppTheme.primaryColor,
                          inactiveTrackColor: Colors.grey.shade200,
                          thumbColor: AppTheme.primaryColor,
                          overlayColor: AppTheme.primaryColor.withAlpha(51),
                        ),
                        child: Slider(
                          value: _searchRadius.toDouble(),
                          min: 5,
                          max: 100,
                          divisions: 19,
                          onChanged: (value) {
                            setState(() {
                              _searchRadius = value.round();
                            });
                          },
                        ),
                      ),
                      Text(
                        'Providers outside this radius will be marked as distant',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Preferred Provider Gender
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.person_outline,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Preferred Provider Gender',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Simplified gender selection buttons
                      Row(
                        children: [
                          Expanded(
                            child:
                                _buildGenderButton('male', 'Male', Icons.male),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildGenderButton(
                                'female', 'Female', Icons.female),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildGenderButton(
                                'any', 'Any', Icons.people_outline),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Preferred Language
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.language,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Preferred Language',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Language selector with simplified style
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey.shade50,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: LanguageSelector(
                            selectedLanguages: _selectedLanguages,
                            onLanguagesChanged: (languages) {
                              setState(() {
                                _selectedLanguages = languages;
                              });
                            },
                            isRequired: false,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Simple Save Button
                  SizedBox(
                    width: double.infinity,
                    height: 44,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _savePreferences,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade800,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 18,
                              width: 18,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  size: 16,
                                ),
                                SizedBox(width: 6),
                                Text(
                                  'Save Preferences',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Simple gender selection button
  Widget _buildGenderButton(String value, String label, IconData icon) {
    final isSelected = _selectedGender == value;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedGender = value;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected ? Colors.grey.shade200 : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.grey.shade400 : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected ? Colors.grey.shade800 : Colors.grey.shade600,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                color: isSelected ? Colors.grey.shade800 : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
