import 'package:flutter/material.dart';
import 'package:medroid_app/models/provider.dart';
import 'package:medroid_app/utils/theme.dart';

class AppointmentSlotSelector extends StatefulWidget {
  final List<Map<String, dynamic>> availableSlots;
  final Function(Map<String, dynamic> selectedSlot, TimeSlot selectedTime)
      onSlotSelected;

  const AppointmentSlotSelector({
    Key? key,
    required this.availableSlots,
    required this.onSlotSelected,
  }) : super(key: key);

  @override
  _AppointmentSlotSelectorState createState() =>
      _AppointmentSlotSelectorState();
}

class _AppointmentSlotSelectorState extends State<AppointmentSlotSelector> {
  int _selectedProviderIndex = 0;
  TimeSlot? _selectedTimeSlot;

  @override
  void initState() {
    super.initState();

    // Automatically select the earliest available appointment
    if (widget.availableSlots.isNotEmpty) {
      // Sort slots by date and time
      final sortedSlots =
          List<Map<String, dynamic>>.from(widget.availableSlots);
      sortedSlots.sort((a, b) {
        // First compare dates
        final dateA = DateTime.parse(a['date']);
        final dateB = DateTime.parse(b['date']);
        final dateComparison = dateA.compareTo(dateB);

        if (dateComparison != 0) {
          return dateComparison;
        }

        // If dates are the same, compare the earliest time slot
        final slotsA = a['slots'] as List;
        final slotsB = b['slots'] as List;

        if (slotsA.isEmpty) return 1;
        if (slotsB.isEmpty) return -1;

        final earliestSlotA = slotsA.first;
        final earliestSlotB = slotsB.first;

        final startTimeA = earliestSlotA['start_time'];
        final startTimeB = earliestSlotB['start_time'];

        return startTimeA.compareTo(startTimeB);
      });

      // Find the index of the earliest slot in the original list
      for (int i = 0; i < widget.availableSlots.length; i++) {
        if (widget.availableSlots[i] == sortedSlots[0]) {
          _selectedProviderIndex = i;
          break;
        }
      }

      // Select the earliest time slot for this provider
      final slots = sortedSlots[0]['slots'] as List;
      if (slots.isNotEmpty) {
        _selectedTimeSlot = TimeSlot(
          startTime: slots[0]['start_time'],
          endTime: slots[0]['end_time'],
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.availableSlots.isEmpty) {
      return const Center(
        child: Text('No available appointment slots found'),
      );
    }

    final selectedProvider = widget.availableSlots[_selectedProviderIndex];
    final providerName = selectedProvider['provider']['name'];
    final date = selectedProvider['date'];
    final dayOfWeek = selectedProvider['day_of_week'];
    final slots = List<TimeSlot>.from(
      (selectedProvider['slots'] as List).map(
        (slot) => TimeSlot(
          startTime: slot['start_time'],
          endTime: slot['end_time'],
        ),
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Provider selection with earliest available appointment
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Select Provider:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            // Show "Earliest Available" badge
            if (_selectedProviderIndex ==
                0) // Assuming the first one is the earliest
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryColor,
                    width: 1,
                  ),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 4),
                    Text(
                      'Earliest Available',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 140, // Increased height for more provider details
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget.availableSlots.length,
            itemBuilder: (context, index) {
              final slot = widget.availableSlots[index];
              final provider = slot['provider'];
              final isSelected = index == _selectedProviderIndex;

              // Get provider specialization if available
              final specialization =
                  provider['specialization'] ?? 'General Practitioner';

              // Format date for display
              final date = slot['date'];
              final formattedDate =
                  date; // You can format this further if needed

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedProviderIndex = index;
                    _selectedTimeSlot = null;
                  });
                },
                child: Container(
                  width: 160, // Wider for more details
                  margin: const EdgeInsets.only(right: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected ? AppTheme.primaryColor : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.grey.shade300,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Provider name
                      Text(
                        provider['name'],
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : Colors.black,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      // Specialization
                      Text(
                        specialization,
                        style: TextStyle(
                          fontSize: 12,
                          color: isSelected
                              ? Colors.white70
                              : Colors.grey.shade600,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      // Date
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 12,
                            color: isSelected
                                ? Colors.white70
                                : Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              formattedDate,
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected
                                    ? Colors.white70
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      // Day of week
                      Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            size: 12,
                            color: isSelected
                                ? Colors.white70
                                : Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              slot['day_of_week'],
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected
                                    ? Colors.white70
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // Show earliest badge if this is the earliest slot
                      if (index == 0 && !isSelected)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: Colors.green,
                                width: 0.5,
                              ),
                            ),
                            child: const Text(
                              'Earliest',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // Date and time selection
        Text(
          'Available Times for $providerName on $dayOfWeek, $date:',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),

        // Time slots with earliest indicator
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: slots.asMap().entries.map((entry) {
            final index = entry.key;
            final slot = entry.value;
            final isSelected = _selectedTimeSlot != null &&
                _selectedTimeSlot!.startTime == slot.startTime &&
                _selectedTimeSlot!.endTime == slot.endTime;

            // Check if this is the earliest time slot
            final isEarliest = index == 0;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTimeSlot = slot;
                });
              },
              child: Stack(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : Colors.grey.shade300,
                      ),
                    ),
                    child: Text(
                      '${slot.startTime} - ${slot.endTime}',
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                  // Show earliest badge if this is the earliest time slot and not selected
                  if (isEarliest && !isSelected)
                    Positioned(
                      top: -5,
                      right: -5,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.access_time,
                          size: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ),

        const SizedBox(height: 24),

        // Appointment summary
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Appointment Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(Icons.person, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Provider: ${widget.availableSlots[_selectedProviderIndex]['provider']['name']}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.calendar_today,
                      size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text(
                    'Date: $date',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.access_time, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text(
                    'Time: ${_selectedTimeSlot?.startTime ?? "Not selected"} - ${_selectedTimeSlot?.endTime ?? ""}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Confirm button
        Center(
          child: ElevatedButton(
            onPressed: _selectedTimeSlot == null
                ? null
                : () {
                    widget.onSlotSelected(
                      widget.availableSlots[_selectedProviderIndex],
                      _selectedTimeSlot!,
                    );
                  },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Confirm Appointment'),
          ),
        ),
      ],
    );
  }
}
