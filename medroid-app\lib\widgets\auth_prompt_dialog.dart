import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class AuthPromptDialog extends StatelessWidget {
  final VoidCallback onLogin;
  final VoidCallback onRegister;
  final String? reason;

  const AuthPromptDialog({
    Key? key,
    required this.onLogin,
    required this.onRegister,
    this.reason,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isDesktop = ResponsiveUtils.isDesktop(context);

    // Responsive sizing
    final dialogWidth = ResponsiveUtils.value<double>(
      context: context,
      mobile: MediaQuery.of(context).size.width * 0.9,
      tablet: 400.0,
      desktop: 450.0,
    );

    final iconSize = ResponsiveUtils.value<double>(
      context: context,
      mobile: 40.0,
      tablet: 48.0,
      desktop: 56.0,
    );

    final titleFontSize = ResponsiveUtils.value<double>(
      context: context,
      mobile: 18.0,
      tablet: 20.0,
      desktop: 22.0,
    );

    final subtitleFontSize = ResponsiveUtils.value<double>(
      context: context,
      mobile: 14.0,
      tablet: 15.0,
      desktop: 16.0,
    );

    final padding = ResponsiveUtils.value<double>(
      context: context,
      mobile: 20.0,
      tablet: 24.0,
      desktop: 28.0,
    );

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(isDesktop ? 20 : 16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 500 : double.infinity,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF2A2D31) : Colors.white,
          borderRadius: BorderRadius.circular(isDesktop ? 20 : 16),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1A000000),
              blurRadius: isDesktop ? 20 : 10,
              offset: Offset(0, isDesktop ? 8 : 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.account_circle_outlined,
              size: iconSize,
              color: AppColors.tealSurge, // App theme color
            ),
            SizedBox(height: isDesktop ? 20 : 16),
            Text(
              reason != null && reason!.contains('appointment')
                  ? 'Sign in to book an appointment'
                  : 'Sign in to continue',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isDesktop ? 16 : 12),
            Text(
              reason != null && reason!.contains('appointment')
                  ? 'To book an appointment with a healthcare provider, you need to create an account or sign in. This allows us to manage your appointments and provide personalized care.'
                  : 'To access this feature, you need to create an account or sign in. Creating an account is free and gives you access to all features.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: subtitleFontSize,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: isDesktop ? 32 : 24),

            // Responsive button layout
            isDesktop
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: onRegister,
                          style: OutlinedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              vertical: isDesktop ? 16 : 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(isDesktop ? 12 : 8),
                            ),
                          ),
                          child: Text(
                            'Register',
                            style: TextStyle(
                              fontSize: isDesktop ? 16 : 14,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: isDesktop ? 16 : 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: onLogin,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                AppColors.coralPop, // App theme color
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(
                              vertical: isDesktop ? 16 : 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(isDesktop ? 12 : 8),
                            ),
                          ),
                          child: Text(
                            'Sign In',
                            style: TextStyle(
                              fontSize: isDesktop ? 16 : 14,
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: onLogin,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                AppColors.coralPop, // App theme color
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Sign In',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton(
                          onPressed: onRegister,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Register',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }
}
