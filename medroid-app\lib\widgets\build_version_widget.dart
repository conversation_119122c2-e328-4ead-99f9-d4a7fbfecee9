import 'package:flutter/material.dart';
import 'package:medroid_app/utils/build_info.dart';

class BuildVersionWidget extends StatelessWidget {
  final bool showFullVersion;
  
  const BuildVersionWidget({
    super.key,
    this.showFullVersion = false,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          showFullVersion ? BuildInfo.fullVersion : BuildInfo.displayVersion,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

class BuildVersionOverlay extends StatelessWidget {
  final Widget child;
  final bool showFullVersion;
  
  const BuildVersionOverlay({
    super.key,
    required this.child,
    this.showFullVersion = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        BuildVersionWidget(showFullVersion: showFullVersion),
      ],
    );
  }
}
