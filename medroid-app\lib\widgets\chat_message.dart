import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:medroid_app/models/chat_message.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/theme.dart';

class ChatMessageWidget extends StatefulWidget {
  final ChatMessage message;
  final VoidCallback? onLoadMore;
  final bool isTyping;

  const ChatMessageWidget({
    Key? key,
    required this.message,
    this.onLoadMore,
    this.isTyping = false,
  }) : super(key: key);

  @override
  State<ChatMessageWidget> createState() => _ChatMessageWidgetState();
}

class _ChatMessageWidgetState extends State<ChatMessageWidget> {
  bool _showTimestamp = false;

  // Helper method to convert text to sentence case
  String toSentenceCase(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  // Helper method to handle URL taps in markdown
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch $url')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isUser = widget.message.isUser;
    final brightness = Theme.of(context).brightness;
    final isLightMode = brightness == Brightness.light;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isUser) _buildUserAvatar(context),
          const SizedBox(width: 8),
          Flexible(
            child: isUser
                ? Align(
                    alignment: Alignment.centerLeft,
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.7,
                      ),
                      child: IntrinsicWidth(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _showTimestamp = !_showTimestamp;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: isLightMode
                                  ? Colors.grey.shade200
                                  : Colors.grey.shade800,
                              borderRadius: BorderRadius.circular(
                                  8), // More square with light curves
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (widget.isTyping)
                                  // Show typing indicator for user
                                  Row(
                                    children: [
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor
                                              .withAlpha(179), // 0.7 opacity
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor
                                              .withAlpha(102), // 0.4 opacity
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                    ],
                                  )
                                else if (widget.message.isText)
                                  MarkdownBody(
                                    data:
                                        toSentenceCase(widget.message.content),
                                    softLineBreak: true,
                                    fitContent: false,
                                    selectable:
                                        false, // Disable selection to fix the TypeErrorImpl exception
                                    styleSheet: MarkdownStyleSheet(
                                      p: GoogleFonts.inter(
                                        height: 1.6,
                                        fontSize: 15,
                                      ),
                                      pPadding:
                                          const EdgeInsets.only(bottom: 8),
                                      strong: GoogleFonts.inter(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      em: GoogleFonts.inter(
                                        fontStyle: FontStyle.italic,
                                      ),
                                      a: GoogleFonts.inter(
                                        color: AppTheme.primaryColor,
                                      ),
                                      h1: GoogleFonts.inter(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      h2: GoogleFonts.inter(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      h3: GoogleFonts.inter(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        height: 1.3,
                                      ),
                                      listBullet: const TextStyle(
                                        color: AppTheme.primaryColor,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      listIndent: 20,
                                      code: TextStyle(
                                        backgroundColor: Colors.grey.shade200,
                                        fontFamily: 'monospace',
                                      ),
                                      codeblockDecoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    onTapLink: (text, href, title) {
                                      if (href != null) {
                                        _launchUrl(href);
                                      }
                                    },
                                  )
                                else if (widget.message.isImage)
                                  _buildImageContent(context, isUser),
                                if (_showTimestamp) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    widget.message.formattedTime,
                                    style: GoogleFonts.inter(
                                      fontSize: 10,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      setState(() {
                        _showTimestamp = !_showTimestamp;
                      });
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.isTyping)
                          // Show typing indicator
                          Padding(
                            padding: const EdgeInsets.only(right: 48),
                            child: Row(
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor
                                        .withAlpha(179), // 0.7 opacity
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor
                                        .withAlpha(102), // 0.4 opacity
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ],
                            ),
                          )
                        else if (widget.message.isAppointment)
                          _buildAppointmentContent(context)
                        else if (widget.message.isText)
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.8,
                            ),
                            padding: const EdgeInsets.only(right: 16),
                            child: MarkdownBody(
                              data: widget.message.content,
                              softLineBreak: true,
                              fitContent: false,
                              selectable:
                                  false, // Disable selection to fix the TypeErrorImpl exception
                              styleSheet: MarkdownStyleSheet(
                                p: GoogleFonts.inter(
                                  height: 1.6,
                                  fontSize: 15,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                pPadding: const EdgeInsets.only(bottom: 8),
                                strong: GoogleFonts.inter(
                                  fontWeight: FontWeight.bold,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                em: GoogleFonts.inter(
                                  fontStyle: FontStyle.italic,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                a: GoogleFonts.inter(
                                  color: AppTheme.primaryColor,
                                ),
                                h1: GoogleFonts.inter(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                h2: GoogleFonts.inter(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                h3: GoogleFonts.inter(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                  height: 1.3,
                                ),
                                listBullet: GoogleFonts.inter(
                                  color: isLightMode
                                      ? AppTheme.primaryColor
                                      : Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                listIndent: 20,
                                blockquote: GoogleFonts.inter(
                                  color: isLightMode
                                      ? Colors.black54
                                      : Colors.white70,
                                  fontStyle: FontStyle.italic,
                                ),
                                code: TextStyle(
                                  backgroundColor: isLightMode
                                      ? Colors.grey.shade200
                                      : Colors.grey.shade800,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                  fontFamily:
                                      'monospace', // Keep monospace for code
                                ),
                                codeblockDecoration: BoxDecoration(
                                  color: isLightMode
                                      ? Colors.grey.shade200
                                      : Colors.grey.shade800,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                tableHead: GoogleFonts.inter(
                                  fontWeight: FontWeight.bold,
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                tableBody: GoogleFonts.inter(
                                  color: isLightMode
                                      ? Colors.black87
                                      : Colors.white,
                                ),
                                tableBorder: TableBorder.all(
                                  color: isLightMode
                                      ? Colors.grey.shade300
                                      : Colors.grey.shade700,
                                  width: 1,
                                ),
                              ),
                              onTapLink: (text, href, title) {
                                if (href != null) {
                                  _launchUrl(href);
                                }
                              },
                            ),
                          )
                        else if (widget.message.isImage)
                          _buildImageContent(context, isUser),
                        if (_showTimestamp) ...[
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                widget.message.formattedTime,
                                style: GoogleFonts.inter(
                                  fontSize: 10,
                                  color: Colors.grey,
                                ),
                              ),
                              // Show "Load More" button for truncated AI responses
                              if (widget.message.isTruncated &&
                                  widget.onLoadMore != null)
                                TextButton(
                                  onPressed: widget.onLoadMore,
                                  style: TextButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 0),
                                    minimumSize: const Size(0, 0),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: Text(
                                    'Load More',
                                    style: GoogleFonts.inter(
                                      fontSize: 10,
                                      color: AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ] else if (widget.message.isTruncated &&
                            widget.onLoadMore != null) ...[
                          const SizedBox(height: 4),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: widget.onLoadMore,
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 0),
                                minimumSize: const Size(0, 0),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Text(
                                'Load More',
                                style: GoogleFonts.inter(
                                  fontSize: 10,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserAvatar(BuildContext context) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withAlpha(50),
        shape: BoxShape.circle,
      ),
      child: const Center(
        child: Icon(
          Icons.person,
          size: 16,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildImageContent(BuildContext context, bool isUser) {
    // If we have a local image path, display it from the file system
    if (widget.message.localImagePath != null &&
        widget.message.localImagePath!.isNotEmpty) {
      // For web, we can't use Image.file directly
      if (kIsWeb) {
        // For web, we should use Image.network with the URL or Image.memory with bytes
        // Since we can't directly access the file in web, we'll show a placeholder
        // In a real implementation, you would need to upload the image to a server
        // and use the URL, or convert it to base64 and use Image.memory
        return ClipRRect(
          borderRadius:
              BorderRadius.circular(4), // More square with light curves
          child: Container(
            width: 200,
            height: 200,
            color: Colors.grey.shade200,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.image, color: Colors.grey),
                  const SizedBox(height: 8),
                  Text(
                    'Image Preview',
                    style: GoogleFonts.inter(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        );
      } else {
        // For mobile platforms, use Image.file as before
        return ClipRRect(
          borderRadius:
              BorderRadius.circular(4), // More square with light curves
          child: Image.file(
            File(widget.message.localImagePath!),
            fit: BoxFit.cover,
            width: 200,
            height: 200,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 200,
                height: 200,
                color: Colors.grey.shade300,
                child: const Center(
                  child: Icon(
                    Icons.error,
                    color: Colors.red,
                  ),
                ),
              );
            },
          ),
        );
      }
    }
    // If we have an image URL, display it from the network
    else if (widget.message.imageUrl != null &&
        widget.message.imageUrl!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(4), // More square with light curves
        child: CachedNetworkImage(
          imageUrl: widget.message.imageUrl!,
          fit: BoxFit.cover,
          width: 200,
          height: 200,
          placeholder: (context, url) => Container(
            width: 200,
            height: 200,
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            width: 200,
            height: 200,
            color: Colors.grey.shade300,
            child: const Center(
              child: Icon(
                Icons.error,
                color: Colors.red,
              ),
            ),
          ),
        ),
      );
    }
    // If we don't have an image, display a placeholder
    else {
      return Container(
        width: 200,
        height: 200,
        color: Colors.grey.shade300,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: Colors.grey,
          ),
        ),
      );
    }
  }

  Widget _buildAppointmentContent(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.85,
      ),
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.tealSurge, // Teal Surge
            AppColors.tealSurge.withAlpha(204), // 80% opacity darker teal
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.tealSurge.withAlpha(51), // 20% opacity
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51), // 20% opacity
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'APPOINTMENT CONFIRMED',
                    style: GoogleFonts.inter(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Content with markdown support for formatting
            MarkdownBody(
              data: widget.message.content,
              softLineBreak: true,
              fitContent: false,
              selectable: false,
              styleSheet: MarkdownStyleSheet(
                p: GoogleFonts.inter(
                  height: 1.5,
                  fontSize: 15,
                  color: Colors.white,
                ),
                strong: GoogleFonts.inter(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                em: GoogleFonts.inter(
                  fontStyle: FontStyle.italic,
                  color: Colors.white,
                ),
                h1: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                h2: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                h3: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                listBullet: GoogleFonts.inter(
                  color: Colors.white,
                ),
                blockquote: GoogleFonts.inter(
                  color: Colors.white.withAlpha(179), // 70% opacity
                  fontStyle: FontStyle.italic,
                ),
                code: TextStyle(
                  backgroundColor: Colors.white.withAlpha(51), // 20% opacity
                  color: Colors.white,
                  fontFamily: 'monospace',
                ),
                codeblockDecoration: BoxDecoration(
                  color: Colors.white.withAlpha(51), // 20% opacity
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Footer with celebration emoji
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(26), // 10% opacity
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '🎉 Your appointment is secured! 🎉',
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
