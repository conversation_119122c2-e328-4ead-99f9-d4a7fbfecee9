import 'dart:io';
import 'package:flutter/material.dart';
import 'package:medroid_app/widgets/universal_chat_input.dart';

class ChatScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;
  final bool showChatInput;
  final Function(String, {File? imageFile})? onSendMessage;
  final String? conversationId;
  final String hintText;
  final bool isInputEnabled;
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const ChatScaffold({
    Key? key,
    required this.body,
    this.appBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.showChatInput = true,
    this.onSendMessage,
    this.conversationId,
    this.hintText = 'Type your health question...',
    this.isInputEnabled = true,
    this.onCreateNewChat,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      drawer: drawer,
      endDrawer: endDrawer,
      backgroundColor: backgroundColor,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      body: Column(
        children: [
          Expanded(child: body),
        ],
      ),
      bottomNavigationBar: showChatInput
          ? UniversalChatInput(
              onSendMessage: onSendMessage,
              conversationId: conversationId,
              hintText: hintText,
              isEnabled: isInputEnabled,
              onCreateNewChat: onCreateNewChat,
            )
          : null,
    );
  }
}
