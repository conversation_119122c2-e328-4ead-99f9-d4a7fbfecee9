import 'package:flutter/material.dart';
import 'package:medroid_app/models/comment.dart';
import 'package:medroid_app/services/comment_service.dart';
import 'package:medroid_app/utils/theme.dart';

class CommentItem extends StatefulWidget {
  final Comment comment;
  final CommentService commentService;
  final String socialContentId;
  final VoidCallback? onCommentUpdated;
  final VoidCallback? onReply;

  const CommentItem({
    Key? key,
    required this.comment,
    required this.commentService,
    required this.socialContentId,
    this.onCommentUpdated,
    this.onReply,
  }) : super(key: key);

  @override
  State<CommentItem> createState() => _CommentItemState();
}

class _CommentItemState extends State<CommentItem> {
  bool _isEditing = false;
  bool _isLoading = false;
  late TextEditingController _editController;
  late Comment _comment;

  @override
  void initState() {
    super.initState();
    _comment = widget.comment;
    _editController = TextEditingController(text: _comment.content);
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Container(
      padding: EdgeInsets.all(isDesktop
          ? 16
          : isTablet
              ? 14
              : 12),
      margin: EdgeInsets.only(bottom: isDesktop ? 12 : 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(isDesktop ? 12 : 8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: isDesktop ? 12 : 8),
          _buildContent(),
          SizedBox(height: isDesktop ? 12 : 8),
          _buildActions(),
          if (_comment.replies.isNotEmpty) ...[
            SizedBox(height: isDesktop ? 12 : 8),
            _buildReplies(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Row(
      children: [
        CircleAvatar(
          radius: isDesktop
              ? 20
              : isTablet
                  ? 18
                  : 16,
          backgroundColor: AppTheme.primaryColor,
          backgroundImage: _comment.user.avatar != null
              ? NetworkImage(_comment.user.avatar!)
              : null,
          child: _comment.user.avatar == null
              ? Text(
                  _comment.user.initials,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isDesktop
                        ? 16
                        : isTablet
                            ? 14
                            : 12,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        SizedBox(width: isDesktop ? 12 : 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _comment.user.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: isDesktop
                      ? 16
                      : isTablet
                          ? 15
                          : 14,
                ),
              ),
              Text(
                _comment.timeAgo,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: isDesktop
                      ? 14
                      : isTablet
                          ? 13
                          : 12,
                ),
              ),
            ],
          ),
        ),
        if (_comment.canEdit || _comment.canDelete)
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              if (_comment.canEdit)
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
              if (_comment.canDelete)
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
            ],
          ),
      ],
    );
  }

  Widget _buildContent() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    if (_isEditing) {
      return Column(
        children: [
          TextField(
            controller: _editController,
            maxLines: null,
            style: TextStyle(
              fontSize: isDesktop
                  ? 16
                  : isTablet
                      ? 15
                      : 14,
            ),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(isDesktop ? 12 : 8),
              ),
              hintText: 'Edit your comment...',
              hintStyle: TextStyle(
                fontSize: isDesktop
                    ? 16
                    : isTablet
                        ? 15
                        : 14,
              ),
              contentPadding: EdgeInsets.all(isDesktop ? 16 : 12),
            ),
          ),
          SizedBox(height: isDesktop ? 12 : 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                    _editController.text = _comment.content;
                  });
                },
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    fontSize: isDesktop
                        ? 16
                        : isTablet
                            ? 15
                            : 14,
                  ),
                ),
              ),
              SizedBox(width: isDesktop ? 12 : 8),
              ElevatedButton(
                onPressed: _isLoading ? null : _saveEdit,
                child: _isLoading
                    ? SizedBox(
                        width: isDesktop ? 20 : 16,
                        height: isDesktop ? 20 : 16,
                        child: const CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(
                        'Save',
                        style: TextStyle(
                          fontSize: isDesktop
                              ? 16
                              : isTablet
                                  ? 15
                                  : 14,
                        ),
                      ),
              ),
            ],
          ),
        ],
      );
    }

    return Text(
      _comment.content,
      style: TextStyle(
        fontSize: isDesktop
            ? 16
            : isTablet
                ? 15
                : 14,
        height: 1.4,
      ),
    );
  }

  Widget _buildActions() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Row(
      children: [
        _buildReactionButton(),
        SizedBox(width: isDesktop ? 20 : 16),
        if (widget.onReply != null)
          TextButton.icon(
            onPressed: widget.onReply,
            icon: Icon(
              Icons.reply,
              size: isDesktop ? 18 : 16,
            ),
            label: Text(
              'Reply',
              style: TextStyle(
                fontSize: isDesktop
                    ? 14
                    : isTablet
                        ? 13
                        : 12,
              ),
            ),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(
                horizontal: isDesktop ? 12 : 8,
                vertical: isDesktop ? 8 : 4,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildReactionButton() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;
    final likeCount = _comment.reactionCounts['like'] ?? 0;
    final isLiked = _comment.userReaction == 'like';

    return InkWell(
      onTap: () => _handleReaction('like'),
      borderRadius: BorderRadius.circular(isDesktop ? 20 : 16),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? 12 : 8,
          vertical: isDesktop ? 8 : 4,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isLiked ? Icons.favorite : Icons.favorite_border,
              size: isDesktop ? 18 : 16,
              color: isLiked ? Colors.red : Colors.grey[600],
            ),
            if (likeCount > 0) ...[
              SizedBox(width: isDesktop ? 6 : 4),
              Text(
                likeCount.toString(),
                style: TextStyle(
                  fontSize: isDesktop
                      ? 14
                      : isTablet
                          ? 13
                          : 12,
                  color: isLiked ? Colors.red : Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReplies() {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width < 768;

    return Padding(
      padding: EdgeInsets.only(
          left: isDesktop
              ? 32
              : isTablet
                  ? 28
                  : 24),
      child: Column(
        children: _comment.replies
            .map((reply) => CommentItem(
                  comment: reply,
                  commentService: widget.commentService,
                  socialContentId: widget.socialContentId,
                  onCommentUpdated: widget.onCommentUpdated,
                ))
            .toList(),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        setState(() {
          _isEditing = true;
        });
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Comment'),
        content: const Text('Are you sure you want to delete this comment?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteComment();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveEdit() async {
    if (_editController.text.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedComment = await widget.commentService.updateComment(
        widget.socialContentId,
        _comment.id,
        _editController.text.trim(),
      );

      setState(() {
        _comment = updatedComment;
        _isEditing = false;
        _isLoading = false;
      });

      widget.onCommentUpdated?.call();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update comment: $e')),
        );
      }
    }
  }

  Future<void> _deleteComment() async {
    try {
      await widget.commentService.deleteComment(
        widget.socialContentId,
        _comment.id,
      );

      widget.onCommentUpdated?.call();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete comment: $e')),
        );
      }
    }
  }

  Future<void> _handleReaction(String reactionType) async {
    try {
      final result = await widget.commentService.reactToComment(
        widget.socialContentId,
        _comment.id,
        reactionType,
      );

      if (result['success'] == true) {
        setState(() {
          _comment = Comment(
            id: _comment.id,
            socialContentId: _comment.socialContentId,
            userId: _comment.userId,
            content: _comment.content,
            parentId: _comment.parentId,
            createdAt: _comment.createdAt,
            updatedAt: _comment.updatedAt,
            user: _comment.user,
            replies: _comment.replies,
            reactionCounts:
                Map<String, int>.from(result['reaction_counts'] ?? {}),
            userReaction: result['user_reaction'],
            canEdit: _comment.canEdit,
            canDelete: _comment.canDelete,
          );
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to react: $e')),
        );
      }
    }
  }
}
