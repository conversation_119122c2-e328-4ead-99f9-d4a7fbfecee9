import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<CustomNavItem> items;
  final bool isAnonymousMode;

  const CustomBottomNavigation({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.isAnonymousMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Material(
      elevation: 0, // Remove default Material elevation
      color: Colors.transparent, // Transparent background
      child: Container(
        height: 80, // Increased height for better touch targets
        padding: EdgeInsets.zero, // No padding
        margin: EdgeInsets.zero, // No margin
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF0A3D53) : Colors.white,
          // Top border instead of shadow for a more elegant look
          border: Border(
            top: BorderSide(
              color: isDarkMode
                  ? Colors.white.withAlpha(10)
                  : const Color(0xFFE6F7F5),
              width: 1,
            ),
          ),
          // Subtle shadow for depth
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(5), // Very subtle shadow
              blurRadius: 4,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          // Set maintainBottomViewPadding to false to prevent overflow
          maintainBottomViewPadding: false,
          bottom: false, // Don't add padding at the bottom
          child: LayoutBuilder(
            builder: (context, constraints) {
              final itemWidth = constraints.maxWidth / items.length;
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(items.length, (index) {
                  final isSelected = currentIndex == index;
                  return SizedBox(
                    width: itemWidth,
                    child: _buildNavItem(context, items[index], isSelected, () {
                      onTap(index);
                    }),
                  );
                }),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, CustomNavItem item,
      bool isSelected, VoidCallback onTap) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // If in anonymous mode and not the first tab (chat), show a different behavior
    if (isAnonymousMode && item.label != 'Chat') {
      return GestureDetector(
        onTap: () {
          // Show login prompt when trying to access restricted features
          _showAuthPrompt(context, item.label);
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // The icon with a lock overlay
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  height: 48,
                  width: 48,
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    item.icon,
                    color:
                        isDarkMode ? Colors.grey.shade400 : AppColors.slateGrey,
                    size: 28,
                  ),
                ),
                // Small lock icon overlay
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color:
                          isDarkMode ? const Color(0xFF0A3D53) : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isDarkMode
                            ? Colors.white.withAlpha(15)
                            : const Color(0xFFE6F7F5),
                        width: 1,
                      ),
                      // More subtle shadow
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 3,
                          spreadRadius: 0,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.lock_outline,
                      color: AppColors.slateGrey,
                      size: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Normal navigation item for authenticated users or the chat tab
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                height: 48,
                width: 48,
                decoration: BoxDecoration(
                  color: isSelected
                      ? isDarkMode
                          ? AppColors.tealSurge.withValues(alpha: 0.12)
                          : AppColors.mintGlow.withValues(alpha: 0.15)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  // Add subtle border for selected items
                  border: isSelected
                      ? Border.all(
                          color: isDarkMode
                              ? AppColors.tealSurge.withValues(alpha: 0.3)
                              : AppColors.mintGlow.withValues(alpha: 0.5),
                          width: 1,
                        )
                      : null,
                ),
                child: Icon(
                  isSelected ? item.activeIcon : item.icon,
                  color: isSelected
                      ? AppColors.tealSurge
                      : isDarkMode
                          ? AppColors.slateGrey.withValues(alpha: 0.8)
                          : AppColors.slateGrey,
                  size: 28,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show authentication prompt when trying to access restricted features
  void _showAuthPrompt(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign in Required'),
        content: Text('Please sign in to access the $featureName feature.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to login screen
              Navigator.of(context).pushNamed('/login');
            },
            child: const Text('Sign In'),
          ),
        ],
      ),
    );
  }
}

class CustomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  CustomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
