import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:medroid_app/utils/app_colors.dart';
import 'dart:async';
import 'dart:js' as js;
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;

/// A direct Stripe form that doesn't use our JavaScript bridge
/// This is a fallback in case the regular form fails
class DirectStripeForm extends StatefulWidget {
  /// Client secret from the Stripe payment intent
  final String clientSecret;

  /// Publishable key for Stripe
  final String publishableKey;

  /// Callback when payment is complete
  final Function(Map<String, dynamic>) onPaymentResult;

  const DirectStripeForm({
    super.key,
    required this.clientSecret,
    required this.publishableKey,
    required this.onPaymentResult,
  });

  @override
  State<DirectStripeForm> createState() => _DirectStripeFormState();
}

class _DirectStripeFormState extends State<DirectStripeForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  bool _isProcessing = false;
  bool _stripeInitialized = false;
  String? _cardElementId;
  String? _errorMessage;

  // Direct references to Stripe objects
  js.JsObject? _stripe;
  js.JsObject? _elements;
  js.JsObject? _cardElement;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _initializeStripe();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    if (kIsWeb && _stripeInitialized) {
      _cleanupStripe();
    }
    super.dispose();
  }

  // Initialize Stripe directly
  void _initializeStripe() {
    debugPrint(
        'Direct Stripe initialization with key: ${widget.publishableKey}');

    try {
      // Check if Stripe is available
      final stripeAvailable =
          js.context.callMethod('eval', ['typeof Stripe !== "undefined"']) ==
              true;
      if (!stripeAvailable) {
        setState(() {
          _errorMessage = 'Stripe.js not available. Please refresh the page.';
        });

        // Try to load Stripe.js dynamically
        final stripeScript = html.ScriptElement()
          ..src = 'https://js.stripe.com/v3/'
          ..type = 'text/javascript'
          ..async = false;

        html.document.head!.append(stripeScript);
        return;
      }

      // Create Stripe instance
      final stripeConstructor = js.context['Stripe'];
      if (stripeConstructor == null) {
        setState(() {
          _errorMessage =
              'Stripe constructor not found. Please refresh the page.';
        });
        return;
      }

      // Initialize Stripe with the publishable key
      _stripe = js.JsObject(stripeConstructor, [widget.publishableKey]);
      if (_stripe == null) {
        setState(() {
          _errorMessage =
              'Failed to create Stripe instance. Please refresh the page.';
        });
        return;
      }

      // Create elements instance
      _elements = _stripe!.callMethod('elements') as js.JsObject?;
      if (_elements == null) {
        setState(() {
          _errorMessage =
              'Failed to create Elements instance. Please refresh the page.';
        });
        return;
      }

      // Create card element with elegant styling
      final options = js.JsObject.jsify({
        'style': {
          'base': {
            'color': '#1A1A2E',
            'fontFamily':
                '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            'fontSmoothing': 'antialiased',
            'fontSize': '16px',
            'fontWeight': '500',
            'lineHeight': '24px',
            '::placeholder': {'color': '#6E7A8A'}
          },
          'invalid': {'color': '#E53E3E', 'iconColor': '#E53E3E'},
          'complete': {'color': '#17C3B2'}
        },
        'hidePostalCode': true
      });

      _cardElement =
          _elements!.callMethod('create', ['card', options]) as js.JsObject?;
      if (_cardElement == null) {
        setState(() {
          _errorMessage =
              'Failed to create card element. Please refresh the page.';
        });
        return;
      }

      setState(() {
        _stripeInitialized = true;
      });

      // Create a unique ID for the card element
      _cardElementId =
          'direct-stripe-card-${DateTime.now().millisecondsSinceEpoch}';

      // Register the HTML element for Flutter to use
      _registerCardElement();

      // Wait a moment for the DOM to update, then mount the card element
      Timer(const Duration(milliseconds: 500), () {
        if (!mounted) return;
        _mountCardElement();
      });
    } catch (e) {
      debugPrint('Error in direct Stripe initialization: $e');
      setState(() {
        _errorMessage = 'Error initializing Stripe: $e';
      });
    }
  }

  // Register card element directly
  void _registerCardElement() {
    if (_cardElementId == null) return;

    try {
      ui_web.platformViewRegistry.registerViewFactory(
        _cardElementId!,
        (int viewId) {
          debugPrint(
              'Creating direct HTML element for card with view ID: $viewId');

          final element = html.DivElement()
            ..id = _cardElementId ?? 'default-card-element-id'
            ..style.width = '100%'
            ..style.height = '100%'
            ..style.padding = '0'
            ..style.margin = '0'
            ..style.border = 'none'
            ..style.background = 'transparent';

          return element;
        },
      );
    } catch (e) {
      debugPrint('Error registering direct card element: $e');
      setState(() {
        _errorMessage = 'Error setting up payment form: $e';
      });
    }
  }

  // Mount card element directly
  void _mountCardElement() {
    if (_cardElement == null || _cardElementId == null) return;

    try {
      _cardElement!.callMethod('mount', ['#$_cardElementId']);
      debugPrint('Direct card element mounted');
    } catch (e) {
      debugPrint('Error mounting direct card element: $e');
      setState(() {
        _errorMessage = 'Error mounting card element: $e';
      });
    }
  }

  // Clean up Stripe elements
  void _cleanupStripe() {
    try {
      if (_cardElement != null) {
        _cardElement!.callMethod('unmount');
        _cardElement = null;
      }
      _elements = null;
      _stripe = null;
    } catch (e) {
      debugPrint('Error cleaning up direct Stripe: $e');
    }
  }

  // Process payment directly
  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_stripeInitialized || _stripe == null || _cardElement == null) {
      setState(() {
        _errorMessage =
            'Payment system not initialized. Please refresh the page.';
      });
      return;
    }

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      final name = _nameController.text.trim();

      debugPrint('Processing payment with direct Stripe integration');
      debugPrint('Client secret: ${widget.clientSecret.substring(0, 10)}...');

      // Create properly structured payment method data according to Stripe API
      final paymentMethodData = js.JsObject.jsify({
        'payment_method': {
          'card': _cardElement,
          'billing_details': {'name': name}
        }
      });

      debugPrint('Payment method data created');

      // Confirm payment
      final completer = Completer<Map<String, dynamic>>();

      // Create success callback
      final successCallback = js.allowInterop((dynamic result) {
        debugPrint('Payment result received: $result');

        if (result == null) {
          debugPrint('Payment result is null');
          completer
              .complete({'success': false, 'error': 'Payment result is null'});
          return;
        }

        // Handle error case
        if (result is js.JsObject
            ? result.hasProperty('error')
            : (result as dynamic)['error'] != null) {
          final error = result is js.JsObject
              ? result['error']
              : (result as dynamic)['error'];
          final errorMessage = error is js.JsObject
              ? (error['message']?.toString() ?? 'Payment failed')
              : (error['message']?.toString() ?? 'Payment failed');

          debugPrint('Payment error: $errorMessage');
          completer.complete({'success': false, 'error': errorMessage});
          return;
        }

        // Handle success case
        if (result is js.JsObject
            ? result.hasProperty('paymentIntent')
            : (result as dynamic)['paymentIntent'] != null) {
          final paymentIntent = result is js.JsObject
              ? result['paymentIntent']
              : (result as dynamic)['paymentIntent'];
          final paymentIntentId = paymentIntent is js.JsObject
              ? paymentIntent['id']?.toString()
              : (paymentIntent as dynamic)['id']?.toString();
          final status = paymentIntent is js.JsObject
              ? paymentIntent['status']?.toString()
              : (paymentIntent as dynamic)['status']?.toString();

          debugPrint(
              'Payment succeeded with intent ID: $paymentIntentId, status: $status');
          completer.complete({
            'success': true,
            'payment_intent_id': paymentIntentId,
            'status': status
          });
          return;
        }

        debugPrint('Unknown payment result format');
        completer.complete(
            {'success': false, 'error': 'Unknown payment result format'});
      });

      // Create error callback
      final errorCallback = js.allowInterop((dynamic error) {
        debugPrint('Payment error received: $error');
        completer.complete({
          'success': false,
          'error': error?.toString() ?? 'Unknown payment error'
        });
      });

      // Call confirmCardPayment with the correct parameter structure
      debugPrint('Calling stripe.confirmCardPayment');
      final promise = _stripe!.callMethod(
          'confirmCardPayment', [widget.clientSecret, paymentMethodData]);

      promise.callMethod('then', [successCallback]);
      promise.callMethod('catch', [errorCallback]);

      final result = await completer.future;

      if (result['success'] == true) {
        widget.onPaymentResult({
          'success': true,
          'payment_intent_id': result['payment_intent_id'],
        });
      } else {
        setState(() {
          _errorMessage = result['error'] ?? 'Payment failed';
        });
        widget.onPaymentResult({
          'success': false,
          'error': result['error'] ?? 'Payment failed',
        });
      }
    } catch (e) {
      debugPrint('Error in direct payment processing: $e');

      setState(() {
        _errorMessage = 'Payment error: $e';
      });

      widget.onPaymentResult({
        'success': false,
        'error': 'Payment error: $e',
      });
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // Validate cardholder name
  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter cardholder name';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) {
      return const Center(
        child: Text('This payment form is only available on web'),
      );
    }

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cardholder Name Field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cardholder Name',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  hintText: 'Enter full name as on card',
                  prefixIcon: Container(
                    margin: const EdgeInsets.all(12),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.tealSurge.withAlpha(26), // 10% opacity
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.person_outline,
                      color: AppColors.tealSurge,
                      size: 20,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: AppColors.tealSurge, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppColors.error, width: 2),
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
                validator: _validateName,
                enabled: !_isProcessing,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimaryLight,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Card Details Section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Card Details',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        _errorMessage != null && _errorMessage!.contains('card')
                            ? AppColors.error
                            : Colors.grey.shade300,
                    width:
                        _errorMessage != null && _errorMessage!.contains('card')
                            ? 2
                            : 1,
                  ),
                  color: Colors.grey.shade50,
                ),
                child: _stripeInitialized && _cardElementId != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 16),
                          child: HtmlElementView(
                            viewType: _cardElementId!,
                          ),
                        ),
                      )
                    : Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.tealSurge),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Loading payment form...',
                              style: TextStyle(
                                color: AppColors.textSecondaryLight,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ],
          ),

          // Error message
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.error.withAlpha(26), // 10% opacity
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.error.withAlpha(51), // 20% opacity
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: AppColors.error,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: AppColors.error,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 32),

          // Process Payment Button
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: AppColors.getPrimaryGradient(),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.tealSurge.withAlpha(77), // 30% opacity
                  offset: const Offset(0, 8),
                  blurRadius: 24,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: (_isProcessing || !_stripeInitialized)
                    ? null
                    : _processPayment,
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isProcessing) ...[
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Processing...',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ] else ...[
                        Icon(
                          Icons.payment,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Process Payment',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Security footer
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock,
                size: 14,
                color: AppColors.textSecondaryLight,
              ),
              const SizedBox(width: 6),
              Text(
                'Your payment is secure and encrypted',
                style: TextStyle(
                  color: AppColors.textSecondaryLight,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
