import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

/// A button with a gradient background
class GradientButton extends StatelessWidget {
  /// The child widget to display inside the button
  final Widget child;

  /// The callback to execute when the button is pressed
  final VoidCallback? onPressed;

  /// The width of the button (optional)
  final double? width;

  /// The height of the button (optional)
  final double? height;

  /// The border radius of the button
  final BorderRadius borderRadius;

  /// The padding inside the button
  final EdgeInsetsGeometry padding;

  /// Whether to use the dark gradient
  final bool useDarkGradient;

  /// Creates a gradient button
  const GradientButton({
    Key? key,
    required this.child,
    required this.onPressed,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(12)),
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
    this.useDarkGradient = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: useDarkGradient || isDarkMode
            ? AppColors.getSecondaryGradient()
            : AppColors.getAccentGradient(),
        borderRadius: borderRadius,
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          foregroundColor: isDarkMode ? Colors.black : Colors.white,
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: padding,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
          ),
        ),
        child: child,
      ),
    );
  }
}
