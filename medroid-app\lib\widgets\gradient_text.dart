import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

/// A text widget with a gradient color
class GradientText extends StatelessWidget {
  /// The text to display
  final String text;

  /// The text style
  final TextStyle style;

  /// The text alignment
  final TextAlign textAlign;

  /// Custom gradient to use
  final Gradient? gradient;

  /// Creates a gradient text widget
  const GradientText(
    this.text, {
    Key? key,
    required this.style,
    this.textAlign = TextAlign.center,
    this.gradient,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) =>
          gradient?.createShader(bounds) ??
          AppColors.getPrimaryGradient().createShader(bounds),
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
      ),
    );
  }
}
