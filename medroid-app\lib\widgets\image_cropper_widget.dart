import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

class AspectRatioOption {
  final String name;
  final double ratio; // 0.0 means free form

  AspectRatioOption(this.name, this.ratio);
}

class ImageCropperWidget extends StatefulWidget {
  final File? imageFile;
  final Uint8List? imageBytes;
  final String fileName;
  final Function(File?, Uint8List?) onCropped;
  final VoidCallback onCancel;

  const ImageCropperWidget({
    Key? key,
    this.imageFile,
    this.imageBytes,
    required this.fileName,
    required this.onCropped,
    required this.onCancel,
  }) : super(key: key);

  @override
  State<ImageCropperWidget> createState() => _ImageCropperWidgetState();
}

class _ImageCropperWidgetState extends State<ImageCropperWidget> {
  late img.Image originalImage;
  bool _isLoading = true;
  String? _error;

  // Crop area properties
  double _cropX = 0;
  double _cropY = 0;
  double _cropWidth = 0;
  double _cropHeight = 0;

  // Display properties
  double _displayWidth = 0;
  double _displayHeight = 0;
  double _scale = 1.0;

  // Zoom properties
  double _zoomScale = 1.0;
  double _minZoom = 0.5;
  double _maxZoom = 3.0;

  // Available aspect ratios
  final List<AspectRatioOption> _aspectRatios = [
    AspectRatioOption('1:1', 1.0),
    AspectRatioOption('4:3', 4.0 / 3.0),
    AspectRatioOption('16:9', 16.0 / 9.0),
    AspectRatioOption('3:4', 3.0 / 4.0),
    AspectRatioOption('9:16', 9.0 / 16.0),
    AspectRatioOption('Free', 0.0), // Free form
  ];

  int _selectedAspectRatioIndex = 1; // Default to 4:3

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      Uint8List imageBytes;

      if (widget.imageFile != null) {
        imageBytes = await widget.imageFile!.readAsBytes();
      } else if (widget.imageBytes != null) {
        imageBytes = widget.imageBytes!;
      } else {
        throw Exception('No image provided');
      }

      originalImage = img.decodeImage(imageBytes)!;

      // Calculate initial crop area for 4:3 aspect ratio
      _calculateInitialCropArea();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load image: $e';
        _isLoading = false;
      });
    }
  }

  void _calculateInitialCropArea() {
    _calculateCropAreaForAspectRatio(_selectedAspectRatioIndex);
  }

  void _calculateCropAreaForAspectRatio(int aspectRatioIndex) {
    final imageWidth = originalImage.width.toDouble();
    final imageHeight = originalImage.height.toDouble();
    final selectedRatio = _aspectRatios[aspectRatioIndex];

    if (selectedRatio.ratio == 0.0) {
      // Free form - use entire image
      _cropX = 0;
      _cropY = 0;
      _cropWidth = imageWidth;
      _cropHeight = imageHeight;
      return;
    }

    final targetAspectRatio = selectedRatio.ratio;
    final currentAspectRatio = imageWidth / imageHeight;

    if (currentAspectRatio > targetAspectRatio) {
      // Image is wider than target, crop width
      _cropHeight = imageHeight;
      _cropWidth = imageHeight * targetAspectRatio;
      _cropX = (imageWidth - _cropWidth) / 2;
      _cropY = 0;
    } else {
      // Image is taller than target, crop height
      _cropWidth = imageWidth;
      _cropHeight = imageWidth / targetAspectRatio;
      _cropX = 0;
      _cropY = (imageHeight - _cropHeight) / 2;
    }
  }

  void _updateDisplayDimensions(BoxConstraints constraints) {
    final maxWidth = constraints.maxWidth - 32; // Account for padding
    final maxHeight = constraints.maxHeight - 200; // Account for UI elements

    final imageAspectRatio = originalImage.width / originalImage.height;

    if (maxWidth / maxHeight > imageAspectRatio) {
      _displayHeight = maxHeight;
      _displayWidth = maxHeight * imageAspectRatio;
    } else {
      _displayWidth = maxWidth;
      _displayHeight = maxWidth / imageAspectRatio;
    }

    _scale = _displayWidth / originalImage.width;
  }

  void _zoomIn() {
    setState(() {
      _zoomScale = (_zoomScale * 1.2).clamp(_minZoom, _maxZoom);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomScale = (_zoomScale / 1.2).clamp(_minZoom, _maxZoom);
    });
  }

  void _selectAspectRatio(int index) {
    setState(() {
      _selectedAspectRatioIndex = index;
      _calculateCropAreaForAspectRatio(index);
    });
  }

  Future<void> _cropAndSave() async {
    try {
      // Crop the image
      final croppedImage = img.copyCrop(
        originalImage,
        x: _cropX.round(),
        y: _cropY.round(),
        width: _cropWidth.round(),
        height: _cropHeight.round(),
      );

      // Encode to bytes
      final croppedBytes =
          Uint8List.fromList(img.encodeJpg(croppedImage, quality: 90));

      if (kIsWeb) {
        // For web, return bytes
        widget.onCropped(null, croppedBytes);
      } else {
        // For mobile, save to temporary file
        final tempDir = Directory.systemTemp;
        final tempFile = File('${tempDir.path}/cropped_${widget.fileName}');
        await tempFile.writeAsBytes(croppedBytes);
        widget.onCropped(tempFile, null);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to crop image: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Crop Image', style: TextStyle(color: Colors.white)),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: widget.onCancel,
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _cropAndSave,
            child: const Text(
              'Done',
              style: TextStyle(
                  color: Colors.blue,
                  fontSize: 16,
                  fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(_error!,
                      style: const TextStyle(color: Colors.white)))
              : LayoutBuilder(
                  builder: (context, constraints) {
                    _updateDisplayDimensions(constraints);

                    return Column(
                      children: [
                        // Instructions
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            'Adjust the crop area for ${_aspectRatios[_selectedAspectRatioIndex].name} aspect ratio',
                            style: const TextStyle(
                                color: Colors.white, fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        // Main crop area with zoom controls
                        Expanded(
                          child: Stack(
                            children: [
                              // Image with crop overlay
                              Center(
                                child: Transform.scale(
                                  scale: _zoomScale,
                                  child: SizedBox(
                                    width: _displayWidth,
                                    height: _displayHeight,
                                    child: Stack(
                                      children: [
                                        // Original image
                                        Image.memory(
                                          Uint8List.fromList(
                                              img.encodeJpg(originalImage)),
                                          width: _displayWidth,
                                          height: _displayHeight,
                                          fit: BoxFit.contain,
                                        ),

                                        // Crop overlay
                                        Positioned(
                                          left: _cropX * _scale,
                                          top: _cropY * _scale,
                                          child: Container(
                                            width: _cropWidth * _scale,
                                            height: _cropHeight * _scale,
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: Colors.white,
                                                  width: 2),
                                            ),
                                          ),
                                        ),

                                        // Dimmed overlay for non-crop areas
                                        SizedBox(
                                          width: _displayWidth,
                                          height: _displayHeight,
                                          child: CustomPaint(
                                            painter: CropOverlayPainter(
                                              cropRect: Rect.fromLTWH(
                                                _cropX * _scale,
                                                _cropY * _scale,
                                                _cropWidth * _scale,
                                                _cropHeight * _scale,
                                              ),
                                              imageSize: Size(_displayWidth,
                                                  _displayHeight),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),

                              // Zoom controls
                              Positioned(
                                right: 16,
                                top: 50,
                                child: Column(
                                  children: [
                                    // Zoom in button
                                    Container(
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.black.withValues(alpha: 0.7),
                                        shape: BoxShape.circle,
                                      ),
                                      child: IconButton(
                                        icon: const Icon(Icons.add,
                                            color: Colors.white),
                                        onPressed: _zoomIn,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    // Zoom out button
                                    Container(
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.black.withValues(alpha: 0.7),
                                        shape: BoxShape.circle,
                                      ),
                                      child: IconButton(
                                        icon: const Icon(Icons.remove,
                                            color: Colors.white),
                                        onPressed: _zoomOut,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Aspect ratio selector
                        Container(
                          height: 80,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _aspectRatios.length,
                            itemBuilder: (context, index) {
                              final isSelected =
                                  index == _selectedAspectRatioIndex;
                              return GestureDetector(
                                onTap: () => _selectAspectRatio(index),
                                child: Container(
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.white.withValues(alpha: 0.2)
                                        : Colors.white.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(20),
                                    border: isSelected
                                        ? Border.all(
                                            color: Colors.white, width: 2)
                                        : null,
                                  ),
                                  child: Center(
                                    child: Text(
                                      _aspectRatios[index].name,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
    );
  }
}

class CropOverlayPainter extends CustomPainter {
  final Rect cropRect;
  final Size imageSize;

  CropOverlayPainter({required this.cropRect, required this.imageSize});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..style = PaintingStyle.fill;

    // Draw dimmed areas around the crop rectangle
    final path = Path()
      ..addRect(Rect.fromLTWH(0, 0, imageSize.width, imageSize.height))
      ..addRect(cropRect)
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
