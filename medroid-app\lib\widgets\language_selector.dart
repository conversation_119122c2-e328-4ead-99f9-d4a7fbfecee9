import 'package:flutter/material.dart';

class LanguageSelector extends StatefulWidget {
  final List<String> selectedLanguages;
  final Function(List<String>) onLanguagesChanged;
  final bool isRequired;

  const LanguageSelector({
    Key? key,
    required this.selectedLanguages,
    required this.onLanguagesChanged,
    this.isRequired = true,
  }) : super(key: key);

  @override
  State<LanguageSelector> createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends State<LanguageSelector> {
  // Common languages list
  final List<String> _commonLanguages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Arabic',
    'Hindi',
    'Portuguese',
    'Russian',
    'Japanese',
    'Italian',
    'Korean',
    'Dutch',
    'Turkish',
    'Polish',
    'Vietnamese',
    'Thai',
    'Greek',
    'Hebrew',
    'Swedish',
    'Norwegian',
    'Danish',
    'Finnish',
    'Czech',
    'Hungarian',
    'Romanian',
    'Bengali',
    'Punjabi',
    'Telugu',
    'Tamil',
    'Urdu',
    'Farsi',
  ];

  // Selected languages
  late List<String> _selectedLanguages;
  
  // Search controller
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedLanguages = List.from(widget.selectedLanguages);
  }

  @override
  void didUpdateWidget(LanguageSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedLanguages != widget.selectedLanguages) {
      _selectedLanguages = List.from(widget.selectedLanguages);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Toggle language selection
  void _toggleLanguage(String language) {
    setState(() {
      if (_selectedLanguages.contains(language)) {
        _selectedLanguages.remove(language);
      } else {
        _selectedLanguages.add(language);
      }
      widget.onLanguagesChanged(_selectedLanguages);
    });
  }

  // Filter languages based on search query
  List<String> _getFilteredLanguages() {
    if (_searchQuery.isEmpty) {
      return _commonLanguages;
    }
    return _commonLanguages
        .where((language) =>
            language.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filteredLanguages = _getFilteredLanguages();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Search languages',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        const SizedBox(height: 8),
        
        // Selected languages chips
        if (_selectedLanguages.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _selectedLanguages.map((language) {
              return Chip(
                label: Text(language),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _toggleLanguage(language),
                backgroundColor: theme.colorScheme.primaryContainer,
                labelStyle: TextStyle(color: theme.colorScheme.onPrimaryContainer),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],
        
        // Language options
        Container(
          constraints: const BoxConstraints(maxHeight: 200),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          child: filteredLanguages.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('No languages found'),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  itemCount: filteredLanguages.length,
                  itemBuilder: (context, index) {
                    final language = filteredLanguages[index];
                    final isSelected = _selectedLanguages.contains(language);
                    
                    return ListTile(
                      title: Text(language),
                      trailing: isSelected
                          ? Icon(Icons.check_circle, color: theme.colorScheme.primary)
                          : const Icon(Icons.circle_outlined),
                      onTap: () => _toggleLanguage(language),
                      selected: isSelected,
                      selectedTileColor: theme.colorScheme.primaryContainer.withOpacity(0.2),
                    );
                  },
                ),
        ),
        
        // Helper text
        if (widget.isRequired) ...[
          const SizedBox(height: 8),
          Text(
            'Please select at least one language',
            style: TextStyle(
              color: _selectedLanguages.isEmpty ? Colors.red : Colors.grey,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }
}
