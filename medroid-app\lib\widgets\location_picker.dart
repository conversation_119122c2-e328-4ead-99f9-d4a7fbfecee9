import 'package:flutter/material.dart';
import 'package:medroid_app/services/location_service.dart';

class LocationPicker extends StatefulWidget {
  final Function(Map<String, dynamic>) onLocationSelected;

  const LocationPicker({
    Key? key,
    required this.onLocationSelected,
  }) : super(key: key);

  @override
  State<LocationPicker> createState() => _LocationPickerState();
}

class _LocationPickerState extends State<LocationPicker> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _addressController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();

  final LocationService _locationService = LocationService();
  List<Map<String, dynamic>> _searchResults = [];
  bool _isSearching = false;
  bool _manualEntry = false;

  @override
  void dispose() {
    _searchController.dispose();
    _addressController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }

  Future<void> _searchLocations() async {
    if (_searchController.text.isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final results =
          await _locationService.getLocationSuggestions(_searchController.text);

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching locations: $e')),
        );
      }
    }
  }

  void _selectLocation(Map<String, dynamic> location) {
    setState(() {
      // Use display_name or address, depending on what's available
      _addressController.text = location['address'] ?? location['display_name'];

      // Make sure we're using the correct format for latitude and longitude
      // Some APIs return lat/lon as strings, others as doubles
      double lat;
      double lon;

      if (location.containsKey('lat') && location.containsKey('lon')) {
        // Handle lat/lon format
        lat = location['lat'] is String
            ? double.parse(location['lat'])
            : location['lat'].toDouble();
        lon = location['lon'] is String
            ? double.parse(location['lon'])
            : location['lon'].toDouble();
      } else if (location.containsKey('latitude') &&
          location.containsKey('longitude')) {
        // Handle latitude/longitude format
        lat = location['latitude'] is String
            ? double.parse(location['latitude'])
            : location['latitude'].toDouble();
        lon = location['longitude'] is String
            ? double.parse(location['longitude'])
            : location['longitude'].toDouble();
      } else {
        // Default values if no coordinates are found
        lat = 0.0;
        lon = 0.0;
      }

      _latitudeController.text = lat.toString();
      _longitudeController.text = lon.toString();
      _searchResults = [];
      _searchController.clear();
    });
  }

  void _toggleManualEntry() {
    setState(() {
      _manualEntry = !_manualEntry;
      if (!_manualEntry) {
        // Clear manual entry fields when switching back to search
        _addressController.clear();
        _latitudeController.clear();
        _longitudeController.clear();
      }
    });
  }

  void _submitLocation() {
    if (_formKey.currentState!.validate()) {
      try {
        // Parse the coordinates as doubles to ensure they're valid
        final latitude = double.parse(_latitudeController.text);
        final longitude = double.parse(_longitudeController.text);

        // Create a properly formatted location object
        final location = {
          'address': _addressController.text,
          'coordinates': [
            latitude,
            longitude,
          ],
        };

        // Log the location data for debugging
        debugPrint('Submitting location: $location');

        widget.onLocationSelected(location);
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Invalid coordinates: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!_manualEntry) ...[
              // Location search
              TextFormField(
                controller: _searchController,
                decoration: InputDecoration(
                  labelText: 'Search Location',
                  hintText: 'Enter city, address, or landmark',
                  border: const OutlineInputBorder(),
                  suffixIcon: _isSearching
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: Padding(
                            padding: EdgeInsets.all(8.0),
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        )
                      : IconButton(
                          icon: const Icon(Icons.search),
                          onPressed: _searchLocations,
                        ),
                ),
                onChanged: (value) {
                  if (value.length > 2) {
                    _searchLocations();
                  }
                },
              ),

              const SizedBox(height: 8),

              // Search results
              if (_searchResults.isNotEmpty)
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _searchResults.length,
                    itemBuilder: (context, index) {
                      final result = _searchResults[index];
                      return ListTile(
                        title: Text(
                          result['display_name'],
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text(
                          '${result['lat']}, ${result['lon']}',
                          style: TextStyle(
                              fontSize: 12, color: Colors.grey.shade600),
                        ),
                        onTap: () => _selectLocation(result),
                      );
                    },
                  ),
                ),

              const SizedBox(height: 16),

              // Selected location display
              if (_addressController.text.isNotEmpty) ...[
                Text(
                  'Selected Location:',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _addressController.text,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Coordinates: ${_latitudeController.text}, ${_longitudeController.text}',
                        style: TextStyle(
                            color: Colors.grey.shade700, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ],

            if (_manualEntry) ...[
              // Manual address entry
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'Address',
                  hintText: 'Enter full address',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an address';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Manual coordinates entry
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _latitudeController,
                      decoration: const InputDecoration(
                        labelText: 'Latitude',
                        hintText: 'e.g., 37.7749',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        try {
                          final lat = double.parse(value);
                          if (lat < -90 || lat > 90) {
                            return 'Invalid';
                          }
                        } catch (e) {
                          return 'Invalid';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      controller: _longitudeController,
                      decoration: const InputDecoration(
                        labelText: 'Longitude',
                        hintText: 'e.g., -122.4194',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        try {
                          final lng = double.parse(value);
                          if (lng < -180 || lng > 180) {
                            return 'Invalid';
                          }
                        } catch (e) {
                          return 'Invalid';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Toggle between search and manual entry
            TextButton.icon(
              onPressed: _toggleManualEntry,
              icon: Icon(_manualEntry ? Icons.search : Icons.edit),
              label: Text(_manualEntry ? 'Search Location' : 'Manual Entry'),
            ),

            const SizedBox(height: 16),

            // Add location button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: (_addressController.text.isNotEmpty &&
                        _latitudeController.text.isNotEmpty &&
                        _longitudeController.text.isNotEmpty)
                    ? _submitLocation
                    : null,
                child: const Text('Add Location'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
