import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/gradient_colors.dart';

/// A specialized button for login/authentication screens with a premium gradient look
class LoginButton extends StatelessWidget {
  /// The child widget to display inside the button
  final Widget child;

  /// The callback to execute when the button is pressed
  final VoidCallback? onPressed;

  /// The width of the button (optional)
  final double? width;

  /// The height of the button (optional)
  final double? height;

  /// Creates a login button with a premium gradient
  const LoginButton({
    Key? key,
    required this.child,
    required this.onPressed,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: GradientColors.getAccentGradient(),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.coralPop.withValues(alpha: 0.4),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: AppColors.coralPop.withValues(alpha: 0.2),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onPressed,
          splashColor: Colors.white.withAlpha(50),
          highlightColor: Colors.white.withAlpha(30),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            alignment: Alignment.center,
            child: child,
          ),
        ),
      ),
    );
  }
}
