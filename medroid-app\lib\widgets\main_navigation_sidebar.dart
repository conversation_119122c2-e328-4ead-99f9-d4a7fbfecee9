import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/custom_bottom_navigation.dart';

class MainNavigationSidebar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onNavigationChanged;
  final Function(String)? onConversationSelected;
  final String? currentConversationId;

  const MainNavigationSidebar({
    Key? key,
    required this.currentIndex,
    required this.onNavigationChanged,
    this.onConversationSelected,
    this.currentConversationId,
  }) : super(key: key);

  @override
  State<MainNavigationSidebar> createState() => _MainNavigationSidebarState();
}

class _MainNavigationSidebarState extends State<MainNavigationSidebar> {
  List<dynamic> _chatHistory = [];
  bool _isLoadingChatHistory = false;

  // Define navigation items - match the ones in main navigation (without History)
  final List<CustomNavItem> _navItems = [
    CustomNavItem(
      icon: Icons.chat_bubble_outline_rounded,
      activeIcon: Icons.chat_bubble_rounded,
      label: 'Chat',
    ),
    CustomNavItem(
      icon: Icons.explore_outlined,
      activeIcon: Icons.explore,
      label: 'Discover',
    ),
    CustomNavItem(
      icon: Icons.shopping_bag_outlined,
      activeIcon: Icons.shopping_bag,
      label: 'Shop',
    ),
    CustomNavItem(
      icon: Icons.person_outline_rounded,
      activeIcon: Icons.person_rounded,
      label: 'Profile',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  // Method to load chat history for sidebar
  Future<void> _loadChatHistory() async {
    if (_isLoadingChatHistory) return;

    setState(() {
      _isLoadingChatHistory = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversations = await apiService.getChatHistory(perPage: 20);

      if (mounted) {
        setState(() {
          _chatHistory = conversations;
          _isLoadingChatHistory = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading sidebar chat history: $e');
      if (mounted) {
        setState(() {
          _isLoadingChatHistory = false;
        });
      }
    }
  }

  void _handleLogout() async {
    // Add logout logic here
    Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Container(
      width: isDesktop ? 240 : 80,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Logo section
          Container(
            height: 80,
            alignment: Alignment.center,
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/medroid_icon.png',
                  height: 32,
                  width: 32,
                ),
                if (isDesktop) ...[
                  const SizedBox(width: 8),
                  const Text(
                    'Medroid',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Navigation items
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Top section (navigation items and chat history)
                Expanded(
                  child: Column(
                    children: [
                      // Main navigation items
                      Column(
                        children: [
                          ...List.generate(_navItems.length, (index) {
                            // Skip History tab (index 2) and Profile tab (index 4) for desktop/tablet
                            // History is accessed via "View all chats", Profile is shown at bottom
                            if (index == 2 || index == 4) {
                              return const SizedBox.shrink();
                            }

                            final item = _navItems[index];
                            // Map the navigation indices correctly:
                            // 0: Chat (index 0)
                            // 1: Discover (index 1)
                            // 3: Shop (index 3 in main navigation)
                            int navigationIndex = index;
                            if (index == 3) {
                              // Shop
                              navigationIndex = 3; // Keep Shop at index 3
                            }

                            final isSelected =
                                widget.currentIndex == navigationIndex;

                            return InkWell(
                              onTap: () =>
                                  widget.onNavigationChanged(navigationIndex),
                              child: Container(
                                height: 40,
                                padding: EdgeInsets.symmetric(
                                  horizontal: isDesktop ? 14 : 6,
                                  vertical: 4,
                                ),
                                margin: const EdgeInsets.only(bottom: 2),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? AppColors.mintGlow
                                          .withValues(alpha: 0.2)
                                      : Colors.transparent,
                                  border: Border(
                                    left: BorderSide(
                                      color: isSelected
                                          ? AppColors.tealSurge
                                          : Colors.transparent,
                                      width: 4,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: isDesktop
                                      ? MainAxisAlignment.start
                                      : MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      isSelected ? item.activeIcon : item.icon,
                                      color: isSelected
                                          ? AppColors.tealSurge
                                          : AppColors.slateGrey,
                                      size: 20,
                                    ),
                                    if (isDesktop) ...[
                                      const SizedBox(width: 8),
                                      Text(
                                        item.label,
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: isSelected
                                              ? AppColors.tealSurge
                                              : AppColors.slateGrey,
                                          fontWeight: isSelected
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            );
                          }),

                          // Appointments menu item
                          if (isDesktop || isTablet)
                            InkWell(
                              onTap: () {
                                Navigator.of(context)
                                    .pushNamed('/appointments');
                              },
                              child: Container(
                                height: 40,
                                padding: EdgeInsets.symmetric(
                                  horizontal: isDesktop ? 14 : 6,
                                  vertical: 4,
                                ),
                                margin: const EdgeInsets.only(bottom: 2),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: widget.currentIndex == -2
                                      ? AppColors.tealSurge.withAlpha(25)
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: isDesktop
                                      ? MainAxisAlignment.start
                                      : MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.calendar_today,
                                      color: widget.currentIndex == -2
                                          ? AppColors.tealSurge
                                          : const Color(0xFF666666),
                                      size: 20,
                                    ),
                                    if (isDesktop) ...[
                                      const SizedBox(width: 8),
                                      Text(
                                        'Appointments',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: widget.currentIndex == -2
                                              ? AppColors.tealSurge
                                              : const Color(0xFF666666),
                                          fontWeight: widget.currentIndex == -2
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),

                      // Chat history section for desktop and tablet
                      if (isDesktop || isTablet) ...[
                        // Chat history header
                        Padding(
                          padding: const EdgeInsets.fromLTRB(14, 10, 14, 4),
                          child: Row(
                            children: [
                              const Expanded(
                                child: Text(
                                  'Chat History',
                                  style: TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF666666),
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              GestureDetector(
                                onTap: _loadChatHistory,
                                child: const Padding(
                                  padding: EdgeInsets.all(2),
                                  child: Icon(
                                    Icons.refresh_outlined,
                                    size: 16,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Chat history list
                        Expanded(
                          child: _isLoadingChatHistory
                              ? const Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                )
                              : _chatHistory.isEmpty
                                  ? const Center(
                                      child: Text(
                                        'No chat history',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: Color(0xFF666666),
                                        ),
                                      ),
                                    )
                                  : ListView.builder(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 14),
                                      itemCount: _chatHistory.length,
                                      itemBuilder: (context, index) {
                                        final conversation =
                                            _chatHistory[index];
                                        final date =
                                            conversation['updated_at'] != null
                                                ? DateTime.parse(
                                                    conversation['updated_at'])
                                                : DateTime.now();

                                        final formattedDate =
                                            DateFormat('MMM d').format(date);
                                        String title = conversation['title'] ??
                                            'Health Conversation';

                                        return StatefulBuilder(
                                            builder: (context, setState) {
                                          bool isHovering = false;

                                          return MouseRegion(
                                            onEnter: (_) => setState(
                                                () => isHovering = true),
                                            onExit: (_) => setState(
                                                () => isHovering = false),
                                            child: InkWell(
                                              onTap: () {
                                                if (widget
                                                        .onConversationSelected !=
                                                    null) {
                                                  widget.onConversationSelected!(
                                                      conversation['id']
                                                          .toString());
                                                }
                                              },
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 6,
                                                        horizontal: 10),
                                                margin: const EdgeInsets.only(
                                                    bottom: 2),
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  color:
                                                      widget.currentConversationId ==
                                                              conversation[
                                                                      'id']
                                                                  .toString()
                                                          ? const Color
                                                              .fromRGBO(139,
                                                              233, 200, 0.1)
                                                          : isHovering
                                                              ? Colors
                                                                  .grey
                                                                  .withAlpha(13)
                                                              : Colors
                                                                  .transparent,
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            title,
                                                            style: TextStyle(
                                                              fontSize: 13,
                                                              fontWeight: widget
                                                                          .currentConversationId ==
                                                                      conversation[
                                                                              'id']
                                                                          .toString()
                                                                  ? FontWeight
                                                                      .bold
                                                                  : FontWeight
                                                                      .normal,
                                                              color: widget
                                                                          .currentConversationId ==
                                                                      conversation[
                                                                              'id']
                                                                          .toString()
                                                                  ? const Color(
                                                                      0xFF17C3B2)
                                                                  : Colors
                                                                      .black87,
                                                            ),
                                                            maxLines: 1,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                          ),
                                                          const SizedBox(
                                                              height: 1),
                                                          Text(
                                                            formattedDate,
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 10,
                                                              color: Color(
                                                                  0xFF666666),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    if (isHovering)
                                                      Icon(
                                                        Icons.more_vert,
                                                        size: 18,
                                                        color: Colors.grey[600],
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        });
                                      },
                                    ),
                        ),

                        // View all link
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(14, 4, 14, 8),
                            child: GestureDetector(
                              onTap: () {
                                Navigator.pushNamed(context, '/chat-history');
                              },
                              child: const Text(
                                'View all chats',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF17C3B2),
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Bottom section with Profile and Logout
                if (isDesktop || isTablet) ...[
                  // Divider
                  Container(
                    height: 1,
                    margin: const EdgeInsets.symmetric(vertical: 6),
                    color: Colors.grey.withAlpha(51),
                  ),

                  // Profile and Logout in one row
                  Container(
                    height: 40,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 14,
                      vertical: 6,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Profile button
                        InkWell(
                          onTap: () =>
                              widget.onNavigationChanged(4), // Profile tab
                          child: Row(
                            children: [
                              const Icon(
                                Icons.person,
                                color: AppColors.slateGrey,
                                size: 20,
                              ),
                              if (isDesktop) ...[
                                const SizedBox(width: 8),
                                const Text(
                                  'Profile',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFF666666),
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),

                        // Logout button (icon only)
                        InkWell(
                          onTap: _handleLogout,
                          child: const Icon(
                            Icons.logout,
                            color: Color(0xFF666666),
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
