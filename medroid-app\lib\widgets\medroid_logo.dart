import 'package:flutter/material.dart';
import 'package:medroid_app/utils/theme.dart';

class MedroidLogo extends StatelessWidget {
  final double size;
  final Color? color;
  final bool useDarkVersion;

  const MedroidLogo({
    Key? key,
    this.size = 32.0,
    this.color,
    this.useDarkVersion = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logoColor = color ?? AppTheme.primaryColor;

    // Use the appropriate icon based on dark mode
    final String logoPath = useDarkVersion
        ? 'assets/images/medroid_icon_dark.png'
        : 'assets/images/medroid_icon.png';

    return Image.asset(
      logoPath,
      width: size,
      height: size,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to the placeholder if the image isn't found
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: logoColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              'M',
              style: TextStyle(
                color: logoColor,
                fontSize: size * 0.6,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }
}
