import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/screens/notifications_screen.dart';
import 'package:medroid_app/services/notification_service.dart';
import 'package:medroid_app/services/web_notification_service.dart';

class NotificationBell extends StatelessWidget {
  const NotificationBell({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Try to get the appropriate notification service based on platform
    ValueNotifier<int> unreadCountNotifier;
    Function getUnreadCount;

    try {
      if (kIsWeb) {
        // For web, use WebNotificationService
        final webNotificationService =
            RepositoryProvider.of<WebNotificationService>(context);
        unreadCountNotifier = webNotificationService.unreadCountNotifier;
        getUnreadCount = webNotificationService.getUnreadCount;
      } else {
        // For mobile, use NotificationService
        final notificationService =
            RepositoryProvider.of<NotificationService>(context);
        unreadCountNotifier = notificationService.unreadCountNotifier;
        getUnreadCount = notificationService.getUnreadCount;
      }
    } catch (e) {
      // If no notification service is available, use a dummy notifier
      debugPrint('No notification service available: $e');
      unreadCountNotifier = ValueNotifier<int>(0);
      getUnreadCount = () async => 0;
    }

    return ValueListenableBuilder<int>(
      valueListenable: unreadCountNotifier,
      builder: (context, unreadCount, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            IconButton(
              icon: const Icon(
                Icons.notifications_outlined,
                color: Colors.black87,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationsScreen(),
                  ),
                ).then((_) {
                  // Refresh unread count when returning from notifications screen
                  getUnreadCount();
                });
              },
            ),
            if (unreadCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEC4899),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    unreadCount > 9 ? '9+' : unreadCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
