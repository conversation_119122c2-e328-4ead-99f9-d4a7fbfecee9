// This is a stub file for non-web platforms
// It's needed to avoid compilation errors when importing dart:html on mobile platforms

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// A platform-aware network image widget that handles loading images differently
/// based on the platform (web vs mobile).
///
/// On mobile platforms, it uses CachedNetworkImage for efficient caching.
/// On web, it uses a custom implementation to avoid CORS and encoding issues.
class PlatformAwareNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget Function(BuildContext, String)? placeholder;
  final Widget Function(BuildContext, String, dynamic)? errorWidget;
  final Function(dynamic)? errorListener;

  const PlatformAwareNetworkImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.errorListener,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // For development mode, check if we're using localhost
    if (imageUrl.contains('localhost') || imageUrl.contains('127.0.0.1')) {
      // In development mode with local server, use a placeholder
      return Container(
        width: width,
        height: height,
        color: Colors.grey[200],
        child: Center(
          child: Icon(
            Icons.image,
            size: (width != null && height != null)
                ? (width! < height! ? width! * 0.4 : height! * 0.4)
                : 24,
            color: Colors.grey[400],
          ),
        ),
      );
    }

    // For mobile platforms, use CachedNetworkImage
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder ?? _defaultPlaceholder,
      errorWidget: errorWidget ?? _defaultErrorWidget,
      errorListener: errorListener,
    );
  }

  // Default placeholder widget
  Widget _defaultPlaceholder(BuildContext context, String url) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[100],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  // Default error widget
  Widget _defaultErrorWidget(BuildContext context, String url, dynamic error) {
    if (errorListener != null) {
      errorListener!(error);
    }
    debugPrint('Error loading image: $error for URL: $url');

    return Container(
      width: width,
      height: height,
      color: Colors.grey[100],
      child: Center(
        child: Icon(
          Icons.image_not_supported,
          size: (width != null && height != null)
              ? (width! < height! ? width! * 0.4 : height! * 0.4)
              : 24,
          color: Colors.grey,
        ),
      ),
    );
  }
}
