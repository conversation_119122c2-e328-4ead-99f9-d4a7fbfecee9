import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:medroid_app/utils/app_colors.dart';
import 'package:google_fonts/google_fonts.dart';

class PremiumEmptyState extends StatefulWidget {
  final Function(String) onSuggestionTap;

  const PremiumEmptyState({
    Key? key,
    required this.onSuggestionTap,
  }) : super(key: key);

  @override
  State<PremiumEmptyState> createState() => _PremiumEmptyStateState();
}

class _PremiumEmptyStateState extends State<PremiumEmptyState>
    with TickerProviderStateMixin {
  // Multiple animations for more interesting effects
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  late AnimationController _rotateController;
  late Animation<double> _rotateAnimation;

  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Subtle pulse animation
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );
    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );
    _pulseController.repeat(reverse: true);

    // Very slow rotation for subtle movement
    _rotateController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );
    _rotateAnimation =
        Tween<double>(begin: 0, end: 2 * math.pi).animate(_rotateController);
    _rotateController.repeat();

    // Fade in animation for elements
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeOut,
      ),
    );
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotateController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Center(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Premium animated logo with teal colors
              Stack(
                alignment: Alignment.center,
                children: [
                  // Background subtle pattern
                  AnimatedBuilder(
                    animation: _rotateAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotateAnimation.value,
                        child: Container(
                          width: 160,
                          height: 160,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Color.fromRGBO(
                                AppColors.tealSurge.red,
                                AppColors.tealSurge.green,
                                AppColors.tealSurge.blue,
                                0.05,
                              ),
                              width: 15,
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // Animated pulse effect
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [
                                AppColors.tealSurge,
                                AppColors.mintGlow,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(50),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.tealSurge.withOpacity(0.2),
                                blurRadius: 15,
                                spreadRadius: 2,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.health_and_safety_outlined,
                              size: 48,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // Decorative ring
                  Positioned(
                    right: 20,
                    top: 20,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: AppColors.mintGlow.withOpacity(0.7),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.mintGlow.withOpacity(0.3),
                            blurRadius: 8,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 40),

              // Welcome text with refined typography
              Text(
                'Welcome to Medroid',
                style: GoogleFonts.inter(
                  fontSize: 26,
                  fontWeight: FontWeight.w700,
                  letterSpacing: -0.5,
                  color: Colors.black.withOpacity(0.85),
                ),
              ),
              const SizedBox(height: 16),

              // Description with refined typography
              Container(
                constraints: const BoxConstraints(maxWidth: 320),
                child: Text(
                  'Your personal AI Doctor. Ask me anything about your health, symptoms, medications, or wellness advice.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 15,
                    fontWeight: FontWeight.w400,
                    height: 1.5,
                    color: Colors.black.withOpacity(0.6),
                    letterSpacing: 0.1,
                  ),
                ),
              ),
              const SizedBox(height: 40),

              // Elegant divider
              Container(
                width: 40,
                height: 3,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.tealSurge.withOpacity(0.3),
                      AppColors.tealSurge,
                      AppColors.tealSurge.withOpacity(0.3),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              ),
              const SizedBox(height: 30),

              // Premium suggestion cards
              Wrap(
                spacing: 12,
                runSpacing: 12,
                alignment: WrapAlignment.center,
                children: [
                  _buildSuggestionCard(
                    'I have a headache',
                    Icons.sick_outlined,
                    AppColors.tealSurge,
                    'headache.png',
                  ),
                  _buildSuggestionCard(
                    'Help me sleep better',
                    Icons.nightlight_outlined,
                    AppColors.tealSurge,
                    'sleep.png',
                  ),
                  _buildSuggestionCard(
                    'Weight loss tips',
                    Icons.fitness_center_outlined,
                    AppColors.tealSurge,
                    'weight.png',
                  ),
                  _buildSuggestionCard(
                    'Skin rash concerns',
                    Icons.healing_outlined,
                    AppColors.tealSurge,
                    'skin.png',
                  ),
                  _buildSuggestionCard(
                    'Book appointment',
                    Icons.calendar_today_outlined,
                    AppColors.tealSurge,
                    'appointment.png',
                  ),
                  _buildSuggestionCard(
                    'Mental health tips',
                    Icons.psychology_outlined,
                    AppColors.tealSurge,
                    'mental.png',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionCard(
      String text, IconData icon, Color color, String imageName) {
    // Calculate appropriate width based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth > 1024;
    final isTablet = screenWidth > 600 && screenWidth <= 1024;

    // Adjust width for different screen sizes
    final cardWidth = isDesktop
        ? 180.0 // Wider for desktop
        : isTablet
            ? 170.0 // Medium width for tablet
            : (screenWidth - 72) / 2; // Mobile width (2 cards per row)

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onSuggestionTap(text),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: cardWidth,
          // Fixed height to ensure consistent sizing
          height: 64,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10), // 0.04 * 255 ≈ 10
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: color.withAlpha(26), // 0.1 * 255 ≈ 26
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: color.withAlpha(20), // 0.08 * 255 ≈ 20
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              Flexible(
                child: Text(
                  text,
                  style: GoogleFonts.inter(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.black.withAlpha(191), // 0.75 * 255 ≈ 191
                  ),
                  // Limit to 2 lines and add ellipsis if text is too long
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
