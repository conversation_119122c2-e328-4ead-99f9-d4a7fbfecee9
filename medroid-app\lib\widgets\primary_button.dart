import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/gradient_colors.dart';

/// A button that uses the primary color with a clean, modern design
class PrimaryButton extends StatelessWidget {
  /// The child widget to display inside the button
  final Widget child;

  /// The callback to execute when the button is pressed
  final VoidCallback? onPressed;

  /// The width of the button (optional)
  final double? width;

  /// The height of the button (optional)
  final double? height;

  /// Whether to use an outlined style instead of filled
  final bool outlined;

  /// Creates a primary button
  const PrimaryButton({
    Key? key,
    required this.child,
    required this.onPressed,
    this.width,
    this.height,
    this.outlined = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: outlined ? null : GradientColors.getAccentGradient(),
        color: outlined ? Colors.transparent : null,
        borderRadius: BorderRadius.circular(12),
        border:
            outlined ? Border.all(color: AppColors.coralPop, width: 2) : null,
        boxShadow: outlined
            ? null
            : [
                BoxShadow(
                  color: AppColors.coralPop.withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 3),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onPressed,
          splashColor: outlined
              ? AppColors.coralPop.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.5),
          highlightColor: outlined
              ? AppColors.coralPop.withValues(alpha: 0.2)
              : Colors.white.withValues(alpha: 0.3),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
            alignment: Alignment.center,
            child: DefaultTextStyle(
              style: TextStyle(
                color: outlined ? AppColors.coralPop : Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}
