import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

class ProfileSectionHeader extends StatelessWidget {
  final String title;

  const ProfileSectionHeader({
    Key? key,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.tealSurge,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 1,
            color: Colors.grey.shade200,
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
