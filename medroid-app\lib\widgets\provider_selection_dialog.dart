import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/widgets/appointment_slots_dialog.dart';

class ProviderSelectionDialog extends StatefulWidget {
  final Function() onClose;
  final Function(Map<String, dynamic>, Map<String, dynamic>)
      onAppointmentSelected;

  const ProviderSelectionDialog({
    Key? key,
    required this.onClose,
    required this.onAppointmentSelected,
  }) : super(key: key);

  @override
  _ProviderSelectionDialogState createState() =>
      _ProviderSelectionDialogState();
}

class _ProviderSelectionDialogState extends State<ProviderSelectionDialog> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _providers = [];
  String? _errorMessage;
  String? _selectedSpecialization;
  List<String> _specializations = [];

  @override
  void initState() {
    super.initState();
    _fetchProviders();
  }

  Future<void> _fetchProviders() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final providers = await apiService.getAvailableProviders();

      // Extract unique specializations
      final Set<String> specializationsSet = {};
      for (var provider in providers) {
        if (provider['specialization'] != null) {
          specializationsSet.add(provider['specialization']);
        }
      }

      setState(() {
        _providers = providers;
        _specializations = specializationsSet.toList()..sort();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load providers: ${e.toString()}';
      });
    }
  }

  void _showAppointmentSlots(Map<String, dynamic> provider) {
    showDialog(
      context: context,
      builder: (context) => AppointmentSlotsDialog(
        providerId: provider['id'].toString(),
        onSlotSelected: (slot) {
          Navigator.of(context).pop();
          widget.onAppointmentSelected(provider, slot);
        },
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredProviders() {
    if (_selectedSpecialization == null) {
      return _providers;
    }

    return _providers
        .where(
            (provider) => provider['specialization'] == _selectedSpecialization)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Select a Provider',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: widget.onClose,
                ),
              ],
            ),
            if (_specializations.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSpecializationFilter(),
            ],
            const SizedBox(height: 16),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(child: Text(_errorMessage!))
                    : _providers.isEmpty
                        ? const Center(child: Text('No providers available'))
                        : Expanded(child: _buildProvidersList()),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecializationFilter() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          FilterChip(
            label: const Text('All'),
            selected: _selectedSpecialization == null,
            onSelected: (selected) {
              setState(() {
                _selectedSpecialization = null;
              });
            },
            backgroundColor: Colors.grey.shade200,
            selectedColor: AppTheme.primaryColor.withOpacity(0.2),
            checkmarkColor: AppTheme.primaryColor,
          ),
          const SizedBox(width: 8),
          ..._specializations.map((specialization) {
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(specialization),
                selected: _selectedSpecialization == specialization,
                onSelected: (selected) {
                  setState(() {
                    _selectedSpecialization = selected ? specialization : null;
                  });
                },
                backgroundColor: Colors.grey.shade200,
                selectedColor: AppTheme.primaryColor.withOpacity(0.2),
                checkmarkColor: AppTheme.primaryColor,
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildProvidersList() {
    final filteredProviders = _getFilteredProviders();

    return ListView.builder(
      shrinkWrap: true,
      itemCount: filteredProviders.length,
      itemBuilder: (context, index) {
        final provider = filteredProviders[index];

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _showAppointmentSlots(provider),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: provider['avatar_url'] != null
                        ? NetworkImage(provider['avatar_url'])
                        : null,
                    child: provider['avatar_url'] == null
                        ? const Icon(Icons.person, size: 30)
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dr. ${provider['name'] ?? 'Unknown'}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          provider['specialization'] ?? 'General Practitioner',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          provider['bio'] ?? 'No bio available',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_forward_ios, size: 16),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
