import 'package:flutter/material.dart';

/// A responsive container that centers its content and adjusts width based on screen size
class ResponsiveCenteredContainer extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry padding;
  final Color? backgroundColor;

  const ResponsiveCenteredContainer({
    Key? key,
    required this.child,
    this.maxWidth = 800,
    this.padding = EdgeInsets.zero,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor,
      width: double.infinity,
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: maxWidth,
          ),
          child: Padding(
            padding: padding,
            child: child,
          ),
        ),
      ),
    );
  }
}
