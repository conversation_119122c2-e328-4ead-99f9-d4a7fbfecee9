import 'package:flutter/material.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

/// A responsive container widget that adapts to different screen sizes
///
/// This widget provides a container with responsive padding, width, and
/// alignment based on the screen size.
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? width;
  final double? height;
  final BoxConstraints? constraints;
  final Alignment? alignment;
  final BoxDecoration? decoration;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.width,
    this.height,
    this.constraints,
    this.alignment,
    this.decoration,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine responsive padding based on screen size
    final responsivePadding = padding ?? ResponsiveUtils.responsivePadding(context);
    
    // Determine responsive width constraint based on screen size
    final responsiveConstraints = constraints ?? ResponsiveUtils.responsiveWidthConstraint(context);
    
    return Container(
      padding: responsivePadding,
      margin: margin,
      color: color,
      width: width,
      height: height,
      constraints: responsiveConstraints,
      alignment: alignment,
      decoration: decoration,
      child: child,
    );
  }
}

/// A responsive centered container that adapts to different screen sizes
///
/// This widget provides a centered container with responsive width and
/// padding based on the screen size.
class ResponsiveCenteredContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? width;
  final double? height;
  final BoxConstraints? constraints;
  final BoxDecoration? decoration;

  const ResponsiveCenteredContainer({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.width,
    this.height,
    this.constraints,
    this.decoration,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine responsive padding based on screen size
    final responsivePadding = padding ?? ResponsiveUtils.responsivePadding(context);
    
    // Determine responsive width constraint based on screen size
    final responsiveConstraints = constraints ?? ResponsiveUtils.responsiveWidthConstraint(context);
    
    return Center(
      child: Container(
        padding: responsivePadding,
        margin: margin,
        color: color,
        width: width ?? ResponsiveUtils.responsiveContainerWidth(context),
        height: height,
        constraints: responsiveConstraints,
        decoration: decoration,
        child: child,
      ),
    );
  }
}

/// A responsive padding widget that adapts to different screen sizes
///
/// This widget provides padding that adjusts based on the screen size.
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;

  const ResponsivePadding({
    Key? key,
    required this.child,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine responsive padding based on screen size
    final responsivePadding = padding ?? ResponsiveUtils.responsivePadding(context);
    
    return Padding(
      padding: responsivePadding,
      child: child,
    );
  }
}

/// A responsive grid view that adapts to different screen sizes
///
/// This widget provides a grid view with a responsive number of columns
/// based on the screen size.
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final double? childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final int? crossAxisCount;

  const ResponsiveGridView({
    Key? key,
    required this.children,
    this.padding,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 10.0,
    this.mainAxisSpacing = 10.0,
    this.crossAxisCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine responsive padding based on screen size
    final responsivePadding = padding ?? ResponsiveUtils.responsivePadding(context);
    
    // Determine responsive grid column count based on screen size
    final responsiveCrossAxisCount = crossAxisCount ?? ResponsiveUtils.responsiveGridCount(context);
    
    return GridView.builder(
      padding: responsivePadding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: responsiveCrossAxisCount,
        childAspectRatio: childAspectRatio!,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
    );
  }
}
