import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/share_card_service.dart';
import '../utils/app_colors.dart';
import '../widgets/primary_button.dart';

class ShareCardDialog extends StatefulWidget {
  final String title;
  final String content;
  final String? subtitle;
  final String? qrCodeData;
  final Color? accentColor;

  const ShareCardDialog({
    super.key,
    required this.title,
    required this.content,
    this.subtitle,
    this.qrCodeData,
    this.accentColor,
  });

  @override
  State<ShareCardDialog> createState() => _ShareCardDialogState();
}

class _ShareCardDialogState extends State<ShareCardDialog> {
  bool _isGenerating = false;
  String? _generatedCardPath;

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width > 600;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: isLargeScreen ? 500 : double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.tealSurge,
                    AppColors.mintGlow,
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.share,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Create Share Card',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Preview section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.backgroundLight,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.tealSurge.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.preview,
                                color: AppColors.tealSurge,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Preview',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.midnightNavy,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Title
                          Text(
                            'Title: ${widget.title}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: AppColors.midnightNavy,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Content preview
                          Text(
                            'Content: ${widget.content.length > 100 ? '${widget.content.substring(0, 100)}...' : widget.content}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.slateGrey,
                            ),
                          ),

                          if (widget.subtitle != null) ...[
                            const SizedBox(height: 8),
                            Text(
                              'Subtitle: ${widget.subtitle}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.slateGrey,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Features info
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.coralPop.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.auto_awesome,
                                color: AppColors.coralPop,
                                size: 18,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Share Card Features',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.midnightNavy,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            '• 1080×1080 PNG optimized for social media\n'
                            '• Beautiful gradient frame with brand colors\n'
                            '• QR code for easy app discovery\n'
                            '• "Made with Medroid" branding\n'
                            '• File size optimized under 200KB',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.slateGrey,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _isGenerating
                                ? null
                                : () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              side:
                                  const BorderSide(color: AppColors.slateGrey),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(
                                color: AppColors.slateGrey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: PrimaryButton(
                            onPressed: _isGenerating ? null : _generateAndShare,
                            height: 50,
                            child: Text(_isGenerating
                                ? 'Generating...'
                                : 'Generate & Share'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateAndShare() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      // Provide haptic feedback
      HapticFeedback.lightImpact();

      final filePath = await ShareCardService.generateShareCard(
        title: widget.title,
        content: widget.content,
        subtitle: widget.subtitle,
        qrCodeData: widget.qrCodeData,
        accentColor: widget.accentColor,
      );

      if (filePath != null) {
        _generatedCardPath = filePath;

        // Share the card
        await ShareCardService.shareCard(
          filePath,
          text: 'Check out this content from Medroid Health App! 🏥✨',
        );

        if (mounted) {
          Navigator.of(context).pop();

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Share card generated successfully!'),
                ],
              ),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        throw Exception('Failed to generate share card');
      }
    } catch (e) {
      debugPrint('Error generating share card: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Failed to generate share card: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  @override
  void dispose() {
    // Clean up generated file if it exists
    if (_generatedCardPath != null) {
      try {
        final file = File(_generatedCardPath!);
        if (file.existsSync()) {
          file.deleteSync();
        }
      } catch (e) {
        debugPrint('Error cleaning up share card file: $e');
      }
    }
    super.dispose();
  }
}

/// Helper function to show the share card dialog
Future<void> showShareCardDialog(
  BuildContext context, {
  required String title,
  required String content,
  String? subtitle,
  String? qrCodeData,
  Color? accentColor,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => ShareCardDialog(
      title: title,
      content: content,
      subtitle: subtitle,
      qrCodeData: qrCodeData,
      accentColor: accentColor,
    ),
  );
}
