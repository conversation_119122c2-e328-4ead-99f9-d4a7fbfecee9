import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../widgets/share_card_dialog.dart';
import '../widgets/primary_button.dart';

/// Test widget to demonstrate share card functionality
class ShareCardTestWidget extends StatelessWidget {
  const ShareCardTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Share Card Test'),
        backgroundColor: AppColors.tealSurge,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: AppColors.backgroundLight,
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Column(
                  children: [
                    Icon(
                      Icons.share,
                      size: 48,
                      color: AppColors.coralPop,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Share Card Generator',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.midnightNavy,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Test the share card functionality with different content types',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.slateGrey,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Test buttons
              Expanded(
                child: ListView(
                  children: [
                    _buildTestCard(
                      context,
                      title: 'Health Milestone',
                      description: 'Test sharing a health milestone achievement',
                      icon: Icons.emoji_events,
                      onTap: () => _testMilestoneShare(context),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTestCard(
                      context,
                      title: 'Health Insight',
                      description: 'Test sharing a health tip or insight',
                      icon: Icons.lightbulb,
                      onTap: () => _testInsightShare(context),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTestCard(
                      context,
                      title: 'Chat Conversation',
                      description: 'Test sharing a chat conversation summary',
                      icon: Icons.chat,
                      onTap: () => _testChatShare(context),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTestCard(
                      context,
                      title: 'Custom Content',
                      description: 'Test with custom title and content',
                      icon: Icons.edit,
                      onTap: () => _testCustomShare(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.tealSurge.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.tealSurge,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppColors.midnightNavy,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.slateGrey,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.slateGrey,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _testMilestoneShare(BuildContext context) async {
    await showShareCardDialog(
      context,
      title: 'Health Milestone',
      content: '🎉 Completed 30 days of daily meditation! Feeling more focused and peaceful than ever.',
      subtitle: 'Mindfulness Journey',
      qrCodeData: 'https://medroid.ai/milestones',
      accentColor: AppColors.coralPop,
    );
  }

  Future<void> _testInsightShare(BuildContext context) async {
    await showShareCardDialog(
      context,
      title: 'Health Insight',
      content: 'Did you know? Drinking water first thing in the morning helps kickstart your metabolism and improves brain function throughout the day.',
      subtitle: 'Daily Health Tip',
      qrCodeData: 'https://medroid.ai/tips',
      accentColor: AppColors.tealSurge,
    );
  }

  Future<void> _testChatShare(BuildContext context) async {
    await showShareCardDialog(
      context,
      title: 'AI Health Consultation',
      content: 'Just had an amazing conversation with Medroid AI about managing stress and improving sleep quality. Got personalized recommendations!',
      subtitle: 'Wellness Chat Summary',
      qrCodeData: 'https://medroid.ai/chat',
      accentColor: AppColors.mintGlow,
    );
  }

  Future<void> _testCustomShare(BuildContext context) async {
    await showShareCardDialog(
      context,
      title: 'My Wellness Journey',
      content: 'Every small step towards better health counts. Today I chose the stairs, drank more water, and took time to breathe deeply.',
      subtitle: 'Personal Reflection',
      qrCodeData: 'https://medroid.ai',
      accentColor: AppColors.coralPop,
    );
  }
}
