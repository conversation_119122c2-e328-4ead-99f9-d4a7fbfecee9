import 'package:flutter/material.dart';
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/platform_aware_network_image_export.dart';
import 'package:medroid_app/widgets/share_card_dialog.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class SocialPostCard extends StatelessWidget {
  final SocialPost post;
  final VoidCallback onLike;
  final VoidCallback onSave;
  final VoidCallback? onTap;

  const SocialPostCard({
    Key? key,
    required this.post,
    required this.onLike,
    required this.onSave,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isLargeScreen = isDesktop || isTablet;

    // Adjust margins based on screen size
    final horizontalMargin = isLargeScreen ? 24.0 : 16.0;
    final verticalMargin = isLargeScreen ? 12.0 : 8.0;

    // For desktop, we'll use a different layout with image on the side
    if (isLargeScreen &&
        (post.contentType == 'image' || post.contentType == 'video')) {
      return Card(
        margin: EdgeInsets.symmetric(
            horizontal: horizontalMargin, vertical: verticalMargin),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left side: Content and actions
                Expanded(
                  flex: 5,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(context),
                      const SizedBox(height: 12),
                      _buildCaption(context),
                      const SizedBox(height: 16),
                      _buildTopics(context),
                      const SizedBox(height: 16),
                      _buildActions(context),
                    ],
                  ),
                ),

                // Right side: Media
                Expanded(
                  flex: 7,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: _buildMedia(context),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Mobile layout (or desktop without media)
    return Card(
      margin: EdgeInsets.symmetric(
          horizontal: horizontalMargin, vertical: verticalMargin),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            if (post.contentType == 'image' || post.contentType == 'video')
              _buildMedia(context),
            _buildCaption(context),
            _buildTopics(context),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // User avatar or initials
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: post.userAvatar == null
                  ? post.sourceIconColor.withAlpha(25)
                  : null,
              borderRadius: BorderRadius.circular(18),
            ),
            child: post.userAvatar != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(18),
                    child: PlatformAwareNetworkImage(
                      imageUrl: post.userAvatar!,
                      width: 36,
                      height: 36,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        // Show initials if avatar fails to load
                        return _buildUserInitials();
                      },
                    ),
                  )
                : _buildUserInitials(),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  post.userName ??
                      (post.source == 'internal'
                          ? 'You'
                          : _capitalize(post.source)),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  post.formattedDate,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build user initials avatar
  Widget _buildUserInitials() {
    // Get initials from username or use first letter of source
    String initials = '';
    if (post.userName != null && post.userName!.isNotEmpty) {
      final nameParts = post.userName!.split(' ');
      if (nameParts.length > 1) {
        // Get first letter of first and last name
        initials = nameParts.first[0] + nameParts.last[0];
      } else {
        // Just get first letter if only one name
        initials = nameParts.first[0];
      }
    } else {
      // Use first letter of source if no username
      initials = post.source.isNotEmpty ? post.source[0].toUpperCase() : 'M';
    }

    return Center(
      child: Text(
        initials.toUpperCase(),
        style: TextStyle(
          color: post.sourceIconColor,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  // Helper method for String capitalization
  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  Widget _buildMedia(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isLargeScreen = isDesktop || isTablet;

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: AspectRatio(
        aspectRatio: 4 / 3, // Instagram-style 4:3 aspect ratio
        child: post.contentType == 'video'
            ? _buildVideoPreview(context)
            : PlatformAwareNetworkImage(
                imageUrl: post.mediaUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[50],
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                errorListener: (exception) {
                  debugPrint(
                      'Image error in card: $exception for URL: ${post.mediaUrl}');
                },
                errorWidget: (context, url, error) {
                  debugPrint(
                      'Error loading image in card: $error for URL: $url');
                  // Show error message - no fallback images
                  return Container(
                    color: Colors.grey[300],
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, size: 48, color: Colors.grey[500]),
                          const SizedBox(height: 8),
                          const Text(
                            'Image could not be loaded',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildVideoPreview(BuildContext context) {
    return GestureDetector(
      onTap: () => _launchUrl(post.mediaUrl),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: double.infinity,
            color: Colors.black,
            child: const Center(
              child: Text(
                'Video content',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(204),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.play_arrow,
              size: 40,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaption(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isLargeScreen = isDesktop || isTablet;

    // Adjust font size and max lines based on screen size
    final fontSize = isLargeScreen ? 15.0 : 14.0;
    final maxLines = isLargeScreen ? 8 : 5;

    return Padding(
      padding: EdgeInsets.all(isLargeScreen ? 16.0 : 12.0),
      child: Text(
        post.caption,
        style: TextStyle(
          fontSize: fontSize,
          height: 1.4,
        ),
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildTopics(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isLargeScreen = isDesktop || isTablet;

    // Adjust spacing and font size based on screen size
    final horizontalPadding = isLargeScreen ? 16.0 : 12.0;
    final chipSpacing = isLargeScreen ? 8.0 : 6.0;
    final fontSize = isLargeScreen ? 12.0 : 10.0;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: Wrap(
        spacing: chipSpacing,
        runSpacing: chipSpacing,
        children: post.healthTopics.map((topic) {
          return Chip(
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            label: Text(
              topic,
              style: TextStyle(fontSize: fontSize),
            ),
            padding: EdgeInsets.zero,
            labelPadding: EdgeInsets.symmetric(
              horizontal: isLargeScreen ? 10.0 : 8.0,
              vertical: isLargeScreen ? 2.0 : 0.0,
            ),
            backgroundColor: AppTheme.primaryColor.withAlpha(25),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isLargeScreen = isDesktop || isTablet;

    return Padding(
      padding: EdgeInsets.all(isLargeScreen ? 16.0 : 12.0),
      child: Row(
        mainAxisAlignment: isLargeScreen
            ? MainAxisAlignment.start
            : MainAxisAlignment.spaceAround,
        children: [
          _buildActionButton(
            context,
            icon: post.isLiked ? Icons.favorite : Icons.favorite_border,
            label: '${post.likeCount}',
            color: post.isLiked ? Colors.red : null,
            onTap: onLike,
            isLargeScreen: isLargeScreen,
          ),
          SizedBox(width: isLargeScreen ? 24.0 : 8.0),
          _buildActionButton(
            context,
            icon: Icons.share,
            label: '${post.shareCount}',
            onTap: () => _sharePost(context),
            isLargeScreen: isLargeScreen,
          ),
          SizedBox(width: isLargeScreen ? 24.0 : 8.0),
          _buildActionButton(
            context,
            icon: post.isSaved ? Icons.bookmark : Icons.bookmark_border,
            label: 'Save',
            color: post.isSaved ? AppTheme.primaryColor : null,
            onTap: onSave,
            isLargeScreen: isLargeScreen,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    Color? color,
    required VoidCallback onTap,
    bool isLargeScreen = false,
  }) {
    // Adjust sizes based on screen size
    final iconSize = isLargeScreen ? 22.0 : 20.0;
    final fontSize = isLargeScreen ? 14.0 : 12.0;
    final spacing = isLargeScreen ? 6.0 : 4.0;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: isLargeScreen ? 10.0 : 8.0,
          horizontal: isLargeScreen ? 16.0 : 12.0,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: iconSize,
              color: color,
            ),
            SizedBox(width: spacing),
            Text(
              label,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: isLargeScreen ? FontWeight.w500 : FontWeight.normal,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sharePost(BuildContext context) async {
    try {
      // Prepare content for the share card
      String title = 'Health Insight';
      String content = post.caption;
      String? subtitle = post.userName != null ? 'by ${post.userName}' : null;

      // Show the share card dialog
      await showShareCardDialog(
        context,
        title: title,
        content: content,
        subtitle: subtitle,
        qrCodeData: 'https://medroid.ai/feed',
        accentColor: AppColors.coralPop,
      );
    } catch (e) {
      debugPrint('Error sharing post: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing post: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      await launchUrl(Uri.parse(url));
    } catch (e) {
      debugPrint('Could not launch URL: $e');
    }
  }
}

extension StringExtension on String {
  String capitalize() {
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}
