import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:medroid_app/utils/app_colors.dart';

class SSOButton extends StatelessWidget {
  final String provider;
  final VoidCallback onPressed;
  final bool isLoading;
  final double? width;
  final double? height;

  const SSOButton({
    Key? key,
    required this.provider,
    required this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      width: width,
      height: height ?? 58,
      decoration: BoxDecoration(
        gradient: _getButtonGradient(isDarkMode),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getBorderColor(isDarkMode),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: _getShadowColor(isDarkMode),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: isDarkMode ? 0.05 : 0.8),
            blurRadius: 1,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: isLoading ? null : onPressed,
          splashColor: _getSplashColor(),
          highlightColor: _getHighlightColor(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  SizedBox(
                    width: 22,
                    height: 22,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _getTextColor(isDarkMode),
                      ),
                    ),
                  )
                else ...[
                  _buildProviderIcon(),
                  const SizedBox(width: 14),
                  Expanded(
                    child: Text(
                      'Continue with $provider',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _getTextColor(isDarkMode),
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  LinearGradient _getButtonGradient(bool isDarkMode) {
    if (provider.toLowerCase() == 'google') {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: isDarkMode
            ? [
                const Color(0xFF2A2A2A),
                const Color(0xFF1E1E1E),
              ]
            : [
                Colors.white,
                const Color(0xFFFAFAFA),
              ],
      );
    } else if (provider.toLowerCase() == 'apple') {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: isDarkMode
            ? [
                const Color(0xFF2A2A2A),
                const Color(0xFF1A1A1A),
              ]
            : [
                const Color(0xFF1D1D1F),
                const Color(0xFF000000),
              ],
      );
    }
    return const LinearGradient(
      colors: [AppColors.surfaceLight, AppColors.surfaceLight],
    );
  }

  Color _getBorderColor(bool isDarkMode) {
    if (provider.toLowerCase() == 'google') {
      return isDarkMode ? Colors.grey[700]! : Colors.grey[200]!;
    } else if (provider.toLowerCase() == 'apple') {
      return isDarkMode ? Colors.grey[700]! : Colors.grey[800]!;
    }
    return Colors.grey[300]!;
  }

  Color _getShadowColor(bool isDarkMode) {
    if (provider.toLowerCase() == 'google') {
      return isDarkMode
          ? Colors.black.withValues(alpha: 0.3)
          : Colors.grey.withValues(alpha: 0.15);
    } else if (provider.toLowerCase() == 'apple') {
      return isDarkMode
          ? Colors.black.withValues(alpha: 0.4)
          : Colors.black.withValues(alpha: 0.2);
    }
    return Colors.black.withValues(alpha: 0.1);
  }

  Color _getTextColor(bool isDarkMode) {
    if (provider.toLowerCase() == 'apple' && !isDarkMode) {
      return Colors.white;
    }
    return isDarkMode ? Colors.white : Colors.black87;
  }

  Color _getSplashColor() {
    if (provider.toLowerCase() == 'google') {
      return Colors.blue.withValues(alpha: 0.1);
    } else if (provider.toLowerCase() == 'apple') {
      return Colors.grey.withValues(alpha: 0.1);
    }
    return AppColors.coralPop.withValues(alpha: 0.1);
  }

  Color _getHighlightColor() {
    if (provider.toLowerCase() == 'google') {
      return Colors.blue.withValues(alpha: 0.05);
    } else if (provider.toLowerCase() == 'apple') {
      return Colors.grey.withValues(alpha: 0.05);
    }
    return AppColors.coralPop.withValues(alpha: 0.05);
  }

  Widget _buildProviderIcon() {
    switch (provider.toLowerCase()) {
      case 'google':
        return Container(
          width: 26,
          height: 26,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Container(
              width: 18,
              height: 18,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
              ),
              child: CustomPaint(
                painter: GoogleLogoPainter(),
              ),
            ),
          ),
        );
      case 'apple':
        return Builder(
          builder: (context) => Container(
            width: 26,
            height: 26,
            decoration: BoxDecoration(
              color: provider.toLowerCase() == 'apple'
                  ? (Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black)
                  : Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Icon(
                Icons.apple,
                size: 18,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.black
                    : Colors.white,
              ),
            ),
          ),
        );
      default:
        return _buildFallbackIcon(
            AppColors.coralPop, provider[0].toUpperCase());
    }
  }

  Widget _buildFallbackIcon(Color color, String text) {
    return Container(
      width: 26,
      height: 26,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: text.isNotEmpty
          ? Center(
              child: Text(
                text,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            )
          : Icon(
              provider.toLowerCase() == 'apple'
                  ? Icons.apple
                  : Icons.account_circle,
              color: Colors.white,
              size: 18,
            ),
    );
  }
}

class SSOIconButton extends StatelessWidget {
  final String provider;
  final VoidCallback onPressed;
  final bool isLoading;
  final double size;

  const SSOIconButton({
    Key? key,
    required this.provider,
    required this.onPressed,
    this.isLoading = false,
    this.size = 56,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: _getButtonGradient(isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getBorderColor(isDarkMode),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: _getShadowColor(isDarkMode),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: isDarkMode ? 0.05 : 0.8),
            blurRadius: 1,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: isLoading ? null : onPressed,
          splashColor: _getSplashColor(),
          highlightColor: _getHighlightColor(),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: isLoading
                  ? SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getTextColor(isDarkMode),
                        ),
                      ),
                    )
                  : _buildProviderIcon(),
            ),
          ),
        ),
      ),
    );
  }

  LinearGradient _getButtonGradient(bool isDarkMode) {
    if (provider.toLowerCase() == 'google') {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: isDarkMode
            ? [
                const Color(0xFF2A2A2A),
                const Color(0xFF1E1E1E),
              ]
            : [
                Colors.white,
                const Color(0xFFFAFAFA),
              ],
      );
    } else if (provider.toLowerCase() == 'apple') {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: isDarkMode
            ? [
                const Color(0xFF2A2A2A),
                const Color(0xFF1A1A1A),
              ]
            : [
                const Color(0xFF1D1D1F),
                const Color(0xFF000000),
              ],
      );
    }
    return const LinearGradient(
      colors: [AppColors.surfaceLight, AppColors.surfaceLight],
    );
  }

  Color _getBorderColor(bool isDarkMode) {
    if (provider.toLowerCase() == 'google') {
      return isDarkMode ? Colors.grey[700]! : Colors.grey[200]!;
    } else if (provider.toLowerCase() == 'apple') {
      return isDarkMode ? Colors.grey[700]! : Colors.grey[800]!;
    }
    return Colors.grey[300]!;
  }

  Color _getShadowColor(bool isDarkMode) {
    if (provider.toLowerCase() == 'google') {
      return isDarkMode
          ? Colors.black.withValues(alpha: 0.3)
          : Colors.grey.withValues(alpha: 0.15);
    } else if (provider.toLowerCase() == 'apple') {
      return isDarkMode
          ? Colors.black.withValues(alpha: 0.4)
          : Colors.black.withValues(alpha: 0.2);
    }
    return Colors.black.withValues(alpha: 0.1);
  }

  Color _getTextColor(bool isDarkMode) {
    if (provider.toLowerCase() == 'apple' && !isDarkMode) {
      return Colors.white;
    }
    return isDarkMode ? Colors.white : Colors.black87;
  }

  Color _getSplashColor() {
    if (provider.toLowerCase() == 'google') {
      return Colors.blue.withValues(alpha: 0.1);
    } else if (provider.toLowerCase() == 'apple') {
      return Colors.grey.withValues(alpha: 0.1);
    }
    return AppColors.coralPop.withValues(alpha: 0.1);
  }

  Color _getHighlightColor() {
    if (provider.toLowerCase() == 'google') {
      return Colors.blue.withValues(alpha: 0.05);
    } else if (provider.toLowerCase() == 'apple') {
      return Colors.grey.withValues(alpha: 0.05);
    }
    return AppColors.coralPop.withValues(alpha: 0.05);
  }

  Widget _buildProviderIcon() {
    return Builder(
      builder: (context) {
        switch (provider.toLowerCase()) {
          case 'google':
            return Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Center(
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: CustomPaint(
                    painter: GoogleLogoPainter(),
                  ),
                ),
              ),
            );
          case 'apple':
            return Builder(
              builder: (context) => Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: provider.toLowerCase() == 'apple'
                      ? (Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    Icons.apple,
                    size: 16,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black
                        : Colors.white,
                  ),
                ),
              ),
            );
          default:
            return _buildFallbackIcon(
                AppColors.coralPop, provider[0].toUpperCase());
        }
      },
    );
  }

  Widget _buildFallbackIcon(Color color, String text) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: text.isNotEmpty
          ? Center(
              child: Text(
                text,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            )
          : Icon(
              provider.toLowerCase() == 'apple'
                  ? Icons.apple
                  : Icons.account_circle,
              color: Colors.white,
              size: 16,
            ),
    );
  }
}

class SSOIconButtonGroup extends StatelessWidget {
  final VoidCallback onGooglePressed;
  final VoidCallback onApplePressed;
  final bool isGoogleLoading;
  final bool isAppleLoading;
  final double iconSize;

  const SSOIconButtonGroup({
    Key? key,
    required this.onGooglePressed,
    required this.onApplePressed,
    this.isGoogleLoading = false,
    this.isAppleLoading = false,
    this.iconSize = 56,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Google Sign-In (available on all platforms)
        SSOIconButton(
          provider: 'Google',
          onPressed: onGooglePressed,
          isLoading: isGoogleLoading,
          size: iconSize,
        ),

        const SizedBox(width: 20),

        // Apple Sign-In (show on all platforms for demo, but handle in service)
        SSOIconButton(
          provider: 'Apple',
          onPressed: onApplePressed,
          isLoading: isAppleLoading,
          size: iconSize,
        ),
      ],
    );
  }
}

class SSODivider extends StatelessWidget {
  final String text;

  const SSODivider({
    Key? key,
    this.text = 'or',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                  ],
                ),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.surfaceDark.withValues(alpha: 0.8)
                  : AppColors.surfaceLight.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                width: 1,
              ),
            ),
            child: Text(
              text,
              style: GoogleFonts.inter(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: isDarkMode
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for Google logo
class GoogleLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Google "G" logo colors
    const blueColor = Color(0xFF4285F4);
    const redColor = Color(0xFFEA4335);
    const yellowColor = Color(0xFFFBBC05);
    const greenColor = Color(0xFF34A853);

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Draw the blue arc (top right)
    paint.color = blueColor;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -1.57, // -90 degrees
      1.57, // 90 degrees
      true,
      paint,
    );

    // Draw the red arc (top left)
    paint.color = redColor;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -3.14, // -180 degrees
      1.57, // 90 degrees
      true,
      paint,
    );

    // Draw the yellow arc (bottom left)
    paint.color = yellowColor;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -1.57, // -90 degrees
      -1.57, // -90 degrees
      true,
      paint,
    );

    // Draw the green arc (bottom right)
    paint.color = greenColor;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      0, // 0 degrees
      1.57, // 90 degrees
      true,
      paint,
    );

    // Draw white center circle to create the "G" shape
    paint.color = Colors.white;
    canvas.drawCircle(center, radius * 0.5, paint);

    // Draw the horizontal line to complete the "G"
    paint.color = blueColor;
    final lineRect = Rect.fromLTWH(
      center.dx,
      center.dy - radius * 0.15,
      radius * 0.6,
      radius * 0.3,
    );
    canvas.drawRect(lineRect, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
