import 'package:flutter/material.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';

class StoryItem extends StatelessWidget {
  final String imageUrl;
  final String username;
  final bool isViewed;
  final bool isCurrentUser;
  final VoidCallback onTap;

  const StoryItem({
    Key? key,
    required this.imageUrl,
    required this.username,
    this.isViewed = false,
    this.isCurrentUser = false,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        margin: const EdgeInsets.symmetric(horizontal: 6),
        child: Column(
          children: [
            // Story container with rectangular shape and curved corners
            Stack(
              alignment: Alignment.center,
              children: [
                // Outer border
                Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16), // Curved corners
                    border: Border.all(
                      color: isViewed
                          ? Colors.grey.withValues(alpha: 0.3)
                          : Colors.white,
                      width: 2,
                    ),
                  ),
                ),
                // Image or placeholder
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14), // Curved corners
                    color: Colors.white,
                    border: Border.all(
                      color: isCurrentUser ? Colors.teal : Colors.transparent,
                      width: isCurrentUser ? 2 : 0,
                    ),
                  ),
                  child: imageUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(14),
                          child: PlatformAwareNetworkImage(
                            imageUrl: imageUrl,
                            fit: BoxFit.cover,
                            width: 64,
                            height: 64,
                            errorWidget: (context, url, error) {
                              return Container(
                                width: 64,
                                height: 64,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  color: Colors.white,
                                ),
                                child: Center(
                                  child: Text(
                                    username.isNotEmpty
                                        ? username.substring(0, 1).toUpperCase()
                                        : '?',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 20,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        )
                      : Center(
                          child: isCurrentUser
                              ? Container(
                                  decoration: BoxDecoration(
                                    color: Colors.teal.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                  child: const Icon(
                                    Icons.add,
                                    color: Colors.teal,
                                    size: 28,
                                  ),
                                )
                              : Text(
                                  username.isNotEmpty
                                      ? username.substring(0, 1).toUpperCase()
                                      : '?',
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                  ),
                                ),
                        ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Username text
            Text(
              isCurrentUser ? 'Your Story' : username,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isCurrentUser ? FontWeight.w600 : FontWeight.normal,
                color: Colors.black87,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
