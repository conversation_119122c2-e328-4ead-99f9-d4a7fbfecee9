import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:medroid_app/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StripeTestWidget extends StatefulWidget {
  const StripeTestWidget({super.key});

  @override
  State<StripeTestWidget> createState() => _StripeTestWidgetState();
}

class _StripeTestWidgetState extends State<StripeTestWidget> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvcController = TextEditingController();
  bool _isLoading = false;
  String? _result;

  @override
  void dispose() {
    _nameController.dispose();
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvcController.dispose();
    super.dispose();
  }

  Future<void> _testStripeConnection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _result = null;
    });

    try {
      final expiry = _expiryController.text;
      final expiryParts = expiry.split('/');
      final expiryMonth = int.parse(expiryParts[0]);
      final yearPart = expiryParts[1];
      final expiryYear =
          yearPart.length == 2 ? int.parse('20$yearPart') : int.parse(yearPart);

      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      final requestData = {
        'card_number': _cardNumberController.text.replaceAll(' ', ''),
        'exp_month': expiryMonth,
        'exp_year': expiryYear,
        'cvc': _cvcController.text,
        'cardholder_name': _nameController.text.trim(),
        'test_only': true, // This tells the backend it's a test request
        // Add dummy values to pass validation (these won't be used in test mode)
        'payment_intent_id': 'pi_test_dummy_for_validation',
        'appointment_id': '1', // Use a dummy appointment ID
      };

      debugPrint('Testing Stripe with: $requestData');

      final response = await http.post(
        Uri.parse('${Constants.baseUrl}/payments/process-web-payment'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestData),
      );

      debugPrint('Test response status: ${response.statusCode}');
      debugPrint('Test response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        setState(() {
          _result = '✅ SUCCESS!\n'
              'Card Brand: ${responseData['card_brand']}\n'
              'Last 4: ${responseData['card_last4']}\n'
              'Stripe Mode: ${responseData['stripe_mode']}\n'
              'Payment Method ID: ${responseData['payment_method_id']}';
        });
      } else {
        String errorMessage = responseData['message'] ?? 'Test failed';
        if (responseData['stripe_error'] != null) {
          final stripeError = responseData['stripe_error'];
          errorMessage += '\n\nStripe Error Details:\n'
              'Code: ${stripeError['code']}\n'
              'Type: ${stripeError['type']}\n'
              'Param: ${stripeError['param']}';
          if (stripeError['decline_code'] != null) {
            errorMessage += '\nDecline Code: ${stripeError['decline_code']}';
          }
        }
        setState(() {
          _result = '❌ FAILED!\n$errorMessage';
        });
      }
    } catch (e) {
      setState(() {
        _result = '❌ ERROR!\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Stripe Connection Test',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Cardholder Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                    value?.isEmpty == true ? 'Required' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _cardNumberController,
                decoration: const InputDecoration(
                  labelText: 'Card Number',
                  border: OutlineInputBorder(),
                  hintText: '4242 4242 4242 4242',
                ),
                validator: (value) =>
                    value?.isEmpty == true ? 'Required' : null,
                onChanged: (value) {
                  final formatted = value
                      .replaceAll(' ', '')
                      .replaceAllMapped(
                        RegExp(r'.{4}'),
                        (match) => '${match.group(0) ?? ''} ',
                      )
                      .trim();
                  if (formatted != value) {
                    _cardNumberController.value = TextEditingValue(
                      text: formatted,
                      selection:
                          TextSelection.collapsed(offset: formatted.length),
                    );
                  }
                },
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _expiryController,
                      decoration: const InputDecoration(
                        labelText: 'MM/YY',
                        border: OutlineInputBorder(),
                        hintText: '12/25',
                      ),
                      validator: (value) =>
                          value?.isEmpty == true ? 'Required' : null,
                      onChanged: (value) {
                        if (value.length == 2 && !value.contains('/')) {
                          _expiryController.value = TextEditingValue(
                            text: '$value/',
                            selection: const TextSelection.collapsed(offset: 3),
                          );
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: _cvcController,
                      decoration: const InputDecoration(
                        labelText: 'CVC',
                        border: OutlineInputBorder(),
                        hintText: '123',
                      ),
                      validator: (value) =>
                          value?.isEmpty == true ? 'Required' : null,
                      maxLength: 4,
                      buildCounter: (context,
                              {required currentLength,
                              required isFocused,
                              maxLength}) =>
                          null,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _testStripeConnection,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Test Stripe Connection'),
                ),
              ),
              if (_result != null) ...[
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _result!.startsWith('✅')
                        ? Colors.green.shade50
                        : Colors.red.shade50,
                    border: Border.all(
                      color:
                          _result!.startsWith('✅') ? Colors.green : Colors.red,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _result!,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                      color: _result!.startsWith('✅')
                          ? Colors.green.shade800
                          : Colors.red.shade800,
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _cardNumberController.text = '4242 4242 4242 4242';
                        _expiryController.text = '12/25';
                        _cvcController.text = '123';
                        _nameController.text = 'Test User';
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade100,
                        foregroundColor: Colors.blue.shade800,
                      ),
                      child: const Text('Use Test Card'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _cardNumberController.text = '5599 0805 4623 0861';
                        _expiryController.text = '10/26';
                        _cvcController.text = '418';
                        _nameController.text = 'Raja Mohan';
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange.shade100,
                        foregroundColor: Colors.orange.shade800,
                      ),
                      child: const Text('Use Your Card'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                'Test Cards:\n'
                '• 4242 4242 4242 4242 (Visa - should work)\n'
                '• 5555 5555 5555 4444 (Mastercard - should work)\n'
                '• Your card: 5599 0805 4623 0861 (let\'s see what happens)',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
