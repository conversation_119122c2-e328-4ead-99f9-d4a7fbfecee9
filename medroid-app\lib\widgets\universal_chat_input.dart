import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class UniversalChatInput extends StatefulWidget {
  final Function(String, {File? imageFile})? onSendMessage;
  final String hintText;
  final bool isEnabled;
  final String? conversationId;
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;
  final FocusNode? focusNode;

  const UniversalChatInput({
    Key? key,
    this.onSendMessage,
    this.hintText = 'Type your health question...',
    this.isEnabled = true,
    this.conversationId,
    this.onCreateNewChat,
    this.focusNode,
  }) : super(key: key);

  @override
  State<UniversalChatInput> createState() => _UniversalChatInputState();
}

class _UniversalChatInputState extends State<UniversalChatInput>
    with SingleTickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();
  late final FocusNode _focusNode;
  bool _isTyping = false;
  File? _selectedImage;
  late AnimationController _sendButtonAnimController;
  late Animation<double> _sendButtonScaleAnimation;
  bool _hasText = false;
  bool _isDropdownOpen = false;

  @override
  void initState() {
    super.initState();
    // Use the provided focusNode or create a new one
    _focusNode = widget.focusNode ?? FocusNode();

    _sendButtonAnimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _sendButtonScaleAnimation = Tween<double>(begin: 1.0, end: 0.85).animate(
      CurvedAnimation(
        parent: _sendButtonAnimController,
        curve: Curves.easeInOut,
      ),
    );

    _messageController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    final hasText = _messageController.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  @override
  void dispose() {
    _messageController.removeListener(_onTextChanged);
    _messageController.dispose();
    // Only dispose the focus node if we created it
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _sendButtonAnimController.dispose();
    super.dispose();
  }

  // Store web image data
  Uint8List? _webImageData;
  XFile? _pickedImage;

  void _toggleAttachmentOptions() {
    // Find the plus button's RenderBox
    final RenderBox? buttonBox = _findPlusButtonRenderBox();
    if (buttonBox == null) return;

    // Get the overlay
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    // Calculate position to show the dropdown touching the plus button
    final buttonPosition =
        buttonBox.localToGlobal(Offset.zero, ancestor: overlay);

    // Set the dropdown to appear directly above the button, touching it
    final RelativeRect position = RelativeRect.fromLTRB(
      buttonPosition.dx - 80, // Position to the left to make it wider
      buttonPosition.dy - 100, // Position it above the button, touching it
      buttonPosition.dx + 80, // Make it wider for better appearance
      buttonPosition.dy,
    );

    // Update the icon to show close icon
    setState(() {
      _isDropdownOpen = true;
    });

    // Show the dropdown menu with elegant styling
    showMenu<String>(
      context: context,
      position: position,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      color: Colors.white,
      // Add subtle animation
      clipBehavior: Clip.antiAlias,
      // Custom styling
      items: [
        // Upload a file
        PopupMenuItem<String>(
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          value: 'upload_file',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.attach_file_outlined,
                  color: Colors.grey.shade800,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Upload a file',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        // Take a screenshot
        PopupMenuItem<String>(
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          value: 'take_screenshot',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.camera_alt_outlined,
                  color: Colors.grey.shade800,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Take a screenshot',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    ).then((value) {
      // Reset the icon back to plus when dropdown closes
      setState(() {
        _isDropdownOpen = false;
      });

      if (value == null) return;

      // Handle menu item selection
      switch (value) {
        case 'upload_file':
          _pickImage(ImageSource.gallery);
          break;
        case 'take_screenshot':
          _pickImage(ImageSource.camera);
          break;
      }
    });
  }

  // Helper method to find the plus button's RenderBox
  RenderBox? _findPlusButtonRenderBox() {
    try {
      // Get the context's RenderBox which contains the button
      final RenderBox contextBox = context.findRenderObject() as RenderBox;

      // For more precise positioning, we'd use a GlobalKey
      // This is a simplified approach that works for this case
      return contextBox;
    } catch (e) {
      return null;
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: source,
        imageQuality: 80,
      );

      if (pickedImage != null && mounted) {
        _pickedImage = pickedImage;

        // Handle differently for web and mobile
        if (kIsWeb) {
          // For web, read the image as bytes
          final bytes = await pickedImage.readAsBytes();
          setState(() {
            _webImageData = bytes;
            // We still need _selectedImage for the API, but it won't be used for display on web
            _selectedImage = null;
          });
        } else {
          // For mobile, use File as before
          setState(() {
            _selectedImage = File(pickedImage.path);
            _webImageData = null;
          });
        }

        // Don't show the snackbar anymore, we'll display in the chat input directly
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Helper method to build image preview based on platform
  Widget _buildImagePreview(double width, double height) {
    if (kIsWeb && _webImageData != null) {
      return Image.memory(
        _webImageData!,
        width: width,
        height: height,
        fit: BoxFit.cover,
      );
    } else if (!kIsWeb && _selectedImage != null) {
      return Image.file(
        _selectedImage!,
        width: width,
        height: height,
        fit: BoxFit.cover,
      );
    } else {
      // Fallback if no image is available
      return Container(
        width: width,
        height: height,
        color: Colors.grey.shade300,
        child: Icon(Icons.image, color: Colors.grey.shade700),
      );
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty && _selectedImage == null && _webImageData == null) {
      return;
    }

    // Animate the send button
    _sendButtonAnimController.forward().then((_) {
      _sendButtonAnimController.reverse();
    });

    // Clear the input field
    _messageController.clear();

    // Store the image locally before clearing it from state
    File? imageToSend;

    // Handle web vs mobile differently
    if (kIsWeb && _webImageData != null && _pickedImage != null) {
      // For web, we need to create a temporary file from the image data
      // This is a workaround since we can't directly use File in web
      // The API will need to handle XFile or bytes for web
      imageToSend = null;

      // We'll pass the XFile directly to the API in web environment
      // The API service should handle this differently for web
    } else {
      // For mobile, use File as before
      imageToSend = _selectedImage;
    }

    // Store references before clearing state
    final webData = _webImageData;
    final pickedXFile = _pickedImage;

    // Clear the selected image
    if (_selectedImage != null || _webImageData != null) {
      setState(() {
        _selectedImage = null;
        _webImageData = null;
        _pickedImage = null;
      });
    }

    // Request focus back to the input field
    _focusNode.requestFocus();

    // If there's a custom handler, use it
    if (widget.onSendMessage != null) {
      if (kIsWeb && webData != null && pickedXFile != null) {
        // For web, we need to handle this differently
        // The API should be updated to accept XFile or bytes for web
        widget.onSendMessage!(message, imageFile: imageToSend);
      } else {
        widget.onSendMessage!(message, imageFile: imageToSend);
      }
      return;
    }

    // If we have a conversation ID, send the message to that conversation
    if (widget.conversationId != null) {
      setState(() {
        _isTyping = true;
      });

      try {
        final apiService = RepositoryProvider.of<ApiService>(context);

        if (imageToSend != null) {
          // Send message with image
          await apiService.sendChatMessageWithImage(
            widget.conversationId!,
            message,
            imageToSend,
          );
        } else {
          // Send text-only message
          await apiService.sendChatMessage(
            widget.conversationId!,
            message,
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error sending message: $e'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isTyping = false;
          });
        }
      }
    }
    // If we don't have a conversation ID but have a createNewChat callback, use it
    else if (widget.onCreateNewChat != null) {
      setState(() {
        _isTyping = true;
      });

      try {
        // Call the callback to create a new chat with the initial message and image
        widget.onCreateNewChat!(
          context,
          initialMessage: message,
          imageFile: imageToSend,
        );
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error creating new chat: $e'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isTyping = false;
          });
        }
      }
    } else {
      // No conversation ID and no callback - show an error
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot send message: No active conversation'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isEnabled = widget.isEnabled && !_isTyping;

    // Build the UI

    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // We've replaced the attachment options panel with a dropdown menu

              // Image preview area
              if (_selectedImage != null || _webImageData != null)
                Container(
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? const Color(0xFF2A2D31)
                        : const Color(0xFFF5F7FA),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isDarkMode
                          ? const Color(0xFF3A3D41)
                          : const Color(0xFFE5E7EB),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(8),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(20),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: _buildImagePreview(60, 60),
                        ),
                      ),
                      const SizedBox(width: 14),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Image attached',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Ready to send',
                              style: TextStyle(
                                fontSize: 12,
                                color: isDarkMode
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: () {
                            setState(() {
                              _selectedImage = null;
                              _webImageData = null;
                              _pickedImage = null;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? const Color(0xFF3A3D41)
                                  : Colors.grey.shade200,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.close_rounded,
                              size: 18,
                              color: isDarkMode
                                  ? Colors.grey.shade300
                                  : Colors.grey.shade700,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Main input container - Google-style search bar
              Center(
                child: Container(
                  width: ResponsiveUtils.isDesktop(context)
                      ? 900 // Increased width for desktop
                      : ResponsiveUtils.isTablet(context)
                          ? 700 // Width for tablet
                          : double.infinity, // Full width for mobile
                  margin: ResponsiveUtils.isDesktop(context) ||
                          ResponsiveUtils.isTablet(context)
                      ? const EdgeInsets.symmetric(horizontal: 16, vertical: 14)
                      : const EdgeInsets.symmetric(
                          horizontal:
                              8, // Small horizontal margin on mobile for padding
                          vertical: 10), // Reduced vertical margin on mobile
                  decoration: BoxDecoration(
                    color: Colors.white, // Keep chat input white as requested
                    borderRadius:
                        BorderRadius.circular(16), // Less rounded corners
                    border: Border.all(
                      color: Colors.grey.shade200, // Light grey border
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Text input field - no border, multi-line support
                      Container(
                        padding: ResponsiveUtils.isDesktop(context) ||
                                ResponsiveUtils.isTablet(context)
                            ? const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12)
                            : const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8), // Reduced padding on mobile
                        constraints: BoxConstraints(
                          // Set minimum height based on device type
                          minHeight: ResponsiveUtils.isDesktop(context) ||
                                  ResponsiveUtils.isTablet(context)
                              ? 80 // Original height for desktop/tablet
                              : 60, // Reduced height for mobile
                        ),
                        child: LayoutBuilder(builder: (context, constraints) {
                          // Determine lines based on device type
                          final isDesktop = ResponsiveUtils.isDesktop(context);
                          final isTablet = ResponsiveUtils.isTablet(context);
                          final maxLines = isDesktop
                              ? 8
                              : (isTablet ? 5 : 4); // Fewer max lines on mobile
                          final minLines = isDesktop || isTablet
                              ? 2
                              : 1; // Single line minimum on mobile

                          return KeyboardListener(
                            focusNode: FocusNode(),
                            onKeyEvent: (KeyEvent event) {
                              if (event is KeyDownEvent) {
                                // Handle Enter key without Shift modifier
                                // On desktop/web: Enter sends, Shift+Enter creates new line
                                // On mobile: This will be handled by onSubmitted
                                if (event.logicalKey ==
                                        LogicalKeyboardKey.enter &&
                                    !HardwareKeyboard.instance.isShiftPressed &&
                                    (ResponsiveUtils.isDesktop(context) ||
                                        kIsWeb)) {
                                  if (isEnabled &&
                                      (_hasText ||
                                          _selectedImage != null ||
                                          _webImageData != null)) {
                                    _sendMessage();
                                  }
                                }
                              }
                            },
                            child: TextField(
                                controller: _messageController,
                                focusNode: _focusNode,
                                decoration: InputDecoration(
                                  hintText: widget.hintText,
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  contentPadding: EdgeInsets.zero,
                                  hintStyle: TextStyle(
                                    color: Colors.grey.shade500,
                                    fontSize: 16,
                                  ),
                                  isDense: true,
                                  // Remove hover color
                                  fillColor: Colors.white,
                                  filled: true,
                                  hoverColor: Colors.transparent,
                                ),
                                textInputAction:
                                    ResponsiveUtils.isDesktop(context) || kIsWeb
                                        ? TextInputAction.newline
                                        : TextInputAction.send,
                                minLines: minLines,
                                maxLines: maxLines,
                                enabled: isEnabled,
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                                cursorColor: AppColors.coralPop,
                                cursorRadius: const Radius.circular(2),
                                keyboardType: TextInputType.multiline,
                                // Handle Enter key for sending message on mobile
                                onSubmitted: (text) {
                                  if (isEnabled &&
                                      (_hasText ||
                                          _selectedImage != null ||
                                          _webImageData != null)) {
                                    _sendMessage();
                                  }
                                }),
                          );
                        }),
                      ),

                      // Bottom row with controls
                      Container(
                        padding: const EdgeInsets.only(
                            left: 8,
                            right: 8,
                            bottom: 12), // Increased bottom padding
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Plus/Close button with animation
                            Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(24),
                                onTap:
                                    isEnabled ? _toggleAttachmentOptions : null,
                                child: SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: Center(
                                    child: AnimatedSwitcher(
                                      duration:
                                          const Duration(milliseconds: 200),
                                      transitionBuilder: (Widget child,
                                          Animation<double> animation) {
                                        return RotationTransition(
                                          turns: animation,
                                          child: ScaleTransition(
                                            scale: animation,
                                            child: child,
                                          ),
                                        );
                                      },
                                      child: _isDropdownOpen
                                          ? const Icon(
                                              Icons.close,
                                              key: ValueKey('close'),
                                              size: 24,
                                              color: Colors.grey,
                                            )
                                          : const Icon(
                                              Icons.add,
                                              key: ValueKey('add'),
                                              size: 24,
                                              color: Colors.grey,
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            // Tools button
                            Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(24),
                                onTap:
                                    null, // Placeholder for tools functionality
                                child: SizedBox(
                                  height: 40,
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.tune,
                                        size: 20,
                                        color: Colors.grey.shade700,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'Tools',
                                        style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            // Spacer
                            const Spacer(),

                            // Microphone button
                            Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(24),
                                onTap: null, // Placeholder for voice input
                                child: const SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: Center(
                                    child: Icon(
                                      Icons.mic_none_rounded,
                                      size: 22,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            // Small spacing between mic and send button
                            const SizedBox(width: 8),

                            // Send button (using theme color)
                            ScaleTransition(
                              scale: _sendButtonScaleAnimation,
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(24),
                                  onTap: isEnabled
                                      ? (_hasText ||
                                              _selectedImage != null ||
                                              _webImageData != null)
                                          ? _sendMessage
                                          : null
                                      : null,
                                  child: Container(
                                    width: 44,
                                    height: 44,
                                    decoration: BoxDecoration(
                                      color: (_hasText ||
                                              _selectedImage != null ||
                                              _webImageData != null)
                                          ? AppColors.coralPop
                                          : AppColors.slateGrey
                                              .withValues(alpha: 0.3),
                                      shape: BoxShape.circle,
                                    ),
                                    child: _isTyping
                                        ? const Center(
                                            child: SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                        Color>(Colors.white),
                                              ),
                                            ),
                                          )
                                        : const Center(
                                            child: Icon(
                                              Icons.send,
                                              size: 20,
                                              color: Colors.white,
                                            ),
                                          ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Disclaimer text
              Container(
                width: ResponsiveUtils.isDesktop(context)
                    ? 900 // Match input width for desktop
                    : ResponsiveUtils.isTablet(context)
                        ? 700 // Match input width for tablet
                        : double.infinity, // Full width for mobile
                margin: ResponsiveUtils.isDesktop(context) ||
                        ResponsiveUtils.isTablet(context)
                    ? const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
                    : const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                child: Text(
                  'Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our Terms of Service and Privacy Policy.',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.isDesktop(context)
                        ? 11
                        : ResponsiveUtils.isTablet(context)
                            ? 10
                            : 9,
                    color: Colors.grey.shade600,
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // We've removed the _buildAttachmentOption method as it's no longer needed
}
