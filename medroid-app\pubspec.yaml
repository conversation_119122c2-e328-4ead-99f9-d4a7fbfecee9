name: medroid_app
description: Healthcare Social Feed & AI Clinical Assistant App - Mobile Only (Android & iOS)

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and styling
  cupertino_icons: ^1.0.5
  cached_network_image: ^3.2.3
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0
  lottie: ^2.3.2
  google_fonts: ^4.0.4

  # State management
  flutter_bloc: ^8.1.2
  provider: ^6.0.5

  # Storage and preferences
  shared_preferences: ^2.1.1

  # Networking
  http: ^0.13.6
  dio: ^5.1.2

  # Utilities
  intl: ^0.18.1
  url_launcher: ^6.1.11
  image_picker: ^1.0.4
  path_provider: ^2.0.15
  share_plus: ^7.0.2
  connectivity_plus: ^4.0.1
  table_calendar: ^3.0.9
  flutter_markdown: ^0.6.15
  crypto: ^3.0.3
  flutter_dotenv: ^5.1.0
  qr_flutter: ^4.1.0

  # Location services
  geocoding: ^2.1.0
  geolocator: ^10.0.0
  flutter_typeahead: ^5.0.0
  flutter_keyboard_visibility: ^6.0.0

  # Media handling
  video_player: ^2.6.1
  chewie: ^1.5.0
  image: ^4.1.7

  # Video consultation
  # JitsiMeet and WebRTC dependencies removed

  # Agora SDK for video consultations
  agora_rtc_engine: ^6.5.2
  permission_handler: ^11.3.0
  flutter_inappwebview: ^6.0.0

  # Audio recording
  record: ^5.0.1
  audioplayers: ^5.1.0

  # Payment processing
  flutter_stripe: ^9.5.0+1

  # Firebase
  firebase_core: ^2.27.1
  firebase_messaging: ^14.7.20
  flutter_slidable: ^4.0.0
  # flutter_local_notifications: ^16.3.2 # Commented out due to compatibility issues

  # SSO Authentication
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1

# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/subtle_pattern.png
    - assets/images/headache.png
    - assets/images/sleep.png
    - assets/images/weight.png
    - assets/images/skin.png
    - assets/images/appointment.png
    - assets/images/mental.png
    - assets/icons/

  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
    - family: NotoSansCJK
      fonts:
        - asset: assets/fonts/NotoSansCJKjp-Regular.otf
          weight: 400
    - family: NotoSansSymbols
      fonts:
        - asset: assets/fonts/NotoSansSymbols-Regular.ttf
          weight: 400
