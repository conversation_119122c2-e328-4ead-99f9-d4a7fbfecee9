import 'package:http/http.dart' as http;
import 'dart:convert';

// A simple script to test the API endpoints
void main() async {
  const baseUrl = 'https://backend.medroid.ai/api';

  print('Testing API connectivity...');

  try {
    // Test basic connectivity
    final response = await http.get(Uri.parse('$baseUrl/'));

    if (response.statusCode == 200) {
      print('Successfully connected to API');
      print('Response: ${response.body}');
    } else {
      print('Failed to connect to API: ${response.statusCode}');
      print('Response: ${response.body}');
    }

    // Try a simple post request
    final chatResponse = await http.post(Uri.parse('$baseUrl/chat/start'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'user_id': 'test_user'}));

    print('Chat start response: ${chatResponse.statusCode}');
    print('Response body: ${chatResponse.body}');
  } catch (e) {
    print('Error testing API: $e');
  }
}
