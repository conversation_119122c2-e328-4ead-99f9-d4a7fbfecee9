import 'dart:convert';
import 'package:http/http.dart' as http;

// A simple script to test authentication directly
void main() async {
  print('Testing Direct Authentication API');
  print('================================');

  const baseUrl = 'https://backend.medroid.ai/api';

  // Test registration
  await testRegistration(baseUrl);

  // Test login
  await testLogin(baseUrl);
}

Future<void> testRegistration(String baseUrl) async {
  print('\nTesting Registration...');

  final userData = {
    'name': 'Test User',
    'email': '<EMAIL>',
    'password': 'password123',
    'password_confirmation': 'password123',
    'role': 'patient',
  };

  try {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(userData),
    );

    print('Status code: ${response.statusCode}');
    print('Response: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      print('✅ Registration successful');
    } else {
      print('❌ Registration failed');
    }
  } catch (e) {
    print('❌ Registration error: $e');
  }
}

Future<void> testLogin(String baseUrl) async {
  print('\nTesting Login...');

  final loginData = {
    'email': '<EMAIL>',
    'password': 'password123',
  };

  try {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(loginData),
    );

    print('Status code: ${response.statusCode}');
    print('Response: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      print('✅ Login successful');
    } else {
      print('❌ Login failed');
    }
  } catch (e) {
    print('❌ Login error: $e');
  }
}
