import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  runApp(const ShareCardTestApp());
}

class ShareCardTestApp extends StatelessWidget {
  const ShareCardTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Share Card Test',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        useMaterial3: true,
      ),
      home: const ShareCardTestScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class ShareCardTestScreen extends StatelessWidget {
  const ShareCardTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Share Card Test'),
        backgroundColor: const Color(0xFF0D9488), // Teal Surge
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF8F9FA), // Background Light
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Column(
                  children: [
                    Icon(
                      Icons.share,
                      size: 48,
                      color: Color(0xFF0D9488), // Teal Surge
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Share Card Generator',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1E293B), // Midnight Navy
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Test the share card functionality with different content types',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF64748B), // Slate Grey
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Test buttons
              Expanded(
                child: ListView(
                  children: [
                    _buildTestCard(
                      context,
                      title: 'Health Milestone',
                      description: 'Test sharing a health milestone achievement',
                      icon: Icons.emoji_events,
                      onTap: () => _showShareCardPreview(
                        context,
                        'Health Milestone',
                        '🎉 Completed 30 days of daily meditation! Feeling more focused and peaceful than ever.',
                        'Mindfulness Journey',
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTestCard(
                      context,
                      title: 'Health Insight',
                      description: 'Test sharing a health tip or insight',
                      icon: Icons.lightbulb,
                      onTap: () => _showShareCardPreview(
                        context,
                        'Health Insight',
                        'Did you know? Drinking water first thing in the morning helps kickstart your metabolism and improves brain function throughout the day.',
                        'Daily Health Tip',
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTestCard(
                      context,
                      title: 'Chat Conversation',
                      description: 'Test sharing a chat conversation summary',
                      icon: Icons.chat,
                      onTap: () => _showShareCardPreview(
                        context,
                        'AI Health Consultation',
                        'Just had an amazing conversation with Medroid AI about managing stress and improving sleep quality. Got personalized recommendations!',
                        'Wellness Chat Summary',
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTestCard(
                      context,
                      title: 'Custom Content',
                      description: 'Test with custom title and content',
                      icon: Icons.edit,
                      onTap: () => _showShareCardPreview(
                        context,
                        'My Wellness Journey',
                        'Every small step towards better health counts. Today I chose the stairs, drank more water, and took time to breathe deeply.',
                        'Personal Reflection',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D9488).withValues(alpha: 0.1), // Teal Surge
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF0D9488), // Teal Surge
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1E293B), // Midnight Navy
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF64748B), // Slate Grey
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Color(0xFF64748B), // Slate Grey
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showShareCardPreview(
    BuildContext context,
    String title,
    String content,
    String subtitle,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Card Preview'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Title: $title', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('Subtitle: $subtitle'),
            const SizedBox(height: 8),
            Text('Content: $content'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF0D9488).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'This would generate a 1080×1080 PNG with:\n'
                '• Beautiful gradient frame\n'
                '• "Made with Medroid" footer\n'
                '• QR code for app discovery\n'
                '• Optimized under 200KB',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              HapticFeedback.lightImpact();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Share card functionality is ready! 🎉'),
                  backgroundColor: Color(0xFF0D9488),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D9488),
              foregroundColor: Colors.white,
            ),
            child: const Text('Generate & Share'),
          ),
        ],
      ),
    );
  }
}
