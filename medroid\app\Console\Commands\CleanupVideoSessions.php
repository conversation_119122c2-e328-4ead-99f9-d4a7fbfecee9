<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\VideoConsultation;
use App\Models\VideoSessionParticipant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CleanupVideoSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'video:cleanup-sessions {--dry-run : Show what would be cleaned up without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up abandoned video sessions and stale participants';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('Running in dry-run mode - no changes will be made');
        }

        $this->info('Starting video session cleanup...');

        // Clean up stale participants (disconnected for more than 5 minutes)
        $this->cleanupStaleParticipants($isDryRun);

        // Clean up abandoned sessions (created but no activity for more than 30 minutes)
        $this->cleanupAbandonedSessions($isDryRun);

        // Clean up sessions that have been ended but still have active participants
        $this->cleanupEndedSessions($isDryRun);

        $this->info('Video session cleanup completed');
    }

    /**
     * Clean up participants who have been disconnected for too long
     */
    private function cleanupStaleParticipants($isDryRun = false)
    {
        $staleThreshold = Carbon::now()->subMinutes(5);
        
        $staleParticipants = VideoSessionParticipant::where('status', 'disconnected')
            ->where('left_at', '<', $staleThreshold)
            ->with('videoConsultation')
            ->get();

        $this->info("Found {$staleParticipants->count()} stale participants to clean up");

        foreach ($staleParticipants as $participant) {
            $this->line("  - Participant {$participant->id} (User: {$participant->user_id}, Session: {$participant->videoConsultation->session_id})");
            
            if (!$isDryRun) {
                $participant->update(['status' => 'left']);
                
                // Recalculate participant count for the session
                if ($participant->videoConsultation) {
                    $participant->videoConsultation->recalculateParticipantCount();
                }
            }
        }
    }

    /**
     * Clean up sessions that were created but abandoned
     */
    private function cleanupAbandonedSessions($isDryRun = false)
    {
        $abandonedThreshold = Carbon::now()->subMinutes(30);
        
        $abandonedSessions = VideoConsultation::whereIn('status', ['created', 'active'])
            ->where('created_at', '<', $abandonedThreshold)
            ->where('current_participants', 0)
            ->get();

        $this->info("Found {$abandonedSessions->count()} abandoned sessions to clean up");

        foreach ($abandonedSessions as $session) {
            $this->line("  - Session {$session->session_id} (Appointment: {$session->appointment_id})");
            
            if (!$isDryRun) {
                $session->abandon();
                
                // Clear the appointment's video session ID
                if ($session->appointment) {
                    $session->appointment->update(['video_session_id' => null]);
                }
                
                Log::info('Abandoned video session cleaned up', [
                    'session_id' => $session->session_id,
                    'appointment_id' => $session->appointment_id,
                ]);
            }
        }
    }

    /**
     * Clean up sessions that are marked as ended but still have active participants
     */
    private function cleanupEndedSessions($isDryRun = false)
    {
        $endedSessions = VideoConsultation::where('status', 'ended')
            ->where('current_participants', '>', 0)
            ->with('participants')
            ->get();

        $this->info("Found {$endedSessions->count()} ended sessions with active participants");

        foreach ($endedSessions as $session) {
            $this->line("  - Session {$session->session_id} has {$session->current_participants} active participants");
            
            if (!$isDryRun) {
                // Mark all participants as left
                $session->participants()->where('status', 'joined')->update([
                    'status' => 'left',
                    'left_at' => now(),
                ]);
                
                // Reset participant count
                $session->update(['current_participants' => 0]);
                
                Log::info('Cleaned up ended session participants', [
                    'session_id' => $session->session_id,
                    'appointment_id' => $session->appointment_id,
                ]);
            }
        }
    }
}
