<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateTestAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-test-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test admin user for testing clubs functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = '<EMAIL>';

        // Check if admin already exists
        $existingAdmin = User::where('email', $email)->first();
        if ($existingAdmin) {
            $this->info('Admin user already exists: ' . $email);
            return;
        }

        // Create admin user
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => $email,
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $this->info('Test admin user created successfully!');
        $this->info('Email: ' . $email);
        $this->info('Password: password123');
        $this->info('You can now login to test the clubs functionality.');
    }
}
