<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Provider;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DiagnoseProviderAvailability extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'diagnose:provider-availability {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose and fix provider availability issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        if ($userId) {
            // Get specific provider by user ID
            $provider = Provider::where('user_id', $userId)->first();

            if (!$provider) {
                $this->error("No provider found with user ID: {$userId}");
                return 1;
            }

            $this->diagnoseProvider($provider);
            $this->fixProvider($provider);
        } else {
            // Get all providers
            $providers = Provider::all();

            if ($providers->isEmpty()) {
                $this->error("No providers found in the database");
                return 1;
            }

            foreach ($providers as $provider) {
                $this->diagnoseProvider($provider);
                $this->fixProvider($provider);
            }
        }

        return 0;
    }

    /**
     * Diagnose provider availability issues
     *
     * @param Provider $provider
     * @return void
     */
    protected function diagnoseProvider(Provider $provider)
    {
        $this->info("Diagnosing provider ID: {$provider->id}, User ID: {$provider->user_id}");

        // Check if weekly_availability exists in the database
        $rawData = DB::table('providers')
            ->where('id', $provider->id)
            ->select('weekly_availability', 'absences')
            ->first();

        $this->info("Raw weekly_availability from database: " . json_encode($rawData->weekly_availability));
        $this->info("Raw absences from database: " . json_encode($rawData->absences));

        // Check if the model accessor is working
        $this->info("Model weekly_availability: " . json_encode($provider->weekly_availability));
        $this->info("Model absences: " . json_encode($provider->absences));

        // Check if the weekly_availability is properly formatted
        if (empty($rawData->weekly_availability)) {
            $this->warn("weekly_availability is empty or null in the database");
        } elseif (!is_array(json_decode($rawData->weekly_availability, true))) {
            $this->error("weekly_availability is not a valid JSON array in the database");
        }

        // Check if the absences is properly formatted
        if (empty($rawData->absences)) {
            $this->warn("absences is empty or null in the database");
        } elseif (!is_array(json_decode($rawData->absences, true))) {
            $this->error("absences is not a valid JSON array in the database");
        }
    }

    /**
     * Fix provider availability issues
     *
     * @param Provider $provider
     * @return void
     */
    protected function fixProvider(Provider $provider)
    {
        $this->info("Fixing provider ID: {$provider->id}, User ID: {$provider->user_id}");

        // Default weekly availability structure - all days start with empty slots
        $defaultWeeklyAvailability = [
            ['day' => 'Monday', 'slots' => []],
            ['day' => 'Tuesday', 'slots' => []],
            ['day' => 'Wednesday', 'slots' => []],
            ['day' => 'Thursday', 'slots' => []],
            ['day' => 'Friday', 'slots' => []],
            ['day' => 'Saturday', 'slots' => []],
            ['day' => 'Sunday', 'slots' => []],
        ];

        // Update directly in the database to bypass any model issues
        DB::table('providers')
            ->where('id', $provider->id)
            ->update([
                'weekly_availability' => json_encode($defaultWeeklyAvailability),
                'absences' => json_encode([]),
            ]);

        $this->info("Provider availability fixed for ID: {$provider->id}");

        // Verify the fix
        $rawData = DB::table('providers')
            ->where('id', $provider->id)
            ->select('weekly_availability', 'absences')
            ->first();

        $this->info("Updated weekly_availability from database: " . json_encode($rawData->weekly_availability));
    }
}
