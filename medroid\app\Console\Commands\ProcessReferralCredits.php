<?php

namespace App\Console\Commands;

use App\Models\Referral;
use App\Services\ReferralService;
use App\Services\UserActivityService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessReferralCredits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'referrals:process-credits';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending referral credits for users with 3+ days of activity';

    /**
     * The referral service instance.
     *
     * @var \App\Services\ReferralService
     */
    protected $referralService;

    /**
     * The user activity service instance.
     *
     * @var \App\Services\UserActivityService
     */
    protected $userActivityService;

    /**
     * Create a new command instance.
     *
     * @param \App\Services\ReferralService $referralService
     * @param \App\Services\UserActivityService $userActivityService
     * @return void
     */
    public function __construct(
        ReferralService $referralService,
        UserActivityService $userActivityService
    ) {
        parent::__construct();
        $this->referralService = $referralService;
        $this->userActivityService = $userActivityService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Processing pending referral credits...');
        
        // Get all completed referrals that haven't been credited yet
        $pendingReferrals = Referral::where('status', 'completed')
            ->where('credit_awarded', false)
            ->whereNotNull('referred_id')
            ->get();
            
        $this->info("Found {$pendingReferrals->count()} pending referrals to process.");
        
        $successCount = 0;
        
        foreach ($pendingReferrals as $referral) {
            $this->info("Processing referral ID: {$referral->id}");
            
            // Try to award the credit
            $result = $this->referralService->awardReferralCredit($referral);
            
            if ($result) {
                $successCount++;
                $this->info("Successfully awarded credit for referral ID: {$referral->id}");
            } else {
                $this->warn("Could not award credit for referral ID: {$referral->id} - user may not have 3 days of activity yet");
            }
        }
        
        $this->info("Processed {$pendingReferrals->count()} referrals, awarded {$successCount} credits.");
        
        return 0;
    }
}
