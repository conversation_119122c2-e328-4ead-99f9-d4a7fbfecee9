<?php

namespace App\Console\Commands;

use App\Models\ScheduledNotification;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessScheduledNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:process-scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process scheduled notifications that are due to be sent';

    /**
     * The notification service instance.
     *
     * @var \App\Services\NotificationService
     */
    protected $notificationService;

    /**
     * Create a new command instance.
     *
     * @param \App\Services\NotificationService $notificationService
     * @return void
     */
    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Processing scheduled notifications...');

        try {
            // Get scheduled notifications that are due to be sent
            $notifications = ScheduledNotification::where('status', 'scheduled')
                ->where('scheduled_at', '<=', now())
                ->get();

            $this->info("Found {$notifications->count()} notifications to process.");

            foreach ($notifications as $notification) {
                $this->processNotification($notification);
            }

            $this->info('Scheduled notifications processed successfully.');
            return 0;
        } catch (\Exception $e) {
            $this->error('Error processing scheduled notifications: ' . $e->getMessage());
            Log::error('Error processing scheduled notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Process a scheduled notification.
     *
     * @param \App\Models\ScheduledNotification $notification
     * @return void
     */
    protected function processNotification(ScheduledNotification $notification)
    {
        try {
            $this->info("Processing notification ID: {$notification->id}");

            // Update status to processing
            $notification->status = 'processing';
            $notification->save();

            // Get recipients
            $recipients = $notification->recipients;
            $userIds = [];

            if ($recipients === 'all') {
                // Send to all users
                $userIds = User::pluck('id')->toArray();
                $this->info("Sending to all users: " . count($userIds) . " recipients");
            } elseif (is_array($recipients)) {
                // Send to specific users
                $userIds = $recipients;
                $this->info("Sending to specific users: " . count($userIds) . " recipients");
            } elseif ($notification->groups && is_array($notification->groups)) {
                // Send to user groups
                $groups = $notification->groups;
                $userIds = User::whereHas('groups', function ($query) use ($groups) {
                    $query->whereIn('id', $groups);
                })->pluck('id')->toArray();
                $this->info("Sending to user groups: " . count($userIds) . " recipients");
            }

            if (empty($userIds)) {
                $this->warn("No recipients found for notification ID: {$notification->id}");
                $notification->status = 'failed';
                $notification->save();
                return;
            }

            $successCount = 0;
            $failCount = 0;

            // Process in batches to avoid memory issues
            $chunks = array_chunk($userIds, 100);
            foreach ($chunks as $chunk) {
                foreach ($chunk as $userId) {
                    $user = User::find($userId);

                    if (!$user) {
                        $failCount++;
                        continue;
                    }

                    $success = $this->notificationService->sendPushNotification(
                        $user,
                        $notification->title,
                        $notification->body,
                        $notification->type,
                        $notification->data ?? []
                    );

                    if ($success) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                }
            }

            // Update notification status
            $notification->status = 'sent';
            $notification->sent_at = now();
            $notification->save();

            $this->info("Notification ID: {$notification->id} processed. Success: {$successCount}, Failed: {$failCount}");
        } catch (\Exception $e) {
            $this->error("Error processing notification ID: {$notification->id} - " . $e->getMessage());
            Log::error("Error processing scheduled notification ID: {$notification->id}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Update notification status
            $notification->status = 'failed';
            $notification->save();
        }
    }
}
