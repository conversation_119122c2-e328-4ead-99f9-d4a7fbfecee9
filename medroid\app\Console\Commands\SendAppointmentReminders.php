<?php

namespace App\Console\Commands;

use App\Models\Appointment;
use App\Notifications\AppointmentReminderNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendAppointmentReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointments:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder notifications for appointments scheduled for tomorrow';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tomorrow = Carbon::tomorrow()->format('Y-m-d');
        
        $this->info("Sending reminders for appointments scheduled on {$tomorrow}");
        
        // Get all scheduled appointments for tomorrow
        $appointments = Appointment::where('date', $tomorrow)
            ->where('status', 'scheduled')
            ->with(['patient.user', 'provider.user'])
            ->get();
        
        $this->info("Found {$appointments->count()} appointments scheduled for tomorrow");
        
        $sentCount = 0;
        $errorCount = 0;
        
        foreach ($appointments as $appointment) {
            try {
                // Skip if patient or provider is missing
                if (!$appointment->patient || !$appointment->provider || 
                    !$appointment->patient->user || !$appointment->provider->user) {
                    $this->warn("Skipping appointment #{$appointment->id} due to missing patient or provider data");
                    continue;
                }
                
                // Send reminder to patient
                $appointment->patient->user->notify(new AppointmentReminderNotification($appointment, false));
                
                // Send reminder to provider
                $appointment->provider->user->notify(new AppointmentReminderNotification($appointment, true));
                
                $sentCount += 2; // Count both notifications
                
                $this->info("Sent reminders for appointment #{$appointment->id}");
                
                // Add a small delay to avoid overwhelming the email server
                usleep(250000); // 0.25 seconds
                
            } catch (\Exception $e) {
                $this->error("Error sending reminder for appointment #{$appointment->id}: {$e->getMessage()}");
                Log::error("Error sending appointment reminder", [
                    'appointment_id' => $appointment->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $errorCount++;
            }
        }
        
        $this->info("Completed sending reminders. Sent: {$sentCount}, Errors: {$errorCount}");
        
        return 0;
    }
}
