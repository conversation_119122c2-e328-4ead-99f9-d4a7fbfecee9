<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Ratchet\Http\HttpServer;
use Ratchet\Server\IoServer;
use Ratchet\WebSocket\WsServer;
use App\Services\SignalingService;

class StartWebSocketServer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'websocket:start {--port=8080}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the WebSocket server for WebRTC signaling';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $port = $this->option('port');
        
        $this->info("Starting WebSocket server on port {$port}...");
        
        $server = IoServer::factory(
            new HttpServer(
                new WsServer(
                    new SignalingService()
                )
            ),
            $port
        );
        
        $this->info("WebSocket server started on port {$port}");
        $this->info("Press Ctrl+C to stop the server");
        
        $server->run();
        
        return 0;
    }
}
