<?php

namespace App\Console\Commands;

use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateAppointmentScheduledAt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointments:update-scheduled-at';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update scheduled_at field for all appointments based on date and time_slot';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Updating scheduled_at field for all appointments...');

        $appointments = Appointment::whereNull('scheduled_at')->get();
        $count = 0;

        $this->output->progressStart(count($appointments));

        foreach ($appointments as $appointment) {
            if ($appointment->date && isset($appointment->time_slot['start_time'])) {
                try {
                    $startTime = $appointment->time_slot['start_time'];
                    $scheduledAt = Carbon::parse($appointment->date->format('Y-m-d') . ' ' . $startTime);
                    
                    $appointment->scheduled_at = $scheduledAt;
                    $appointment->save();
                    
                    $count++;
                } catch (\Exception $e) {
                    $this->error("Failed to update appointment ID {$appointment->id}: {$e->getMessage()}");
                }
            }
            
            $this->output->progressAdvance();
        }

        $this->output->progressFinish();
        $this->info("Updated scheduled_at for {$count} appointments.");

        return Command::SUCCESS;
    }
}
