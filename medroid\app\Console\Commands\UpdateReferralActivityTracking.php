<?php

namespace App\Console\Commands;

use App\Models\Referral;
use App\Services\UserActivityService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateReferralActivityTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'referrals:update-activity';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update activity tracking for referrals';

    /**
     * The user activity service instance.
     *
     * @var \App\Services\UserActivityService
     */
    protected $userActivityService;

    /**
     * Create a new command instance.
     *
     * @param \App\Services\UserActivityService $userActivityService
     * @return void
     */
    public function __construct(UserActivityService $userActivityService)
    {
        parent::__construct();
        $this->userActivityService = $userActivityService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Updating referral activity tracking...');
        
        // Get all completed referrals that haven't been credited yet
        $pendingReferrals = Referral::where('status', 'completed')
            ->where('credit_awarded', false)
            ->whereNotNull('referred_id')
            ->get();
            
        $this->info("Found {$pendingReferrals->count()} pending referrals to update.");
        
        $updatedCount = 0;
        $eligibleCount = 0;
        
        foreach ($pendingReferrals as $referral) {
            $this->info("Processing referral ID: {$referral->id}");
            
            // Get the referred user
            $referredUser = $referral->referred;
            
            if (!$referredUser) {
                $this->warn("Referred user not found for referral ID: {$referral->id}");
                continue;
            }
            
            // Get the activity streak for the referred user
            $activityStreak = $this->userActivityService->getUserActivityStreak($referredUser);
            
            // Update the referral with the activity streak
            $referral->consecutive_days_active = $activityStreak;
            
            // Check if the user is eligible for credit (3+ days of activity)
            $isEligible = $activityStreak >= 3;
            $referral->eligible_for_credit = $isEligible;
            
            $referral->save();
            $updatedCount++;
            
            if ($isEligible) {
                $eligibleCount++;
                $this->info("Referral ID: {$referral->id} is now eligible for credit with {$activityStreak} days of activity");
            } else {
                $this->info("Referral ID: {$referral->id} has {$activityStreak} days of activity, needs " . (3 - $activityStreak) . " more days");
            }
        }
        
        $this->info("Updated {$updatedCount} referrals, {$eligibleCount} are now eligible for credit.");
        
        return 0;
    }
}
