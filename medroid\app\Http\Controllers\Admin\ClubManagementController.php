<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FounderReferralCode;
use App\Models\UserClub;
use App\Models\User;
use App\Services\ClubService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ClubManagementController extends Controller
{
    protected $clubService;

    public function __construct(ClubService $clubService)
    {
        $this->clubService = $clubService;
    }

    /**
     * Get all founder referral codes
     */
    public function getFounderCodes(Request $request)
    {
        if (!$request->user()->can('view settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = FounderReferralCode::query();

        // Apply search filter
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $codes = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Add usage statistics
        $codes->getCollection()->transform(function ($code) {
            $stats = $this->clubService->getFounderCodeStats($code);
            return array_merge($code->toArray(), [
                'usage_stats' => $stats,
                'usage_percentage' => $code->max_uses ? 
                    round(($code->current_uses / $code->max_uses) * 100, 1) : null,
            ]);
        });

        return response()->json($codes);
    }

    /**
     * Create a new founder referral code
     */
    public function createFounderCode(Request $request)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:50|unique:founder_referral_codes,code',
            'description' => 'nullable|string|max:255',
            'max_uses' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $code = $this->clubService->createFounderCode($request->all());
            
            return response()->json([
                'message' => 'Founder code created successfully',
                'code' => $code,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating founder code',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a founder referral code
     */
    public function updateFounderCode(Request $request, $id)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $code = FounderReferralCode::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'code' => 'sometimes|required|string|max:50|unique:founder_referral_codes,code,' . $id,
            'description' => 'nullable|string|max:255',
            'max_uses' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $code->update($request->all());
            
            return response()->json([
                'message' => 'Founder code updated successfully',
                'code' => $code,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating founder code',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a founder referral code
     */
    public function deleteFounderCode(Request $request, $id)
    {
        if (!$request->user()->can('delete settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $code = FounderReferralCode::findOrFail($id);

        // Check if code has been used
        if ($code->current_uses > 0) {
            return response()->json([
                'message' => 'Cannot delete code that has been used',
            ], 400);
        }

        $code->delete();

        return response()->json([
            'message' => 'Founder code deleted successfully',
        ]);
    }

    /**
     * Get club members
     */
    public function getClubMembers(Request $request)
    {
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = UserClub::with(['user', 'founderCode']);

        // Filter by club type
        if ($request->has('club_type')) {
            $query->where('club_type', $request->club_type);
        }

        // Filter by verification status
        if ($request->has('verified')) {
            $query->where('is_verified', $request->boolean('verified'));
        }

        // Search by user name or email
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $members = $query->orderBy('joined_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Transform the data
        $members->getCollection()->transform(function ($membership) {
            return [
                'id' => $membership->id,
                'user' => [
                    'id' => $membership->user->id,
                    'name' => $membership->user->name,
                    'email' => $membership->user->email,
                    'total_points' => $membership->user->total_points,
                    'activity_streak' => $membership->user->activity_streak,
                    'created_at' => $membership->user->created_at,
                ],
                'club_type' => $membership->club_type,
                'display_name' => $membership->display_name,
                'membership_level' => $membership->membership_level,
                'level_display' => $membership->level_display,
                'is_verified' => $membership->is_verified,
                'verification_method' => $membership->verification_method,
                'founder_code_used' => $membership->founder_code_used,
                'joined_at' => $membership->joined_at,
                'benefits' => $membership->benefits,
            ];
        });

        return response()->json($members);
    }

    /**
     * Get club statistics
     */
    public function getClubStats(Request $request)
    {
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $stats = [
            'total_members' => UserClub::count(),
            'founder_members' => UserClub::where('club_type', 'founder')->count(),
            'verified_members' => UserClub::where('is_verified', true)->count(),
            'active_codes' => FounderReferralCode::where('is_active', true)->count(),
            'total_codes' => FounderReferralCode::count(),
        ];

        // Membership levels breakdown
        $levelBreakdown = UserClub::selectRaw('membership_level, COUNT(*) as count')
            ->groupBy('membership_level')
            ->pluck('count', 'membership_level')
            ->toArray();

        // Club types breakdown
        $clubBreakdown = UserClub::selectRaw('club_type, COUNT(*) as count')
            ->groupBy('club_type')
            ->pluck('count', 'club_type')
            ->toArray();

        // Recent members (last 30 days)
        $recentMembers = UserClub::where('joined_at', '>=', now()->subDays(30))->count();

        return response()->json([
            'stats' => $stats,
            'level_breakdown' => $levelBreakdown,
            'club_breakdown' => $clubBreakdown,
            'recent_members' => $recentMembers,
        ]);
    }

    /**
     * Manually verify a club membership
     */
    public function verifyMembership(Request $request, $membershipId)
    {
        if (!$request->user()->can('edit users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $membership = UserClub::findOrFail($membershipId);
        
        $membership->update([
            'is_verified' => true,
            'verification_method' => 'manual_admin',
        ]);

        return response()->json([
            'message' => 'Membership verified successfully',
            'membership' => $membership,
        ]);
    }
}
