<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Appointment;
use App\Models\ChatConversation;
use App\Models\DiagnosticFeedback;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class AnalyticsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth.token');
    }

    /**
     * Get all KPI metrics for the dashboard.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getKpiMetrics(Request $request)
    {
        try {
            // Check if user has permission to view analytics or is an admin
            $user = $request->user();
            if (!$user->can('view analytics') && $user->role !== 'admin') {
                return response()->json(['message' => 'Unauthorized - You do not have permission to view analytics'], 403);
            }

            // Log the user's role and permissions
            \Log::info('User accessing analytics', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_role' => $user->role,
                'has_view_analytics' => $user->can('view analytics'),
                'is_admin' => $user->role === 'admin'
            ]);

            // Get date range from request or default to last 30 days
            $startDate = $request->input('start_date')
                ? Carbon::parse($request->input('start_date'))
                : Carbon::now()->subDays(30);

            $endDate = $request->input('end_date')
                ? Carbon::parse($request->input('end_date'))
                : Carbon::now();

            // Cache key based on date range
            $cacheKey = "kpi_metrics_{$startDate->format('Y-m-d')}_{$endDate->format('Y-m-d')}";

            // Check if we have cached data
            if (Cache::has($cacheKey) && !$request->input('refresh', false)) {
                return response()->json(Cache::get($cacheKey));
            }

            // Get all metrics
            $userMetrics = $this->getUserMetrics($startDate, $endDate);
            $appointmentMetrics = $this->getAppointmentMetrics($startDate, $endDate);
            $locationMetrics = $this->getLocationMetrics($startDate, $endDate);
            $diagnosticMetrics = $this->getDiagnosticMetrics($startDate, $endDate);
            $revenueMetrics = $this->getRevenueMetrics($startDate, $endDate);

            // Combine all metrics
            $metrics = array_merge(
                $userMetrics,
                $appointmentMetrics,
                $locationMetrics,
                $diagnosticMetrics,
                $revenueMetrics
            );

            // Cache the results for 1 hour
            Cache::put($cacheKey, $metrics, 60 * 60);

            return response()->json($metrics);
        } catch (\Exception $e) {
            \Log::error('Error in getKpiMetrics: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Error fetching KPI data: ' . $e->getMessage(),
                'error' => true
            ], 500);
        }
    }

    /**
     * Get user-related KPI metrics.
     *
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    private function getUserMetrics(Carbon $startDate, Carbon $endDate)
    {
        // Total users
        $totalUsers = User::count();

        // Monthly active users (users who logged in within the last 30 days)
        $monthlyActiveUsers = User::where('last_login_at', '>=', Carbon::now()->subDays(30))->count();

        // Daily active users (users who logged in today)
        $dailyActiveUsers = User::where('last_login_at', '>=', Carbon::today())->count();

        // User growth rate (percentage increase in users over the last 30 days)
        $usersLastMonth = User::where('created_at', '<', Carbon::now()->subDays(30))->count();
        $newUsers = $totalUsers - $usersLastMonth;
        $userGrowthRate = $usersLastMonth > 0
            ? round(($newUsers / $usersLastMonth) * 100, 1)
            : 0;

        // User role distribution
        $patientCount = User::where('role', 'patient')->count();
        $providerCount = User::where('role', 'provider')->count();
        $adminCount = User::where('role', 'admin')->count();

        // User verification status
        $verifiedUsers = User::whereNotNull('email_verified_at')->count();
        $verificationRate = $totalUsers > 0
            ? round(($verifiedUsers / $totalUsers) * 100, 1)
            : 0;

        return [
            'total_users' => $totalUsers,
            'monthly_active_users' => $monthlyActiveUsers,
            'daily_active_users' => $dailyActiveUsers,
            'user_growth_rate' => $userGrowthRate,
            'patient_count' => $patientCount,
            'provider_count' => $providerCount,
            'admin_count' => $adminCount,
            'verified_users' => $verifiedUsers,
            'verification_rate' => $verificationRate
        ];
    }

    /**
     * Get appointment-related KPI metrics.
     *
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    private function getAppointmentMetrics(Carbon $startDate, Carbon $endDate)
    {
        // Total appointments
        $totalAppointments = Appointment::count();

        // Monthly appointments
        $monthlyAppointments = Appointment::where('date', '>=', Carbon::now()->subDays(30))->count();

        // Daily appointments
        $dailyAppointments = Appointment::where('date', '=', Carbon::today()->format('Y-m-d'))->count();

        // Appointment completion rate
        $completedAppointments = Appointment::where('status', 'completed')->count();
        $appointmentCompletionRate = $totalAppointments > 0
            ? round(($completedAppointments / $totalAppointments) * 100, 1)
            : 0;

        // Appointment cancellation rate
        $cancelledAppointments = Appointment::where('status', 'cancelled')->count();
        $appointmentCancellationRate = $totalAppointments > 0
            ? round(($cancelledAppointments / $totalAppointments) * 100, 1)
            : 0;

        // Check if the ChatConversation model exists and the table exists
        try {
            // Total consults (AI chat conversations)
            $totalConsults = ChatConversation::count();

            // Monthly consults
            $monthlyConsults = ChatConversation::where('created_at', '>=', Carbon::now()->subDays(30))->count();

            // Daily consults
            $dailyConsults = ChatConversation::where('created_at', '>=', Carbon::today())->count();

            // Consult to appointment ratio
            $consultToAppointmentRatio = $totalAppointments > 0
                ? round(($totalConsults / $totalAppointments) * 100, 2)
                : 0;

            // Repeat consult users (users who have had more than one consult)
            $repeatConsultUsers = DB::table('chat_conversations')
                ->select('patient_id', DB::raw('COUNT(*) as consult_count'))
                ->groupBy('patient_id')
                ->having('consult_count', '>', 1)
                ->count();
        } catch (\Exception $e) {
            \Log::warning('Error fetching chat conversation data: ' . $e->getMessage());
            // Set default values if there's an error
            $totalConsults = 0;
            $monthlyConsults = 0;
            $dailyConsults = 0;
            $consultToAppointmentRatio = 0;
            $repeatConsultUsers = 0;
        }

        // Get appointment distribution by provider
        $appointmentsByProvider = Appointment::select('provider_id', DB::raw('COUNT(*) as count'))
            ->groupBy('provider_id')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                $provider = Provider::find($item->provider_id);
                $providerName = $provider ? $provider->user->name : 'Unknown';
                return [
                    'provider_id' => $item->provider_id,
                    'provider_name' => $providerName,
                    'count' => $item->count
                ];
            });

        return [
            'total_appointments' => $totalAppointments,
            'monthly_appointments' => $monthlyAppointments,
            'daily_appointments' => $dailyAppointments,
            'appointment_completion_rate' => $appointmentCompletionRate,
            'appointment_cancellation_rate' => $appointmentCancellationRate,
            'completed_appointments' => $completedAppointments,
            'cancelled_appointments' => $cancelledAppointments,
            'total_consults' => $totalConsults,
            'monthly_consults' => $monthlyConsults,
            'daily_consults' => $dailyConsults,
            'consult_to_appointment_ratio' => $consultToAppointmentRatio,
            'repeat_consult_users' => $repeatConsultUsers,
            'appointments_by_provider' => $appointmentsByProvider
        ];
    }

    /**
     * Get location-based KPI metrics.
     *
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    private function getLocationMetrics(Carbon $startDate, Carbon $endDate)
    {
        // Get user locations from the database
        // This assumes you have a way to track user locations
        // For now, we'll return a placeholder

        return [
            'user_locations' => [
                ['location' => 'New York', 'count' => 120],
                ['location' => 'Los Angeles', 'count' => 85],
                ['location' => 'Chicago', 'count' => 65],
                ['location' => 'Houston', 'count' => 45],
                ['location' => 'Phoenix', 'count' => 30],
            ],
        ];
    }

    /**
     * Get diagnostic accuracy metrics.
     *
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    private function getDiagnosticMetrics(Carbon $startDate, Carbon $endDate)
    {
        // Check if the DiagnosticFeedback model exists
        if (!class_exists('App\Models\DiagnosticFeedback')) {
            return [
                'diagnostic_accuracy' => 0,
                'diagnostic_feedback_count' => 0,
                'top_accurate_diagnoses' => [],
                'top_inaccurate_diagnoses' => [],
            ];
        }

        // Get diagnostic accuracy percentage
        $totalFeedback = DiagnosticFeedback::count();
        $accurateDiagnoses = DiagnosticFeedback::where('is_accurate', true)->count();

        $diagnosticAccuracy = $totalFeedback > 0
            ? round(($accurateDiagnoses / $totalFeedback) * 100, 2)
            : 0;

        // Get top accurate diagnoses
        $topAccurateDiagnoses = DiagnosticFeedback::where('is_accurate', true)
            ->select('ai_diagnosis', DB::raw('COUNT(*) as count'))
            ->groupBy('ai_diagnosis')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();

        // Get top inaccurate diagnoses
        $topInaccurateDiagnoses = DiagnosticFeedback::where('is_accurate', false)
            ->select('ai_diagnosis', DB::raw('COUNT(*) as count'))
            ->groupBy('ai_diagnosis')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();

        return [
            'diagnostic_accuracy' => $diagnosticAccuracy,
            'diagnostic_feedback_count' => $totalFeedback,
            'top_accurate_diagnoses' => $topAccurateDiagnoses,
            'top_inaccurate_diagnoses' => $topInaccurateDiagnoses,
        ];
    }

    /**
     * Get revenue-related KPI metrics.
     *
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    private function getRevenueMetrics(Carbon $startDate, Carbon $endDate)
    {
        // Check if the Payment model exists
        if (!class_exists('App\Models\Payment')) {
            return [
                'total_revenue' => 0,
                'monthly_revenue' => 0,
                'average_appointment_value' => 0,
            ];
        }

        // Get total revenue (in cents, convert to dollars)
        $totalRevenue = DB::table('payment_history')
            ->where('status', 'completed')
            ->sum('amount') / 100;

        // Get monthly revenue
        $monthlyRevenue = DB::table('payment_history')
            ->where('status', 'completed')
            ->where('paid_at', '>=', Carbon::now()->subDays(30))
            ->sum('amount') / 100;

        // Get average appointment value
        $appointmentCount = Appointment::whereNotNull('payment_intent_id')->count();
        $averageAppointmentValue = $appointmentCount > 0
            ? round(($totalRevenue / $appointmentCount), 2)
            : 0;

        // Get revenue by provider (top 5)
        $revenueByProvider = DB::table('payment_history')
            ->join('appointments', 'payment_history.appointment_id', '=', 'appointments.id')
            ->select('appointments.provider_id', DB::raw('SUM(payment_history.amount) as total_revenue'))
            ->where('payment_history.status', 'completed')
            ->groupBy('appointments.provider_id')
            ->orderBy('total_revenue', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                $provider = Provider::find($item->provider_id);
                $providerName = $provider ? $provider->user->name : 'Unknown';
                return [
                    'provider_id' => $item->provider_id,
                    'provider_name' => $providerName,
                    'revenue' => round($item->total_revenue / 100, 2)
                ];
            });

        return [
            'total_revenue' => round($totalRevenue, 2),
            'monthly_revenue' => round($monthlyRevenue, 2),
            'average_appointment_value' => $averageAppointmentValue,
            'revenue_by_provider' => $revenueByProvider
        ];
    }
}
