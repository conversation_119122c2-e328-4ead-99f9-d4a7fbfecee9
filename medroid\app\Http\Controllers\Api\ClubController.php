<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ClubService;
use App\Services\BadgeService;
use App\Services\PointsService;
use Illuminate\Http\Request;

class ClubController extends Controller
{
    protected $clubService;
    protected $badgeService;
    protected $pointsService;

    public function __construct(
        ClubService $clubService,
        BadgeService $badgeService,
        PointsService $pointsService
    ) {
        $this->clubService = $clubService;
        $this->badgeService = $badgeService;
        $this->pointsService = $pointsService;
    }

    /**
     * Get user's club profile
     */
    public function getProfile(Request $request)
    {
        $user = $request->user();
        
        $clubMemberships = $this->clubService->getUserClubMemberships($user);
        $badges = $this->badgeService->getUserBadges($user);
        $pointsSummary = $this->pointsService->getUserPointsSummary($user);
        $pointsHistory = $this->pointsService->getPointsHistory($user, 30);

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'is_founder_member' => $user->is_founder_member,
                'total_points' => $user->total_points,
                'activity_streak' => $user->activity_streak,
                'last_activity_at' => $user->last_activity_at,
            ],
            'club_memberships' => $clubMemberships,
            'badges' => $badges,
            'points_summary' => $pointsSummary,
            'points_history' => $pointsHistory,
        ]);
    }

    /**
     * Get user's badges
     */
    public function getBadges(Request $request)
    {
        $user = $request->user();
        $badges = $this->badgeService->getUserBadges($user);

        return response()->json([
            'badges' => $badges,
            'total_badges' => count($badges),
            'total_points_from_badges' => array_sum(array_column($badges, 'points_awarded')),
        ]);
    }

    /**
     * Get user's points summary
     */
    public function getPoints(Request $request)
    {
        $user = $request->user();
        $pointsSummary = $this->pointsService->getUserPointsSummary($user);
        
        $days = $request->get('days', 30);
        $pointsHistory = $this->pointsService->getPointsHistory($user, $days);

        return response()->json([
            'summary' => $pointsSummary,
            'history' => $pointsHistory,
            'points_config' => $this->pointsService->getPointsConfig(),
        ]);
    }

    /**
     * Get leaderboard
     */
    public function getLeaderboard(Request $request)
    {
        $limit = $request->get('limit', 10);
        $leaderboard = $this->pointsService->getLeaderboard($limit);
        
        $user = $request->user();
        $userRank = $this->pointsService->getUserRank($user);

        return response()->json([
            'leaderboard' => $leaderboard,
            'user_rank' => array_merge($userRank, [
                'user_id' => $user->id,
                'name' => $user->name,
                'total_points' => $user->total_points,
                'is_founder' => $user->is_founder_member,
            ]),
        ]);
    }

    /**
     * Check for new badges (called after activities)
     */
    public function checkBadges(Request $request)
    {
        $user = $request->user();
        $newBadges = $this->badgeService->checkActivityBadges($user);

        return response()->json([
            'new_badges' => array_map(function ($badge) {
                return [
                    'id' => $badge->id,
                    'type' => $badge->badge_type,
                    'name' => $badge->badge_name,
                    'description' => $badge->badge_description,
                    'icon_url' => $badge->icon_url,
                    'color' => $badge->badge_color,
                    'points_awarded' => $badge->points_awarded,
                ];
            }, $newBadges),
            'total_new_badges' => count($newBadges),
        ]);
    }

    /**
     * Award points for app usage time
     */
    public function awardTimePoints(Request $request)
    {
        $user = $request->user();
        $minutesSpent = $request->get('minutes', 0);

        if ($minutesSpent <= 0) {
            return response()->json(['message' => 'Invalid time spent'], 400);
        }

        $pointsAwarded = $this->pointsService->awardTimeBasedPoints($user, $minutesSpent);

        if ($pointsAwarded) {
            return response()->json([
                'message' => 'Points awarded for app usage',
                'points_awarded' => $pointsAwarded->points_earned,
                'total_points' => $user->fresh()->total_points,
            ]);
        }

        return response()->json([
            'message' => 'No points awarded (daily limit reached or insufficient time)',
            'total_points' => $user->total_points,
        ]);
    }

    /**
     * Get all available badge definitions
     */
    public function getBadgeDefinitions(Request $request)
    {
        $definitions = $this->badgeService->getAllBadgeDefinitions();
        $user = $request->user();
        $userBadges = $user->badges()->pluck('badge_type')->toArray();

        $badgeList = [];
        foreach ($definitions as $type => $definition) {
            $badgeList[] = [
                'type' => $type,
                'name' => $definition['name'],
                'description' => $definition['description'],
                'icon' => $definition['icon'],
                'color' => $definition['color'],
                'points' => $definition['points'],
                'earned' => in_array($type, $userBadges),
            ];
        }

        return response()->json([
            'badges' => $badgeList,
            'total_available' => count($badgeList),
            'user_earned' => count($userBadges),
        ]);
    }

    /**
     * Get club membership benefits
     */
    public function getMembershipBenefits(Request $request)
    {
        $user = $request->user();
        $memberships = $this->clubService->getUserClubMemberships($user);

        $allBenefits = [];
        foreach ($memberships as $membership) {
            if ($membership['benefits']) {
                $allBenefits = array_merge($allBenefits, $membership['benefits']);
            }
        }

        return response()->json([
            'memberships' => $memberships,
            'benefits' => $allBenefits,
            'is_founder' => $user->is_founder_member,
        ]);
    }
}
