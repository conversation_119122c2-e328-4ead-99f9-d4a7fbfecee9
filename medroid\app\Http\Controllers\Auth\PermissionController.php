<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class PermissionController extends Controller
{
    /**
     * Get the authenticated user's permissions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getUserPermissions(Request $request)
    {
        try {
            $user = $request->user();

            if (!$user) {
                \Log::warning('No authenticated user found when fetching permissions');
                return response()->json(['message' => 'Unauthenticated'], 401);
            }

            // Get permissions based on user role
            if (method_exists($user, 'getPermissionsForRole')) {
                $permissions = $user->getPermissionsForRole($user->role);
                return response()->json($permissions);
            }

            // Fallback if the method doesn't exist
            $permissions = [];

            // Admin has all permissions
            if ($user->role === 'admin') {
                $permissions = [
                    'view users', 'create users', 'edit users', 'delete users',
                    'view providers', 'create providers', 'edit providers', 'delete providers',
                    'view patients', 'create patients', 'edit patients', 'delete patients',
                    'view appointments', 'create appointments', 'edit appointments', 'delete appointments',
                    'view payments', 'create payments', 'edit payments', 'delete payments',
                    'view chats', 'create chats', 'edit chats', 'delete chats',
                    'view analytics', 'manage permissions',
                    'view settings', 'edit settings',
                ];
            }
            // Manager has view and edit permissions but not create or delete
            else if ($user->role === 'manager') {
                $permissions = [
                    'view users', 'edit users',
                    'view providers', 'edit providers',
                    'view patients', 'edit patients',
                    'view appointments', 'edit appointments',
                    'view payments', 'edit payments',
                    'view chats', 'edit chats',
                    'view analytics',
                    'view settings', 'edit settings',
                ];
            }
            // Provider role has specific permissions
            else if ($user->role === 'provider') {
                $permissions = [
                    'view appointments', 'edit appointments',
                    'view patients',
                    'view payments',
                    'view chats', 'create chats',
                    'view diagnostic feedback', 'create diagnostic feedback', 'edit diagnostic feedback',
                ];
            }
            // Patient role has specific permissions
            else if ($user->role === 'patient') {
                $permissions = [
                    'view appointments', 'create appointments',
                    'view providers',
                    'view payments', 'create payments',
                    'view chats', 'create chats',
                ];
            }
            // Regular user has only view permissions
            else {
                $permissions = [
                    'view users',
                    'view providers',
                    'view patients',
                    'view appointments',
                    'view payments',
                    'view chats',
                ];
            }

            \Log::info('Permissions fetched for user', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_role' => $user->role,
                'permissions_count' => count($permissions)
            ]);

            return response()->json($permissions);
        } catch (\Exception $e) {
            \Log::error('Error fetching permissions: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['message' => 'Error fetching permissions'], 500);
        }
    }

    /**
     * Display a listing of all permissions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view permissions
        $user = $request->user();
        if (!$user || ($user->role !== 'admin' && !$user->can('manage permissions'))) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $permissions = Permission::all();

        // Group permissions by category (based on naming convention)
        $groupedPermissions = [];
        foreach ($permissions as $permission) {
            $parts = explode(' ', $permission->name);
            $action = $parts[0]; // e.g., 'view', 'create', 'edit', 'delete'
            $resource = implode(' ', array_slice($parts, 1)); // e.g., 'users', 'providers', etc.

            if (!isset($groupedPermissions[$resource])) {
                $groupedPermissions[$resource] = [];
            }

            $groupedPermissions[$resource][] = [
                'id' => $permission->id,
                'name' => $permission->name,
                'action' => $action,
            ];
        }

        return response()->json([
            'permissions' => $permissions,
            'grouped_permissions' => $groupedPermissions
        ]);
    }

    /**
     * Get all roles.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRoles(Request $request)
    {
        // Check if user has permission to view permissions
        $user = $request->user();
        if (!$user || ($user->role !== 'admin' && !$user->can('manage permissions'))) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $roles = Role::with('permissions')->get();

        return response()->json($roles);
    }

    /**
     * Create a new role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createRole(Request $request)
    {
        // Check if user has permission to edit features
        $user = $request->user();
        if (!$user || ($user->role !== 'admin' && !$user->can('manage permissions'))) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $role = Role::create(['name' => $request->name]);
        $permissions = Permission::whereIn('id', $request->permissions)->get();
        $role->syncPermissions($permissions);

        return response()->json([
            'message' => 'Role created successfully',
            'role' => $role->load('permissions'),
        ], 201);
    }

    /**
     * Update a role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateRole(Request $request, $id)
    {
        // Check if user has permission to edit features
        $user = $request->user();
        if (!$user || ($user->role !== 'admin' && !$user->can('manage permissions'))) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $role = Role::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:roles,name,' . $id,
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $role->name = $request->name;
        $role->save();

        $permissions = Permission::whereIn('id', $request->permissions)->get();
        $role->syncPermissions($permissions);

        return response()->json([
            'message' => 'Role updated successfully',
            'role' => $role->load('permissions'),
        ]);
    }

    /**
     * Delete a role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteRole(Request $request, $id)
    {
        // Check if user has permission to edit features
        $user = $request->user();
        if (!$user || ($user->role !== 'admin' && !$user->can('manage permissions'))) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $role = Role::findOrFail($id);

        // Prevent deletion of core roles
        if (in_array($role->name, ['admin', 'manager', 'provider', 'patient'])) {
            return response()->json([
                'message' => 'Cannot delete core system roles',
            ], 422);
        }

        $role->delete();

        return response()->json([
            'message' => 'Role deleted successfully',
        ]);
    }

    /**
     * Assign permissions to a role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignPermissions(Request $request)
    {
        // Check if user has permission to edit features
        $user = $request->user();
        if (!$user || ($user->role !== 'admin' && !$user->can('manage permissions'))) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'role_id' => 'required|exists:roles,id',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $role = Role::findOrFail($request->role_id);
        $permissions = Permission::whereIn('id', $request->permissions)->get();
        $role->syncPermissions($permissions);

        return response()->json([
            'message' => 'Permissions assigned successfully',
            'role' => $role->load('permissions'),
        ]);
    }
}
