<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/Register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => 'required|in:patient,provider,admin',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date|before:today',
            'referral_code' => 'nullable|string|max:255',
        ]);

        // Handle referral code if provided
        $referredBy = null;
        if ($request->referral_code) {
            $referrer = User::where('referral_code', $request->referral_code)->first();
            if ($referrer) {
                $referredBy = $referrer->id;
            }
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'referred_by' => $referredBy,
        ]);

        // Create role-specific profile
        if ($request->role === 'patient') {
            Patient::create([
                'user_id' => $user->id,
                'gender' => $request->gender,
                'date_of_birth' => $request->date_of_birth,
                'health_history' => [],
                'allergies' => [],
                'preferences' => [
                    'feed_topics' => [],
                    'notification_settings' => [
                        'push' => true,
                        'email' => true
                    ]
                ],
                'appointment_preferences' => [
                    'preferred_location' => null,
                    'preferred_gender' => null,
                    'preferred_language' => null
                ],
            ]);
        } elseif ($request->role === 'provider') {
            Provider::create([
                'user_id' => $user->id,
                'specialization' => null,
                'license_number' => null,
                'verification_status' => 'pending',
                'years_of_experience' => 0,
                'bio' => null,
                'rating' => 0,
                'accepts_insurance' => false,
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'absences' => [],
            ]);
        }

        // Assign patient role
        $user->assignRole('patient');

        event(new Registered($user));

        Auth::login($user);

        return to_route('dashboard');
    }
}
