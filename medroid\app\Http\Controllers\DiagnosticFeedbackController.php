<?php

namespace App\Http\Controllers;

use App\Models\DiagnosticFeedback;
use App\Models\Provider;
use App\Models\ChatConversation;
use App\Models\Appointment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DiagnosticFeedbackController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth.token');
    }

    /**
     * Display a listing of the diagnostic feedback.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view diagnostic feedback
        if (!$request->user()->can('view diagnostic feedback')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = DiagnosticFeedback::with(['provider', 'patient']);

        // Filter by provider if specified
        if ($request->has('provider_id')) {
            $query->where('provider_id', $request->provider_id);
        }

        // Filter by accuracy if specified
        if ($request->has('is_accurate')) {
            $query->where('is_accurate', $request->is_accurate);
        }

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $feedback = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($feedback);
    }

    /**
     * Store a newly created diagnostic feedback in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'appointment_id' => 'nullable|exists:appointments,id',
            'chat_conversation_id' => 'required|exists:chat_conversations,id',
            'ai_diagnoses' => 'required|array',
            'ai_diagnosis' => 'nullable|string',
            'provider_diagnosis' => 'required|string',
            'is_accurate' => 'required|boolean',
            'feedback_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get the user and check if they are a provider
        $user = $request->user();
        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json(['message' => 'Only providers can submit diagnostic feedback'], 403);
        }

        // Get the chat conversation to find the patient
        $chatConversation = ChatConversation::findOrFail($request->chat_conversation_id);
        
        // Create the feedback
        $feedback = DiagnosticFeedback::create([
            'provider_id' => $provider->id,
            'patient_id' => $chatConversation->patient_id,
            'appointment_id' => $request->appointment_id,
            'chat_conversation_id' => $request->chat_conversation_id,
            'ai_diagnoses' => $request->ai_diagnoses,
            'ai_diagnosis' => $request->ai_diagnosis ?? $request->ai_diagnoses[0] ?? null,
            'provider_diagnosis' => $request->provider_diagnosis,
            'is_accurate' => $request->is_accurate,
            'feedback_notes' => $request->feedback_notes,
        ]);

        return response()->json([
            'message' => 'Diagnostic feedback submitted successfully',
            'feedback' => $feedback
        ], 201);
    }

    /**
     * Display the specified diagnostic feedback.
     *
     * @param  int  $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id, Request $request)
    {
        // Check if user has permission to view diagnostic feedback
        if (!$request->user()->can('view diagnostic feedback')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $feedback = DiagnosticFeedback::with(['provider', 'patient', 'appointment', 'chatConversation'])
            ->findOrFail($id);

        return response()->json($feedback);
    }

    /**
     * Update the specified diagnostic feedback in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'provider_diagnosis' => 'required|string',
            'is_accurate' => 'required|boolean',
            'feedback_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get the user and check if they are a provider
        $user = $request->user();
        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json(['message' => 'Only providers can update diagnostic feedback'], 403);
        }

        // Find the feedback
        $feedback = DiagnosticFeedback::findOrFail($id);

        // Check if the provider owns this feedback
        if ($feedback->provider_id !== $provider->id) {
            return response()->json(['message' => 'You can only update your own feedback'], 403);
        }

        // Update the feedback
        $feedback->update([
            'provider_diagnosis' => $request->provider_diagnosis,
            'is_accurate' => $request->is_accurate,
            'feedback_notes' => $request->feedback_notes,
        ]);

        return response()->json([
            'message' => 'Diagnostic feedback updated successfully',
            'feedback' => $feedback
        ]);
    }
}
