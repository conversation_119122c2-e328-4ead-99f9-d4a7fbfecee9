<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ImageProxyController extends Controller
{
    public function proxy(Request $request)
    {
        // Handle CORS preflight
        if ($request->getMethod() === 'OPTIONS') {
            return response('', 200)
                ->header('Access-Control-Allow-Origin', '*')
                ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
                ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept')
                ->header('Access-Control-Max-Age', '3600');
        }
        
        $imageUrl = $request->query('url');
        
        if (!$imageUrl) {
            return response()->json(['error' => 'URL parameter is required'], 400)
                ->header('Access-Control-Allow-Origin', '*');
        }
        
        // Validate that it's a local storage URL for security
        if (!str_contains($imageUrl, '/storage/')) {
            return response()->json(['error' => 'Invalid URL'], 400)
                ->header('Access-Control-Allow-Origin', '*');
        }
        
        // Get the file path from the URL
        $path = str_replace(request()->getSchemeAndHttpHost(), '', $imageUrl);
        $fullPath = public_path($path);
        
        if (!file_exists($fullPath)) {
            return response()->json(['error' => 'File not found'], 404)
                ->header('Access-Control-Allow-Origin', '*');
        }
        
        // Get file info
        $mimeType = mime_content_type($fullPath);
        $fileSize = filesize($fullPath);
        
        // Read file content
        $fileContent = file_get_contents($fullPath);
        
        // Create response with proper CORS headers
        return response($fileContent, 200, [
            'Content-Type' => $mimeType,
            'Content-Length' => $fileSize,
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, OPTIONS',
            'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }
}
