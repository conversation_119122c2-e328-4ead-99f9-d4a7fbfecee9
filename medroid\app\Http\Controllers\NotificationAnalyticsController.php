<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\NotificationAnalytic;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotificationAnalyticsController extends Controller
{
    /**
     * Get notification analytics data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // Determine date range
            $days = $request->input('days', 30);
            $startDate = null;
            $endDate = null;

            if ($request->has('start_date') && $request->has('end_date')) {
                $startDate = Carbon::parse($request->input('start_date'))->startOfDay();
                $endDate = Carbon::parse($request->input('end_date'))->endOfDay();
            } else {
                $endDate = Carbon::now();
                $startDate = Carbon::now()->subDays($days);
            }

            // Previous period for comparison
            $previousStartDate = (clone $startDate)->subDays($days);
            $previousEndDate = (clone $endDate)->subDays($days);

            // Get total counts
            $totalSent = $this->getTotalSent($startDate, $endDate);
            $previousTotalSent = $this->getTotalSent($previousStartDate, $previousEndDate);

            // Calculate percentage change
            $sentChangePercentage = 0;
            if ($previousTotalSent > 0) {
                $sentChangePercentage = round((($totalSent - $previousTotalSent) / $previousTotalSent) * 100, 2);
            }

            // Get delivery, open, and click counts
            $delivered = $this->getDeliveredCount($startDate, $endDate);
            $opened = $this->getOpenedCount($startDate, $endDate);
            $clicked = $this->getClickedCount($startDate, $endDate);

            // Calculate rates
            $deliveryRate = $totalSent > 0 ? round(($delivered / $totalSent) * 100, 2) : 0;
            $openRate = $delivered > 0 ? round(($opened / $delivered) * 100, 2) : 0;
            $clickRate = $opened > 0 ? round(($clicked / $opened) * 100, 2) : 0;

            // Get data by notification type
            $byType = $this->getDataByType($startDate, $endDate);

            // Get top performing notifications
            $topPerforming = $this->getTopPerformingNotifications($startDate, $endDate);

            // Get time series data
            $timeData = $this->getTimeSeriesData($startDate, $endDate);

            return response()->json([
                'totalSent' => $totalSent,
                'delivered' => $delivered,
                'opened' => $opened,
                'clicked' => $clicked,
                'deliveryRate' => $deliveryRate,
                'openRate' => $openRate,
                'clickRate' => $clickRate,
                'sentChangePercentage' => $sentChangePercentage,
                'byType' => $byType,
                'topPerforming' => $topPerforming,
                'timeData' => $timeData
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching notification analytics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to fetch notification analytics'
            ], 500);
        }
    }

    /**
     * Get total sent notifications count
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return int
     */
    private function getTotalSent(Carbon $startDate, Carbon $endDate)
    {
        return Notification::whereBetween('created_at', [$startDate, $endDate])->count();
    }

    /**
     * Get delivered notifications count
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return int
     */
    private function getDeliveredCount(Carbon $startDate, Carbon $endDate)
    {
        return NotificationAnalytic::where('event', 'delivered')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    /**
     * Get opened notifications count
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return int
     */
    private function getOpenedCount(Carbon $startDate, Carbon $endDate)
    {
        return NotificationAnalytic::where('event', 'opened')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    /**
     * Get clicked notifications count
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return int
     */
    private function getClickedCount(Carbon $startDate, Carbon $endDate)
    {
        return NotificationAnalytic::where('event', 'clicked')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    /**
     * Get analytics data grouped by notification type
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getDataByType(Carbon $startDate, Carbon $endDate)
    {
        $types = ['appointment', 'message', 'system', 'marketing'];
        $result = [];

        foreach ($types as $type) {
            $sent = Notification::where('type', $type)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            if ($sent === 0) {
                continue;
            }

            $notificationIds = Notification::where('type', $type)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->pluck('id')
                ->toArray();

            $delivered = NotificationAnalytic::where('event', 'delivered')
                ->whereIn('notification_id', $notificationIds)
                ->count();

            $opened = NotificationAnalytic::where('event', 'opened')
                ->whereIn('notification_id', $notificationIds)
                ->count();

            $clicked = NotificationAnalytic::where('event', 'clicked')
                ->whereIn('notification_id', $notificationIds)
                ->count();

            $openRate = $delivered > 0 ? round(($opened / $delivered) * 100, 2) : 0;
            $clickRate = $opened > 0 ? round(($clicked / $opened) * 100, 2) : 0;

            $result[] = [
                'name' => $type,
                'sent' => $sent,
                'delivered' => $delivered,
                'opened' => $opened,
                'clicked' => $clicked,
                'openRate' => $openRate,
                'clickRate' => $clickRate
            ];
        }

        return $result;
    }

    /**
     * Get top performing notifications
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getTopPerformingNotifications(Carbon $startDate, Carbon $endDate)
    {
        $notifications = Notification::whereBetween('created_at', [$startDate, $endDate])
            ->take(10)
            ->get();

        $result = [];

        foreach ($notifications as $notification) {
            $delivered = NotificationAnalytic::where('notification_id', $notification->id)
                ->where('event', 'delivered')
                ->count();

            if ($delivered === 0) {
                continue;
            }

            $opened = NotificationAnalytic::where('notification_id', $notification->id)
                ->where('event', 'opened')
                ->count();

            $clicked = NotificationAnalytic::where('notification_id', $notification->id)
                ->where('event', 'clicked')
                ->count();

            $openRate = round(($opened / $delivered) * 100, 2);
            $clickRate = $opened > 0 ? round(($clicked / $opened) * 100, 2) : 0;

            $result[] = [
                'id' => $notification->id,
                'title' => $notification->title,
                'type' => $notification->type,
                'sent' => $delivered, // Using delivered as sent for simplicity
                'opened' => $opened,
                'clicked' => $clicked,
                'openRate' => $openRate,
                'clickRate' => $clickRate
            ];
        }

        // Sort by open rate descending
        usort($result, function ($a, $b) {
            return $b['openRate'] <=> $a['openRate'];
        });

        return array_slice($result, 0, 10);
    }

    /**
     * Get time series data for charts
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getTimeSeriesData(Carbon $startDate, Carbon $endDate)
    {
        $days = $startDate->diffInDays($endDate) + 1;
        $interval = 'day';
        $format = 'Y-m-d';

        if ($days > 90) {
            $interval = 'week';
            $format = 'Y-W';
        } elseif ($days > 30) {
            $interval = 'week';
            $format = 'Y-W';
        }

        $labels = [];
        $sentData = [];
        $openedData = [];
        $clickedData = [];

        $current = clone $startDate;

        while ($current <= $endDate) {
            $nextDate = clone $current;

            if ($interval === 'day') {
                $nextDate->addDay();
            } elseif ($interval === 'week') {
                $nextDate->addWeek();
            }

            $periodLabel = $current->format($format);
            $labels[] = $periodLabel;

            // Get sent count
            $sent = Notification::whereBetween('created_at', [$current, $nextDate])
                ->count();
            $sentData[] = $sent;

            // Get notification IDs for this period
            $notificationIds = Notification::whereBetween('created_at', [$current, $nextDate])
                ->pluck('id')
                ->toArray();

            // Get opened count
            $opened = NotificationAnalytic::where('event', 'opened')
                ->whereIn('notification_id', $notificationIds)
                ->count();
            $openedData[] = $opened;

            // Get clicked count
            $clicked = NotificationAnalytic::where('event', 'clicked')
                ->whereIn('notification_id', $notificationIds)
                ->count();
            $clickedData[] = $clicked;

            $current = $nextDate;
        }

        return [
            'labels' => $labels,
            'sent' => $sentData,
            'opened' => $openedData,
            'clicked' => $clickedData
        ];
    }
}