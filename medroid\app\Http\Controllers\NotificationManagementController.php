<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\NotificationTemplate;
use App\Models\ScheduledNotification;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class NotificationManagementController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get all notifications for admin panel
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $limit = $request->input('limit', 50);
            $offset = $request->input('offset', 0);
            $search = $request->input('search');
            $type = $request->input('type');
            $status = $request->input('status');

            $query = Notification::query()->orderBy('created_at', 'desc');

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('body', 'like', "%{$search}%");
                });
            }

            if ($type) {
                $query->where('type', $type);
            }

            if ($status) {
                if ($status === 'read') {
                    $query->whereNotNull('read_at');
                } elseif ($status === 'unread') {
                    $query->whereNull('read_at');
                }
            }

            $total = $query->count();
            $notifications = $query->skip($offset)->take($limit)->get();

            // Add recipient count and status for admin display
            foreach ($notifications as $notification) {
                $notification->recipient_count = 1; // Individual notification
                $notification->status = $notification->read_at ? 'read' : 'unread';
            }

            return response()->json([
                'notifications' => $notifications,
                'total' => $total,
                'has_more' => ($offset + $limit) < $total
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching notifications for admin', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to fetch notifications'
            ], 500);
        }
    }

    /**
     * Get all scheduled notifications
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function scheduledNotifications()
    {
        try {
            $notifications = ScheduledNotification::where('status', 'scheduled')
                ->where('scheduled_at', '>', now())
                ->orderBy('scheduled_at', 'asc')
                ->get();

            return response()->json([
                'notifications' => $notifications
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching scheduled notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to fetch scheduled notifications'
            ], 500);
        }
    }

    /**
     * Create a new notification
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'body' => 'required|string',
                'type' => 'required|string|in:appointment,message,system,marketing',
                'recipients' => 'required',
                'data' => 'nullable|array',
                'scheduled_at' => 'nullable|date|after:now',
                'save_as_template' => 'nullable|boolean',
                'template_name' => 'required_if:save_as_template,true|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle scheduled notifications
            if ($request->has('scheduled_at')) {
                return $this->createScheduledNotification($request);
            }

            // Handle immediate notifications
            $recipients = $request->input('recipients');
            $userIds = [];

            if ($recipients === 'all') {
                // Send to all users
                $userIds = User::pluck('id')->toArray();
            } elseif (is_array($recipients)) {
                // Send to specific users
                $userIds = $recipients;
            } elseif ($request->has('groups') && is_array($request->input('groups'))) {
                // Send to user groups
                $groups = $request->input('groups');
                $userIds = User::whereHas('groups', function ($query) use ($groups) {
                    $query->whereIn('id', $groups);
                })->pluck('id')->toArray();
            }

            if (empty($userIds)) {
                return response()->json([
                    'message' => 'No recipients specified'
                ], 422);
            }

            $successCount = 0;
            $failCount = 0;

            foreach ($userIds as $userId) {
                $user = User::find($userId);

                if (!$user) {
                    $failCount++;
                    continue;
                }

                $success = $this->notificationService->sendPushNotification(
                    $user,
                    $request->input('title'),
                    $request->input('body'),
                    $request->input('type'),
                    $request->input('data', [])
                );

                if ($success) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            // Save as template if requested
            if ($request->input('save_as_template', false)) {
                $this->saveAsTemplate($request);
            }

            return response()->json([
                'message' => 'Notifications sent',
                'success_count' => $successCount,
                'fail_count' => $failCount
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to create notification'
            ], 500);
        }
    }

    /**
     * Create a scheduled notification
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function createScheduledNotification(Request $request)
    {
        try {
            $scheduledNotification = ScheduledNotification::create([
                'title' => $request->input('title'),
                'body' => $request->input('body'),
                'type' => $request->input('type'),
                'recipients' => $request->input('recipients'),
                'groups' => $request->input('groups'),
                'data' => $request->input('data'),
                'scheduled_at' => $request->input('scheduled_at'),
                'status' => 'scheduled'
            ]);

            // Save as template if requested
            if ($request->input('save_as_template', false)) {
                $this->saveAsTemplate($request);
            }

            return response()->json([
                'message' => 'Notification scheduled successfully',
                'notification' => $scheduledNotification
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error scheduling notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to schedule notification'
            ], 500);
        }
    }

    /**
     * Save notification as a template
     *
     * @param Request $request
     * @return NotificationTemplate|null
     */
    private function saveAsTemplate(Request $request)
    {
        try {
            return NotificationTemplate::create([
                'name' => $request->input('template_name'),
                'title' => $request->input('title'),
                'body' => $request->input('body'),
                'type' => $request->input('type'),
                'icon' => $request->input('icon', 'default'),
                'data' => $request->input('data')
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving notification template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Send a test notification
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTest(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'body' => 'required|string',
                'type' => 'required|string|in:appointment,message,system,marketing',
                'recipient' => 'required|email',
                'sendEmail' => 'nullable|boolean',
                'data' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->input('recipient'))->first();

            if (!$user) {
                return response()->json([
                    'message' => 'User not found with the provided email'
                ], 404);
            }

            $success = $this->notificationService->sendPushNotification(
                $user,
                $request->input('title'),
                $request->input('body'),
                $request->input('type'),
                $request->input('data', [])
            );

            if ($success) {
                return response()->json([
                    'message' => 'Test notification sent successfully'
                ]);
            } else {
                return response()->json([
                    'message' => 'Failed to send test notification'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Error sending test notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to send test notification'
            ], 500);
        }
    }

    /**
     * Cancel a scheduled notification
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelScheduled($id)
    {
        try {
            $notification = ScheduledNotification::findOrFail($id);

            if ($notification->status !== 'scheduled') {
                return response()->json([
                    'message' => 'Only scheduled notifications can be cancelled'
                ], 422);
            }

            $notification->status = 'cancelled';
            $notification->save();

            return response()->json([
                'message' => 'Scheduled notification cancelled successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling scheduled notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to cancel scheduled notification'
            ], 500);
        }
    }

    /**
     * Send a scheduled notification immediately
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendNow($id)
    {
        try {
            $notification = ScheduledNotification::findOrFail($id);

            if ($notification->status !== 'scheduled') {
                return response()->json([
                    'message' => 'Only scheduled notifications can be sent immediately'
                ], 422);
            }

            // Process the notification immediately
            // This would typically be handled by a job or command
            // For simplicity, we'll just update the status here
            $notification->status = 'processing';
            $notification->save();

            // Trigger the notification sending process
            // This would be a background job in a real implementation

            return response()->json([
                'message' => 'Notification is being sent immediately'
            ]);
        } catch (\Exception $e) {
            Log::error('Error sending scheduled notification immediately', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to send notification immediately'
            ], 500);
        }
    }
}