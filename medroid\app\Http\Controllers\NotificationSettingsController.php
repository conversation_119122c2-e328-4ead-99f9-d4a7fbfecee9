<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class NotificationSettingsController extends Controller
{
    /**
     * Get all notification settings
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $generalSettings = $this->getSettingsByGroup('notification_general');
            $firebaseSettings = $this->getSettingsByGroup('notification_firebase');
            $emailSettings = $this->getSettingsByGroup('notification_email');

            return response()->json([
                'settings' => [
                    'general' => $generalSettings,
                    'firebase' => $firebaseSettings,
                    'email' => $emailSettings
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching notification settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to fetch notification settings'
            ], 500);
        }
    }

    /**
     * Update general notification settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateGeneralSettings(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'defaultIcon' => 'nullable|string|url',
                'defaultSound' => 'nullable|string|in:default,none,custom',
                'customSoundUrl' => 'nullable|string|url',
                'enableBadge' => 'nullable|boolean',
                'enableVibration' => 'nullable|boolean',
                'throttleLimit' => 'nullable|integer|min:0',
                'quietHoursStart' => 'nullable|string',
                'quietHoursEnd' => 'nullable|string',
                'enableQuietHours' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $this->updateSettings('notification_general', $request->all());

            return response()->json([
                'message' => 'General notification settings updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating general notification settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to update general notification settings'
            ], 500);
        }
    }

    /**
     * Update Firebase settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateFirebaseSettings(Request $request)
    {
        return response()->json([
            'message' => 'Firebase settings are configured via environment variables in the .env file.'
        ], 200);
    }

    /**
     * Update email notification settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateEmailSettings(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'enabled' => 'nullable|boolean',
                'senderName' => 'nullable|string',
                'senderEmail' => 'nullable|email',
                'subjectPrefix' => 'nullable|string',
                'footerText' => 'nullable|string',
                'includeUnsubscribe' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $this->updateSettings('notification_email', $request->all());

            return response()->json([
                'message' => 'Email notification settings updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating email notification settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to update email notification settings'
            ], 500);
        }
    }

    /**
     * Test Firebase connection
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testFirebaseConnection(Request $request)
    {
        try {
            $apiKey = config('services.firebase.api_key');

            if (empty($apiKey)) {
                return response()->json([
                    'message' => 'Firebase API key is not configured in your .env file.'
                ], 400);
            }

            if (\App\Facades\Firebase::testConnection()) {
                return response()->json([
                    'message' => 'Firebase connection successful. Your API key is valid for sending notifications to your Flutter app.'
                ], 200);
            } else {
                return response()->json([
                    'message' => 'Firebase connection failed. Your API key may be invalid or restricted.'
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error testing Firebase connection',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get settings by group
     *
     * @param string $group
     * @return array
     */
    private function getSettingsByGroup($group)
    {
        $settings = Setting::where('group', $group)->get();
        $result = [];

        foreach ($settings as $setting) {
            $value = $setting->value;

            // Convert boolean strings to actual booleans
            if ($value === 'true') {
                $value = true;
            } elseif ($value === 'false') {
                $value = false;
            } elseif (is_numeric($value)) {
                // Convert numeric strings to numbers
                $value = $value + 0;
            }

            $result[$setting->key] = $value;
        }

        return $result;
    }

    /**
     * Update settings
     *
     * @param string $group
     * @param array $data
     * @return void
     */
    private function updateSettings($group, $data)
    {
        foreach ($data as $key => $value) {
            Setting::updateOrCreate(
                ['group' => $group, 'key' => $key],
                ['value' => $value]
            );
        }
    }

    /**
     * Update .env file with new settings
     *
     * @param array $data
     * @return void
     */
    private function updateEnvFile($data)
    {
        $envFile = app()->environmentFilePath();
        $envContents = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            if ($value) {
                $envContents = preg_replace(
                    "/^{$key}=.*/m",
                    "{$key}={$value}",
                    $envContents
                );

                // If the key doesn't exist, add it
                if (!preg_match("/^{$key}=/m", $envContents)) {
                    $envContents .= "\n{$key}={$value}";
                }
            }
        }

        file_put_contents($envFile, $envContents);
    }
}