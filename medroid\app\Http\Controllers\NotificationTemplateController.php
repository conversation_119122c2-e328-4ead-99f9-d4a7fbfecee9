<?php

namespace App\Http\Controllers;

use App\Models\NotificationTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class NotificationTemplateController extends Controller
{
    /**
     * Get all notification templates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $templates = NotificationTemplate::orderBy('name')->get();
            
            return response()->json([
                'templates' => $templates
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching notification templates', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to fetch notification templates'
            ], 500);
        }
    }

    /**
     * Get a specific notification template
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            
            return response()->json([
                'template' => $template
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching notification template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to fetch notification template'
            ], 500);
        }
    }

    /**
     * Create a new notification template
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'title' => 'required|string|max:255',
                'body' => 'required|string',
                'type' => 'required|string|in:appointment,message,system,marketing',
                'icon' => 'nullable|string',
                'data' => 'nullable|array'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            $template = NotificationTemplate::create([
                'name' => $request->input('name'),
                'title' => $request->input('title'),
                'body' => $request->input('body'),
                'type' => $request->input('type'),
                'icon' => $request->input('icon', 'default'),
                'data' => $request->input('data')
            ]);
            
            return response()->json([
                'message' => 'Notification template created successfully',
                'template' => $template
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error creating notification template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to create notification template'
            ], 500);
        }
    }

    /**
     * Update a notification template
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'title' => 'required|string|max:255',
                'body' => 'required|string',
                'type' => 'required|string|in:appointment,message,system,marketing',
                'icon' => 'nullable|string',
                'data' => 'nullable|array'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            $template = NotificationTemplate::findOrFail($id);
            
            $template->update([
                'name' => $request->input('name'),
                'title' => $request->input('title'),
                'body' => $request->input('body'),
                'type' => $request->input('type'),
                'icon' => $request->input('icon', 'default'),
                'data' => $request->input('data')
            ]);
            
            return response()->json([
                'message' => 'Notification template updated successfully',
                'template' => $template
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating notification template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to update notification template'
            ], 500);
        }
    }

    /**
     * Delete a notification template
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $template->delete();
            
            return response()->json([
                'message' => 'Notification template deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting notification template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to delete notification template'
            ], 500);
        }
    }
}
