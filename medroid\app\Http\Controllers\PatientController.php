<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use App\Models\User;
use App\Models\Appointment;
use App\Models\HealthRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PatientController extends Controller
{
    /**
     * Display a listing of the patients.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view patients
        if (!$request->user()->can('view patients')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = Patient::with('user');

        // Filter by search term if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Sort by column
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDir = $request->input('sort_dir', 'desc');

        // Handle sorting by user attributes
        if (in_array($sortBy, ['name', 'email'])) {
            $query->join('users', 'patients.user_id', '=', 'users.id')
                  ->orderBy('users.' . $sortBy, $sortDir)
                  ->select('patients.*');
        } else {
            $query->orderBy($sortBy, $sortDir);
        }

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $patients = $query->paginate($perPage);

        // Add credit balance to each patient
        foreach ($patients as $patient) {
            // Get the user associated with this patient
            $user = $patient->user;

            if ($user) {
                // Load the user's credit
                $userCredit = $user->credit;
                $creditBalance = $userCredit ? $userCredit->balance : 0;
                $patient->credit_balance = $creditBalance;
            } else {
                $patient->credit_balance = 0;
            }
        }

        return response()->json($patients);
    }

    /**
     * Display the specified patient.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        // Check if user has permission to view patients
        if (!$request->user()->can('view patients')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $patient = Patient::with(['user', 'appointments'])->findOrFail($id);

        return response()->json($patient);
    }

    /**
     * Update the specified patient in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Check if user has permission to edit patients
        if (!$request->user()->can('edit patients')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $patient = Patient::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'gender' => 'nullable|string|in:male,female,other',
            'date_of_birth' => 'nullable|date',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'zip_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'preferred_language' => 'nullable|string|max:50',
            'preferred_provider_gender' => 'nullable|string|in:male,female,no_preference',
            'preferred_location' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update patient profile
        $patient->update($request->only([
            'gender',
            'date_of_birth',
            'phone',
            'address',
            'city',
            'state',
            'zip_code',
            'country',
            'emergency_contact_name',
            'emergency_contact_phone',
            'preferred_language',
            'preferred_provider_gender',
            'preferred_location',
        ]));

        // Update user information if provided
        if ($request->has('user')) {
            $userValidator = Validator::make($request->user, [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email,' . $patient->user_id,
            ]);

            if ($userValidator->fails()) {
                return response()->json(['errors' => $userValidator->errors()], 422);
            }

            $user = User::findOrFail($patient->user_id);
            $user->update($request->user);
        }

        // Reload the patient with user relationship
        $patient = Patient::with('user')->findOrFail($id);

        return response()->json($patient);
    }

    /**
     * Get the patient's health records.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHealthRecords(Request $request, $id)
    {
        // Check if user has permission to view patients
        if (!$request->user()->can('view patients')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $patient = Patient::findOrFail($id);

        // Get health records with pagination
        $perPage = $request->input('per_page', 10);
        $healthRecords = HealthRecord::where('patient_id', $id)
            ->orderBy('date', 'desc')
            ->paginate($perPage);

        return response()->json($healthRecords);
    }

    /**
     * Get the patient's appointment history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppointmentHistory(Request $request, $id)
    {
        // Check if user has permission to view patients and appointments
        if (!$request->user()->can('view patients') || !$request->user()->can('view appointments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $patient = Patient::findOrFail($id);

        // Get appointments with pagination
        $perPage = $request->input('per_page', 10);
        $appointments = Appointment::where('patient_id', $id)
            ->with(['provider.user', 'service'])
            ->orderBy('scheduled_at', 'desc')
            ->paginate($perPage);

        return response()->json($appointments);
    }

    /**
     * Remove the specified patient from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            // Check if user has permission to delete patients
            if (!$request->user() || !$request->user()->can('delete patients')) {
                return response()->json(['message' => 'Unauthorized to delete patients'], 403);
            }

            try {
                $patient = Patient::findOrFail($id);
                $user = User::findOrFail($patient->user_id);

                // Prevent deleting your own account
                if ($request->user()->id === $patient->user_id) {
                    return response()->json(['message' => 'You cannot delete your own account'], 400);
                }

                // Begin transaction to ensure both patient and user are deleted
                DB::beginTransaction();

                try {
                    // Delete related appointments
                    $appointmentsDeleted = Appointment::where('patient_id', $id)->delete();

                    // Delete related health records
                    $healthRecordsDeleted = HealthRecord::where('patient_id', $id)->delete();

                    // Delete the patient
                    $patientDeleted = $patient->delete();

                    // Remove roles from user before deleting to avoid model_has_roles issues
                    $user->roles()->detach();

                    // Delete the user
                    $userDeleted = $user->delete();

                    DB::commit();

                    // Log the deletion for debugging
                    Log::info('Patient deleted successfully', [
                        'patient_id' => $id,
                        'user_id' => $patient->user_id,
                        'appointments_deleted' => $appointmentsDeleted,
                        'health_records_deleted' => $healthRecordsDeleted,
                        'patient_deleted' => $patientDeleted,
                        'user_deleted' => $userDeleted,
                        'deleted_by' => $request->user()->id
                    ]);

                    return response()->json(['message' => 'Patient deleted successfully']);
                } catch (\Exception $e) {
                    DB::rollBack();

                    Log::error('Failed to delete patient in transaction', [
                        'patient_id' => $id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    return response()->json([
                        'message' => 'Failed to delete patient: ' . $e->getMessage()
                    ], 500);
                }
            } catch (\Exception $e) {
                Log::error('Failed to find patient for deletion', [
                    'patient_id' => $id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'message' => 'Patient not found or could not be deleted: ' . $e->getMessage()
                ], 404);
            }
        } catch (\Exception $e) {
            // Catch any authentication or permission errors
            Log::error('Authentication or permission error in patient deletion', [
                'patient_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user() ? $request->user()->id : 'none'
            ]);

            return response()->json([
                'message' => 'Authentication error: ' . $e->getMessage()
            ], 401);
        }
    }
}
