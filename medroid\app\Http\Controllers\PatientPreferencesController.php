<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PatientPreferencesController extends Controller
{
    /**
     * Get the patient's appointment preferences
     */
    public function getAppointmentPreferences(Request $request)
    {
        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        return response()->json([
            'appointment_preferences' => $patient->appointment_preferences ?? [
                'preferred_location' => null,
                'preferred_gender' => null,
                'preferred_language' => null,
                'latitude' => null,
                'longitude' => null,
                'search_radius' => '25'
            ]
        ]);
    }

    /**
     * Update the patient's appointment preferences
     */
    public function updateAppointmentPreferences(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'preferred_location' => 'nullable|string',
            'preferred_gender' => 'nullable|string|in:male,female,any',
            'preferred_language' => 'nullable|string',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'search_radius' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        // Get current preferences or initialize if not set
        $appointmentPreferences = $patient->appointment_preferences ?? [
            'preferred_location' => null,
            'preferred_gender' => null,
            'preferred_language' => null,
            'latitude' => null,
            'longitude' => null,
            'search_radius' => '25'
        ];

        // Update only the fields that are provided in the request
        if ($request->has('preferred_location')) {
            $appointmentPreferences['preferred_location'] = $request->preferred_location;
        }

        if ($request->has('preferred_gender')) {
            $appointmentPreferences['preferred_gender'] = $request->preferred_gender;
        }

        if ($request->has('preferred_language')) {
            $appointmentPreferences['preferred_language'] = $request->preferred_language;
        }

        if ($request->has('latitude')) {
            $appointmentPreferences['latitude'] = $request->latitude;
        }

        if ($request->has('longitude')) {
            $appointmentPreferences['longitude'] = $request->longitude;
        }

        if ($request->has('search_radius')) {
            $appointmentPreferences['search_radius'] = $request->search_radius;
        }

        // Save the updated preferences
        $patient->appointment_preferences = $appointmentPreferences;
        $patient->save();

        return response()->json([
            'message' => 'Appointment preferences updated successfully',
            'appointment_preferences' => $patient->appointment_preferences
        ]);
    }
}
