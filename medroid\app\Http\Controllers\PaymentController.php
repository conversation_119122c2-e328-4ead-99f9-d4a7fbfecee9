<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\Payment;
use App\Models\Provider;
use App\Models\Service;
use App\Models\User;
use App\Notifications\AppointmentConfirmedNotification;
use App\Services\CreditService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Stripe;

class PaymentController extends Controller
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }
    /**
     * Create a payment intent for an appointment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createPaymentIntent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'appointment_id' => 'required|exists:appointments,id',
            'amount' => 'required|numeric|min:0.50',
            'currency' => 'required|string|size:3',
            'use_credits' => 'nullable|boolean',
            'credit_amount' => 'nullable|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $appointmentId = $request->appointment_id;
        $amount = $request->amount;
        $currency = strtolower($request->currency);
        $useCredits = $request->use_credits ?? false;
        $creditAmount = $request->credit_amount ?? 0;

        // Get the appointment
        $appointment = Appointment::findOrFail($appointmentId);

        // Check if the user is the patient of this appointment
        if ($appointment->patient->user_id !== $user->id) {
            return response()->json([
                'message' => 'You are not authorized to make a payment for this appointment'
            ], 403);
        }

        // Handle credit usage if requested
        $creditApplied = 0;
        if ($useCredits && $creditAmount > 0) {
            // Check if user has enough credits
            if (!$this->creditService->hasEnoughCredit($user->id, $creditAmount)) {
                return response()->json([
                    'message' => 'Insufficient credit balance',
                    'available_credit' => $this->creditService->getUserCredit($user->id)->balance,
                ], 400);
            }

            // Make sure credit amount is not more than the appointment amount
            if ($creditAmount > $amount) {
                $creditAmount = $amount;
            }

            // Apply the credits
            try {
                $this->creditService->useCredit(
                    $user->id,
                    $creditAmount,
                    'appointment',
                    $appointmentId,
                    "Credit used for appointment #$appointmentId"
                );

                $creditApplied = $creditAmount;

                // Reduce the amount to be charged via Stripe
                $amount = max(0, $amount - $creditAmount);
            } catch (\Exception $e) {
                Log::error("Error applying credits: " . $e->getMessage());
                return response()->json([
                    'message' => 'Error applying credits: ' . $e->getMessage()
                ], 500);
            }
        }

        // Check if the appointment already has a payment intent
        if ($appointment->payment_intent_id) {
            // Retrieve the existing payment intent
            try {
                Stripe::setApiKey(config('services.stripe.secret'));
                $paymentIntent = PaymentIntent::retrieve($appointment->payment_intent_id);

                // If the payment intent is still valid, return it
                if ($paymentIntent->status !== 'canceled' && $paymentIntent->status !== 'succeeded') {
                    return response()->json([
                        'id' => $paymentIntent->id,
                        'client_secret' => $paymentIntent->client_secret,
                        'amount' => $amount,
                        'currency' => $currency,
                    ]);
                }
            } catch (ApiErrorException $e) {
                // If the payment intent doesn't exist anymore, create a new one
                Log::error('Error retrieving payment intent: ' . $e->getMessage());
            }
        }

        // Create a new payment intent
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Convert amount to cents for Stripe
            $amountInCents = (int)($amount * 100);

            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents,
                'currency' => $currency,
                'metadata' => [
                    'appointment_id' => $appointmentId,
                    'user_id' => $user->id,
                ],
            ]);

            // Update the appointment with the payment intent ID
            $appointment->payment_intent_id = $paymentIntent->id;
            $appointment->amount = $amount;
            $appointment->payment_status = 'pending';
            $appointment->save();

            return response()->json([
                'id' => $paymentIntent->id,
                'client_secret' => $paymentIntent->client_secret,
                'amount' => $amount,
                'currency' => $currency,
                'credit_applied' => $creditApplied,
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Error creating payment intent: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error creating payment intent: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm a payment for an appointment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function confirmPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_intent_id' => 'required|string',
            'appointment_id' => 'required|exists:appointments,id',
            'is_web_simulation' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $paymentIntentId = $request->payment_intent_id;
        $appointmentId = $request->appointment_id;
        $isWebSimulation = $request->is_web_simulation ?? false;

        // Log the request for debugging
        Log::info('Payment confirmation request:', [
            'payment_intent_id' => $paymentIntentId,
            'appointment_id' => $appointmentId,
            'is_web_simulation' => $isWebSimulation,
            'user_id' => $user->id,
        ]);

        // Get the appointment
        $appointment = Appointment::findOrFail($appointmentId);

        // Check if the user is the patient of this appointment
        if ($appointment->patient->user_id !== $user->id) {
            return response()->json([
                'message' => 'You are not authorized to confirm payment for this appointment'
            ], 403);
        }

        // Check if the payment intent ID matches the one stored in the appointment
        if ($appointment->payment_intent_id !== $paymentIntentId) {
            return response()->json([
                'message' => 'Payment intent ID does not match the one stored for this appointment'
            ], 400);
        }

        // Web simulation is disabled - all payments must go through real Stripe processing
        if ($isWebSimulation) {
            Log::warning('Web simulation attempted but is disabled. All payments must use real Stripe processing.');
            return response()->json([
                'success' => false,
                'message' => 'Web simulation is disabled. Please use real payment processing.'
            ], 400);
        }

        // For real payments, check with Stripe
        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            // Check if the payment was successful
            if ($paymentIntent->status === 'succeeded') {
                // Check if the slot is still available before confirming
                $provider = $appointment->provider;
                $isAvailable = $provider->isAvailable(
                    $appointment->date->format('Y-m-d'),
                    $appointment->time_slot,
                    $appointment->id // Exclude this appointment from the check
                );

                if (!$isAvailable) {
                    Log::warning('Slot is no longer available for appointment #' . $appointmentId);

                    // Refund the payment since the slot is no longer available
                    try {
                        $refund = \Stripe\Refund::create([
                            'payment_intent' => $paymentIntentId,
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'This time slot is no longer available. Your payment has been refunded. Please choose another time.'
                        ], 400);
                    } catch (\Exception $e) {
                        Log::error('Error refunding payment: ' . $e->getMessage());

                        return response()->json([
                            'success' => false,
                            'message' => 'This time slot is no longer available. Please contact support for a refund.'
                        ], 400);
                    }
                }

                // Update the appointment payment status and change status from pending_payment to scheduled
                $appointment->payment_status = 'paid';
                $appointment->paid_at = now();

                // If the appointment was pending payment, change it to scheduled
                if ($appointment->status === 'pending_payment') {
                    $appointment->status = 'scheduled';
                }

                $appointment->save();

                // Create a payment record
                $payment = Payment::create([
                    'user_id' => $user->id,
                    'appointment_id' => $appointmentId,
                    'payment_id' => $paymentIntentId,
                    'amount' => $appointment->amount,
                    'currency' => $paymentIntent->currency,
                    'payment_method_type' => $paymentIntent->payment_method_types[0] ?? null,
                    'payment_method_details' => $paymentIntent->charges->data[0]->payment_method_details->card->last4 ?? null,
                    'status' => 'succeeded',
                    'description' => 'Payment for appointment #' . $appointmentId,
                    'paid_at' => now(),
                ]);

                // Send payment confirmation notifications
                try {
                    $patient = Patient::findOrFail($appointment->patient_id);
                    $provider = Provider::findOrFail($appointment->provider_id);

                    // Send notification to patient
                    $patient->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        false
                    ));

                    // Send notification to provider
                    $provider->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        true
                    ));

                    Log::info('Payment confirmation notifications sent for Stripe payment', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'amount' => $payment->amount
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send payment confirmation notification for Stripe payment', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'error' => $e->getMessage()
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Payment confirmed successfully',
                    'payment' => $payment,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment has not been completed yet',
                    'status' => $paymentIntent->status,
                ]);
            }
        } catch (ApiErrorException $e) {
            Log::error('Error confirming payment: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error confirming payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process web payment with real Stripe API.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function processWebPayment(Request $request)
    {
        Log::info('Processing web payment request:', [
            'request_data' => $request->all(),
            'user_id' => $request->user()->id ?? 'unknown'
        ]);

        // Check if this is a test request
        $isTest = $request->boolean('test_only', false);

        if ($isTest) {
            // For test requests, only validate card details
            $validator = Validator::make($request->all(), [
                'card_number' => 'required|string',
                'exp_month' => 'required|integer|min:1|max:12',
                'exp_year' => 'required|integer|min:' . date('Y'),
                'cvc' => 'required|string|min:3|max:4',
                'cardholder_name' => 'required|string|max:255',
            ]);
        } else {
            // For regular payment requests, validate everything
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
                'appointment_id' => 'required|exists:appointments,id',
                'card_number' => 'required|string',
                'exp_month' => 'required|integer|min:1|max:12',
                'exp_year' => 'required|integer|min:' . date('Y'),
                'cvc' => 'required|string|min:3|max:4',
                'cardholder_name' => 'required|string|max:255',
            ]);
        }

        if ($validator->fails()) {
            Log::error('Payment validation failed:', [
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $cardNumber = $request->card_number;
        $expMonth = $request->exp_month;
        $expYear = $request->exp_year;
        $cvc = $request->cvc;
        $cardholderName = $request->cardholder_name;

        // Handle test requests differently
        if ($isTest) {
            Log::info('Processing Stripe test request');

            // For test requests, just try to create a payment method
            try {
                Stripe::setApiKey(config('services.stripe.secret'));

                $paymentMethod = \Stripe\PaymentMethod::create([
                    'type' => 'card',
                    'card' => [
                        'number' => $cardNumber,
                        'exp_month' => $expMonth,
                        'exp_year' => $expYear,
                        'cvc' => $cvc,
                    ],
                    'billing_details' => [
                        'name' => $cardholderName,
                    ],
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Stripe connection and card validation successful',
                    'payment_method_id' => $paymentMethod->id,
                    'card_brand' => $paymentMethod->card->brand ?? 'unknown',
                    'card_last4' => $paymentMethod->card->last4 ?? 'unknown',
                    'stripe_mode' => strpos(config('services.stripe.secret'), 'sk_live_') === 0 ? 'live' : 'test',
                ]);
            } catch (\Stripe\Exception\ApiErrorException $e) {
                Log::error('Stripe test failed:', [
                    'message' => $e->getMessage(),
                    'stripe_code' => $e->getStripeCode(),
                    'stripe_type' => $e->getError()->type ?? 'unknown',
                    'stripe_param' => $e->getError()->param ?? 'unknown',
                    'full_error' => $e->getJsonBody(),
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Stripe test failed: ' . $e->getMessage(),
                    'stripe_error' => [
                        'code' => $e->getStripeCode(),
                        'type' => $e->getError()->type ?? 'unknown',
                        'param' => $e->getError()->param ?? 'unknown',
                        'decline_code' => $e->getError()->decline_code ?? null,
                    ]
                ], 400);
            }
        }

        // Regular payment processing
        $paymentIntentId = $request->payment_intent_id;
        $appointmentId = $request->appointment_id;

        // Get the appointment
        $appointment = Appointment::findOrFail($appointmentId);

        // Check if the user is the patient of this appointment
        if ($appointment->patient->user_id !== $user->id) {
            return response()->json([
                'message' => 'You are not authorized to process payment for this appointment'
            ], 403);
        }

        // Check if the payment intent ID matches
        if ($appointment->payment_intent_id !== $paymentIntentId) {
            return response()->json([
                'message' => 'Payment intent ID does not match'
            ], 400);
        }

        // Process real Stripe payment
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            Log::info('Creating Stripe payment method with card details');

            // Create a payment method with the provided card details
            $paymentMethod = \Stripe\PaymentMethod::create([
                'type' => 'card',
                'card' => [
                    'number' => $cardNumber,
                    'exp_month' => $expMonth,
                    'exp_year' => $expYear,
                    'cvc' => $cvc,
                ],
                'billing_details' => [
                    'name' => $cardholderName,
                ],
            ]);

            Log::info('Payment method created successfully', ['payment_method_id' => $paymentMethod->id]);

            // Retrieve the payment intent
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            Log::info('Retrieved payment intent', [
                'payment_intent_id' => $paymentIntentId,
                'status' => $paymentIntent->status,
                'amount' => $paymentIntent->amount
            ]);

            // Confirm the payment intent with the payment method
            $paymentIntent->confirm([
                'payment_method' => $paymentMethod->id,
            ]);

            Log::info('Payment intent confirmed', ['status' => $paymentIntent->status]);

            // Check if payment succeeded
            if ($paymentIntent->status === 'succeeded') {
                // Update appointment status
                $appointment->payment_status = 'paid';
                $appointment->paid_at = now();

                if ($appointment->status === 'pending_payment') {
                    $appointment->status = 'scheduled';
                }
                $appointment->save();

                // Create payment record if it doesn't exist
                $payment = Payment::where('payment_id', $paymentIntentId)->first();
                if (!$payment) {
                    $payment = Payment::create([
                        'user_id' => $user->id,
                        'appointment_id' => $appointment->id,
                        'payment_id' => $paymentIntentId,
                        'amount' => $appointment->amount,
                        'currency' => $paymentIntent->currency,
                        'payment_method_type' => 'card',
                        'payment_method_details' => substr($cardNumber, -4),
                        'status' => 'succeeded',
                        'description' => 'Payment for appointment #' . $appointment->id,
                        'paid_at' => now(),
                    ]);
                }

                // Send notifications
                try {
                    $patient = Patient::findOrFail($appointment->patient_id);
                    $provider = Provider::findOrFail($appointment->provider_id);

                    // Send notification to patient
                    $patient->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        false
                    ));

                    // Send notification to provider
                    $provider->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        true
                    ));

                    Log::info('Payment confirmation notifications sent for web payment', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'amount' => $payment->amount
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send payment confirmation notification for web payment', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'error' => $e->getMessage()
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Payment processed successfully',
                    'payment' => $payment,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment failed',
                    'status' => $paymentIntent->status,
                ], 400);
            }
        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe API error processing web payment:', [
                'message' => $e->getMessage(),
                'stripe_code' => $e->getStripeCode(),
                'stripe_type' => $e->getError()->type ?? 'unknown',
                'stripe_param' => $e->getError()->param ?? 'unknown',
                'payment_intent_id' => $paymentIntentId,
                'appointment_id' => $appointmentId,
                'full_error' => $e->getJsonBody(),
            ]);

            $errorMessage = 'Payment processing failed';

            // Provide specific error messages based on Stripe error codes
            if ($e->getStripeCode() === 'card_declined') {
                $errorMessage = 'Your card was declined. Please try a different card.';
            } elseif ($e->getStripeCode() === 'insufficient_funds') {
                $errorMessage = 'Insufficient funds. Please try a different card.';
            } elseif ($e->getStripeCode() === 'expired_card') {
                $errorMessage = 'Your card has expired. Please try a different card.';
            } elseif ($e->getStripeCode() === 'incorrect_cvc') {
                $errorMessage = 'Incorrect CVC. Please check your card details.';
            } elseif ($e->getStripeCode() === 'processing_error') {
                $errorMessage = 'Processing error. Please try again.';
            } elseif ($e->getStripeCode() === 'invalid_number') {
                $errorMessage = 'Invalid card number. Please check your card details.';
            } elseif ($e->getStripeCode() === 'invalid_expiry_month') {
                $errorMessage = 'Invalid expiry month. Please check your card details.';
            } elseif ($e->getStripeCode() === 'invalid_expiry_year') {
                $errorMessage = 'Invalid expiry year. Please check your card details.';
            } elseif ($e->getStripeCode() === 'invalid_cvc') {
                $errorMessage = 'Invalid CVC. Please check your card details.';
            } else {
                // Include the actual Stripe error for debugging
                $errorMessage = 'Stripe Error: ' . $e->getMessage();
            }

            return response()->json([
                'success' => false,
                'message' => $errorMessage,
                'stripe_error' => [
                    'code' => $e->getStripeCode(),
                    'type' => $e->getError()->type ?? 'unknown',
                    'param' => $e->getError()->param ?? 'unknown',
                    'message' => $e->getMessage(),
                    'decline_code' => $e->getError()->decline_code ?? null,
                ]
            ], 400);
        } catch (\Exception $e) {
            Log::error('Unexpected error processing web payment:', [
                'message' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
                'appointment_id' => $appointmentId,
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unexpected error: ' . $e->getMessage(),
                'error_details' => [
                    'file' => basename($e->getFile()),
                    'line' => $e->getLine(),
                    'type' => get_class($e),
                ]
            ], 500);
        }
    }

    /**
     * Confirm payment processed by Stripe Elements.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function confirmElementsPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_intent_id' => 'required|string',
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            Log::error('Elements payment confirmation validation failed:', [
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $paymentIntentId = $request->payment_intent_id;
        $appointmentId = $request->appointment_id;

        Log::info('Confirming Stripe Elements payment:', [
            'payment_intent_id' => $paymentIntentId,
            'appointment_id' => $appointmentId,
            'user_id' => $user->id,
        ]);

        // Get the appointment
        $appointment = Appointment::findOrFail($appointmentId);

        // Check if the user is the patient of this appointment
        if ($appointment->patient->user_id !== $user->id) {
            return response()->json([
                'message' => 'You are not authorized to confirm payment for this appointment'
            ], 403);
        }

        // Check if the payment intent ID matches
        if ($appointment->payment_intent_id !== $paymentIntentId) {
            return response()->json([
                'message' => 'Payment intent ID does not match'
            ], 400);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Retrieve the payment intent to check its status
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            Log::info('Retrieved payment intent status:', [
                'payment_intent_id' => $paymentIntentId,
                'status' => $paymentIntent->status,
                'amount' => $paymentIntent->amount,
            ]);

            // Check if payment succeeded
            if ($paymentIntent->status === 'succeeded') {
                // Update appointment status
                $appointment->payment_status = 'paid';
                $appointment->paid_at = now();

                if ($appointment->status === 'pending_payment') {
                    $appointment->status = 'scheduled';
                }
                $appointment->save();

                // Create payment record if it doesn't exist
                $payment = Payment::where('payment_id', $paymentIntentId)->first();
                if (!$payment) {
                    $payment = Payment::create([
                        'user_id' => $user->id,
                        'appointment_id' => $appointment->id,
                        'payment_id' => $paymentIntentId,
                        'amount' => $appointment->amount,
                        'currency' => $paymentIntent->currency,
                        'payment_method_type' => 'card',
                        'payment_method_details' => $paymentIntent->charges->data[0]->payment_method_details->card->last4 ?? 'unknown',
                        'status' => 'succeeded',
                        'description' => 'Payment for appointment #' . $appointment->id,
                        'paid_at' => now(),
                    ]);
                }

                // Send notifications
                try {
                    $patient = Patient::findOrFail($appointment->patient_id);
                    $provider = Provider::findOrFail($appointment->provider_id);

                    // Send notification to patient
                    $patient->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        false
                    ));

                    // Send notification to provider
                    $provider->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        true
                    ));

                    Log::info('Payment confirmation notifications sent for Elements payment', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'amount' => $payment->amount
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send payment confirmation notification for Elements payment', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'error' => $e->getMessage()
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Payment confirmed successfully',
                    'payment' => $payment,
                    'appointment' => $appointment,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not completed',
                    'status' => $paymentIntent->status,
                ], 400);
            }
        } catch (ApiErrorException $e) {
            Log::error('Error confirming Elements payment:', [
                'message' => $e->getMessage(),
                'stripe_code' => $e->getStripeCode(),
                'payment_intent_id' => $paymentIntentId,
                'appointment_id' => $appointmentId,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error confirming payment: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            Log::error('Unexpected error confirming Elements payment:', [
                'message' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
                'appointment_id' => $appointmentId,
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test Stripe connection and card validation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function testStripeConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'card_number' => 'required|string',
            'exp_month' => 'required|integer|min:1|max:12',
            'exp_year' => 'required|integer|min:' . date('Y'),
            'cvc' => 'required|string|min:3|max:4',
            'cardholder_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            Log::info('Testing Stripe connection with card details', [
                'card_number_length' => strlen($request->card_number),
                'exp_month' => $request->exp_month,
                'exp_year' => $request->exp_year,
                'cvc_length' => strlen($request->cvc),
                'stripe_key_prefix' => substr(config('services.stripe.secret'), 0, 7),
            ]);

            // Try to create a payment method to test the card
            $paymentMethod = \Stripe\PaymentMethod::create([
                'type' => 'card',
                'card' => [
                    'number' => $request->card_number,
                    'exp_month' => $request->exp_month,
                    'exp_year' => $request->exp_year,
                    'cvc' => $request->cvc,
                ],
                'billing_details' => [
                    'name' => $request->cardholder_name,
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stripe connection and card validation successful',
                'payment_method_id' => $paymentMethod->id,
                'card_brand' => $paymentMethod->card->brand ?? 'unknown',
                'card_last4' => $paymentMethod->card->last4 ?? 'unknown',
                'stripe_mode' => strpos(config('services.stripe.secret'), 'sk_live_') === 0 ? 'live' : 'test',
            ]);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe test failed:', [
                'message' => $e->getMessage(),
                'stripe_code' => $e->getStripeCode(),
                'stripe_type' => $e->getError()->type ?? 'unknown',
                'stripe_param' => $e->getError()->param ?? 'unknown',
                'full_error' => $e->getJsonBody(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Stripe test failed: ' . $e->getMessage(),
                'stripe_error' => [
                    'code' => $e->getStripeCode(),
                    'type' => $e->getError()->type ?? 'unknown',
                    'param' => $e->getError()->param ?? 'unknown',
                    'decline_code' => $e->getError()->decline_code ?? null,
                ]
            ], 400);
        } catch (\Exception $e) {
            Log::error('Unexpected error in Stripe test:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unexpected error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payment history for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getPaymentHistory(Request $request)
    {
        $user = $request->user();
        $payments = Payment::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->with('appointment')
            ->get();

        return response()->json([
            'payments' => $payments
        ]);
    }

    /**
     * Create a payment intent for a new appointment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createAppointmentWithPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:providers,id',
            'service_id' => 'required|exists:services,id',
            'date' => 'required|date|after_or_equal:today',
            'time_slot.start_time' => 'required|string',
            'time_slot.end_time' => 'required|string',
            'reason' => 'required|string',
            'notes' => 'nullable|string',
            'currency' => 'required|string|size:3',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patient = $user->patient;

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        // Get the service to determine the price
        $service = Service::findOrFail($request->service_id);
        $amount = $service->price;

        if (!$amount || $amount <= 0) {
            return response()->json([
                'message' => 'Service price is not set or invalid'
            ], 400);
        }

        $currency = strtolower($request->currency);

        // Create a temporary appointment record with pending status
        $appointmentData = [
            'patient_id' => $patient->id,
            'provider_id' => $request->provider_id,
            'service_id' => $service->id,
            'date' => $request->date,
            'time_slot' => $request->time_slot,
            'reason' => $request->reason,
            'notes' => $request->notes ?? '',
            'status' => 'pending_payment', // Special status for appointments awaiting payment
            'payment_status' => 'unpaid',
            'amount' => $amount,
            'payment_due_by' => now()->addHours(1), // Payment due within 1 hour
            'is_telemedicine' => $service->is_telemedicine ?? false,
        ];

        $appointment = Appointment::create($appointmentData);

        // Create a payment intent
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Convert amount to cents for Stripe
            $amountInCents = (int)($amount * 100);

            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents,
                'currency' => $currency,
                'metadata' => [
                    'appointment_id' => $appointment->id,
                    'user_id' => $user->id,
                    'service_id' => $service->id,
                ],
            ]);

            // Update the appointment with the payment intent ID
            $appointment->payment_intent_id = $paymentIntent->id;
            $appointment->save();

            return response()->json([
                'success' => true,
                'message' => 'Appointment created and awaiting payment',
                'appointment' => $appointment,
                'payment' => [
                    'id' => $paymentIntent->id,
                    'client_secret' => $paymentIntent->client_secret,
                    'amount' => $amount,
                    'currency' => $currency,
                ],
            ], 201);
        } catch (ApiErrorException $e) {
            // Delete the appointment if payment intent creation fails
            $appointment->delete();

            Log::error('Error creating payment intent: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error creating payment intent: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a listing of the payments for management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view payments
        if (!$request->user()->can('view payments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = Payment::with(['user', 'appointment.patient.user', 'appointment.provider.user']);

        // Filter by status if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        // Filter by amount range if provided
        if ($request->has('min_amount') && is_numeric($request->min_amount)) {
            $query->where('amount', '>=', $request->min_amount * 100); // Convert to cents
        }

        if ($request->has('max_amount') && is_numeric($request->max_amount)) {
            $query->where('amount', '<=', $request->max_amount * 100); // Convert to cents
        }

        // Filter by search term if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhereHas('appointment.patient.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('appointment.provider.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Sort by column
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDir = $request->input('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $payments = $query->paginate($perPage);

        return response()->json($payments);
    }

    /**
     * Display the specified payment for management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        // Check if user has permission to view payments
        if (!$request->user()->can('view payments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $payment = Payment::with(['user', 'appointment.patient.user', 'appointment.provider.user', 'appointment.service'])->findOrFail($id);

        return response()->json($payment);
    }

    /**
     * Process a refund for a payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function processRefund(Request $request, $id)
    {
        // Check if user has permission to edit payments
        if (!$request->user()->can('edit payments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $payment = Payment::findOrFail($id);

        // Check if payment can be refunded
        if ($payment->status !== 'succeeded') {
            return response()->json(['message' => 'Only succeeded payments can be refunded'], 422);
        }

        if ($payment->status === 'refunded') {
            return response()->json(['message' => 'Payment has already been refunded'], 422);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'nullable|numeric|min:0.01|max:' . $payment->amount,
            'reason' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Set Stripe API key
        Stripe::setApiKey(config('services.stripe.secret'));

        try {
            // First, check the actual PaymentIntent status in Stripe
            $paymentIntent = PaymentIntent::retrieve($payment->payment_id);

            Log::info('Retrieved PaymentIntent for refund:', [
                'payment_intent_id' => $payment->payment_id,
                'status' => $paymentIntent->status,
                'amount_received' => $paymentIntent->amount_received,
                'charges_count' => count($paymentIntent->charges->data ?? [])
            ]);

            // Check if the PaymentIntent actually has successful charges
            if ($paymentIntent->status !== 'succeeded' || empty($paymentIntent->charges->data)) {
                Log::warning('Cannot refund PaymentIntent - no successful charges found:', [
                    'payment_intent_id' => $payment->payment_id,
                    'status' => $paymentIntent->status,
                    'charges' => $paymentIntent->charges->data ?? []
                ]);

                return response()->json([
                    'message' => 'This payment cannot be refunded because it was not successfully charged through Stripe.'
                ], 422);
            }

            // Process refund through Stripe
            $refundAmount = $request->has('amount') ? $request->amount : $payment->amount;
            $refundAmountInCents = (int)($refundAmount * 100); // Convert to cents for Stripe

            Log::info('Processing Stripe refund:', [
                'payment_id' => $payment->id,
                'stripe_payment_id' => $payment->payment_id,
                'refund_amount' => $refundAmount,
                'refund_amount_cents' => $refundAmountInCents
            ]);

            // For Stripe refunds, we can use payment_intent directly
            $refund = Refund::create([
                'payment_intent' => $payment->payment_id,
                'amount' => $refundAmountInCents,
                'reason' => 'requested_by_customer',
            ]);

            Log::info('Stripe refund created successfully:', [
                'refund_id' => $refund->id,
                'amount' => $refund->amount,
                'status' => $refund->status
            ]);

            // Update payment record
            $payment->update([
                'status' => 'refunded',
            ]);

            // Update associated appointment if needed
            if ($payment->appointment_id) {
                $appointment = Appointment::find($payment->appointment_id);
                if ($appointment) {
                    $appointment->update([
                        'payment_status' => 'refunded',
                    ]);
                }
            }

            return response()->json([
                'message' => 'Refund processed successfully',
                'payment' => $payment,
                'refund' => $refund,
            ]);
        } catch (\Exception $e) {
            Log::error('Error processing refund:', [
                'payment_id' => $payment->id,
                'stripe_payment_id' => $payment->payment_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['message' => 'Error processing refund: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get payment statistics and analytics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics(Request $request)
    {
        // Check if user has permission to view payments
        if (!$request->user()->can('view payments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get date range from request or default to last 30 days
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))
            : Carbon::now()->subDays(30);

        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))
            : Carbon::now();

        // Get total revenue
        $totalRevenue = Payment::whereBetween('payment_history.created_at', [$startDate, $endDate])
            ->where('payment_history.status', 'succeeded')
            ->sum('payment_history.amount');

        // Get revenue by day
        $revenueByDay = Payment::whereBetween('payment_history.created_at', [$startDate, $endDate])
            ->where('payment_history.status', 'succeeded')
            ->selectRaw('DATE(payment_history.created_at) as date, sum(payment_history.amount) as total')
            ->groupBy(DB::raw('DATE(payment_history.created_at)'))
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'total' => $item->total, // Amount is already in dollars
                ];
            });

        // Get payments by status
        $paymentsByStatus = Payment::whereBetween('payment_history.created_at', [$startDate, $endDate])
            ->selectRaw('payment_history.status, count(*) as count, sum(payment_history.amount) as total')
            ->groupBy('payment_history.status')
            ->get()
            ->map(function ($item) {
                return [
                    'status' => $item->status,
                    'count' => $item->count,
                    'total' => $item->total, // Amount is already in dollars
                ];
            });

        // Get top providers by revenue
        $topProvidersByRevenue = Payment::whereBetween('payment_history.created_at', [$startDate, $endDate])
            ->where('payment_history.status', 'succeeded')
            ->join('appointments', 'payment_history.appointment_id', '=', 'appointments.id')
            ->join('providers', 'appointments.provider_id', '=', 'providers.id')
            ->join('users', 'providers.user_id', '=', 'users.id')
            ->selectRaw('providers.id as provider_id, users.name as provider_name, sum(payment_history.amount) as total')
            ->groupBy('providers.id', 'users.name')
            ->orderBy('total', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                return [
                    'provider_id' => $item->provider_id,
                    'provider_name' => $item->provider_name,
                    'total' => $item->total, // Amount is already in dollars
                ];
            });

        // Count payments by status
        $statusCounts = [];
        foreach ($paymentsByStatus as $statusData) {
            $statusCounts[$statusData['status']] = $statusData['count'];
        }

        return response()->json([
            'total_revenue' => number_format($totalRevenue, 2), // Amount is already in dollars
            'revenue_by_day' => $revenueByDay,
            'payments_by_status' => $paymentsByStatus,
            'top_providers_by_revenue' => $topProvidersByRevenue,
            'date_range' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
            // Add these fields for the PaymentList.vue component
            'completed' => $statusCounts['succeeded'] ?? 0,
            'pending' => $statusCounts['pending'] ?? 0,
            'refunded' => $statusCounts['refunded'] ?? 0,
        ]);
    }
}
