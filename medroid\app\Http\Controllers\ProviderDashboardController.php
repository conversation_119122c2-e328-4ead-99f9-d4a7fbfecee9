<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Provider;
use App\Models\Appointment;
use Carbon\Carbon;

class ProviderDashboardController extends Controller
{
    /**
     * Show the provider dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        $provider = Provider::where('user_id', $user->id)->first();
        
        if (!$provider) {
            // If provider profile doesn't exist, redirect to create profile
            return redirect()->route('provider.profile.create')
                ->with('error', 'Please complete your provider profile first.');
        }
        
        // Get today's appointments
        $today = Carbon::today()->format('Y-m-d');
        $todayAppointments = Appointment::where('provider_id', $provider->id)
            ->where('date', $today)
            ->orderBy('time_slot->start_time')
            ->get();
        
        // Get upcoming appointments (excluding today)
        $upcomingAppointments = Appointment::where('provider_id', $provider->id)
            ->where('date', '>', $today)
            ->orderBy('date')
            ->orderBy('time_slot->start_time')
            ->limit(5)
            ->get();
        
        // Get total patients count (unique patients who have had appointments)
        $totalPatients = Appointment::where('provider_id', $provider->id)
            ->distinct('patient_id')
            ->count('patient_id');
        
        // Get monthly revenue
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $endOfMonth = Carbon::now()->endOfMonth()->format('Y-m-d');
        $monthlyRevenue = Appointment::where('provider_id', $provider->id)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_status', 'paid')
            ->sum('amount');
        
        return view('provider.dashboard', [
            'provider' => $provider,
            'todayAppointments' => $todayAppointments,
            'upcomingAppointments' => $upcomingAppointments,
            'totalPatients' => $totalPatients,
            'monthlyRevenue' => $monthlyRevenue,
        ]);
    }

    /**
     * Get provider dashboard data for API calls.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardData()
    {
        $user = Auth::user();
        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json(['error' => 'Provider profile not found'], 404);
        }

        // Get today's appointments
        $today = Carbon::today()->format('Y-m-d');
        $todayAppointments = Appointment::where('provider_id', $provider->id)
            ->where('date', $today)
            ->orderBy('time_slot->start_time')
            ->get();

        // Get upcoming appointments (excluding today)
        $upcomingAppointments = Appointment::where('provider_id', $provider->id)
            ->where('date', '>', $today)
            ->orderBy('date')
            ->orderBy('time_slot->start_time')
            ->limit(5)
            ->get();

        // Get total patients count (unique patients who have had appointments)
        $totalPatients = Appointment::where('provider_id', $provider->id)
            ->distinct('patient_id')
            ->count('patient_id');

        // Get monthly revenue
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $endOfMonth = Carbon::now()->endOfMonth()->format('Y-m-d');
        $monthlyRevenue = Appointment::where('provider_id', $provider->id)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_status', 'paid')
            ->sum('amount');

        // Calculate completion rate
        $totalAppointments = Appointment::where('provider_id', $provider->id)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->count();

        $completedAppointments = Appointment::where('provider_id', $provider->id)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('status', 'completed')
            ->count();

        $completionRate = $totalAppointments > 0 ? round(($completedAppointments / $totalAppointments) * 100) : 0;

        return response()->json([
            'stats' => [
                'totalPatients' => $totalPatients,
                'upcomingAppointments' => $upcomingAppointments->count(),
                'monthlyEarnings' => number_format($monthlyRevenue, 2),
                'completionRate' => $completionRate,
            ],
            'todayAppointments' => $todayAppointments,
            'upcomingAppointments' => $upcomingAppointments,
        ]);
    }
}
