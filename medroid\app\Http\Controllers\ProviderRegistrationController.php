<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Provider;
use App\Notifications\ProviderRegistrationNotification;

class ProviderRegistrationController extends Controller
{
    /**
     * Get provider registration form data (API endpoint).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function showRegistrationForm()
    {
        return response()->json([
            'specializations' => [
                'General Practice',
                'Cardiology',
                'Dermatology',
                'Endocrinology',
                'Gastroenterology',
                'Neurology',
                'Oncology',
                'Orthopedics',
                'Pediatrics',
                'Psychiatry',
                'Radiology',
                'Surgery',
                'Other'
            ],
            'languages' => [
                'English',
                'Spanish',
                'French',
                'German',
                'Italian',
                'Portuguese',
                'Arabic',
                'Chinese',
                'Japanese',
                'Other'
            ]
        ]);
    }

    /**
     * Get registration success data (API endpoint).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function showSuccessPage()
    {
        return response()->json([
            'message' => 'Provider registration submitted successfully',
            'next_steps' => [
                'Your application is under review',
                'You will receive an email confirmation within 24 hours',
                'Our team will contact you if additional information is needed',
                'Once approved, you will receive login credentials'
            ]
        ]);
    }

    /**
     * Handle a provider registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'required|string|max:20',
            'gender' => 'required|in:male,female,other',
            'specialization' => 'required|string',
            'license_number' => 'required|string|unique:providers',
            'years_of_experience' => 'required|integer|min:0|max:50',
            'bio' => 'required|string|min:50|max:1000',
            'languages' => 'nullable|array',
            'languages.*' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Create the user with provider role
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'role' => 'provider',
        ]);

        // Process languages if provided
        $languages = $request->languages ?? [];

        // Create the provider profile
        $provider = Provider::create([
            'user_id' => $user->id,
            'specialization' => $request->specialization,
            'license_number' => $request->license_number,
            'verification_status' => 'pending', // All new providers start as pending
            'gender' => $request->gender,
            'years_of_experience' => $request->years_of_experience,
            'bio' => $request->bio,
            'languages' => $languages,
            'weekly_availability' => [
                [
                    'day' => 'Monday',
                    'slots' => []
                ],
                [
                    'day' => 'Tuesday',
                    'slots' => []
                ],
                [
                    'day' => 'Wednesday',
                    'slots' => []
                ],
                [
                    'day' => 'Thursday',
                    'slots' => []
                ],
                [
                    'day' => 'Friday',
                    'slots' => []
                ],
                [
                    'day' => 'Saturday',
                    'slots' => []
                ],
                [
                    'day' => 'Sunday',
                    'slots' => []
                ]
            ],
            'absences' => [],
            'practice_locations' => [],
        ]);

        // Send provider registration notification
        try {
            $user->notify(new ProviderRegistrationNotification($user, $provider));
            Log::info('Provider registration notification sent', ['user_id' => $user->id, 'email' => $user->email]);
        } catch (\Exception $e) {
            Log::error('Failed to send provider registration notification', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
        }

        // Return success response
        return response()->json([
            'success' => true,
            'message' => 'Provider registration submitted successfully',
            'application_id' => $provider->id,
            'user_id' => $user->id
        ], 201);
    }
}
