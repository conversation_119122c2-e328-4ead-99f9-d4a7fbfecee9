<?php

namespace App\Http\Controllers;

use App\Models\Provider;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ServiceController extends Controller
{
    /**
     * Display a listing of the provider's services.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();

        if ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            $services = $provider->services()->get();

        } else {
            // For patients or other users, filter by provider_id if provided
            if ($request->has('provider_id')) {
                // Get the provider and its weekly availability
                $provider = Provider::find($request->provider_id);
                
                if (!$provider) {
                    return response()->json([
                        'message' => 'Provider not found'
                    ], 404);
                }
                
                // Get the provider's services
                $services = Service::where('provider_id', $request->provider_id)
                    ->where('active', true)
                    ->get();
                
                // Add availability information to each service
                foreach ($services as $service) {
                    // Get the provider's weekly availability
                    $weeklyAvailability = $provider->weekly_availability;
                    
                    // Set the availability on the service
                    $service->availability = $weeklyAvailability;
                }
            } else {
                // Get all active services
                $services = Service::where('active', true)
                    ->with('provider.user')
                    ->get();
                
                // Add availability information to each service
                foreach ($services as $service) {
                    // Get the provider
                    $provider = $service->provider;
                    
                    if ($provider) {
                        // Get the provider's weekly availability
                        $weeklyAvailability = $provider->weekly_availability;
                        
                        // Set the availability on the service
                        $service->availability = $weeklyAvailability;
                    }
                }
            }
        }

        return response()->json($services);
    }

    /**
     * Store a newly created service in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'duration' => 'required|integer|min:5',
            'price' => 'required|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'availability' => 'nullable|array',
            'active' => 'boolean',
            'is_telemedicine' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Only providers can create services'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        $service = Service::create([
            'provider_id' => $provider->id,
            'name' => $request->name,
            'description' => $request->description,
            'duration' => $request->duration,
            'price' => $request->price,
            'category' => $request->category,
            'availability' => $request->availability,
            'active' => $request->has('active') ? $request->active : true,
            'is_telemedicine' => $request->has('is_telemedicine') ? $request->is_telemedicine : false,
        ]);

        return response()->json([
            'message' => 'Service created successfully',
            'service' => $service
        ], 201);
    }

    /**
     * Display the specified service.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $service = Service::with('provider.user')->findOrFail($id);
        
        // Add availability information to the service
        if ($service->provider) {
            // Get the provider's weekly availability
            $weeklyAvailability = $service->provider->weekly_availability;
            
            // Set the availability on the service
            $service->availability = $weeklyAvailability;
        }

        return response()->json($service);
    }

    /**
     * Update the specified service in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'duration' => 'sometimes|required|integer|min:5',
            'price' => 'sometimes|required|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'availability' => 'nullable|array',
            'active' => 'boolean',
            'is_telemedicine' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Only providers can update services'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        $service = Service::findOrFail($id);

        if ($service->provider_id !== $provider->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $service->update($request->all());

        return response()->json([
            'message' => 'Service updated successfully',
            'service' => $service
        ]);
    }

    /**
     * Remove the specified service from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();

        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Only providers can delete services'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        $service = Service::findOrFail($id);

        if ($service->provider_id !== $provider->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if there are any appointments for this service
        $hasAppointments = $service->appointments()->exists();

        if ($hasAppointments) {
            // Instead of deleting, mark as inactive
            $service->update(['active' => false]);

            return response()->json([
                'message' => 'Service has existing appointments and cannot be deleted. It has been marked as inactive.'
            ]);
        }

        $service->delete();

        return response()->json([
            'message' => 'Service deleted successfully'
        ]);
    }

    /**
     * Get services by category.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getByCategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $services = Service::where('category', $request->category)
            ->where('active', true)
            ->with('provider.user')
            ->get();
            
        // Add availability information to each service
        foreach ($services as $service) {
            // Get the provider
            $provider = $service->provider;
            
            if ($provider) {
                // Get the provider's weekly availability
                $weeklyAvailability = $provider->weekly_availability;
                
                // Set the availability on the service
                $service->availability = $weeklyAvailability;
            }
        }

        return response()->json($services);
    }

    /**
     * Get all service categories.
     *
     * @return \Illuminate\Http\Response
     */
    public function getCategories()
    {
        $categories = Service::where('active', true)
            ->distinct()
            ->pluck('category')
            ->filter()
            ->values();

        return response()->json(['categories' => $categories]);
    }
}
