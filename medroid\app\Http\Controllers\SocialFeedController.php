<?php

namespace App\Http\Controllers;

use App\Models\SocialContent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SocialFeedController extends Controller
{
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $preferredTopics = [];

            $query = SocialContent::with(['user', 'comments'])->where('filtered_status', 'approved');

            // Only show internal posts (user-generated content)
            $query->where('source', 'internal');

            if ($request->has('content_type')) {
                $query->where('content_type', $request->content_type);
            }

            // Sort by relevance or recency
            $sortBy = $request->get('sort_by', 'relevance');
            if ($sortBy === 'relevance') {
                $query->orderBy('relevance_score', 'desc');
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $content = $query->paginate(10);

            // Add user-specific interaction data
            $content->each(function ($item) use ($user) {
                try {
                    $item->liked = $item->likes()->where('user_id', $user->id)->exists();
                    $item->saved = $item->saves()->where('user_id', $user->id)->exists();

                    // Add user permissions for this post
                    $item->can_edit = $item->user_id === $user->id;
                    $item->can_delete = $item->user_id === $user->id;

                // Update engagement metrics with actual comments count
                $engagementMetrics = $item->engagement_metrics ?? [];
                $engagementMetrics['comments'] = $item->comments->count();
                $item->engagement_metrics = $engagementMetrics;

                    // For internal posts without a user relationship, add the current user's information
                    if ($item->source === 'internal' && !$item->user) {
                        $item->user = [
                            'id' => $user->id,
                            'name' => $user->name,
                            'avatar' => $user->profile_image ?? null
                        ];
                    } else if ($item->user) {
                        // Format the user data consistently
                        $item->user = [
                            'id' => $item->user->id,
                            'name' => $item->user->name,
                            'avatar' => $item->user->profile_image ?? null
                        ];
                    }
                } catch (\Exception $e) {
                    // If there's an issue with a specific item, just skip the processing
                    \Log::error('Error processing feed item: ' . $e->getMessage());
                }
            });

            return response()->json($content);
        } catch (\Exception $e) {
            \Log::error('Error in feed index: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading feed',
                'error' => $e->getMessage(),
                'data' => [],
                'current_page' => 1,
                'last_page' => 1,
                'next_page_url' => null,
                'prev_page_url' => null,
                'total' => 0
            ], 500);
        }
    }

    public function topics()
    {
        // Get most popular health topics from internal posts only
        $topics = SocialContent::where('filtered_status', 'approved')
            ->where('source', 'internal') // Only consider internal posts
            ->get()
            ->pluck('health_topics')
            ->flatten()
            ->countBy()
            ->sortDesc()
            ->take(20)
            ->keys();

        return response()->json(['topics' => $topics]);
    }

    public function saveContent(Request $request, $contentId)
    {
        $user = $request->user();
        $content = SocialContent::findOrFail($contentId);

        // Toggle save status
        if ($content->saves()->where('user_id', $user->id)->exists()) {
            $content->saves()->detach($user->id);
            $saved = false;
        } else {
            $content->saves()->attach($user->id);
            $saved = true;
        }

        return response()->json([
            'message' => $saved ? 'Content saved' : 'Content unsaved',
            'saved' => $saved
        ]);
    }

    public function likeContent(Request $request, $contentId)
    {
        $user = $request->user();
        $content = SocialContent::findOrFail($contentId);

        // Toggle like status
        if ($content->likes()->where('user_id', $user->id)->exists()) {
            $content->likes()->detach($user->id);
            $liked = false;
        } else {
            $content->likes()->attach($user->id);
            $liked = true;
        }

        // Update engagement metrics
        $likeCount = $content->likes()->count();
        $engagementMetrics = $content->engagement_metrics;
        $engagementMetrics['likes'] = $likeCount;
        $content->engagement_metrics = $engagementMetrics;
        $content->save();

        return response()->json([
            'message' => $liked ? 'Content liked' : 'Content unliked',
            'liked' => $liked,
            'like_count' => $likeCount
        ]);
    }

    /**
     * Create a new social post with media.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'caption' => 'required|string|max:1000',
            'health_topics' => 'required|json',
            'source' => 'required|string|in:internal',
            'media' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = $request->user();

            // Parse health topics from JSON
            $healthTopics = json_decode($request->health_topics, true);

            // Default values
            $mediaUrl = null;
            $contentType = 'text';

            // Handle media upload if provided
            if ($request->hasFile('media')) {
                $file = $request->file('media');
                $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();

                // Make sure the directory exists
                $directory = storage_path('app/public/social_media');
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                }

                // Store the file in the public disk under social_media folder
                $path = $file->storeAs('social_media', $filename, 'public');

                // Log the file path for debugging
                \Illuminate\Support\Facades\Log::info('Social media image stored', [
                    'path' => $path,
                    'full_path' => storage_path('app/public/' . $path),
                    'url' => Storage::url($path)
                ]);

                // Make sure the URL is properly formatted for the frontend
                $mediaUrl = url(Storage::url($path));
                $contentType = 'image';

                // Log the final URL for debugging
                \Illuminate\Support\Facades\Log::info('Final media URL', [
                    'url' => $mediaUrl
                ]);
            }

            // Create the social content
            $content = SocialContent::create([
                'source' => 'internal',
                'source_id' => (string) Str::uuid(),
                'content_type' => $contentType,
                'media_url' => $mediaUrl,
                'caption' => $request->caption,
                'health_topics' => $healthTopics,
                'relevance_score' => 1.0, // Default high relevance for user-created content
                'engagement_metrics' => [
                    'likes' => 0,
                    'shares' => 0,
                    'saves' => 0,
                    'comments' => 0,
                ],
                'filtered_status' => 'approved', // Auto-approve user content for now
                'user_id' => $user->id, // Store the user ID with the post
            ]);

            return response()->json([
                'message' => 'Post created successfully',
                'post' => $content
            ], 201);

        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error creating post: ' . $e->getMessage());

            return response()->json([
                'message' => 'Failed to create post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a social post.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $contentId
     * @return \Illuminate\Http\Response
     */
    public function deleteContent(Request $request, $contentId)
    {
        try {
            $user = $request->user();
            $content = SocialContent::findOrFail($contentId);

            // Check if the user is the owner of the post
            if ($content->user_id !== $user->id) {
                return response()->json([
                    'message' => 'You are not authorized to delete this post'
                ], 403);
            }

            // If the post has a media file, delete it from storage
            if ($content->media_url && $content->source === 'internal') {
                // Extract the filename from the URL
                $path = parse_url($content->media_url, PHP_URL_PATH);
                if ($path) {
                    $relativePath = str_replace('/storage/', '', $path);
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    }
                }
            }

            // Delete the post
            $content->delete();

            return response()->json([
                'message' => 'Post deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error deleting post: ' . $e->getMessage());

            return response()->json([
                'message' => 'Failed to delete post: ' . $e->getMessage()
            ], 500);
        }
    }
}