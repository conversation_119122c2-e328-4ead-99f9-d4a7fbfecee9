<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserCredit;
use App\Models\CreditTransaction;
use App\Services\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UserCreditController extends Controller
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    /**
     * Get the current user's credit balance
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCreditBalance(Request $request)
    {
        $user = Auth::user();
        $credit = $this->creditService->getUserCredit($user->id);

        return response()->json([
            'balance' => $credit->balance,
            'total_earned' => $credit->total_earned,
            'total_used' => $credit->total_used,
        ]);
    }

    /**
     * Get the current user's credit transactions
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCreditTransactions(Request $request)
    {
        $user = Auth::user();
        $limit = $request->input('limit', 20);
        $transactions = $this->creditService->getUserTransactions($user->id, $limit);

        return response()->json([
            'transactions' => $transactions,
        ]);
    }

    /**
     * Use credits for a payment
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function useCredits(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $amount = $request->input('amount');
        $appointmentId = $request->input('appointment_id');

        // Check if user has enough credit
        if (!$this->creditService->hasEnoughCredit($user->id, $amount)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient credit balance.',
            ], 400);
        }

        try {
            // Use the credit
            $transaction = $this->creditService->useCredit(
                $user->id,
                $amount,
                'appointment',
                $appointmentId,
                "Credit used for appointment #$appointmentId"
            );

            return response()->json([
                'success' => true,
                'message' => 'Credits applied successfully.',
                'transaction' => $transaction,
                'remaining_balance' => $this->creditService->getUserCredit($user->id)->balance,
            ]);
        } catch (\Exception $e) {
            Log::error("Error using credits: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply credits. Please try again.',
            ], 500);
        }
    }

    /**
     * Add credits to a user (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addCredits(Request $request)
    {
        // Check if user has permission to manage credits
        // For admin panel, we'll allow any admin user to add credits
        $user = Auth::user();
        if (!$user || (!$user->can('manage credits') && $user->role !== 'admin')) {
            Log::warning('Unauthorized attempt to add credits', [
                'user_id' => $user ? $user->id : null,
                'role' => $user ? $user->role : null,
                'ip' => $request->ip()
            ]);
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'source' => 'required|string',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            Log::warning('Validation failed when adding credits', [
                'errors' => $validator->errors()->toArray(),
                'input' => $request->all()
            ]);
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $userId = $request->input('user_id');
        $amount = $request->input('amount');
        $source = $request->input('source');
        $description = $request->input('description');

        // Log the attempt
        Log::info('Adding credits to user', [
            'admin_id' => $user->id,
            'user_id' => $userId,
            'amount' => $amount,
            'source' => $source,
            'description' => $description
        ]);

        try {
            // Verify the user exists
            $targetUser = User::find($userId);
            if (!$targetUser) {
                Log::error("User not found when adding credits: {$userId}");
                return response()->json([
                    'success' => false,
                    'message' => 'User not found.',
                ], 404);
            }

            // Add the credits
            $transaction = $this->creditService->addCredit(
                $userId,
                $amount,
                $source,
                null,
                $description ?? "Credit added by admin"
            );

            // Get the updated balance
            $newBalance = $this->creditService->getUserCredit($userId)->balance;

            Log::info('Credits added successfully', [
                'admin_id' => $user->id,
                'user_id' => $userId,
                'amount' => $amount,
                'transaction_id' => $transaction->id,
                'new_balance' => $newBalance
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Credits added successfully.',
                'transaction' => $transaction,
                'new_balance' => $newBalance,
            ]);
        } catch (\Exception $e) {
            Log::error("Error adding credits: " . $e->getMessage(), [
                'admin_id' => $user->id,
                'user_id' => $userId,
                'amount' => $amount,
                'source' => $source,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to add credits. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all credit transactions (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllCreditTransactions(Request $request)
    {
        // Check if user has permission to view credits
        if (!Auth::user()->can('view credits')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = CreditTransaction::with('user');

        // Filter by user if provided
        if ($request->has('user_id')) {
            $query->where('user_id', $request->input('user_id'));
        }

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }

        // Filter by source if provided
        if ($request->has('source')) {
            $query->where('source', $request->input('source'));
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [
                $request->input('start_date'),
                $request->input('end_date'),
            ]);
        }

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $transactions = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($transactions);
    }
}
