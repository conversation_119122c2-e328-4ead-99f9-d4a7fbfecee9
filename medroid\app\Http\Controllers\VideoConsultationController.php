<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Services\VideoSessionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class VideoConsultationController extends Controller
{
    protected $videoSessionService;

    public function __construct(VideoSessionService $videoSessionService)
    {
        $this->videoSessionService = $videoSessionService;
    }

    /**
     * Check if a telemedicine appointment is ready and initialize it if needed
     */
    public function checkAppointmentStatus(Request $request, $appointmentId)
    {
        $validator = Validator::make(['appointment_id' => $appointmentId], [
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $appointment = Appointment::findOrFail($appointmentId);
        $user = $request->user();

        // Check if the user is authorized to access this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            return response()->json([
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        // Check if this is a telemedicine appointment
        if (!$appointment->isTelemedicine()) {
            return response()->json([
                'message' => 'This is not a telemedicine appointment'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'is_telemedicine' => true,
            'is_ready' => $appointment->isVideoSessionReady(),
            'status' => $appointment->status,
        ]);
    }

    /**
     * Initialize a video consultation for an appointment
     */
    public function initializeSession(Request $request, $appointmentId)
    {
        $validator = Validator::make(['appointment_id' => $appointmentId], [
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $appointment = Appointment::findOrFail($appointmentId);
        $user = $request->user();

        // Check if the user is authorized to access this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            return response()->json([
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        // Check if this is a telemedicine appointment
        if (!$appointment->isTelemedicine()) {
            return response()->json([
                'message' => 'This is not a telemedicine appointment'
            ], 400);
        }

        // Create a new video session using the new architecture
        $result = $this->videoSessionService->createSession($appointment, $user);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to initialize video session'
            ], 500);
        }

        Log::info('Video session initialized successfully', [
            'appointment_id' => $appointment->id,
            'session_id' => $result['session']->session_id,
            'created_by' => $user->id,
        ]);

        // Immediately join the session as the provider
        $joinResult = $this->videoSessionService->joinSession($appointment, $user);

        if (!$joinResult['success']) {
            return response()->json([
                'success' => false,
                'message' => $joinResult['message'] ?? 'Failed to join video session'
            ], 500);
        }

        Log::info('Video session initialized and provider joined successfully', [
            'appointment_id' => $appointment->id,
            'session_id' => $result['session']->session_id,
            'created_by' => $user->id,
            'provider_uid' => $joinResult['participant']->agora_uid,
        ]);

        return response()->json([
            'success' => true,
            'session_id' => $result['session']->session_id,
            'channel_name' => $result['session']->channel_name,
            'status' => $joinResult['session']->status,
            'message' => $result['message'],
            'participants' => $joinResult['session']->current_participants,
            'session_data' => $joinResult['session_data'],
            'user_role' => $joinResult['participant']->role,
        ]);
    }

    /**
     * End a video consultation session
     */
    public function endSession(Request $request, $appointmentId)
    {
        $validator = Validator::make(['appointment_id' => $appointmentId], [
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $appointment = Appointment::findOrFail($appointmentId);
        $user = $request->user();

        // Check if the user is authorized to access this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            return response()->json([
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        // End the video session using the new architecture
        $result = $this->videoSessionService->endSession($appointment, $user);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to end video session'
            ], 500);
        }

        // End consultation tracking if it was started
        if ($appointment->consultation_started_at !== null && $appointment->consultation_ended_at === null) {
            $appointment->endConsultation();

            // Calculate duration
            $duration = $appointment->getConsultationDuration();
            $formattedDuration = $appointment->getFormattedConsultationDuration();

            // Log consultation end
            Log::info('Video consultation ended', [
                'appointment_id' => $appointment->id,
                'started_at' => $appointment->consultation_started_at,
                'ended_at' => $appointment->consultation_ended_at,
                'duration_minutes' => $duration,
                'formatted_duration' => $formattedDuration,
                'user_role' => $this->getUserRole($user, $appointment)
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Video session ended successfully',
            'consultation_started_at' => $appointment->consultation_started_at,
            'consultation_ended_at' => $appointment->consultation_ended_at,
            'consultation_duration' => $appointment->getFormattedConsultationDuration(),
        ]);
    }

    /**
     * Leave a video session (participant leaves but session continues)
     */
    public function leaveSession(Request $request, $appointmentId)
    {
        $user = $request->user();
        $appointment = Appointment::findOrFail($appointmentId);

        // Check if user is authorized for this appointment
        if (!$this->isUserAuthorizedForAppointment($user, $appointment)) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        // Leave the video session
        $result = $this->videoSessionService->leaveSession($appointment, $user);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to leave video session'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Left video session successfully'
        ]);
    }

    /**
     * Get session status and participant information
     */
    public function getSessionStatus(Request $request, $appointmentId)
    {
        $user = $request->user();
        $appointment = Appointment::findOrFail($appointmentId);

        // Check if user is authorized for this appointment
        if (!$this->isUserAuthorizedForAppointment($user, $appointment)) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        $videoConsultation = $appointment->videoConsultation;

        if (!$videoConsultation) {
            return response()->json([
                'success' => false,
                'message' => 'No video session found for this appointment'
            ], 404);
        }

        $participants = $videoConsultation->participants()->with('user')->get();

        return response()->json([
            'success' => true,
            'session' => [
                'id' => $videoConsultation->session_id,
                'status' => $videoConsultation->status,
                'channel_name' => $videoConsultation->channel_name,
                'current_participants' => $videoConsultation->current_participants,
                'max_participants' => $videoConsultation->max_participants,
                'started_at' => $videoConsultation->started_at,
                'ended_at' => $videoConsultation->ended_at,
            ],
            'participants' => $participants->map(function ($participant) {
                // Safely convert agora_uid to integer, handle edge cases
                return [
                    'user_id' => $participant->user_id,
                    'agora_uid' => (int) $participant->agora_uid,
                    'user_name' => $participant->user->name,
                    'role' => $participant->role,
                    'status' => $participant->status,
                    'joined_at' => $participant->joined_at,
                    'left_at' => $participant->left_at,
                ];
            }),
        ]);
    }

    /**
     * Mark a participant as disconnected when frontend detects they're not in Agora
     */
    public function markParticipantDisconnected(Request $request, $appointmentId)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $appointment = Appointment::findOrFail($appointmentId);
        $currentUser = $request->user();
        $disconnectedUserId = $request->input('user_id');

        // Check if current user is authorized for this appointment
        if (!$this->canAccessAppointment($currentUser, $appointment)) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        $videoConsultation = $appointment->videoConsultation;
        if (!$videoConsultation) {
            return response()->json([
                'success' => false,
                'message' => 'No video session found for this appointment'
            ], 404);
        }

        // Find the participant to mark as disconnected
        $participant = $videoConsultation->participants()
            ->where('user_id', $disconnectedUserId)
            ->first();

        if (!$participant) {
            return response()->json([
                'success' => false,
                'message' => 'Participant not found in this session'
            ], 404);
        }

        // Only update if they're currently marked as joined
        if ($participant->status === 'joined') {
            $participant->disconnect();

            \Log::info('Participant marked as disconnected due to Agora sync issue', [
                'appointment_id' => $appointmentId,
                'disconnected_user_id' => $disconnectedUserId,
                'agora_uid' => $participant->agora_uid,
                'reported_by' => $currentUser->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Participant marked as disconnected',
                'participant_status' => $participant->status
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Participant was already disconnected',
            'participant_status' => $participant->status
        ]);
    }

    /**
     * Check if user is authorized for appointment (helper method)
     */
    protected function isUserAuthorizedForAppointment($user, Appointment $appointment)
    {
        return $this->canAccessAppointment($user, $appointment);
    }

    /**
     * Join a video consultation session
     */
    public function joinSession(Request $request, $appointmentId)
    {
        $appointment = Appointment::findOrFail($appointmentId);
        $user = $request->user();

        // Check if the user is authorized to access this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            return abort(403, 'You are not authorized to access this appointment');
        }

        // Check if this is a telemedicine appointment
        if (!$appointment->isTelemedicine()) {
            return abort(400, 'This is not a telemedicine appointment');
        }

        // Join the video session using the new architecture
        $result = $this->videoSessionService->joinSession($appointment, $user);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to join video session'
            ], 500);
        }

        // Start consultation tracking if not already started
        if ($appointment->consultation_started_at === null) {
            $appointment->startConsultation();

            // Log consultation start
            Log::info('Video consultation started', [
                'appointment_id' => $appointment->id,
                'started_at' => $appointment->consultation_started_at,
                'user_role' => $result['participant']->role
            ]);
        }

        Log::info('User joined video session successfully', [
            'appointment_id' => $appointment->id,
            'session_id' => $result['session']->session_id,
            'user_id' => $user->id,
            'role' => $result['participant']->role,
            'agora_uid' => $result['participant']->agora_uid,
        ]);

        // Return session data as JSON for Vue.js frontend
        return response()->json([
            'success' => true,
            'appointment' => $appointment,
            'sessionData' => $result['session_data'],
            'userRole' => $result['participant']->role,
            'userName' => $user->name,
            'provider' => 'agora',
            'session' => [
                'id' => $result['session']->session_id,
                'status' => $result['session']->status,
                'participants' => $result['session']->current_participants,
            ]
        ]);
    }





    /**
     * Check if a user can access an appointment
     */
    protected function canAccessAppointment($user, Appointment $appointment)
    {
        // Admin and manager can access any appointment
        if (in_array($user->role, ['admin', 'manager'])) {
            return true;
        }

        if ($user->role === 'patient') {
            $patient = $user->patient;
            return $patient && $patient->id === $appointment->patient_id;
        }

        if ($user->role === 'provider') {
            $provider = $user->provider;
            return $provider && $provider->id === $appointment->provider_id;
        }

        // For testing purposes, allow access if user has permission to view appointments
        if ($user->can('view appointments')) {
            return true;
        }

        return false;
    }

    /**
     * Get the user's role for this appointment (patient or provider)
     */
    protected function getUserRole($user, Appointment $appointment)
    {
        if ($user->role === 'patient') {
            return 'patient';
        }

        if ($user->role === 'provider') {
            return 'provider';
        }

        // For admin/manager, determine role based on appointment context
        if (in_array($user->role, ['admin', 'manager'])) {
            // Check if they have a provider profile
            if ($user->provider) {
                return 'provider';
            }
            // Check if they have a patient profile
            if ($user->patient) {
                return 'patient';
            }
            // Default to provider for admin users
            return 'provider';
        }

        // Default to patient for other roles
        return 'patient';
    }

    /**
     * Get session data for a video consultation (using new architecture)
     */
    public function getSessionData(Request $request, $appointmentId)
    {
        $validator = Validator::make(['appointment_id' => $appointmentId], [
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $appointment = Appointment::findOrFail($appointmentId);
        $user = $request->user();

        // Check if the user is authorized to access this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            return response()->json([
                'message' => 'You are not authorized to access this appointment'
            ], 403);
        }

        // Check if this is a telemedicine appointment
        if (!$appointment->isTelemedicine()) {
            return response()->json([
                'message' => 'This is not a telemedicine appointment'
            ], 400);
        }

        // Use the new video session service to join the session
        $result = $this->videoSessionService->joinSession($appointment, $user);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to get session data'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'session_data' => $result['session_data'],
            'user_role' => $result['participant']->role,
            'user_name' => $user->name,
            'session' => [
                'id' => $result['session']->session_id,
                'status' => $result['session']->status,
                'participants' => $result['session']->current_participants,
            ]
        ]);
    }






}
