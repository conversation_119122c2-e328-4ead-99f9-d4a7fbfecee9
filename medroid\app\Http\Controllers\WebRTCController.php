<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\VideoConsultation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class WebRTCController extends Controller
{
    /**
     * Initialize WebRTC session for an appointment
     */
    public function initializeSession(Request $request, $appointmentId)
    {
        try {
            $user = Auth::user();
            $appointment = Appointment::findOrFail($appointmentId);

            // Verify user has access to this appointment
            if (!$this->userCanAccessAppointment($user, $appointment)) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Check if session already exists
            $videoConsultation = VideoConsultation::where('appointment_id', $appointmentId)->first();
            
            if (!$videoConsultation) {
                // Create new video consultation session
                $sessionId = 'webrtc-' . Str::uuid();
                
                $videoConsultation = VideoConsultation::create([
                    'appointment_id' => $appointmentId,
                    'session_id' => $sessionId,
                    'token' => Str::random(32),
                    'url' => config('app.url') . "/video/session/{$sessionId}",
                    'started_at' => now(),
                ]);
            }

            // Generate ICE servers configuration
            $iceServers = $this->getIceServers();

            return response()->json([
                'success' => true,
                'session_id' => $videoConsultation->session_id,
                'token' => $videoConsultation->token,
                'ice_servers' => $iceServers,
                'signaling_url' => config('app.url') . "/api/webrtc/signaling/{$videoConsultation->session_id}",
                'appointment' => [
                    'id' => $appointment->id,
                    'patient_name' => $appointment->patient->user->name,
                    'provider_name' => $appointment->provider->user->name,
                    'scheduled_at' => $appointment->scheduled_at,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('WebRTC session initialization failed', [
                'appointment_id' => $appointmentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to initialize video session'
            ], 500);
        }
    }

    /**
     * Handle WebRTC signaling (offer, answer, ICE candidates)
     */
    public function handleSignaling(Request $request, $sessionId)
    {
        try {
            $user = Auth::user();
            $videoConsultation = VideoConsultation::where('session_id', $sessionId)->firstOrFail();
            $appointment = $videoConsultation->appointment;

            // Verify user has access
            if (!$this->userCanAccessAppointment($user, $appointment)) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $type = $request->input('type');
            $data = $request->input('data');
            $targetUserId = $request->input('target_user_id');

            // Store signaling data in cache for real-time delivery
            $signalingKey = "webrtc_signaling_{$sessionId}_{$targetUserId}";
            
            cache()->put($signalingKey, [
                'type' => $type,
                'data' => $data,
                'from_user_id' => $user->id,
                'timestamp' => now()->toISOString()
            ], 300); // 5 minutes TTL

            // Log signaling for debugging
            Log::info('WebRTC signaling', [
                'session_id' => $sessionId,
                'type' => $type,
                'from_user' => $user->id,
                'to_user' => $targetUserId
            ]);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('WebRTC signaling failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Signaling failed'], 500);
        }
    }

    /**
     * Poll for signaling messages
     */
    public function pollSignaling(Request $request, $sessionId)
    {
        try {
            $user = Auth::user();
            $videoConsultation = VideoConsultation::where('session_id', $sessionId)->firstOrFail();
            $appointment = $videoConsultation->appointment;

            // Verify user has access
            if (!$this->userCanAccessAppointment($user, $appointment)) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Get signaling messages for this user
            $signalingKey = "webrtc_signaling_{$sessionId}_{$user->id}";
            $message = cache()->pull($signalingKey); // Get and remove

            if ($message) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => null
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Polling failed'], 500);
        }
    }

    /**
     * End video consultation session
     */
    public function endSession(Request $request, $sessionId)
    {
        try {
            $user = Auth::user();
            $videoConsultation = VideoConsultation::where('session_id', $sessionId)->firstOrFail();
            $appointment = $videoConsultation->appointment;

            // Verify user has access
            if (!$this->userCanAccessAppointment($user, $appointment)) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Update session end time
            $videoConsultation->update([
                'ended_at' => now()
            ]);

            // Update appointment status
            $appointment->update([
                'status' => 'completed',
                'consultation_ended_at' => now()
            ]);

            Log::info('WebRTC session ended', [
                'session_id' => $sessionId,
                'appointment_id' => $appointment->id,
                'ended_by' => $user->id
            ]);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('Failed to end WebRTC session', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to end session'], 500);
        }
    }

    /**
     * Get ICE servers configuration
     */
    private function getIceServers()
    {
        return [
            ['urls' => 'stun:stun.l.google.com:19302'],
            ['urls' => 'stun:stun1.l.google.com:19302'],
            ['urls' => 'stun:stun2.l.google.com:19302'],
            // Add TURN servers if available
            // [
            //     'urls' => 'turn:your-turn-server.com:3478',
            //     'username' => 'your-username',
            //     'credential' => 'your-password'
            // ]
        ];
    }

    /**
     * Check if user can access the appointment
     */
    private function userCanAccessAppointment($user, $appointment)
    {
        return $appointment->patient->user_id === $user->id || 
               $appointment->provider->user_id === $user->id;
    }
}
