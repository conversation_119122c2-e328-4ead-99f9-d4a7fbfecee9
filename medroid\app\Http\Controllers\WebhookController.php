<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Payment;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Notifications\AppointmentConfirmedNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Cashier\Http\Controllers\WebhookController as CashierController;

class WebhookController extends CashierController
{
    /**
     * Handle a Stripe webhook call.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handleWebhook(Request $request)
    {
        // Skip webhook verification for local development
        if (env('STRIPE_WEBHOOK_VERIFY', true) === false || env('APP_ENV') === 'local') {
            Log::info('Webhook verification skipped for local development');

            $payload = json_decode($request->getContent(), true);

            if (!$payload || !isset($payload['type'])) {
                Log::error('Invalid webhook payload received');
                return response('Invalid payload', 400);
            }

            // Handle the webhook event based on type
            $eventType = $payload['type'];

            // Map event types to method names
            $eventMethods = [
                'payment_intent.succeeded' => 'handlePaymentIntentSucceeded',
                'payment_intent.payment_failed' => 'handlePaymentIntentPaymentFailed',
                'payment_intent.canceled' => 'handlePaymentIntentCanceled',
            ];

            if (isset($eventMethods[$eventType]) && method_exists($this, $eventMethods[$eventType])) {
                Log::info('Handling webhook event: ' . $eventType);
                return $this->{$eventMethods[$eventType]}($payload);
            } else {
                Log::info('Unhandled webhook event: ' . $eventType);
                return $this->successMethod();
            }
        }

        // For production, use the parent method with verification
        return parent::handleWebhook($request);
    }
    /**
     * Handle payment intent succeeded.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handlePaymentIntentSucceeded(array $payload)
    {
        $paymentIntent = $payload['data']['object'];
        $paymentIntentId = $paymentIntent['id'];

        Log::info('Payment intent succeeded: ' . $paymentIntentId);

        // Check if we have an appointment with this payment intent
        $appointment = Appointment::where('payment_intent_id', $paymentIntentId)->first();

        if ($appointment) {
            // Update the appointment payment status
            $appointment->payment_status = 'paid';
            $appointment->paid_at = now();

            // If the appointment was pending payment, change it to scheduled
            if ($appointment->status === 'pending_payment') {
                $appointment->status = 'scheduled';
            }

            $appointment->save();

            // Check if we already have a payment record
            $payment = Payment::where('payment_id', $paymentIntentId)->first();

            if (!$payment) {
                // Create a payment record
                $userId = $appointment->patient->user_id;

                $payment = Payment::create([
                    'user_id' => $userId,
                    'appointment_id' => $appointment->id,
                    'payment_id' => $paymentIntentId,
                    'amount' => $appointment->amount,
                    'currency' => $paymentIntent['currency'],
                    'payment_method_type' => $paymentIntent['payment_method_types'][0] ?? null,
                    'payment_method_details' => $paymentIntent['charges']['data'][0]['payment_method_details']['card']['last4'] ?? null,
                    'status' => 'succeeded',
                    'description' => 'Payment for appointment #' . $appointment->id,
                    'paid_at' => now(),
                ]);

                // Send payment confirmation notifications
                try {
                    $patient = Patient::findOrFail($appointment->patient_id);
                    $provider = Provider::findOrFail($appointment->provider_id);

                    // Send notification to patient
                    $patient->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        false
                    ));

                    // Send notification to provider
                    $provider->user->notify(new AppointmentConfirmedNotification(
                        $appointment,
                        $payment,
                        true
                    ));

                    Log::info('Payment confirmation notifications sent', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'amount' => $payment->amount
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send payment confirmation notification', [
                        'appointment_id' => $appointment->id,
                        'payment_id' => $payment->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        return $this->successMethod();
    }

    /**
     * Handle payment intent payment failed.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handlePaymentIntentPaymentFailed(array $payload)
    {
        $paymentIntent = $payload['data']['object'];
        $paymentIntentId = $paymentIntent['id'];

        Log::info('Payment intent failed: ' . $paymentIntentId);

        // Check if we have an appointment with this payment intent
        $appointment = Appointment::where('payment_intent_id', $paymentIntentId)->first();

        if ($appointment) {
            // Update the appointment payment status
            $appointment->payment_status = 'failed';
            $appointment->save();
        }

        return $this->successMethod();
    }

    /**
     * Handle payment intent canceled.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handlePaymentIntentCanceled(array $payload)
    {
        $paymentIntent = $payload['data']['object'];
        $paymentIntentId = $paymentIntent['id'];

        Log::info('Payment intent canceled: ' . $paymentIntentId);

        // Check if we have an appointment with this payment intent
        $appointment = Appointment::where('payment_intent_id', $paymentIntentId)->first();

        if ($appointment) {
            // Update the appointment payment status
            $appointment->payment_status = 'unpaid';
            $appointment->payment_intent_id = null;
            $appointment->save();
        }

        return $this->successMethod();
    }
}
