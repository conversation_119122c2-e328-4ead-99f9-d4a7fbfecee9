<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * This is a temporary replacement for FetchSocialContentJob
 * that does nothing but log that social media fetching is disabled.
 * 
 * This allows us to temporarily disable social media integration
 * without breaking any code that might be dispatching these jobs.
 */
class DisabledFetchSocialContentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $source;
    protected $limit;
    
    /**
     * Create a new job instance.
     */
    public function __construct($source = 'all', $limit = 20)
    {
        $this->source = $source;
        $this->limit = $limit;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Social media fetching is temporarily disabled. Skipping fetch for source: {$this->source}");
    }
}
