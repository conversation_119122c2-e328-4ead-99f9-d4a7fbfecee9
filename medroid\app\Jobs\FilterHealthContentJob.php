<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * This is a temporary replacement for FilterHealthContentJob
 * that does nothing but log that social media content filtering is disabled.
 * 
 * This allows us to temporarily disable social media integration
 * without breaking any code that might be dispatching these jobs.
 */
class DisabledFilterHealthContentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $source;
    protected $content;
    
    /**
     * Create a new job instance.
     */
    public function __construct($source, $content)
    {
        $this->source = $source;
        $this->content = $content;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Social media content filtering is temporarily disabled. Skipping filter for source: {$this->source}");
    }
}
