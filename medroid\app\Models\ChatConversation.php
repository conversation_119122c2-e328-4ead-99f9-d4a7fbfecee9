<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ChatConversation extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'patient_id',
        'patient_email',
        'title',
        'messages',
        'health_concerns',
        'recommendations',
        'escalated',
        'escalation_reason',
        'referral_note',
        'is_anonymous',
        'anonymous_id',
        'is_public',
        'shared_at',
    ];

    protected $casts = [
        'messages' => 'array',
        'health_concerns' => 'array',
        'recommendations' => 'array',
        'escalated' => 'boolean',
        'is_anonymous' => 'boolean',
        'is_public' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'shared_at' => 'datetime',
    ];

    /**
     * Add a message to the conversation.
     *
     * @param array $message
     * @return bool
     */
    public function addMessage(array $message)
    {
        try {
            $messages = $this->messages ?? [];
            $messages[] = $message;
            $this->messages = $messages;
            return $this->save();
        } catch (\Exception $e) {
            Log::error("Error adding message to conversation {$this->id}: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Generate a title for the conversation based on the first user message.
     *
     * @return string
     */
    public function generateTitle()
    {
        try {
            if (empty($this->messages)) {
                return 'New Conversation';
            }

            // Find the first user message
            $firstUserMessage = null;
            foreach ($this->messages as $message) {
                if ($message['role'] === 'user') {
                    $firstUserMessage = $message['content'];
                    break;
                }
            }

            if (!$firstUserMessage) {
                return 'Health Conversation';
            }

            // Create a simple title from the first user message
            return strlen($firstUserMessage) > 50
                ? substr($firstUserMessage, 0, 47) . '...'
                : $firstUserMessage;
        } catch (\Exception $e) {
            Log::error("Error generating title for conversation {$this->id}: {$e->getMessage()}");
            return 'Health Conversation';
        }
    }

    /**
     * Generate a summary of the conversation for sharing to the feed.
     *
     * @return string
     */
    public function generateSummary()
    {
        try {
            if (empty($this->messages)) {
                return 'No content to summarize.';
            }

            // Extract the first few messages (up to 3) to create a summary
            $summaryContent = '';
            $messageCount = 0;

            foreach ($this->messages as $message) {
                if ($messageCount >= 3) break;

                if ($message['role'] === 'user') {
                    $content = $message['content'];
                    // Limit each message to 100 characters
                    if (strlen($content) > 100) {
                        $content = substr($content, 0, 97) . '...';
                    }
                    $summaryContent .= "Question: {$content}\n\n";
                    $messageCount++;
                } else if ($message['role'] === 'assistant' && $messageCount > 0) {
                    $content = $message['content'];
                    // Limit each response to 200 characters
                    if (strlen($content) > 200) {
                        $content = substr($content, 0, 197) . '...';
                    }
                    $summaryContent .= "Answer: {$content}\n\n";
                    $messageCount++;
                }
            }

            if (empty($summaryContent)) {
                return 'Health conversation shared from Medroid.';
            }

            return $summaryContent . "\nRead more in the Medroid app.";
        } catch (\Exception $e) {
            Log::error("Error generating summary for conversation {$this->id}: {$e->getMessage()}");
            return 'Health conversation shared from Medroid.';
        }
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }
}