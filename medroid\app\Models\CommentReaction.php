<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommentReaction extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'comment_reactions';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'comment_id',
        'user_id',
        'reaction_type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Available reaction types.
     */
    const REACTION_TYPES = [
        'like',
        'love',
        'laugh',
        'angry',
        'sad',
        'wow'
    ];

    /**
     * Get the comment that this reaction belongs to.
     */
    public function comment()
    {
        return $this->belongsTo(SocialContentComment::class, 'comment_id');
    }

    /**
     * Get the user who made this reaction.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by reaction type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('reaction_type', $type);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
