<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DiagnosticFeedback extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'provider_id',
        'patient_id',
        'appointment_id',
        'chat_conversation_id',
        'ai_diagnoses',
        'ai_diagnosis',
        'provider_diagnosis',
        'is_accurate',
        'feedback_notes',
    ];

    protected $casts = [
        'ai_diagnoses' => 'array',
        'is_accurate' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the provider that provided the feedback.
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the patient associated with this feedback.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the appointment associated with this feedback.
     */
    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the chat conversation associated with this feedback.
     */
    public function chatConversation()
    {
        return $this->belongsTo(ChatConversation::class);
    }
}
