<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'subject',
        'content',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get a template by its slug.
     *
     * @param string $slug
     * @return \App\Models\EmailTemplate|null
     */
    public static function findBySlug(string $slug)
    {
        // First try to find an active template by slug
        $template = static::where('slug', $slug)->where('is_active', true)->first();

        // If not found, try to find any template by slug (even inactive ones)
        if (!$template) {
            $template = static::where('slug', $slug)->first();
        }

        return $template;
    }
}
