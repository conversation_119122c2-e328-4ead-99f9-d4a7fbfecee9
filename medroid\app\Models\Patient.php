<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Patient extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'user_id',
        'gender',
        'date_of_birth',
        'health_history',
        'allergies',
        'medications',
        'medical_conditions',
        'preferences',
        'appointment_preferences',
        'communication_preferences',
        'insurance_provider',
        'insurance_policy_number',
        'insurance_expiry_date',
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_relationship',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'health_history' => 'array',
        'allergies' => 'array',
        'medications' => 'array',
        'medical_conditions' => 'array',
        'preferences' => 'array',
        'appointment_preferences' => 'array',
        'communication_preferences' => 'array',
        'insurance_expiry_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function chatConversations()
    {
        return $this->hasMany(ChatConversation::class);
    }

    /**
     * Get the health records for the patient.
     */
    public function healthRecords()
    {
        return $this->hasMany(HealthRecord::class);
    }
}