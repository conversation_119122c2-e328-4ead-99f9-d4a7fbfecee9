<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PersonalAccessToken extends Model
{
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'token',
        'user_id',
        'abilities',
        'name',
        'expires_at',
        'last_used_at',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'abilities' => 'array',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that the token belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Find the token instance matching the given token.
     *
     * @param  string  $token
     * @return static|null
     */
    public static function findToken($token)
    {
        return static::where('token', hash('sha256', $token))->first();
    }

    /**
     * Create a new personal access token for the user.
     *
     * @param  \App\Models\User  $user
     * @param  string  $name
     * @param  array  $abilities
     * @param  \DateTimeInterface|null  $expiresAt
     * @return static
     */
    public static function createToken($user, $name, array $abilities = ['*'], $expiresAt = null)
    {
        $plainTextToken = Str::random(40);

        $token = $user->tokens()->create([
            'token' => hash('sha256', $plainTextToken),
            'name' => $name,
            'abilities' => $abilities,
            'expires_at' => $expiresAt,
        ]);

        return [
            'accessToken' => $plainTextToken,
            'token' => $token,
        ];
    }

    /**
     * Determine if the token has a given ability.
     *
     * @param  string|array  $ability
     * @param  array|mixed  $arguments
     * @return bool
     */
    public function can($ability, $arguments = [])
    {
        // First check if the token has the wildcard ability
        if (in_array('*', $this->abilities)) {
            return true;
        }

        // If it's a string, check if it's in the token's abilities
        if (is_string($ability) && in_array($ability, $this->abilities)) {
            return true;
        }

        // If it's an array, check if any of the abilities are in the token's abilities
        if (is_array($ability)) {
            foreach ($ability as $a) {
                if (in_array($a, $this->abilities)) {
                    return true;
                }
            }
        }

        // If the token doesn't have the ability, delegate to the user if available
        $user = $this->user;
        if ($user) {
            return $user->can($ability, $arguments);
        }

        return false;
    }

    /**
     * Determine if the token is missing a given ability.
     *
     * @param  string|array  $ability
     * @param  array|mixed  $arguments
     * @return bool
     */
    public function cant($ability, $arguments = [])
    {
        return ! $this->can($ability, $arguments);
    }
}
