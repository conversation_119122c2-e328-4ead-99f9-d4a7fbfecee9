<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Referral extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'referrer_id',
        'referred_id',
        'referral_code',
        'email',
        'status',
        'credit_awarded',
        'credit_amount',
        'completed_at',
        'expired_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'credit_amount' => 'decimal:2',
        'credit_awarded' => 'boolean',
        'completed_at' => 'datetime',
        'expired_at' => 'datetime',
    ];

    /**
     * Get the user who referred (the referrer).
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the user who was referred.
     */
    public function referred()
    {
        return $this->belongsTo(User::class, 'referred_id');
    }

    /**
     * Get the credit transaction associated with this referral.
     */
    public function creditTransaction()
    {
        return $this->morphOne(CreditTransaction::class, 'reference');
    }
}
