<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'provider_id',
        'name',
        'description',
        'duration',
        'price',
        'category',
        'availability',
        'active',
        'is_telemedicine',
        'supports_video',
        'supports_audio',
        'supports_chat',
        'discount_percentage',
        'discount_valid_until',
    ];

    protected $casts = [
        'duration' => 'integer',
        'price' => 'decimal:2',
        'availability' => 'array',
        'active' => 'boolean',
        'is_telemedicine' => 'boolean',
        'supports_video' => 'boolean',
        'supports_audio' => 'boolean',
        'supports_chat' => 'boolean',
        'discount_percentage' => 'decimal:2',
        'discount_valid_until' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function provider()
    {
        return $this->belongsTo(Provider::class, 'provider_id', 'id');
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class, 'service_id', 'id');
    }
}
