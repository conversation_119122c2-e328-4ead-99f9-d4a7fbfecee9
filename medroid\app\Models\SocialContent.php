<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SocialContent extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'source',
        'source_id',
        'content_type',
        'media_url',
        'caption',
        'health_topics',
        'relevance_score',
        'engagement_metrics',
        'filtered_status',
        'created_at',
        'user_id',
    ];

    protected $casts = [
        'health_topics' => 'array',
        'engagement_metrics' => 'array',
        'relevance_score' => 'decimal:2',
        'created_at' => 'datetime',
    ];

    public function likes()
    {
        return $this->belongsToMany(User::class, 'content_likes', 'content_id', 'user_id');
    }

    public function saves()
    {
        return $this->belongsToMany(User::class, 'content_saves', 'content_id', 'user_id');
    }

    /**
     * Get the user that created this content.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the comments for this social content.
     */
    public function comments()
    {
        return $this->hasMany(SocialContentComment::class);
    }

    /**
     * Get the top-level comments (not replies) for this social content.
     */
    public function topLevelComments()
    {
        return $this->hasMany(SocialContentComment::class)->whereNull('parent_id');
    }
}