<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SocialContentComment extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'social_content_comments';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'social_content_id',
        'user_id',
        'content',
        'parent_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'time_ago',
        'reaction_counts',
        'user_reaction',
        'can_edit',
        'can_delete'
    ];

    /**
     * Get the social content that this comment belongs to.
     */
    public function socialContent()
    {
        return $this->belongsTo(SocialContent::class);
    }

    /**
     * Get the user who made this comment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent comment (for replies).
     */
    public function parent()
    {
        return $this->belongsTo(SocialContentComment::class, 'parent_id');
    }

    /**
     * Get the replies to this comment.
     */
    public function replies()
    {
        return $this->hasMany(SocialContentComment::class, 'parent_id');
    }

    /**
     * Get the reactions for this comment.
     */
    public function reactions()
    {
        return $this->hasMany(CommentReaction::class, 'comment_id');
    }

    /**
     * Scope to get only top-level comments (not replies).
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Get formatted time ago.
     */
    public function getTimeAgoAttribute()
    {
        $now = now();
        $diff = $this->created_at->diff($now);

        if ($diff->y > 0) {
            return $diff->y . 'y';
        } elseif ($diff->m > 0) {
            return $diff->m . 'mo';
        } elseif ($diff->d > 0) {
            return $diff->d . 'd';
        } elseif ($diff->h > 0) {
            return $diff->h . 'h';
        } elseif ($diff->i > 0) {
            return $diff->i . 'm';
        } else {
            return 'now';
        }
    }

    /**
     * Get reaction counts grouped by type.
     */
    public function getReactionCountsAttribute()
    {
        return $this->reactions()
            ->selectRaw('reaction_type, COUNT(*) as count')
            ->groupBy('reaction_type')
            ->pluck('count', 'reaction_type')
            ->toArray();
    }

    /**
     * Get current user's reaction to this comment.
     */
    public function getUserReactionAttribute()
    {
        $user = auth()->user();
        if (!$user) {
            return null;
        }

        $reaction = $this->reactions()
            ->where('user_id', $user->id)
            ->first();

        return $reaction ? $reaction->reaction_type : null;
    }

    /**
     * Check if current user can edit this comment.
     */
    public function getCanEditAttribute()
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // User can edit their own comments within 24 hours
        return $this->user_id === $user->id &&
               $this->created_at->diffInHours(now()) <= 24;
    }

    /**
     * Check if current user can delete this comment.
     */
    public function getCanDeleteAttribute()
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // User can delete their own comments or admin can delete any
        return $this->user_id === $user->id || $user->hasRole('admin');
    }
}
