<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Story extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'user_id',
        'media_url',
        'media_type',
        'caption',
        'expires_at',
        'is_active',
        'viewers',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'viewers' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who created this story.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only active stories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get stories for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if the story has expired.
     */
    public function getIsExpiredAttribute()
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if a user has viewed this story.
     */
    public function hasBeenViewedBy($userId)
    {
        $viewers = $this->viewers ?? [];
        return in_array($userId, $viewers);
    }

    /**
     * Mark story as viewed by a user.
     */
    public function markAsViewedBy($userId)
    {
        $viewers = $this->viewers ?? [];
        if (!in_array($userId, $viewers)) {
            $viewers[] = $userId;
            $this->viewers = $viewers;
            $this->save();
        }
    }

    /**
     * Get the number of viewers.
     */
    public function getViewersCountAttribute()
    {
        return count($this->viewers ?? []);
    }

    /**
     * Get formatted time remaining.
     */
    public function getTimeRemainingAttribute()
    {
        if ($this->is_expired) {
            return 'Expired';
        }

        $diff = now()->diff($this->expires_at);

        if ($diff->h > 0) {
            return $diff->h . 'h ' . $diff->i . 'm';
        } else {
            return $diff->i . 'm';
        }
    }

    /**
     * Automatically set expiration time to 24 hours from creation.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($story) {
            if (!$story->expires_at) {
                $story->expires_at = Carbon::now()->addHours(24);
            }
        });
    }

    /**
     * Get formatted media URL.
     */
    public function getFormattedMediaUrlAttribute()
    {
        if (empty($this->media_url)) {
            return '';
        }

        // If the URL already starts with http:// or https://, return it as is
        if (str_starts_with($this->media_url, 'http://') || str_starts_with($this->media_url, 'https://')) {
            return $this->media_url;
        }

        // If the URL starts with /storage, return it as is (let frontend handle the base URL)
        if (str_starts_with($this->media_url, '/storage')) {
            return $this->media_url;
        }

        // Otherwise, prepend /storage/
        return '/storage/' . $this->media_url;
    }
}
