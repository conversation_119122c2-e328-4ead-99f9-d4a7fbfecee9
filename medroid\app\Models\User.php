<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        "name",
        "email",
        "password",
        "role",
        "phone_number",
        "date_of_birth",
        "is_active",
        "email_verified_at",
        "profile_image",
        "referral_code",
        "referred_by",
        "is_founder_member",
        "total_points",
        "activity_streak",
        "last_activity_at",
        "club_memberships",
        "sso_provider",
        "sso_provider_id",
        "sso_access_token",
        "sso_refresh_token",
        "sso_token_expires_at",
        "sso_profile_data",
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        "password",
        "remember_token",
        "sso_access_token",
        "sso_refresh_token",
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            "email_verified_at" => "datetime",
            "date_of_birth" => "date",
            "is_active" => "boolean",
            "is_founder_member" => "boolean",
            "last_activity_at" => "datetime",
            "club_memberships" => "array",
            "sso_token_expires_at" => "datetime",
            "sso_profile_data" => "array",
            'password' => 'hashed',
        ];
    }

    /**
     * Get the patient profile associated with the user.
     */
    public function patient()
    {
        return $this->hasOne(Patient::class);
    }

    /**
     * Get the provider profile associated with the user.
     */
    public function provider()
    {
        return $this->hasOne(Provider::class);
    }
}
