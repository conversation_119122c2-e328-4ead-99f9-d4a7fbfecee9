<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserBadge extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'badge_type',
        'badge_name',
        'badge_description',
        'badge_icon',
        'badge_color',
        'points_awarded',
        'earned_at',
        'criteria_met',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'earned_at' => 'datetime',
        'criteria_met' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the badge.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for founder badges
     */
    public function scopeFounder($query)
    {
        return $query->where('badge_type', 'founder');
    }

    /**
     * Get badge icon URL
     */
    public function getIconUrlAttribute(): string
    {
        if ($this->badge_icon) {
            return asset('storage/' . $this->badge_icon);
        }

        // Default icons based on badge type
        $defaultIcons = [
            'founder' => 'badges/founder.svg',
            'early_adopter' => 'badges/early-adopter.svg',
            'active_user' => 'badges/active-user.svg',
            'referrer' => 'badges/referrer.svg',
            'chat_master' => 'badges/chat-master.svg',
            'appointment_pro' => 'badges/appointment-pro.svg',
        ];

        return asset('images/' . ($defaultIcons[$this->badge_type] ?? 'badges/default.svg'));
    }

    /**
     * Get formatted earned date
     */
    public function getFormattedEarnedDateAttribute(): string
    {
        return $this->earned_at->format('M j, Y');
    }
}
