<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserCredit extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'balance',
        'total_earned',
        'total_used',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'balance' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_used' => 'decimal:2',
    ];

    /**
     * Get the user that owns the credits.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the credit transactions for this user credit.
     */
    public function transactions()
    {
        return $this->hasMany(CreditTransaction::class, 'user_id', 'user_id');
    }
}
