<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPoint extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'activity_type',
        'points_earned',
        'source',
        'reference_type',
        'reference_id',
        'earned_date',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'earned_date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the points.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the referenced model (polymorphic relationship)
     */
    public function reference()
    {
        return $this->morphTo('reference', 'reference_type', 'reference_id');
    }

    /**
     * Scope for specific activity type
     */
    public function scopeActivityType($query, $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('earned_date', [$startDate, $endDate]);
    }

    /**
     * Get points summary for a user
     */
    public static function getUserPointsSummary($userId)
    {
        return self::where('user_id', $userId)
            ->selectRaw('
                activity_type,
                SUM(points_earned) as total_points,
                COUNT(*) as activity_count,
                MAX(earned_date) as last_earned
            ')
            ->groupBy('activity_type')
            ->get();
    }
}
