<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoConsultation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'appointment_id',
        'session_id',
        'channel_name',
        'agora_app_id',
        'status',
        'created_by',
        'started_at',
        'ended_at',
        'max_participants',
        'current_participants',
        'session_config',
        'has_recording',
        'recording_path',
        'recording_started_at',
        'recording_ended_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'has_recording' => 'boolean',
        'recording_started_at' => 'datetime',
        'recording_ended_at' => 'datetime',
        'session_config' => 'array',
    ];

    /**
     * Get the appointment that owns the video consultation.
     */
    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the user who created the session.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the participants of the video consultation.
     */
    public function participants()
    {
        return $this->hasMany(VideoSessionParticipant::class);
    }

    /**
     * Get active participants.
     */
    public function activeParticipants()
    {
        return $this->participants()->where('status', 'joined');
    }

    /**
     * Check if session is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if session can accept new participants.
     */
    public function canAcceptParticipants()
    {
        // Recalculate current participants to ensure accuracy
        $this->recalculateParticipantCount();

        return $this->status === 'created' ||
               ($this->status === 'active' && $this->current_participants < $this->max_participants);
    }

    /**
     * Recalculate and update the current participant count based on actual joined participants.
     */
    public function recalculateParticipantCount()
    {
        $actualCount = $this->participants()->where('status', 'joined')->count();

        if ($this->current_participants !== $actualCount) {
            \Log::info('Participant count mismatch detected', [
                'session_id' => $this->session_id,
                'stored_count' => $this->current_participants,
                'actual_count' => $actualCount,
            ]);

            $this->update(['current_participants' => $actualCount]);
        }

        return $actualCount;
    }

    /**
     * Start the session.
     */
    public function start()
    {
        $this->update([
            'status' => 'active',
            'started_at' => now(),
        ]);
    }

    /**
     * End the session.
     */
    public function end()
    {
        $this->update([
            'status' => 'ended',
            'ended_at' => now(),
        ]);

        // Update all participants to left status
        $this->participants()->where('status', 'joined')->update([
            'status' => 'left',
            'left_at' => now(),
        ]);
    }

    /**
     * Mark session as abandoned.
     */
    public function abandon()
    {
        $this->update([
            'status' => 'abandoned',
            'ended_at' => now(),
        ]);
    }

    /**
     * Get the transcription for the video consultation.
     */
    public function transcription()
    {
        return $this->hasOne(Transcription::class);
    }
}
