<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoSessionParticipant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'video_consultation_id',
        'user_id',
        'agora_uid',
        'agora_token',
        'role',
        'status',
        'joined_at',
        'left_at',
        'token_expires_at',
        'connection_info',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'joined_at' => 'datetime',
        'left_at' => 'datetime',
        'token_expires_at' => 'datetime',
        'connection_info' => 'array',
    ];

    /**
     * Get the video consultation that owns the participant.
     */
    public function videoConsultation()
    {
        return $this->belongsTo(VideoConsultation::class);
    }

    /**
     * Get the user that owns the participant.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if participant is currently joined.
     */
    public function isJoined()
    {
        return $this->status === 'joined';
    }

    /**
     * Check if token is expired.
     */
    public function isTokenExpired()
    {
        return $this->token_expires_at && $this->token_expires_at->isPast();
    }

    /**
     * Join the session.
     */
    public function join()
    {
        // Only increment if not already joined to prevent double counting
        $wasAlreadyJoined = $this->isJoined();

        $this->update([
            'status' => 'joined',
            'joined_at' => now(),
        ]);

        // Update participant count in video consultation only if not already joined
        if (!$wasAlreadyJoined) {
            $this->videoConsultation->increment('current_participants');
        }
    }

    /**
     * Leave the session.
     */
    public function leave()
    {
        // Only decrement if currently joined to prevent double decrementing
        $wasJoined = $this->isJoined();

        $this->update([
            'status' => 'left',
            'left_at' => now(),
        ]);

        // Update participant count in video consultation only if was joined
        if ($wasJoined) {
            $this->videoConsultation->decrement('current_participants');
        }
    }

    /**
     * Mark as disconnected.
     */
    public function disconnect()
    {
        // Only decrement if currently joined to prevent double decrementing
        $wasJoined = $this->isJoined();

        $this->update([
            'status' => 'disconnected',
            'left_at' => now(),
        ]);

        // Update participant count in video consultation only if was joined
        if ($wasJoined) {
            $this->videoConsultation->decrement('current_participants');
        }
    }

    /**
     * Refresh the Agora token.
     */
    public function refreshToken($newToken, $expiresAt)
    {
        $this->update([
            'agora_token' => $newToken,
            'token_expires_at' => $expiresAt,
        ]);
    }
}
