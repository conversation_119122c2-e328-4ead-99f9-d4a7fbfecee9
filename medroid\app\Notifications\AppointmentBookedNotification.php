<?php

namespace App\Notifications;

use App\Models\Appointment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Carbon\Carbon;

class AppointmentBookedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The appointment instance.
     *
     * @var \App\Models\Appointment
     */
    protected $appointment;

    /**
     * Whether this notification is for the provider.
     *
     * @var bool
     */
    protected $isForProvider;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Appointment  $appointment
     * @param  bool  $isForProvider
     * @return void
     */
    public function __construct(Appointment $appointment, bool $isForProvider = false)
    {
        $this->appointment = $appointment;
        $this->isForProvider = $isForProvider;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $date = Carbon::parse($this->appointment->date)->format('l, F j, Y');
        $startTime = $this->appointment->time_slot['start_time'];
        $endTime = $this->appointment->time_slot['end_time'];
        
        $subject = $this->isForProvider 
            ? 'New Appointment Booked' 
            : 'Your Appointment Has Been Booked';
        
        $view = $this->isForProvider 
            ? 'emails.appointment-booked-provider' 
            : 'emails.appointment-booked-patient';
            
        return (new MailMessage)
            ->subject($subject)
            ->view($view, [
                'appointment' => $this->appointment,
                'date' => $date,
                'startTime' => $startTime,
                'endTime' => $endTime,
                'patient' => $this->appointment->patient->user,
                'provider' => $this->appointment->provider->user,
                'paymentStatus' => $this->appointment->payment_status,
                'appointmentStatus' => $this->appointment->status,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'appointment_id' => $this->appointment->id,
            'date' => $this->appointment->date,
            'time_slot' => $this->appointment->time_slot,
            'status' => $this->appointment->status,
            'payment_status' => $this->appointment->payment_status,
        ];
    }
}
