<?php

namespace App\Notifications;

use App\Models\Appointment;
use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Carbon\Carbon;

class AppointmentConfirmedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The appointment instance.
     *
     * @var \App\Models\Appointment
     */
    protected $appointment;

    /**
     * The payment instance.
     *
     * @var \App\Models\Payment|null
     */
    protected $payment;

    /**
     * Whether this notification is for the provider.
     *
     * @var bool
     */
    protected $isForProvider;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Appointment  $appointment
     * @param  \App\Models\Payment|null  $payment
     * @param  bool  $isForProvider
     * @return void
     */
    public function __construct(Appointment $appointment, Payment $payment = null, bool $isForProvider = false)
    {
        $this->appointment = $appointment;
        $this->payment = $payment;
        $this->isForProvider = $isForProvider;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $date = Carbon::parse($this->appointment->date)->format('l, F j, Y');
        $startTime = $this->appointment->time_slot['start_time'];
        $endTime = $this->appointment->time_slot['end_time'];
        
        $subject = $this->isForProvider 
            ? 'Appointment Payment Confirmed' 
            : 'Your Appointment Payment Has Been Confirmed';
        
        $view = $this->isForProvider 
            ? 'emails.appointment-confirmed-provider' 
            : 'emails.appointment-confirmed-patient';
            
        return (new MailMessage)
            ->subject($subject)
            ->view($view, [
                'appointment' => $this->appointment,
                'payment' => $this->payment,
                'date' => $date,
                'startTime' => $startTime,
                'endTime' => $endTime,
                'patient' => $this->appointment->patient->user,
                'provider' => $this->appointment->provider->user,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'appointment_id' => $this->appointment->id,
            'payment_id' => $this->payment ? $this->payment->id : null,
            'date' => $this->appointment->date,
            'time_slot' => $this->appointment->time_slot,
            'status' => $this->appointment->status,
            'payment_status' => $this->appointment->payment_status,
            'amount' => $this->payment ? $this->payment->amount : $this->appointment->amount,
        ];
    }
}
