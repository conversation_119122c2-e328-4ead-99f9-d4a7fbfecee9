<?php

namespace App\Notifications;

use App\Models\Appointment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Carbon\Carbon;

class AppointmentReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The appointment instance.
     *
     * @var \App\Models\Appointment
     */
    protected $appointment;

    /**
     * Whether this notification is for the provider.
     *
     * @var bool
     */
    protected $isForProvider;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Appointment  $appointment
     * @param  bool  $isForProvider
     * @return void
     */
    public function __construct(Appointment $appointment, bool $isForProvider = false)
    {
        $this->appointment = $appointment;
        $this->isForProvider = $isForProvider;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the array representation of the notification for database storage.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        $date = Carbon::parse($this->appointment->date)->format('l, F j, Y');
        $startTime = $this->appointment->time_slot['start_time'];

        $title = $this->isForProvider
            ? 'Reminder: Upcoming Appointment Tomorrow'
            : 'Reminder: Your Appointment is Tomorrow';

        $body = $this->isForProvider
            ? "You have an appointment with {$this->appointment->patient->user->name} tomorrow at {$startTime}"
            : "You have an appointment with Dr. {$this->appointment->provider->user->name} tomorrow at {$startTime}";

        // After creating the database notification, send a push notification
        app(\App\Services\NotificationService::class)->sendPushNotification(
            $notifiable,
            $title,
            $body,
            'appointment_reminder',
            [
                'appointment_id' => $this->appointment->id,
                'date' => $this->appointment->date,
                'time_slot' => $this->appointment->time_slot,
            ]
        );

        return [
            'title' => $title,
            'body' => $body,
            'type' => 'appointment_reminder',
            'data' => [
                'appointment_id' => $this->appointment->id,
                'date' => $this->appointment->date,
                'time_slot' => $this->appointment->time_slot,
            ],
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $date = Carbon::parse($this->appointment->date)->format('l, F j, Y');
        $startTime = $this->appointment->time_slot['start_time'];
        $endTime = $this->appointment->time_slot['end_time'];

        $subject = $this->isForProvider
            ? 'Reminder: Upcoming Appointment Tomorrow'
            : 'Reminder: Your Appointment is Tomorrow';

        $view = $this->isForProvider
            ? 'emails.appointment-reminder-provider'
            : 'emails.appointment-reminder-patient';

        return (new MailMessage)
            ->subject($subject)
            ->view($view, [
                'appointment' => $this->appointment,
                'date' => $date,
                'startTime' => $startTime,
                'endTime' => $endTime,
                'patient' => $this->appointment->patient->user,
                'provider' => $this->appointment->provider->user,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'appointment_id' => $this->appointment->id,
            'date' => $this->appointment->date,
            'time_slot' => $this->appointment->time_slot,
            'status' => $this->appointment->status,
        ];
    }
}
