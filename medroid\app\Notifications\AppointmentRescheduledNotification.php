<?php

namespace App\Notifications;

use App\Models\Appointment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Carbon\Carbon;

class AppointmentRescheduledNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The appointment instance.
     *
     * @var \App\Models\Appointment
     */
    protected $appointment;

    /**
     * The old date.
     *
     * @var string
     */
    protected $oldDate;

    /**
     * The old time slot.
     *
     * @var array
     */
    protected $oldTimeSlot;

    /**
     * Whether this notification is for the provider.
     *
     * @var bool
     */
    protected $isForProvider;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Appointment  $appointment
     * @param  string  $oldDate
     * @param  array  $oldTimeSlot
     * @param  bool  $isForProvider
     * @return void
     */
    public function __construct(Appointment $appointment, string $oldDate, array $oldTimeSlot, bool $isForProvider = false)
    {
        $this->appointment = $appointment;
        $this->oldDate = $oldDate;
        $this->oldTimeSlot = $oldTimeSlot;
        $this->isForProvider = $isForProvider;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $oldDate = Carbon::parse($this->oldDate)->format('l, F j, Y');
        $oldStartTime = $this->oldTimeSlot['start_time'];
        $oldEndTime = $this->oldTimeSlot['end_time'];
        
        $newDate = Carbon::parse($this->appointment->date)->format('l, F j, Y');
        $newStartTime = $this->appointment->time_slot['start_time'];
        $newEndTime = $this->appointment->time_slot['end_time'];
        
        $subject = $this->isForProvider 
            ? 'Appointment Rescheduled' 
            : 'Your Appointment Has Been Rescheduled';
        
        $view = $this->isForProvider 
            ? 'emails.appointment-rescheduled-provider' 
            : 'emails.appointment-rescheduled-patient';
            
        return (new MailMessage)
            ->subject($subject)
            ->view($view, [
                'appointment' => $this->appointment,
                'oldDate' => $oldDate,
                'oldStartTime' => $oldStartTime,
                'oldEndTime' => $oldEndTime,
                'newDate' => $newDate,
                'newStartTime' => $newStartTime,
                'newEndTime' => $newEndTime,
                'patient' => $this->appointment->patient->user,
                'provider' => $this->appointment->provider->user,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'appointment_id' => $this->appointment->id,
            'old_date' => $this->oldDate,
            'old_time_slot' => $this->oldTimeSlot,
            'new_date' => $this->appointment->date,
            'new_time_slot' => $this->appointment->time_slot,
            'status' => $this->appointment->status,
        ];
    }
}
