<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Provider;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ProviderRegistrationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The user instance.
     *
     * @var \App\Models\User
     */
    protected $user;

    /**
     * The provider instance.
     *
     * @var \App\Models\Provider
     */
    protected $provider;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Provider  $provider
     * @return void
     */
    public function __construct(User $user, Provider $provider)
    {
        $this->user = $user;
        $this->provider = $provider;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Welcome to Medroid Health Care Provider Network')
            ->view('emails.provider-registration', [
                'user' => $this->user,
                'provider' => $this->provider,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'user_id' => $this->user->id,
            'provider_id' => $this->provider->id,
            'name' => $this->user->name,
            'email' => $this->user->email,
            'specialization' => $this->provider->specialization,
        ];
    }
}
