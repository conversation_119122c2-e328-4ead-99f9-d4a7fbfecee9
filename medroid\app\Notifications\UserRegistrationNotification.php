<?php

namespace App\Notifications;

use App\Models\User;
use App\Services\EmailTemplateService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class UserRegistrationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The user instance.
     *
     * @var \App\Models\User
     */
    protected $user;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        try {
            $emailTemplateService = app(EmailTemplateService::class);
            $template = $emailTemplateService->renderTemplate('user-registration', [
                'user' => $this->user,
            ]);

            if ($template) {
                return (new MailMessage)
                    ->subject($template['subject'])
                    ->view('emails.layouts.app', [
                        'content' => $template['content'],
                        'title' => $template['subject'],
                    ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to render email template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        // Fallback to the static template if the dynamic one fails
        return (new MailMessage)
            ->subject('Welcome to Medroid')
            ->view('emails.user-registration', [
                'user' => $this->user,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'user_id' => $this->user->id,
            'name' => $this->user->name,
            'email' => $this->user->email,
        ];
    }
}
