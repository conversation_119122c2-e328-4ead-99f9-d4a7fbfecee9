<?php

namespace App\Providers;

use App\Models\PersonalAccessToken;
use Illuminate\Auth\RequestGuard;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;
use Illuminate\Http\Request;

class TokenGuardServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        Auth::extend('token', function ($app, $name, array $config) {
            return new RequestGuard(function (Request $request) use ($config) {
                $token = $request->bearerToken();
                
                if (!$token) {
                    return null;
                }
                
                $accessToken = PersonalAccessToken::findToken($token);
                
                if (!$accessToken || 
                    ($accessToken->expires_at && $accessToken->expires_at->isPast())) {
                    return null;
                }
                
                $accessToken->last_used_at = now();
                $accessToken->save();
                
                return $accessToken->user;
            }, $app['request']);
        });
    }
}
