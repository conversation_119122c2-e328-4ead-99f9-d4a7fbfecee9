<?php

namespace App\Repositories;

use App\Services\Database\DatabaseServiceInterface;

abstract class BaseRepository implements RepositoryInterface
{
    /**
     * The database service instance.
     *
     * @var DatabaseServiceInterface
     */
    protected $databaseService;
    
    /**
     * Find a record by its primary key.
     *
     * @param mixed $id
     * @return mixed
     */
    public function find($id)
    {
        return $this->databaseService->find($id);
    }
    
    /**
     * Find records by criteria.
     *
     * @param array $criteria
     * @return mixed
     */
    public function findBy(array $criteria)
    {
        return $this->databaseService->findBy($criteria);
    }
    
    /**
     * Create a new record.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        return $this->databaseService->create($data);
    }
    
    /**
     * Update a record.
     *
     * @param mixed $id
     * @param array $data
     * @return mixed
     */
    public function update($id, array $data)
    {
        return $this->databaseService->update($id, $data);
    }
    
    /**
     * Delete a record.
     *
     * @param mixed $id
     * @return bool
     */
    public function delete($id)
    {
        return $this->databaseService->delete($id);
    }
    
    /**
     * Get all records.
     *
     * @return mixed
     */
    public function all()
    {
        return $this->databaseService->all();
    }
    
    /**
     * Get paginated results.
     *
     * @param int $perPage
     * @return mixed
     */
    public function paginate($perPage = 15)
    {
        return $this->databaseService->paginate($perPage);
    }
}
