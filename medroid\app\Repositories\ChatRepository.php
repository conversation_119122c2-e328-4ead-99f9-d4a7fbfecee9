<?php

namespace App\Repositories;

use App\Models\ChatConversation;
use App\Services\Database\DatabaseServiceFactory;

class ChatRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param ChatConversation $model
     * @return void
     */
    public function __construct(ChatConversation $model)
    {
        $this->databaseService = DatabaseServiceFactory::create($model);
    }
    
    /**
     * Find conversations by patient ID.
     *
     * @param string $patientId
     * @return mixed
     */
    public function findByPatientId(string $patientId)
    {
        return $this->findBy(['patient_id' => $patientId]);
    }
    
    /**
     * Add a message to a conversation.
     *
     * @param string $conversationId
     * @param array $message
     * @return mixed
     */
    public function addMessage(string $conversationId, array $message)
    {
        $conversation = $this->find($conversationId);
        
        if (!$conversation) {
            return false;
        }
        
        $messages = $conversation->messages ?? [];
        $messages[] = $message;
        
        return $this->update($conversationId, ['messages' => $messages]);
    }
}
