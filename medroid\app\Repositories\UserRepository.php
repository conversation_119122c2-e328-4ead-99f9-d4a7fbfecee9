<?php

namespace App\Repositories;

use App\Models\User;
use App\Services\Database\DatabaseServiceFactory;

class UserRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param User $model
     * @return void
     */
    public function __construct(User $model)
    {
        $this->databaseService = DatabaseServiceFactory::create($model);
    }
    
    /**
     * Find a user by email.
     *
     * @param string $email
     * @return mixed
     */
    public function findByEmail(string $email)
    {
        return $this->findBy(['email' => $email])->first();
    }
    
    /**
     * Find users by role.
     *
     * @param string $role
     * @return mixed
     */
    public function findByRole(string $role)
    {
        return $this->findBy(['role' => $role]);
    }
}
