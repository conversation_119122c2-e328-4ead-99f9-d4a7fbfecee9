<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\User;
use App\Notifications\AppointmentBookedNotification;
use App\Notifications\AppointmentCancelledNotification;
use App\Notifications\AppointmentRescheduledNotification;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AppointmentService
{
    /**
     * Get appointments for a user
     */
    public function getUserAppointments(User $user, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Appointment::with(['provider.user', 'patient.user', 'service']);

        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();
            if ($patient) {
                $query->where('patient_id', $patient->id);
            } else {
                return new LengthAwarePaginator([], 0, $perPage);
            }
        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();
            if ($provider) {
                $query->where('provider_id', $provider->id);
            } else {
                return new LengthAwarePaginator([], 0, $perPage);
            }
        }

        // Apply filters
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('scheduled_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('scheduled_at', '<=', $filters['date_to']);
        }

        $query->orderBy('scheduled_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Get a single appointment with authorization check
     */
    public function getAppointment(int $appointmentId, User $user): ?Appointment
    {
        $appointment = Appointment::with(['provider.user', 'patient.user', 'service'])
            ->find($appointmentId);

        if (!$appointment) {
            return null;
        }

        // Check authorization
        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();
            if (!$patient || $appointment->patient_id !== $patient->id) {
                return null;
            }
        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();
            if (!$provider || $appointment->provider_id !== $provider->id) {
                return null;
            }
        } elseif ($user->role !== 'admin') {
            return null;
        }

        return $appointment;
    }

    /**
     * Create a new appointment
     */
    public function createAppointment(array $data, User $user): ?Appointment
    {
        try {
            DB::beginTransaction();

            // Get patient
            $patient = Patient::where('user_id', $user->id)->first();
            if (!$patient) {
                throw new \Exception('Patient profile not found');
            }

            // Validate provider
            $provider = Provider::find($data['provider_id']);
            if (!$provider) {
                throw new \Exception('Provider not found');
            }

            // Check availability
            if (!$this->isTimeSlotAvailable($data['provider_id'], $data['scheduled_at'])) {
                throw new \Exception('Time slot is not available');
            }

            $appointmentData = [
                'patient_id' => $patient->id,
                'provider_id' => $data['provider_id'],
                'service_id' => $data['service_id'] ?? null,
                'scheduled_at' => $data['scheduled_at'],
                'duration' => $data['duration'] ?? 30,
                'is_telemedicine' => $data['is_telemedicine'] ?? false,
                'reason' => $data['reason'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => 'scheduled',
                'amount' => $data['amount'] ?? null
            ];

            $appointment = Appointment::create($appointmentData);

            // Send notifications
            $provider->user->notify(new AppointmentBookedNotification($appointment));

            DB::commit();

            return $appointment->load(['provider.user', 'patient.user', 'service']);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create appointment: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update appointment
     */
    public function updateAppointment(int $appointmentId, array $data, User $user): ?Appointment
    {
        try {
            DB::beginTransaction();

            $appointment = $this->getAppointment($appointmentId, $user);
            if (!$appointment) {
                throw new \Exception('Appointment not found or unauthorized');
            }

            // Check if appointment can be updated
            if (!in_array($appointment->status, ['scheduled', 'confirmed'])) {
                throw new \Exception('Appointment cannot be updated');
            }

            $appointment->update($data);

            DB::commit();

            return $appointment->load(['provider.user', 'patient.user', 'service']);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update appointment: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Cancel appointment
     */
    public function cancelAppointment(int $appointmentId, User $user, ?string $reason = null): bool
    {
        try {
            DB::beginTransaction();

            $appointment = $this->getAppointment($appointmentId, $user);
            if (!$appointment) {
                return false;
            }

            // Check if appointment can be cancelled
            if (!in_array($appointment->status, ['scheduled', 'confirmed'])) {
                return false;
            }

            $appointment->update([
                'status' => 'cancelled',
                'cancellation_reason' => $reason,
                'cancelled_at' => now()
            ]);

            // Send notifications
            if ($user->role === 'patient') {
                $appointment->provider->user->notify(new AppointmentCancelledNotification($appointment));
            } else {
                $appointment->patient->user->notify(new AppointmentCancelledNotification($appointment));
            }

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel appointment: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get available time slots for a provider
     */
    public function getAvailableTimeSlots(int $providerId, string $date): array
    {
        $provider = Provider::find($providerId);
        if (!$provider) {
            return [];
        }

        // Get provider's availability for the day
        $dayOfWeek = Carbon::parse($date)->dayOfWeek;
        
        // This is a simplified version - you would implement based on your availability system
        $availableSlots = [];
        $startTime = Carbon::parse($date . ' 09:00:00');
        $endTime = Carbon::parse($date . ' 17:00:00');

        // Get existing appointments for the day
        $existingAppointments = Appointment::where('provider_id', $providerId)
            ->whereDate('scheduled_at', $date)
            ->whereIn('status', ['scheduled', 'confirmed', 'in_progress'])
            ->pluck('scheduled_at')
            ->map(function ($time) {
                return Carbon::parse($time)->format('H:i');
            })
            ->toArray();

        // Generate 30-minute slots
        while ($startTime->lt($endTime)) {
            $timeSlot = $startTime->format('H:i');
            
            if (!in_array($timeSlot, $existingAppointments)) {
                $availableSlots[] = [
                    'time' => $timeSlot,
                    'datetime' => $startTime->toDateTimeString(),
                    'available' => true
                ];
            }
            
            $startTime->addMinutes(30);
        }

        return $availableSlots;
    }

    /**
     * Check if a time slot is available
     */
    public function isTimeSlotAvailable(int $providerId, string $datetime): bool
    {
        $existingAppointment = Appointment::where('provider_id', $providerId)
            ->where('scheduled_at', $datetime)
            ->whereIn('status', ['scheduled', 'confirmed', 'in_progress'])
            ->exists();

        return !$existingAppointment;
    }

    /**
     * Get appointment statistics
     */
    public function getAppointmentStatistics(array $filters = []): array
    {
        $query = Appointment::query();

        // Apply date filters
        if (!empty($filters['date_from'])) {
            $query->where('scheduled_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('scheduled_at', '<=', $filters['date_to']);
        }

        $totalAppointments = $query->count();
        
        $appointmentsByStatus = $query->groupBy('status')
            ->selectRaw('status, count(*) as count')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'total_appointments' => $totalAppointments,
            'appointments_by_status' => $appointmentsByStatus,
            'completion_rate' => $totalAppointments > 0 
                ? round(($appointmentsByStatus['completed'] ?? 0) / $totalAppointments * 100, 2) 
                : 0
        ];
    }

    /**
     * Reschedule appointment
     */
    public function rescheduleAppointment(int $appointmentId, string $newDateTime, User $user): ?Appointment
    {
        try {
            DB::beginTransaction();

            $appointment = $this->getAppointment($appointmentId, $user);
            if (!$appointment) {
                throw new \Exception('Appointment not found or unauthorized');
            }

            // Check if new time slot is available
            if (!$this->isTimeSlotAvailable($appointment->provider_id, $newDateTime)) {
                throw new \Exception('New time slot is not available');
            }

            $oldDateTime = $appointment->scheduled_at;
            
            $appointment->update([
                'scheduled_at' => $newDateTime,
                'status' => 'scheduled' // Reset to scheduled if it was confirmed
            ]);

            // Send notifications
            if ($user->role === 'patient') {
                $appointment->provider->user->notify(new AppointmentRescheduledNotification($appointment, $oldDateTime));
            } else {
                $appointment->patient->user->notify(new AppointmentRescheduledNotification($appointment, $oldDateTime));
            }

            DB::commit();

            return $appointment->load(['provider.user', 'patient.user', 'service']);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to reschedule appointment: ' . $e->getMessage());
            return null;
        }
    }
}
