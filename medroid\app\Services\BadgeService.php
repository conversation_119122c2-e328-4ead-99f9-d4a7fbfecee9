<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserBadge;
use Illuminate\Support\Facades\Log;

class BadgeService
{
    /**
     * Badge definitions with their criteria and rewards
     */
    protected $badgeDefinitions = [
        'founder' => [
            'name' => 'Medroid Founder',
            'description' => 'Founding member of the Medroid community',
            'icon' => 'founder.svg',
            'color' => '#FFD700',
            'points' => 100,
        ],
        'early_adopter' => [
            'name' => 'Early Adopter',
            'description' => 'One of the first 1000 users',
            'icon' => 'early-adopter.svg',
            'color' => '#FF9F6E',
            'points' => 50,
        ],
        'active_user' => [
            'name' => 'Active User',
            'description' => 'Used the app for 7 consecutive days',
            'icon' => 'active-user.svg',
            'color' => '#4CAF50',
            'points' => 25,
        ],
        'chat_master' => [
            'name' => 'Chat Master',
            'description' => 'Completed 10 chat sessions',
            'icon' => 'chat-master.svg',
            'color' => '#2196F3',
            'points' => 30,
        ],
        'appointment_pro' => [
            'name' => 'Appointment Pro',
            'description' => 'Booked 5 appointments',
            'icon' => 'appointment-pro.svg',
            'color' => '#9C27B0',
            'points' => 40,
        ],
        'referrer' => [
            'name' => 'Referrer',
            'description' => 'Successfully referred 3 users',
            'icon' => 'referrer.svg',
            'color' => '#FF5722',
            'points' => 60,
        ],
        'social_butterfly' => [
            'name' => 'Social Butterfly',
            'description' => 'Posted 10 stories',
            'icon' => 'social-butterfly.svg',
            'color' => '#E91E63',
            'points' => 20,
        ],
        'level_upgrade' => [
            'name' => 'Level Up',
            'description' => 'Upgraded membership level',
            'icon' => 'level-up.svg',
            'color' => '#FFC107',
            'points' => 15,
        ],
    ];

    /**
     * Award a badge to a user
     */
    public function awardBadge(User $user, string $badgeType, array $criteria = []): ?UserBadge
    {
        try {
            // Check if user already has this badge
            $existingBadge = UserBadge::where('user_id', $user->id)
                ->where('badge_type', $badgeType)
                ->first();

            if ($existingBadge) {
                Log::info('User already has badge', [
                    'user_id' => $user->id,
                    'badge_type' => $badgeType,
                ]);
                return $existingBadge;
            }

            $definition = $this->badgeDefinitions[$badgeType] ?? null;
            if (!$definition) {
                Log::warning('Unknown badge type', ['badge_type' => $badgeType]);
                return null;
            }

            $badge = UserBadge::create([
                'user_id' => $user->id,
                'badge_type' => $badgeType,
                'badge_name' => $definition['name'],
                'badge_description' => $definition['description'],
                'badge_icon' => $definition['icon'],
                'badge_color' => $definition['color'],
                'points_awarded' => $definition['points'],
                'earned_at' => now(),
                'criteria_met' => $criteria,
            ]);

            // Award points for earning the badge
            app(PointsService::class)->awardPoints($user, 'badge_earned', $definition['points'], [
                'badge_type' => $badgeType,
                'badge_id' => $badge->id,
            ]);

            Log::info('Badge awarded', [
                'user_id' => $user->id,
                'badge_type' => $badgeType,
                'badge_id' => $badge->id,
                'points' => $definition['points'],
            ]);

            return $badge;

        } catch (\Exception $e) {
            Log::error('Error awarding badge: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'badge_type' => $badgeType,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check and award activity-based badges
     */
    public function checkActivityBadges(User $user): array
    {
        $awardedBadges = [];

        // Early adopter badge (first 1000 users)
        if ($user->id <= 1000) {
            $badge = $this->awardBadge($user, 'early_adopter');
            if ($badge) $awardedBadges[] = $badge;
        }

        // Active user badge (7+ day streak)
        if ($user->activity_streak >= 7) {
            $badge = $this->awardBadge($user, 'active_user', [
                'streak_days' => $user->activity_streak,
            ]);
            if ($badge) $awardedBadges[] = $badge;
        }

        // Chat master badge (10+ chats)
        $chatCount = $user->activityLogs()
            ->where('activity_type', 'chat')
            ->count();
        if ($chatCount >= 10) {
            $badge = $this->awardBadge($user, 'chat_master', [
                'chat_count' => $chatCount,
            ]);
            if ($badge) $awardedBadges[] = $badge;
        }

        // Appointment pro badge (5+ appointments)
        $appointmentCount = $user->activityLogs()
            ->where('activity_type', 'appointment')
            ->count();
        if ($appointmentCount >= 5) {
            $badge = $this->awardBadge($user, 'appointment_pro', [
                'appointment_count' => $appointmentCount,
            ]);
            if ($badge) $awardedBadges[] = $badge;
        }

        // Referrer badge (3+ successful referrals)
        $referralCount = $user->referrals()
            ->where('status', 'completed')
            ->count();
        if ($referralCount >= 3) {
            $badge = $this->awardBadge($user, 'referrer', [
                'referral_count' => $referralCount,
            ]);
            if ($badge) $awardedBadges[] = $badge;
        }

        // Social butterfly badge (10+ stories)
        $storyCount = $user->activityLogs()
            ->where('activity_type', 'story_post')
            ->count();
        if ($storyCount >= 10) {
            $badge = $this->awardBadge($user, 'social_butterfly', [
                'story_count' => $storyCount,
            ]);
            if ($badge) $awardedBadges[] = $badge;
        }

        return $awardedBadges;
    }

    /**
     * Get user's badges with display information
     */
    public function getUserBadges(User $user): array
    {
        return $user->badges()
            ->orderBy('earned_at', 'desc')
            ->get()
            ->map(function ($badge) {
                return [
                    'id' => $badge->id,
                    'type' => $badge->badge_type,
                    'name' => $badge->badge_name,
                    'description' => $badge->badge_description,
                    'icon_url' => $badge->icon_url,
                    'color' => $badge->badge_color,
                    'points_awarded' => $badge->points_awarded,
                    'earned_at' => $badge->earned_at,
                    'formatted_date' => $badge->formatted_earned_date,
                    'criteria_met' => $badge->criteria_met,
                ];
            })
            ->toArray();
    }

    /**
     * Get badge definition
     */
    public function getBadgeDefinition(string $badgeType): ?array
    {
        return $this->badgeDefinitions[$badgeType] ?? null;
    }

    /**
     * Get all badge definitions
     */
    public function getAllBadgeDefinitions(): array
    {
        return $this->badgeDefinitions;
    }
}
