<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\Patient;
use App\Models\User;
use App\Repositories\ChatRepository;
use Illuminate\Support\Facades\Log;

class ChatService
{
    /**
     * The chat repository instance.
     *
     * @var ChatRepository
     */
    protected $chatRepository;

    /**
     * Create a new service instance.
     *
     * @param ChatRepository $chatRepository
     * @return void
     */
    public function __construct(ChatRepository $chatRepository)
    {
        $this->chatRepository = $chatRepository;
    }

    /**
     * Get all conversations for a patient.
     *
     * @param int $patientId MySQL patient ID
     * @return \Illuminate\Support\Collection
     */
    public function getConversationsForPatient(int $patientId)
    {
        try {
            // Get the patient from MySQL
            $patient = Patient::with('user')->find($patientId);

            if (!$patient) {
                return collect([]);
            }

            // Find conversations in MongoDB by patient email (as a cross-database identifier)
            // Only return conversations that have at least one message
            $conversations = ChatConversation::where('patient_email', $patient->user->email)
                ->orderBy('updated_at', 'desc')
                ->get();

            // Filter out conversations with no messages
            $filteredConversations = $conversations->filter(function ($conversation) {
                return is_array($conversation->messages) && count($conversation->messages) > 0;
            });

            return $filteredConversations;
        } catch (\Exception $e) {
            Log::error("Error getting conversations for patient {$patientId}: {$e->getMessage()}");
            return collect([]);
        }
    }

    /**
     * Create a new conversation for a patient.
     *
     * @param int $patientId MySQL patient ID
     * @param string $title
     * @return ChatConversation|null
     */
    public function createConversation(int $patientId, string $title = null)
    {
        try {
            // Get the patient from MySQL
            $patient = Patient::with('user')->find($patientId);

            if (!$patient) {
                return null;
            }

            // Create a new conversation in MongoDB
            $conversation = new ChatConversation([
                'patient_email' => $patient->user->email,
                'title' => $title ?? 'New Conversation',
                'messages' => [],
                'health_concerns' => [],
                'recommendations' => [],
                'escalated' => false,
                'is_public' => false,
            ]);

            $conversation->save();

            return $conversation;
        } catch (\Exception $e) {
            Log::error("Error creating conversation for patient {$patientId}: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Add a message to a conversation.
     *
     * @param string $conversationId MongoDB conversation ID
     * @param string $content
     * @param string $role
     * @param array $metadata
     * @return ChatConversation|null
     */
    public function addMessage(string $conversationId, string $content, string $role = 'user', array $metadata = [])
    {
        try {
            // Get the conversation from MongoDB
            $conversation = ChatConversation::find($conversationId);

            if (!$conversation) {
                return null;
            }

            // Add the message
            $message = [
                'content' => $content,
                'role' => $role,
                'timestamp' => now(),
                'metadata' => $metadata,
            ];

            $conversation->addMessage($message);

            // Generate a title if this is the first message and no title exists
            if (count($conversation->messages) === 1 && $conversation->title === 'New Conversation') {
                $conversation->title = $conversation->generateTitle();
                $conversation->save();
            }

            return $conversation;
        } catch (\Exception $e) {
            Log::error("Error adding message to conversation {$conversationId}: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Get a conversation by ID.
     *
     * @param string $conversationId MongoDB conversation ID
     * @return ChatConversation|null
     */
    public function getConversation(string $conversationId)
    {
        try {
            return ChatConversation::find($conversationId);
        } catch (\Exception $e) {
            Log::error("Error getting conversation {$conversationId}: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Delete a conversation.
     *
     * @param string $conversationId MongoDB conversation ID
     * @return bool
     */
    public function deleteConversation(string $conversationId)
    {
        try {
            $conversation = ChatConversation::find($conversationId);

            if (!$conversation) {
                return false;
            }

            return $conversation->delete();
        } catch (\Exception $e) {
            Log::error("Error deleting conversation {$conversationId}: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Share a conversation to the public feed.
     *
     * @param string $conversationId MongoDB conversation ID
     * @return bool
     */
    public function shareConversation(string $conversationId)
    {
        try {
            $conversation = ChatConversation::find($conversationId);

            if (!$conversation) {
                return false;
            }

            $conversation->is_public = true;
            $conversation->shared_at = now();
            $conversation->save();

            return true;
        } catch (\Exception $e) {
            Log::error("Error sharing conversation {$conversationId}: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Make a conversation private.
     *
     * @param string $conversationId MongoDB conversation ID
     * @return bool
     */
    public function makeConversationPrivate(string $conversationId)
    {
        try {
            $conversation = ChatConversation::find($conversationId);

            if (!$conversation) {
                return false;
            }

            $conversation->is_public = false;
            $conversation->save();

            return true;
        } catch (\Exception $e) {
            Log::error("Error making conversation private {$conversationId}: {$e->getMessage()}");
            return false;
        }
    }
}
