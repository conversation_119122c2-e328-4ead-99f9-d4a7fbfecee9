<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ContentFilterService
{
    protected $mistralService;
    protected $healthKeywords;
    
    public function __construct()
    {
        $this->mistralService = new MistralNemoService();
        $this->healthKeywords = $this->getHealthKeywords();
    }
    
    /**
     * Extract health topics from text using Mistral LLM
     */
    public function extractHealthTopics($text)
    {
        // Initial basic keyword check to avoid unnecessary API calls
        $containsHealthTerms = $this->basicHealthCheck($text);
        
        if (!$containsHealthTerms) {
            return [];
        }
        
        // Use Mistral Nemo for more sophisticated analysis
        return $this->mistralService->analyzeHealthConcerns($text);
    }
    
    /**
     * Calculate a relevance score for health content
     */
    public function calculateRelevanceScore($healthTopics)
    {
        if (empty($healthTopics)) {
            return 0;
        }
        
        // More topics indicates higher relevance (up to a point)
        $topicCount = count($healthTopics);
        
        // Base score on topic count, maxing out at 5 topics
        $baseScore = min($topicCount / 5, 1) * 0.8;
        
        // Add randomness for demonstration purposes
        // In a real system, this would be based on more sophisticated factors
        $randomFactor = mt_rand(0, 20) / 100;
        
        return min($baseScore + $randomFactor, 1);
    }
    
    /**
     * Perform basic health term check
     */
    private function basicHealthCheck($text)
    {
        $text = strtolower($text);
        
        foreach ($this->healthKeywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if content violates community guidelines
     */
    public function checkContentSafety($text, $mediaUrl = null)
    {
        // Implement content safety checks
        // For demo purposes, this is a simplified implementation
        
        $prohibitedTerms = [
            'illegal drugs', 'suicide methods', 'self-harm', 'violence',
            'hate speech', 'discrimination', 'terrorism',
        ];
        
        $text = strtolower($text);
        
        foreach ($prohibitedTerms as $term) {
            if (strpos($text, $term) !== false) {
                return [
                    'safe' => false,
                    'reason' => "Content contains prohibited term: {$term}",
                ];
            }
        }
        
        // In a real system, you would also check images/videos
        // using a content moderation API
        
        return [
            'safe' => true,
            'reason' => null,
        ];
    }
    
    /**
     * Get basic list of health keywords for initial filtering
     */
    private function getHealthKeywords()
    {
        return [
            'health', 'wellness', 'fitness', 'exercise', 'workout',
            'diet', 'nutrition', 'protein', 'carbs', 'fat', 'vitamins',
            'supplements', 'mental health', 'anxiety', 'depression', 'therapy',
            'medication', 'prescription', 'doctor', 'hospital', 'clinic',
            'symptoms', 'diagnosis', 'treatment', 'recovery', 'healing',
            'chronic', 'condition', 'disease', 'illness', 'infection',
            'immune', 'vaccine', 'prevention', 'diabetes', 'heart', 'cardio',
            'blood pressure', 'cholesterol', 'cancer', 'screening',
            'healthy living', 'lifestyle', 'sleep', 'stress', 'mindfulness',
            'meditation', 'yoga', 'physical therapy', 'rehab',
            'weight loss', 'obesity', 'BMI', 'metabolism',
            'alcohol', 'smoking', 'tobacco', 'addiction', 'recovery',
            'surgery', 'procedure', 'medical', 'healthcare',
            'pregnancy', 'prenatal', 'postnatal', 'fertility',
            'pediatric', 'children', 'elderly', 'aging', 'geriatric',
            'allergies', 'asthma', 'respiratory', 'lungs', 'breathing',
            'dental', 'vision', 'hearing', 'skin', 'dermatology',
        ];
    }
}