<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserCredit;
use App\Models\CreditTransaction;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CreditService
{
    /**
     * Get or create a user's credit record
     *
     * @param int $userId
     * @return UserCredit
     */
    public function getUserCredit(int $userId)
    {
        $credit = UserCredit::firstOrCreate(
            ['user_id' => $userId],
            [
                'balance' => 0.00,
                'total_earned' => 0.00,
                'total_used' => 0.00,
            ]
        );

        return $credit;
    }

    /**
     * Add credit to a user's account
     *
     * @param int $userId
     * @param float $amount
     * @param string $source
     * @param string|int|null $referenceId
     * @param string|null $description
     * @param array|null $metadata
     * @return CreditTransaction
     */
    public function addCredit(
        int $userId,
        float $amount,
        string $source,
        $referenceId = null,
        ?string $description = null,
        ?array $metadata = null
    ) {
        DB::beginTransaction();

        try {
            // Get or create user credit record
            $userCredit = $this->getUserCredit($userId);

            // Update the credit balance
            $userCredit->balance += $amount;
            $userCredit->total_earned += $amount;
            $userCredit->save();

            // Create a transaction record
            $transaction = new CreditTransaction([
                'user_id' => $userId,
                'amount' => $amount,
                'type' => 'earned',
                'source' => $source,
                'reference_id' => $referenceId,
                'reference_type' => $source === 'referral' ? 'App\\Models\\Referral' : null,
                'description' => $description ?? "Credit added from {$source}",
                'metadata' => $metadata,
            ]);

            $transaction->save();

            DB::commit();
            return $transaction;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error adding credit: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Use credit from a user's account
     *
     * @param int $userId
     * @param float $amount
     * @param string $source
     * @param string|int|null $referenceId
     * @param string|null $description
     * @param array|null $metadata
     * @return CreditTransaction|bool
     */
    public function useCredit(
        int $userId,
        float $amount,
        string $source,
        $referenceId = null,
        ?string $description = null,
        ?array $metadata = null
    ) {
        DB::beginTransaction();

        try {
            // Get user credit record
            $userCredit = $this->getUserCredit($userId);

            // Check if user has enough credit
            if ($userCredit->balance < $amount) {
                DB::rollBack();
                return false;
            }

            // Update the credit balance
            $userCredit->balance -= $amount;
            $userCredit->total_used += $amount;
            $userCredit->save();

            // Create a transaction record
            $transaction = new CreditTransaction([
                'user_id' => $userId,
                'amount' => $amount,
                'type' => 'used',
                'source' => $source,
                'reference_id' => $referenceId,
                'reference_type' => $source === 'appointment' ? 'App\\Models\\Appointment' : null,
                'description' => $description ?? "Credit used for {$source}",
                'metadata' => $metadata,
            ]);

            $transaction->save();

            DB::commit();
            return $transaction;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error using credit: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get credit transactions for a user
     *
     * @param int $userId
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserTransactions(int $userId, int $limit = 20)
    {
        return CreditTransaction::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Check if a user has enough credit
     *
     * @param int $userId
     * @param float $amount
     * @return bool
     */
    public function hasEnoughCredit(int $userId, float $amount)
    {
        $userCredit = $this->getUserCredit($userId);
        return $userCredit->balance >= $amount;
    }
}
