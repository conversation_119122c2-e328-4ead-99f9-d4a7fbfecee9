<?php

namespace App\Services\Database;

use Illuminate\Database\Eloquent\Model;

class DatabaseServiceFactory
{
    /**
     * Create a new database service instance based on the model type.
     *
     * @param Model $model
     * @return DatabaseServiceInterface
     */
    public static function create(Model $model): DatabaseServiceInterface
    {
        return new MySQLDatabaseService($model);
    }
}
