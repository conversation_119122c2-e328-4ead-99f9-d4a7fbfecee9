<?php

namespace App\Services\Database;

interface DatabaseServiceInterface
{
    /**
     * Find a record by its primary key.
     *
     * @param mixed $id
     * @return mixed
     */
    public function find($id);
    
    /**
     * Find records by criteria.
     *
     * @param array $criteria
     * @return mixed
     */
    public function findBy(array $criteria);
    
    /**
     * Create a new record.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data);
    
    /**
     * Update a record.
     *
     * @param mixed $id
     * @param array $data
     * @return mixed
     */
    public function update($id, array $data);
    
    /**
     * Delete a record.
     *
     * @param mixed $id
     * @return bool
     */
    public function delete($id);
    
    /**
     * Get all records.
     *
     * @return mixed
     */
    public function all();
    
    /**
     * Get paginated results.
     *
     * @param int $perPage
     * @return mixed
     */
    public function paginate($perPage = 15);
}
