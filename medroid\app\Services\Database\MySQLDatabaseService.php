<?php

namespace App\Services\Database;

use Illuminate\Database\Eloquent\Model;

class MySQLDatabaseService implements DatabaseServiceInterface
{
    /**
     * The model instance.
     *
     * @var Model
     */
    protected $model;
    
    /**
     * Create a new database service instance.
     *
     * @param Model $model
     * @return void
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find a record by its primary key.
     *
     * @param mixed $id
     * @return mixed
     */
    public function find($id)
    {
        return $this->model->find($id);
    }
    
    /**
     * Find records by criteria.
     *
     * @param array $criteria
     * @return mixed
     */
    public function findBy(array $criteria)
    {
        $query = $this->model->newQuery();
        
        foreach ($criteria as $key => $value) {
            $query->where($key, $value);
        }
        
        return $query->get();
    }
    
    /**
     * Create a new record.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        return $this->model->create($data);
    }
    
    /**
     * Update a record.
     *
     * @param mixed $id
     * @param array $data
     * @return mixed
     */
    public function update($id, array $data)
    {
        $record = $this->find($id);
        
        if (!$record) {
            return false;
        }
        
        $record->update($data);
        
        return $record;
    }
    
    /**
     * Delete a record.
     *
     * @param mixed $id
     * @return bool
     */
    public function delete($id)
    {
        $record = $this->find($id);
        
        if (!$record) {
            return false;
        }
        
        return $record->delete();
    }
    
    /**
     * Get all records.
     *
     * @return mixed
     */
    public function all()
    {
        return $this->model->all();
    }
    
    /**
     * Get paginated results.
     *
     * @param int $perPage
     * @return mixed
     */
    public function paginate($perPage = 15)
    {
        return $this->model->paginate($perPage);
    }
}
