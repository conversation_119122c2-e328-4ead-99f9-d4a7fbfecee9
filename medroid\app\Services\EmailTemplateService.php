<?php

namespace App\Services;

use App\Models\EmailTemplate;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;

class EmailTemplateService
{
    /**
     * Render an email template with the given data.
     *
     * @param string $slug
     * @param array $data
     * @return array|null
     */
    public function renderTemplate(string $slug, array $data = [])
    {
        try {
            // First try to find by slug
            // First try to find by slug
            $template = EmailTemplate::findBySlug($slug);

            // If not found by slug, try to find by ID (in case the slug is actually an ID)
            if (!$template && is_numeric($slug)) {
                $template = EmailTemplate::find($slug);
            }

            // If not found by slug, try to find by ID (in case the slug is actually an ID)
            if (!$template && is_numeric($slug)) {
                $template = EmailTemplate::find($slug);
            }

            if (!$template) {
                Log::warning("Email template not found: {$slug}");
                return [
                    'subject' => 'Template Not Found',
                    'content' => '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                        <h3>Template Not Found</h3>
                        <p>The requested email template "' . htmlspecialchars($slug) . '" could not be found.</p>
                    </div>',
                ];
                return [
                    'subject' => 'Template Not Found',
                    'content' => '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                        <h3>Template Not Found</h3>
                        <p>The requested email template "' . htmlspecialchars($slug) . '" could not be found.</p>
                    </div>',
                ];
            }

            // Compile the template content
            $content = $this->compileTemplate($template->content, $data);

            return [
                'subject' => $template->subject,
                'content' => $content,
            ];
        } catch (\Exception $e) {
            Log::error("Error rendering email template: {$e->getMessage()}", [
                'slug' => $slug,
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'subject' => 'Error Rendering Template',
                'content' => '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                    <h3>Error Rendering Template</h3>
                    <p>' . htmlspecialchars($e->getMessage()) . '</p>
                    <p>Please check the template syntax and make sure all required variables are provided.</p>
                </div>',
            ];

            return [
                'subject' => 'Error Rendering Template',
                'content' => '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                    <h3>Error Rendering Template</h3>
                    <p>' . htmlspecialchars($e->getMessage()) . '</p>
                    <p>Please check the template syntax and make sure all required variables are provided.</p>
                </div>',
            ];
        }
    }

    /**
     * Compile a template string with the given data.
     *
     * @param string $template
     * @param array $data
     * @return string
     */
    private function compileTemplate(string $template, array $data = [])
    {
        try {
            // Create a temporary view file
            $tempView = 'email_template_' . md5($template . time());

            // Store the template content in the view
            View::addNamespace('email_templates', resource_path('views/emails'));

            // Make sure the layouts directory exists
            if (!file_exists(resource_path('views/emails/layouts'))) {
                Log::warning("Email layouts directory not found");
            }

            // Wrap the template content in a try-catch block to handle undefined variables
            $wrappedTemplate = '@php
try {
@endphp
' . $template . '
@php
} catch (\Throwable $e) {
    echo "<div style=\"color: red; padding: 20px; border: 1px solid red; margin: 20px 0;\">
        <h3>Error in template</h3>
        <p>" . htmlspecialchars($e->getMessage()) . "</p>
        <p>Please check the template syntax and make sure all required variables are provided.</p>
    </div>";
}
@endphp';

            // Compile the template with Blade
            $content = Blade::render($wrappedTemplate, $data);

            return $content;
        } catch (\Exception $e) {
            Log::error("Error compiling email template: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);

            // Return a fallback content with error details
            return '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                <h3>Error rendering template</h3>
                <p>' . htmlspecialchars($e->getMessage()) . '</p>
                <p>Please check the template syntax and make sure all required variables are provided.</p>
            </div>';
        }
    }

    /**
     * Get all email templates.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllTemplates()
    {
        return EmailTemplate::all();
    }

    /**
     * Get an email template by ID.
     *
     * @param int $id
     * @return \App\Models\EmailTemplate|null
     */
    public function getTemplateById(int $id)
    {
        return EmailTemplate::find($id);
    }

    /**
     * Update an email template.
     *
     * @param int $id
     * @param array $data
     * @return \App\Models\EmailTemplate|null
     */
    public function updateTemplate(int $id, array $data)
    {
        $template = EmailTemplate::find($id);

        if (!$template) {
            return null;
        }

        $template->update($data);
        return $template;
    }
}
