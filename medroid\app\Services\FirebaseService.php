<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FirebaseService
{
    /**
     * Send a notification to a device token
     *
     * @param string $token
     * @param string $title
     * @param string $body
     * @param array $data
     * @return bool
     */
    public function sendNotification($token, $title, $body, $data = [])
    {
        try {
            $apiKey = config('services.firebase.api_key');

            if (empty($apiKey)) {
                Log::error('Firebase API key is not configured');
                return false;
            }

            // For Flutter, we use the legacy FCM API which works better with Flutter apps
            $payload = [
                'to' => $token,
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    'badge' => '1',
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                ],
                'data' => $data,
                'priority' => 'high',
                'content_available' => true,
                'mutable_content' => true,
            ];

            // Add Android specific configuration
            $payload['android'] = [
                'priority' => 'high',
                'notification' => [
                    'channel_id' => 'high_importance_channel', // This should match your Flutter app's channel ID
                    'sound' => 'default',
                    'notification_priority' => 'PRIORITY_HIGH',
                    'visibility' => 'PUBLIC'
                ]
            ];

            // Legacy FCM API endpoint
            $url = "https://fcm.googleapis.com/fcm/send";

            $response = Http::withHeaders([
                'Authorization' => 'key=' . $apiKey,
                'Content-Type' => 'application/json'
            ])->post($url, $payload);

            if ($response->successful()) {
                Log::info('Push notification sent to Flutter app', [
                    'token' => $token,
                    'response' => $response->json()
                ]);
                return true;
            } else {
                Log::error('Failed to send push notification to Flutter app', [
                    'token' => $token,
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Error sending Firebase notification to Flutter app', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Send a notification to multiple device tokens
     *
     * @param array $tokens
     * @param string $title
     * @param string $body
     * @param array $data
     * @return bool
     */
    public function sendMulticastNotification(array $tokens, $title, $body, $data = [])
    {
        if (empty($tokens)) {
            return false;
        }

        $success = false;

        foreach ($tokens as $token) {
            $result = $this->sendNotification($token, $title, $body, $data);
            if ($result) {
                $success = true;
            }
        }

        return $success;
    }

    /**
     * Test the Firebase connection
     *
     * @return bool
     */
    public function testConnection()
    {
        try {
            $apiKey = config('services.firebase.api_key');

            if (empty($apiKey)) {
                return false;
            }

            // Test connection by validating the API key with the legacy FCM API
            // This is the same API we use for sending notifications to Flutter
            $url = "https://fcm.googleapis.com/fcm/send";

            $response = Http::withHeaders([
                'Authorization' => 'key=' . $apiKey,
                'Content-Type' => 'application/json'
            ])->post($url, [
                'to' => 'test_token', // Invalid token, but API will still validate our key
                'notification' => [
                    'title' => 'Test',
                    'body' => 'Test',
                ],
                'content_available' => true,
                'priority' => 'high',
            ]);

            // Even with an invalid token, if our API key is valid, we'll get a specific error
            // rather than an authentication error
            $responseData = $response->json();

            // Check if we got a response with a "results" field, which indicates our key was accepted
            return $response->successful() ||
                   (isset($responseData['results']) && is_array($responseData['results']));
        } catch (\Exception $e) {
            Log::error('Error testing Firebase connection', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
