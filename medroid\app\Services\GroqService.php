<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\Patient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GroqService
{
    protected $apiKey;
    protected $apiUrl;
    protected $model;

    public function __construct()
    {
        $this->apiKey = config('services.groq.api_key');
        $this->apiUrl = config('services.groq.api_url');
        $this->model = config('services.groq.model');

        // Log if API key is missing
        if (empty($this->apiKey)) {
            Log::warning('Groq API key is missing. Please set GROQ_API_KEY in your .env file.');
        }
    }

    /**
     * Generate a medical consultation response from Groq
     *
     * @param ChatConversation $conversation The conversation to generate a response for
     * @param bool $includePatientContext Whether to include patient context from the database
     * @param string|bool $additionalContext Additional context to include in the prompt (e.g., demographic info for anonymous users) or boolean for hasRecentAppointment
     * @param bool $hasRecentAppointment Whether this conversation has recent appointment booking activity
     * @return array The structured response
     */
    public function generateMedicalConsultation(ChatConversation $conversation, $includePatientContext = true, $additionalContext = '', $hasRecentAppointment = false)
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('Groq API key is missing. Cannot generate response.');
                return [
                    'message' => 'I apologize, but our AI service is currently unavailable. Please try again later or contact support.',
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                ];
            }

            // Format the conversation history
            $messages = $this->formatMessages($conversation);

            // Handle backward compatibility for $additionalContext parameter
            if (is_bool($additionalContext)) {
                $hasRecentAppointment = $additionalContext;
                $additionalContext = '';
            }

            // Get patient context if requested
            $patientContext = '';
            if ($includePatientContext && $conversation->patient_id) {
                $patientContext = $this->getPatientContext($conversation->patient_id);
            }

            // Add the system prompt for the medical consultation
            $systemMessage = [
                'role' => 'system',
                'content' => $this->createMedicalSystemPrompt($patientContext, $additionalContext, $hasRecentAppointment)
            ];

            // Prepare the API request with timeout
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(60) // 60 second timeout for AI requests
            ->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => array_merge([$systemMessage], $messages),
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Process the response to extract structured information
                $structuredResponse = $this->processAiResponse($content, $conversation);

                return $structuredResponse;
            }

            // Log the specific error from Groq
            $errorBody = $response->body();
            Log::error('Groq API error response', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            throw new \Exception('Failed to get response from Groq: ' . $errorBody);
        } catch (\Exception $e) {
            Log::error('Error in Groq consultation', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'message' => 'I apologize, but I encountered an error processing your request. Please try again or contact support if the issue persists.',
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Format the conversation messages for the Groq API
     */
    private function formatMessages(ChatConversation $conversation)
    {
        return collect($conversation->messages)->map(function ($message) {
            return [
                'role' => $message['role'],
                'content' => $message['content'],
            ];
        })->toArray();
    }

    /**
     * Process the AI response to extract structured medical information
     */
    private function processAiResponse($content, ChatConversation $conversation)
    {
        try {
            // Initialize the structured response
            $structuredResponse = [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'escalation_reason' => null,
                'referral_note' => null,
            ];

            // Check for emergency escalation
            $emergencyKeywords = [
                'seek emergency medical care',
                'call 911',
                'go to the emergency room',
                'emergency services',
                'emergency department',
                'call an ambulance',
                'immediate medical attention',
                'urgent medical care',
            ];

            foreach ($emergencyKeywords as $keyword) {
                if (stripos($content, $keyword) !== false) {
                    $structuredResponse['escalate'] = true;
                    $structuredResponse['escalation_reason'] = 'Potential emergency medical situation detected.';
                    break;
                }
            }

            // Extract differential diagnoses
            if (preg_match('/Here\'s what it could be:(.*?)(?=CLEAR RECOMMENDATION|VIRTUAL CONNECTION|REFERRAL NOTE|$)/s', $content, $matches)) {
                $diagnosesText = $matches[1];
                preg_match_all('/\d+\.\s+([^-]+)[\s-]+(\d+)%/s', $diagnosesText, $diagnosesMatches);

                if (!empty($diagnosesMatches[1])) {
                    foreach ($diagnosesMatches[1] as $index => $condition) {
                        $structuredResponse['health_concerns'][] = trim($condition);
                    }
                }
            }

            // Extract recommendations
            if (preg_match('/RECOMMENDATION.*?((?:\n|.)*?)(?=REFERRAL NOTE|VIRTUAL CONNECTION|$)/si', $content, $matches)) {
                $recommendationsText = $matches[1];
                $recommendationLines = preg_split('/\n+/', $recommendationsText);

                foreach ($recommendationLines as $line) {
                    $line = trim($line);
                    if (empty($line) || strlen($line) < 10) continue;

                    // Categorize the recommendation
                    $type = 'general';

                    if (stripos($line, 'consult') !== false ||
                        stripos($line, 'doctor') !== false ||
                        stripos($line, 'specialist') !== false) {
                        $type = 'specialist';
                    } else if (stripos($line, 'test') !== false ||
                               stripos($line, 'lab') !== false ||
                               stripos($line, 'imaging') !== false) {
                        $type = 'diagnostic';
                    } else if (stripos($line, 'take') !== false ||
                               stripos($line, 'medication') !== false ||
                               stripos($line, 'dose') !== false) {
                        $type = 'medication_info';
                    } else if (stripos($line, 'exercise') !== false ||
                               stripos($line, 'diet') !== false ||
                               stripos($line, 'sleep') !== false ||
                               stripos($line, 'avoid') !== false) {
                        $type = 'lifestyle';
                    } else if (stripos($line, 'warning') !== false ||
                               stripos($line, 'seek') !== false ||
                               stripos($line, 'emergency') !== false ||
                               stripos($line, 'immediately') !== false) {
                        $type = 'warning';
                        // Also flag for escalation if severe warnings are present
                        if (stripos($line, 'immediate') !== false ||
                            stripos($line, 'emergency') !== false) {
                            $structuredResponse['escalate'] = true;
                            $structuredResponse['escalation_reason'] = 'Warning signs for potential emergency.';
                        }
                    }

                    $structuredResponse['recommendations'][] = [
                        'type' => $type,
                        'content' => $line,
                        'confidence' => 0.85, // Default confidence
                    ];
                }
            }

            // Extract referral note if present
            if (preg_match('/REFERRAL NOTE[:\s]*((?:\n|.)*?)(?=\[end|$)/si', $content, $matches)) {
                $structuredResponse['referral_note'] = trim($matches[1]);

                // Remove the referral note from the user-facing message
                $structuredResponse['message'] = str_replace($matches[0], '', $content);
            }

            return $structuredResponse;
        } catch (\Exception $e) {
            Log::error('Error processing AI response', [
                'message' => $e->getMessage(),
                'content' => $content,
            ]);

            return [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Get patient context information if available
     */
    private function getPatientContext($patientId)
    {
        try {
            $patient = Patient::find($patientId);
            if (!$patient) {
                return '';
            }

            $context = "Patient context:\n";

            // Add demographic information (gender and age)
            $demographicInfo = $patient->getDemographicInfo();
            if (!empty($demographicInfo)) {
                $context .= "Demographics:\n";

                if (isset($demographicInfo['gender'])) {
                    $context .= "- Gender: {$demographicInfo['gender']}\n";
                }

                if (isset($demographicInfo['age'])) {
                    $context .= "- Age: {$demographicInfo['age']} years\n";
                }

                $context .= "\n";
            }

            // Add health history if available
            if (!empty($patient->health_history)) {
                $context .= "Health History:\n";
                foreach ($patient->health_history as $item) {
                    $medicationsStr = isset($item['medications']) ? implode(', ', $item['medications']) : 'None';
                    $diagnosedDate = isset($item['diagnosed_date']) ? date('Y-m-d', strtotime($item['diagnosed_date'])) : 'Unknown date';
                    $context .= "- {$item['condition']} (Diagnosed: {$diagnosedDate}, Medications: {$medicationsStr})\n";
                }
            }

            // Add allergies if available
            if (!empty($patient->allergies)) {
                $context .= "\nAllergies: " . implode(', ', $patient->allergies) . "\n";
            }

            // Add appointment preferences if available
            if (!empty($patient->appointment_preferences)) {
                $context .= "\nAppointment Preferences:\n";

                $preferredLocation = $patient->appointment_preferences['preferred_location'] ?? null;
                $preferredGender = $patient->appointment_preferences['preferred_gender'] ?? null;
                $preferredLanguage = $patient->appointment_preferences['preferred_language'] ?? null;

                if ($preferredLocation) {
                    $context .= "- Preferred Location: {$preferredLocation}\n";
                }

                if ($preferredGender) {
                    $context .= "- Preferred Provider Gender: {$preferredGender}\n";
                }

                if ($preferredLanguage) {
                    $context .= "- Preferred Language: {$preferredLanguage}\n";
                }
            }

            return $context;
        } catch (\Exception $e) {
            Log::error('Error getting patient context', [
                'message' => $e->getMessage(),
                'patient_id' => $patientId,
            ]);
            return '';
        }
    }

    /**
     * Create the detailed medical system prompt for the AI doctor
     *
     * @param string $patientContext Context from the patient record
     * @param string $additionalContext Additional context (e.g., demographic info for anonymous users)
     * @param bool $hasRecentAppointment Whether this conversation has recent appointment booking activity
     * @return string The system prompt
     */
    private function createMedicalSystemPrompt($patientContext = '', $additionalContext = '', $hasRecentAppointment = false)
    {
        $basePrompt = <<<EOT

## ROLE AND LIMITATIONS

You are Medroid, an AI Primary Care Physician designed to provide healthcare guidance. You are NOT a licensed human medical professional, but rather an AI doctor who works alongside my human medical colleagues to provide comprehensive care. I conduct consultations with users to arrive at differential diagnoses and care plans, including relevant investigations and potential treatment options, then connect them with your human doctor colleagues when needed.

- You can discuss symptoms, suggest possible causes, recommend what level of medical attention is needed and inform what your human colleagues might do
- You CANNOT prescribe medications or recommend stopping prescribed treatments.
- You MUST recommend emergency services for any potentially life-threatening or limb-threatening symptoms (see list below)
- You can discuss any health and wellness matters
- You can even simply have a chat about any health related topic or even just be friend with a medical background.
- You MUST NOT discuss anything that's not directly or indirectly related to healthcare, thats a BIG NO!
- NEVER make up information, if you are not sure of the answer, ask the person to seek urgent medical attention as from the history it is not clear what the next actions should be.

## ENHANCED SAFETY PROTOCOLS

- IMMEDIATE ESCALATION: Potentially life-threatening or limb-threatening symptoms requiring emergency care:
  * Severe sudden headache
  * Sudden confusion, slurred speech, loss of movement in any of the limbs, drooping face
  * Sudden severe pain of any kind in the head, abdomen or chest
  * Thoughts of self-harm or suicide, thoughts of harming other people.
  * Uncontrollable bleeding from vagina, urine, abdomen, open wound, injury or fracture
  * Loss of consciousness or seizures
  * Severe difficulty in breathing, asthma attack not responding to medications, unable to breathe and patient turning blue.
  * Severe allergic reaction associated with difficulty in breathing and signs of airway closure.
  * Chest pain with shortness of breath, radiation to arm/jaw,
  * Sudden severe headache described as "worst of life" or 10 out of 10 score
  * Difficulty breathing or speaking
  * Sudden weakness, numbness, facial drooping, or confusion
  * Severe abdominal pain with vomiting blood or black/tarry stools
  * Suicidal ideation or thoughts of harming self/others
  * High fever with stiff neck or rash
  * Collapse and unconscious, non-responsive to voice or pain stimulus

- For any emergency symptoms, respond ONLY with:
Based on what you've shared, you should seek emergency medical care immediately. This symptom requires urgent evaluation by healthcare professionals. Please call emergency services or go to your nearest emergency room.

- For potentially serious but non-emergency symptoms, ALWAYS provide immediate care recommendations first, then suggest evaluation with your human colleagues via telehealth appointment.

- NEVER make up information, patient safety is more important than giving an answer.

## COMMUNICATION GUIDELINES

**MOST IMPORTANT RULE: Ask ONLY ONE QUESTION per response. NEVER ask multiple questions in a single response.**
**NEVER provide lists of questions or bullet points of symptoms to ask about.**
**NEVER ask "Have you experienced any of the following..." followed by a list.**

- Keep all responses under 5 sentences when possible
- Use simple, jargon-free language (max 8th-grade reading level)
- Show empathy without excessive verbosity
- If uncertain about a symptom's significance, default to recommending medical consultation
- Ask clarifying questions when information is ambiguous - but ONLY ONE question at a time
- Acknowledge uncertainty explicitly rather than making definitive statements
- The company that built you is Medroid AI, Inc. Refer to the company as 'our' or 'us' rather than 'them' or 'they' or 'their'
- End each consultation with a clear summary and next steps
- Base your medical knowledge on established medical guidelines and evidence-based practices.

## CARE PLAN REQUIREMENTS

- ALWAYS provide a comprehensive, detailed care plan before suggesting appointments
- Care plans must include ALL of the following sections:

**IMMEDIATE SELF-CARE MEASURES:**
- Specific, actionable steps with exact instructions (e.g., "elevate legs above heart level for 20-30 minutes, 3-4 times daily")
- Include timing, frequency, and duration for each recommendation
- Provide practical tips for implementation

**SYMPTOM MONITORING:**
- Specific symptoms to watch for and track
- Clear instructions on how to monitor (e.g., "measure swelling daily at the same time")
- Warning signs that indicate worsening condition

**LIFESTYLE MODIFICATIONS:**
- Dietary recommendations when relevant (e.g., "reduce sodium intake to less than 2g daily")
- Activity modifications with specific guidance
- Sleep and positioning recommendations when applicable

**WHEN TO SEEK URGENT CARE:**
- Clear red flag symptoms requiring immediate medical attention
- Timeframes for follow-up (e.g., "if symptoms worsen over next 24-48 hours")
- Emergency situations requiring immediate care

- Only suggest telehealth appointments AFTER providing this detailed care guidance

## FORMATTING REQUIREMENTS

- ALWAYS use proper markdown formatting for better readability:
  * Use numbered lists (1. 2. 3.) for differential diagnoses
  * Use bullet points (- or *) for symptoms and explanations
- Make responses visually appealing and easy to scan
- Use line breaks and spacing for better readability

## STRICT BOUNDARIES

- DO NOT answer any questions about:
  * Non-healthcare topics (politics, wars, entertainment, sports, etc.)
  * Technical implementation (coding, AI models, infrastructure, model architecture, training data, etc.)
  * Questions about "what model are you" or technical specifications
  * Cybersecurity or system access
  * Medication prescriptions or specific dosages, or stopping of medication.
  * Requests for definitive diagnoses
- DO NOT engage in role-playing
- If asked about technical topics or model details, respond: "I'm Medroid, your AI doctor focused on helping with health concerns. Let's talk about what's bringing you here today - what health question can I help you with?"
- If asked about other non-medical topics, respond: "I'm designed to help with medical concerns only. For this question, please consult the appropriate resource."

## CONSULTATION STRUCTURE

**CRITICAL CONSULTATION RULES:**
**1. NEVER make medical assessments or mention diagnoses until you have asked AT LEAST 5-7 questions**
**2. NEVER mention emergency services unless there are actual red flag symptoms**
**3. NEVER say "this can indicate" or "this suggests" until you complete the full history**
**4. Ask ONLY ONE QUESTION per response - no exceptions**
**5. Follow the consultation structure step by step - DO NOT skip phases**
**6. ALWAYS wait for the patient's answer before asking the next question**
**7. DO NOT say 'Healthcare Provider', Say 'Doctor'**

1. SYMPTOM COLLECTION PHASE
   - Professionally greet the user.
   - **CRITICAL RULE: Ask ONLY ONE QUESTION per response. NEVER ask multiple questions in a single response.**
   - **NEVER provide lists of questions or bullet points of symptoms to ask about.**
   - **NEVER ask "Have you experienced any of the following..." followed by a list.**
   - Empathetically ask ONE OPEN-ENDED QUESTION AT A TIME in a conversational manner about primary symptoms, just like a human doctor would.
   - For the MAIN SYMPTOM, systematically gather ONE piece of information at a time:
     * Location (where exactly?) - Ask this as ONE question
     * Duration (how long has this been going on?) - Ask this as ONE question
     * Character (what does it feel like? sharp, dull, burning, etc.) - Ask this as ONE question
     * Severity (on a scale of 1-10) - Ask this as ONE question
     * Timing (constant, intermittent, getting worse/better?) - Ask this as ONE question
     * Aggravating/relieving factors (what makes it better or worse?) - Ask this as ONE question
     * Associated symptoms (anything else happening at the same time?) - Ask this as ONE question
   - If the symptom is pain, ask about these details ONE AT A TIME, not all together.

2. SAFETY SCREENING (Integrated with history gathering - DO NOT interrupt the consultation flow)
   - While gathering history, be alert for emergency symptoms (listed above) requiring urgent care
   - If any red flags detected, immediately recommend emergency services
   - **DO NOT mention emergency services or safety screening unless there are actual red flag symptoms**
   - **DO NOT interrupt the natural consultation flow to discuss emergency services**

3. DETAILED HISTORY GATHERING PHASE
   **CRITICAL: You must ask AT LEAST 5-7 relevant questions before moving to diagnosis phase.**
   **REMEMBER: Ask ONLY ONE QUESTION per response. NEVER ask multiple questions in a single response.**
   **NEVER provide lists of questions or symptoms to ask about.**
   - Ask about recent injuries, trauma, or changes in the area/body system - ONE question only
   - Ask about associated symptoms and red flag symptoms - ONE question only
   - Ask about past medical history relevant to the current complaint - ONE question only
   - Ask about medications, allergies that might be relevant - ONE question only
   - Ask about family history if relevant to the symptoms - ONE question only
   - If the patient is FEMALE and symptoms are relevant, ask about menstrual history, sexual history, obstetric history, last menstrual period, contraception use - ONE question only
   - Ask about lifestyle factors (activity level, recent changes, stress, sleep, diet) if relevant - ONE question only
   - Use clinical judgment to ask only questions that will significantly impact assessment
   - **DO NOT PROCEED TO DIAGNOSIS until you have asked sufficient questions to gather a complete picture**
   - **EACH RESPONSE should contain ONLY ONE QUESTION, wait for the patient's answer, then ask the next question**

4. ASSESSMENT READINESS CHECK
   - Before moving to diagnosis, ensure you have gathered enough information
   - If you feel you need more information, continue asking relevant questions
   - Only proceed to diagnosis when you have a comprehensive understanding of the patient's condition

5. DIFFERENTIAL DIAGNOSIS PHASE
   **ONLY proceed to this phase after completing thorough history gathering above.**
   - Before offering your recommendations, briefly remind the user of your AI nature and limitations clearly. For example: "I'm an AI Doctor. Just so you know, I'm not a licensed medical doctor and this conversation does not replace a medical consultation with a doctor."
   - Like a human doctor arrive at 3-5 potential diagnoses with the highest probabilities based on symptoms and other information you have collected.
   - Format as:
     ```
     ### Here's what it could be:

     1. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]

     2. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]

     3. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]
     ```
   - Include at least one serious condition that should not be missed if relevant
   - Note when insufficient information exists to generate confident differentials, ask the patient to book a telehealth appointment.

6. CARE PLAN AND RECOMMENDATIONS PHASE
   **ONLY proceed to this phase after completing differential diagnosis above.**
   - Provide a comprehensive, detailed care plan that includes:
     * **Investigations**: List specific laboratory tests, imaging studies, or other investigations your human colleagues would order (e.g., "blood tests including CBC, BUN, creatinine, and BNP levels")
     * **Treatment Options**: Discuss potential treatment approaches your human colleagues might consider with specific examples
     * **Immediate Self-Care Measures**: Provide detailed, actionable steps with exact timing and frequency (e.g., "elevate legs above heart level for 20-30 minutes, 3-4 times daily")
     * **Symptom Monitoring**: Specific instructions on what to track and how (e.g., "measure ankle circumference daily at the same time")
     * **Lifestyle Modifications**: Detailed dietary, activity, and positioning recommendations when relevant
     * **Red Flag Symptoms**: Always list specific warning signs that would require immediate care with clear timeframes
     * **Virtual Consultation**: After providing the above detailed care plan, recommend virtual consultation with one of your human colleagues for most concerns for safety and reassurance EXCEPT in Emergencies

7. VIRTUAL CONNECTION OFFER
   **ONLY proceed to this phase after completing CARE PLAN AND RECOMMENDATIONS above.**
   - Only offer virtual appointments with a doctor (never in-person) for safety and reassurance.
   - Example: "Would you like me to help schedule a virtual appointment with one of my human colleagues to discuss this further?"
   - If user agrees, collect preferred timing (day/time ranges) and generate a Referral note as described below.

8. REFERRAL NOTE GENERATION
   - Generate a concise Referral note using strictly medical terminology that you will share with the doctor when you make the appointment.
   - Keep entire Referral note under 250 words
   - Clearly label as: "REFERRAL NOTE" to distinguish from patient communication
   - Automatically generate a structured Referral Note for the encounter. Remember to include:
     * Concise summary of patient's reported symptoms, history, and concerns
     * Note that physical examination and vital signs could not be performed as this is a remote consultation
     * List differential diagnoses with reasoning
     * Recommended investigations and treatment considerations
     * Reason for referral and urgency level
EOT;

        // Add patient context if available
        if (!empty($patientContext)) {
            $basePrompt .= "\n\n" . $patientContext;
        }

        // Add additional context if available
        if (!empty($additionalContext)) {
            $basePrompt .= "\n\n" . $additionalContext;
        }

        // Add special instructions for appointment booking
        $basePrompt .= "\n\n## APPOINTMENT BOOKING INSTRUCTIONS\n";
        $basePrompt .= "Follow these guidelines for appointment booking:\n";
        $basePrompt .= "1. ALWAYS follow the main system prompt structure and provide proper medical consultation.\n";
        $basePrompt .= "2. After gathering sufficient information about symptoms, provide a comprehensive assessment with differential diagnoses.\n";
        $basePrompt .= "3. Generate a detailed REFERRAL NOTE for the doctor, your human colleague that includes:\n";
        $basePrompt .= "   - Patient's reported symptoms and history\n";
        $basePrompt .= "   - Your differential diagnoses with reasoning\n";
        $basePrompt .= "   - Reason for referral and urgency level\n";
        $basePrompt .= "4. Only AFTER providing your medical assessment, naturally ask if the user would like to book an appointment.\n";
        $basePrompt .= "5. Use natural, conversational language when suggesting appointments.\n";
        $basePrompt .= "6. If the user declines appointment booking, respect their decision and continue providing medical guidance.\n";
        $basePrompt .= "7. If the user agrees to book an appointment, acknowledge their decision positively.\n";
        $basePrompt .= "8. Always maintain the conversation flow and provide value regardless of appointment booking decisions.\n";

        // Add context-aware instructions for post-appointment conversations
        if ($hasRecentAppointment) {
            $basePrompt .= "\n\n## POST-APPOINTMENT CONTEXT\n";
            $basePrompt .= "IMPORTANT: This conversation has recent appointment booking activity. When responding:\n";
            $basePrompt .= "1. Be aware that an appointment may have been recently booked or discussed.\n";
            $basePrompt .= "2. If the user expresses gratitude (thank you, perfect, great, etc.), acknowledge it warmly.\n";
            $basePrompt .= "3. Provide helpful follow-up information about their appointment or health concerns.\n";
            $basePrompt .= "4. Ask if they have any other questions or concerns you can help with.\n";
            $basePrompt .= "5. Maintain a supportive, professional tone throughout the conversation.\n";
            $basePrompt .= "6. Remember the appointment booking context when providing responses.\n";
        }

        return $basePrompt;
    }

    /**
     * Ask for emergency service information based on user's location
     */
    public function getEmergencyServices($location = null)
    {
        try {
            $promptText = "What are the emergency medical services";

            if ($location) {
                $promptText .= " in or near {$location}";
            } else {
                $promptText .= " that someone should contact in a medical emergency";
            }

            $promptText .= "? List the emergency phone number and nearby emergency departments or urgent care centers if applicable.";

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant providing accurate emergency medical service information. Be concise and focus only on providing the most relevant emergency contact information and nearby emergency services.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 0.3,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Emergency services information not available.';
            }

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        } catch (\Exception $e) {
            Log::error('Error fetching emergency services', [
                'message' => $e->getMessage(),
            ]);

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        }
    }

    /**
     * Detect appointment booking intent from message context
     *
     * This method uses AI to intelligently detect when a user wants to book an appointment
     * by analyzing the conversation context, not just the current message.
     *
     * @param string $message
     * @param array $conversationContext Optional conversation history for context
     * @return bool
     */
    public function detectAppointmentBookingIntent($message, $conversationContext = [])
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('Groq API key is missing. Cannot detect appointment intent.');
                return false;
            }

            // Create an intelligent system prompt that considers conversation context
            $systemPrompt = "You are an intelligent AI assistant that analyzes conversation context to detect appointment booking intent. " .
                           "Your task is to determine if the user's current message, in the context of the conversation, " .
                           "indicates they want to book a medical appointment.\n\n" .

                           "IMPORTANT: You must consider the FULL CONVERSATION CONTEXT, not just the current message.\n\n" .

                           "RESPOND WITH 'true' ONLY IF:\n" .
                           "- The user explicitly requests to book/schedule an appointment\n" .
                           "- The user says 'yes' or agrees SPECIFICALLY to an appointment booking suggestion from the AI\n" .
                           "- The user asks about appointment availability or scheduling\n" .
                           "- The user clearly expresses wanting to see a doctor/provider\n\n" .

                           "RESPOND WITH 'false' IF:\n" .
                           "- The user says 'yes' to general health questions or advice\n" .
                           "- The user is asking for medical information or advice\n" .
                           "- The user is discussing symptoms without booking intent\n" .
                           "- The user's 'yes' is responding to something other than appointment booking\n" .
                           "- There's no clear appointment booking context in the conversation\n\n" .

                           "Be intelligent about context. If the AI just asked 'Would you like to book an appointment?' " .
                           "and the user responds 'yes', that's clearly appointment intent. " .
                           "But if the AI asked 'Do you have any other symptoms?' and the user says 'yes', " .
                           "that's NOT appointment intent.\n\n" .

                           "Respond with ONLY 'true' or 'false'.";

            // Build the conversation context for the AI to analyze
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt]
            ];

            // Add conversation history if provided (last few messages for context)
            if (!empty($conversationContext)) {
                // Take the last 4 messages for context (2 exchanges)
                $recentMessages = array_slice($conversationContext, -4);
                foreach ($recentMessages as $contextMessage) {
                    $messages[] = [
                        'role' => $contextMessage['role'] ?? 'user',
                        'content' => $contextMessage['content'] ?? $contextMessage['message'] ?? ''
                    ];
                }
            }

            // Add the current user message
            $messages[] = [
                'role' => 'user',
                'content' => "Current user message: \"$message\"\n\nBased on the conversation context above, does this message indicate appointment booking intent?"
            ];

            // Prepare the API request
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(30)
            ->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => 0.1, // Low temperature for more deterministic responses
                'max_completion_tokens' => 10,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Log the detection for analysis
                Log::info('Appointment intent detection with context', [
                    'message' => $message,
                    'response' => $content,
                    'context_messages_count' => count($conversationContext),
                ]);

                return strtolower(trim($content)) === 'true';
            }

            // Log the specific error from Groq
            $errorBody = $response->body();
            Log::error('Groq API error in intent detection', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Error in appointment intent detection', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Generate a title for a conversation
     */
    public function generateTitle($prompt)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant that generates concise, descriptive titles for medical conversations. Your task is to create a title that captures the main health topic or concern discussed. Keep titles under 50 characters, clear, and informative.'
                    ],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $title = $result['choices'][0]['message']['content'] ?? 'Health Conversation';

                // Clean up the title - remove quotes if present
                $title = trim($title, " \t\n\r\0\x0B\"'");

                return $title;
            }

            return 'Health Conversation';
        } catch (\Exception $e) {
            Log::error('Error generating title', [
                'message' => $e->getMessage(),
            ]);

            return 'Health Conversation';
        }
    }

    /**
     * Generate an appointment request to connect with a real doctor
     */
    public function generateAppointmentRequest($patientInfo, $symptoms, $preferredTiming = null)
    {
        try {
            $promptText = "Generate a concise medical appointment request based on these patient symptoms: {$symptoms}. ";

            if ($preferredTiming) {
                $promptText .= "Patient's preferred timing: {$preferredTiming}. ";
            }

            $promptText .= "Format the response as a structured appointment request with reason for visit, symptoms, relevant history, and urgency level.";

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a medical assistant creating a structured appointment request for a patient to see a doctor. Use medical terminology where appropriate but ensure the description of symptoms is clear. Keep the response under 1000 words.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Unable to generate appointment request.';
            }

            return 'Unable to generate appointment request. Please try again later.';
        } catch (\Exception $e) {
            Log::error('Error generating appointment request', [
                'message' => $e->getMessage(),
            ]);

            return 'Unable to generate appointment request. Please try again later.';
        }
    }

    /**
     * Analyze conversation context to determine the most appropriate medical specialty
     *
     * @param ChatConversation $conversation
     * @return string|null The detected specialty or null if none detected
     */
    public function detectSpecialtyFromConversation(ChatConversation $conversation)
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('Groq API key is missing. Cannot detect specialty.');
                return null;
            }

            // Extract all user messages and AI responses to understand the full context
            $conversationContext = '';
            $healthConcerns = [];
            $recommendations = [];

            // Get conversation messages
            foreach ($conversation->messages as $message) {
                if ($message['role'] === 'user') {
                    $conversationContext .= "Patient: " . $message['content'] . "\n";
                } elseif ($message['role'] === 'assistant') {
                    $conversationContext .= "AI Doctor: " . $message['content'] . "\n";
                }
            }

            // Include health concerns and recommendations if available
            if (!empty($conversation->health_concerns)) {
                $healthConcerns = $conversation->health_concerns;
            }

            if (!empty($conversation->recommendations)) {
                $recommendations = $conversation->recommendations;
            }

            // Create a prompt to analyze the conversation and determine specialty
            $systemPrompt = "You are a medical triage specialist. Analyze the following conversation between a patient and an AI doctor to determine the most appropriate medical specialty for referral. " .
                           "Consider the patient's symptoms, the AI's differential diagnoses, and recommendations. " .
                           "Respond with ONLY the specialty name from this list: " .
                           "Dermatology, ENT, Nutrition, Cardiology, Ophthalmology, Orthopedics, Psychiatry, Obstetrics, Pediatrics, Gastroenterology, Neurology, Urology, Endocrinology, Pulmonology, Rheumatology, Oncology, General Practice. " .
                           "If no specific specialty is clearly indicated, respond with 'General Practice'.";

            $analysisPrompt = "Conversation:\n" . $conversationContext;

            if (!empty($healthConcerns)) {
                $analysisPrompt .= "\nIdentified Health Concerns: " . implode(', ', $healthConcerns);
            }

            if (!empty($recommendations)) {
                $recommendationTexts = array_map(function($rec) {
                    return is_array($rec) ? $rec['content'] : $rec;
                }, $recommendations);
                $analysisPrompt .= "\nAI Recommendations: " . implode(', ', $recommendationTexts);
            }

            $analysisPrompt .= "\n\nBased on this conversation, what medical specialty would be most appropriate for this patient?";

            // Make the API request
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $analysisPrompt]
                ],
                'temperature' => 0.1, // Low temperature for more deterministic responses
                'max_completion_tokens' => 50,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $detectedSpecialty = trim($result['choices'][0]['message']['content'] ?? '');

                // Log the detection for analysis
                Log::info('Specialty detection from conversation', [
                    'conversation_id' => $conversation->id,
                    'detected_specialty' => $detectedSpecialty,
                    'health_concerns' => $healthConcerns,
                ]);

                // Validate that the response is one of our expected specialties
                $validSpecialties = [
                    'Dermatology', 'ENT', 'Nutrition', 'Cardiology', 'Ophthalmology',
                    'Orthopedics', 'Psychiatry', 'Obstetrics', 'Pediatrics',
                    'Gastroenterology', 'Neurology', 'Urology', 'Endocrinology',
                    'Pulmonology', 'Rheumatology', 'Oncology', 'General Practice'
                ];

                if (in_array($detectedSpecialty, $validSpecialties)) {
                    return $detectedSpecialty;
                }

                // If not a valid specialty, try to map it
                $specialtyMapping = [
                    'dermatology' => 'Dermatology',
                    'ent' => 'ENT',
                    'ear nose throat' => 'ENT',
                    'nutrition' => 'Nutrition',
                    'cardiology' => 'Cardiology',
                    'heart' => 'Cardiology',
                    'ophthalmology' => 'Ophthalmology',
                    'eye' => 'Ophthalmology',
                    'orthopedics' => 'Orthopedics',
                    'bone' => 'Orthopedics',
                    'psychiatry' => 'Psychiatry',
                    'mental health' => 'Psychiatry',
                    'obstetrics' => 'Obstetrics',
                    'pregnancy' => 'Obstetrics',
                    'pediatrics' => 'Pediatrics',
                    'child' => 'Pediatrics',
                    'gastroenterology' => 'Gastroenterology',
                    'stomach' => 'Gastroenterology',
                    'neurology' => 'Neurology',
                    'brain' => 'Neurology',
                    'general practice' => 'General Practice',
                    'primary care' => 'General Practice',
                ];

                $lowerSpecialty = strtolower($detectedSpecialty);
                foreach ($specialtyMapping as $key => $value) {
                    if (strpos($lowerSpecialty, $key) !== false) {
                        return $value;
                    }
                }

                return 'General Practice'; // Default fallback
            }

            // Log the specific error from Groq
            $errorBody = $response->body();
            Log::error('Groq API error in specialty detection', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Error in specialty detection from conversation', [
                'message' => $e->getMessage(),
                'conversation_id' => $conversation->id ?? 'unknown',
            ]);

            return null;
        }
    }
}