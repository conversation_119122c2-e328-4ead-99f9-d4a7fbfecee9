<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\Patient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAiGpt4Service
{
    protected $apiKey;
    protected $apiUrl = 'https://api.openai.com/v1';
    protected $model = 'gpt-4o-mini';

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');

        // Log if API key is missing
        if (empty($this->apiKey)) {
            Log::warning('OpenAI API key is missing. Please set OPENAI_API_KEY in your .env file.');
        }
    }

    /**
     * Generate a medical consultation response from GPT-4o
     *
     * @param ChatConversation $conversation The conversation to generate a response for
     * @param bool $includePatientContext Whether to include patient context from the database
     * @param string $additionalContext Additional context to include in the prompt (e.g., demographic info for anonymous users)
     * @return array The structured response
     */
    public function generateMedicalConsultation(ChatConversation $conversation, $includePatientContext = true, $additionalContext = '')
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('OpenAI API key is missing. Cannot generate response.');
                return [
                    'message' => 'I apologize, but our AI service is currently unavailable. Please try again later or contact support.',
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                ];
            }

            // Format the conversation history
            $messages = $this->formatMessages($conversation);

            // Get patient context if requested
            $patientContext = '';
            if ($includePatientContext && $conversation->patient_id) {
                $patientContext = $this->getPatientContext($conversation->patient_id);
            }

            // Add the system prompt for the medical consultation
            $systemMessage = [
                'role' => 'system',
                'content' => $this->createMedicalSystemPrompt($patientContext, $additionalContext)
            ];

            // Prepare the API request with timeout
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(60) // 60 second timeout for AI requests
            ->post("{$this->apiUrl}/chat/completions", [
                'model' => $this->model,
                'messages' => array_merge([$systemMessage], $messages),
                'temperature' => 1.00,
                'max_tokens' => 10000,
                'top_p' => 1.00,
                'store' => true,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Process the response to extract structured information
                $structuredResponse = $this->processAiResponse($content, $conversation);

                return $structuredResponse;
            }

            // Log the specific error from OpenAI
            $errorBody = $response->body();
            Log::error('OpenAI API error response', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            throw new \Exception('Failed to get response from OpenAI: ' . $errorBody);
        } catch (\Exception $e) {
            Log::error('Error in OpenAI consultation', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'message' => 'I apologize, but I encountered an error processing your request. Please try again or contact support if the issue persists.',
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Format the conversation messages for the OpenAI API
     */
    private function formatMessages(ChatConversation $conversation)
    {
        return collect($conversation->messages)->map(function ($message) {
            return [
                'role' => $message['role'],
                'content' => $message['content'],
            ];
        })->toArray();
    }

    /**
     * Process the AI response to extract structured medical information
     */
    private function processAiResponse($content, ChatConversation $conversation)
    {
        try {
            // Initialize the structured response
            $structuredResponse = [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'escalation_reason' => null,
                'referral_note' => null,
            ];

            // Check for emergency escalation
            $emergencyKeywords = [
                'seek emergency medical care',
                'call 911',
                'go to the emergency room',
                'emergency services',
                'emergency department',
                'call an ambulance',
                'immediate medical attention',
                'urgent medical care',
            ];

            foreach ($emergencyKeywords as $keyword) {
                if (stripos($content, $keyword) !== false) {
                    $structuredResponse['escalate'] = true;
                    $structuredResponse['escalation_reason'] = 'Potential emergency medical situation detected.';
                    break;
                }
            }

            // Extract differential diagnoses
            if (preg_match('/Here\'s what it could be:(.*?)(?=CLEAR RECOMMENDATION|VIRTUAL CONNECTION|REFERRAL NOTE|$)/s', $content, $matches)) {
                $diagnosesText = $matches[1];
                preg_match_all('/\d+\.\s+([^-]+)[\s-]+(\d+)%/s', $diagnosesText, $diagnosesMatches);

                if (!empty($diagnosesMatches[1])) {
                    foreach ($diagnosesMatches[1] as $index => $condition) {
                        $structuredResponse['health_concerns'][] = trim($condition);
                    }
                }
            }

            // Extract recommendations
            if (preg_match('/RECOMMENDATION.*?((?:\n|.)*?)(?=REFERRAL NOTE|VIRTUAL CONNECTION|$)/si', $content, $matches)) {
                $recommendationsText = $matches[1];
                $recommendationLines = preg_split('/\n+/', $recommendationsText);

                foreach ($recommendationLines as $line) {
                    $line = trim($line);
                    if (empty($line) || strlen($line) < 10) continue;

                    // Categorize the recommendation
                    $type = 'general';

                    if (stripos($line, 'consult') !== false ||
                        stripos($line, 'doctor') !== false ||
                        stripos($line, 'specialist') !== false) {
                        $type = 'specialist';
                    } else if (stripos($line, 'test') !== false ||
                               stripos($line, 'lab') !== false ||
                               stripos($line, 'imaging') !== false) {
                        $type = 'diagnostic';
                    } else if (stripos($line, 'take') !== false ||
                               stripos($line, 'medication') !== false ||
                               stripos($line, 'dose') !== false) {
                        $type = 'medication_info';
                    } else if (stripos($line, 'exercise') !== false ||
                               stripos($line, 'diet') !== false ||
                               stripos($line, 'sleep') !== false ||
                               stripos($line, 'avoid') !== false) {
                        $type = 'lifestyle';
                    } else if (stripos($line, 'warning') !== false ||
                               stripos($line, 'seek') !== false ||
                               stripos($line, 'emergency') !== false ||
                               stripos($line, 'immediately') !== false) {
                        $type = 'warning';
                        // Also flag for escalation if severe warnings are present
                        if (stripos($line, 'immediate') !== false ||
                            stripos($line, 'emergency') !== false) {
                            $structuredResponse['escalate'] = true;
                            $structuredResponse['escalation_reason'] = 'Warning signs for potential emergency.';
                        }
                    }

                    $structuredResponse['recommendations'][] = [
                        'type' => $type,
                        'content' => $line,
                        'confidence' => 0.85, // Default confidence
                    ];
                }
            }

            // Extract referral note if present
            if (preg_match('/REFERRAL NOTE[:\s]*((?:\n|.)*?)(?=\[end|$)/si', $content, $matches)) {
                $structuredResponse['referral_note'] = trim($matches[1]);

                // Remove the referral note from the user-facing message
                $structuredResponse['message'] = str_replace($matches[0], '', $content);
            }

            return $structuredResponse;
        } catch (\Exception $e) {
            Log::error('Error processing AI response', [
                'message' => $e->getMessage(),
                'content' => $content,
            ]);

            return [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Get patient context information if available
     */
    private function getPatientContext($patientId)
    {
        try {
            $patient = Patient::find($patientId);
            if (!$patient) {
                return '';
            }

            $context = "Patient context:\n";

            // Add demographic information (gender and age)
            $demographicInfo = $patient->getDemographicInfo();
            if (!empty($demographicInfo)) {
                $context .= "Demographics:\n";

                if (isset($demographicInfo['gender'])) {
                    $context .= "- Gender: {$demographicInfo['gender']}\n";
                }

                if (isset($demographicInfo['age'])) {
                    $context .= "- Age: {$demographicInfo['age']} years\n";
                }

                $context .= "\n";
            }

            // Add health history if available
            if (!empty($patient->health_history)) {
                $context .= "Health History:\n";
                foreach ($patient->health_history as $item) {
                    $medicationsStr = isset($item['medications']) ? implode(', ', $item['medications']) : 'None';
                    $diagnosedDate = isset($item['diagnosed_date']) ? date('Y-m-d', strtotime($item['diagnosed_date'])) : 'Unknown date';
                    $context .= "- {$item['condition']} (Diagnosed: {$diagnosedDate}, Medications: {$medicationsStr})\n";
                }
            }

            // Add allergies if available
            if (!empty($patient->allergies)) {
                $context .= "\nAllergies: " . implode(', ', $patient->allergies) . "\n";
            }

            // Add appointment preferences if available
            if (!empty($patient->appointment_preferences)) {
                $context .= "\nAppointment Preferences:\n";

                $preferredLocation = $patient->appointment_preferences['preferred_location'] ?? null;
                $preferredGender = $patient->appointment_preferences['preferred_gender'] ?? null;
                $preferredLanguage = $patient->appointment_preferences['preferred_language'] ?? null;

                if ($preferredLocation) {
                    $context .= "- Preferred Location: {$preferredLocation}\n";
                }

                if ($preferredGender) {
                    $context .= "- Preferred Provider Gender: {$preferredGender}\n";
                }

                if ($preferredLanguage) {
                    $context .= "- Preferred Language: {$preferredLanguage}\n";
                }
            }

            return $context;
        } catch (\Exception $e) {
            Log::error('Error getting patient context', [
                'message' => $e->getMessage(),
                'patient_id' => $patientId,
            ]);
            return '';
        }
    }

    /**
     * Create the detailed medical system prompt for the AI doctor
     *
     * @param string $patientContext Context from the patient record
     * @param string $additionalContext Additional context (e.g., demographic info for anonymous users)
     * @return string The system prompt
     */
    private function createMedicalSystemPrompt($patientContext = '', $additionalContext = '')
    {
        $basePrompt = <<<EOT

## ROLE AND LIMITATIONS

You are Medroid, an AI Primary Care Physician designed to provide healthcare guidance. You are NOT a licensed human medical professional, but rather an AI doctor who does a consultation with users to arrive at a list of differential diagnoses and a careplan including relevant investigations and potential treatment options and determine appropriate next steps. Offer a virtual consultation with a human doctor and some self-care options (where applicable).

- You can discuss symptoms, suggest possible causes, recommend what level of medical attention is needed and inform what a human doctor might do
- You CANNOT prescribe medications or recommend stopping prescribed treatments.
- You MUST recommend emergency services for any potentially life-threatening or limb-threatening symptoms (see list below)
- You can discuss any health and wellness matters
- You can even simply have a chat about any health related topic or even just be friend with a medical background.
- You MUST NOT discuss anything that's not directly or indirectly related to healthcare, thats a BIG NO!
- NEVER make up information, if you are not sure of the answer, ask the person to seek urgent medical attention as from the history it is not clear what the next actions should be.

## ENHANCED SAFETY PROTOCOLS

- IMMEDIATE ESCALATION: Potentially life-threatening or limb-threatening symptoms requiring emergency care:
  * Severe sudden headache
  * Sudden confusion, slurred speech, loss of movement in any of the limbs, drooping face
  * Sudden severe pain of any kind in the head, abdomen or chest
  * Thoughts of self-harm or suicide, thoughts of harming other people.
  * Uncontrollable bleeding from vagina, urine, abdomen, open wound, injury or fracture
  * Loss of consciousness or seizures
  * Severe difficulty in breathing, asthma attack not responding to medications, unable to breathe and patient turning blue.
  * Severe allergic reaction associated with difficulty in breathing and signs of airway closure.
  * Chest pain with shortness of breath, radiation to arm/jaw,
  * Sudden severe headache described as "worst of life" or 10 out of 10 score
  * Difficulty breathing or speaking
  * Sudden weakness, numbness, facial drooping, or confusion
  * Severe abdominal pain with vomiting blood or black/tarry stools
  * Suicidal ideation or thoughts of harming self/others
  * High fever with stiff neck or rash
  * Collapse and unconscious, non-responsive to voice or pain stimulus

- For any emergency symptoms, respond ONLY with:
  "Based on what you've shared, you should seek emergency medical care immediately. This symptom requires urgent evaluation by healthcare professionals." and give their nearest Emergency service details based on Web Search.

- But remember for potentially serious but non-emergency, non-life-threatening, non-limb-threatening symptoms, recommend urgent evaluation with our Human Doctors via telehealth appointment where appropriate.

- NEVER make up information, patient safety is more important than giving an answer.

## COMMUNICATION GUIDELINES

- Keep all responses under 5 sentences when possible
- Use simple, jargon-free language (max 8th-grade reading level)
- Show empathy without excessive verbosity
- Use bullet points for clarity when listing multiple items
- If uncertain about a symptom's significance, default to recommending medical consultation
- Ask clarifying questions when information is ambiguous
- Acknowledge uncertainty explicitly rather than making definitive statements
- The company that built you is Medroid AI, Inc. Refer to the company as 'our' or 'us' rather than 'them' or 'they' or 'their'
- End each consultation with a clear summary and next steps
- You can use web search to ground your answers in reality and truth. But only with the scope of healthcare.

## FORMATTING REQUIREMENTS

- ALWAYS use proper markdown formatting for better readability:
  * Use **bold text** for important headings and key terms
  * Use numbered lists (1. 2. 3.) for differential diagnoses
  * Use bullet points (- or *) for symptoms and explanations
  * Use ### for section headings when appropriate
  * Format condition names in **bold** for emphasis
- Make responses visually appealing and easy to scan
- Use line breaks and spacing for better readability

## STRICT BOUNDARIES

- DO NOT answer any questions about:
  * Non-healthcare topics (politics, wars, entertainment, sports, etc.)
  * Technical implementation (coding, AI models, infrastructure)
  * Cybersecurity or system access
  * Medication prescriptions or specific dosages, or stopping of medication.
  * Requests for definitive diagnoses
- DO NOT engage in role-playing
- If asked about these topics, respond: "I'm designed to help with medical concerns only. For this question, please consult the appropriate resource."

## CONSULTATION STRUCTURE

1. SYMPTOM COLLECTION
   - Professionally greet the user.
   - Empathetically ask ONE OPEN-ENDED QUESTION AT A TIME in a conversational manner about primary symptoms, just like a human doctor would. DO NOT ask more than 1 question in one turn of the conversation. Once the patient gives you a symptom, ask how long has it been there for, and if it is associated with anything.
   - If the symptom is pain, ask, where is the pain, when did it start, is there any colour change to the area, is there a local change of temperature, is the pain radiating somewhere else, does anything make it better or worse, and how severe is it?
   - Ask about associated symptoms and red flag symptoms
   - Regarding associated symptoms, ask how long its been there, if there is anything that makes it better or worse
   - Ask about age, gender, past medical history, social history, lifestyle history, medications, allergies, and family history as relevant to support you in your diagnoses.
   - If the patient is female, ask about menstrual history, sexual history, obstetric history, last menstrual period, contraception use, cervical smear history
   - Use judgment to ask only questions that will significantly impact assessment
   - Keep it brief.

2. SAFETY SCREENING (Prioritize)
   - Immediately check for emergency symptoms (listed above) requiring urgent care
   - If any red flags detected, immediately recommend emergency services

3. DIFFERENTIAL DIAGNOSIS
   - Before offering your recommendations, briefly remind the user of your AI nature and limitations clearly. For example: I'm an AI Doctor. Just so you know, I'm not a licensed medical doctor and this conversation does not replace a medical consultation with a doctor.
   - Like a human doctor arrive at 3-5 potential diagnoses with the highest probabilities based on symptoms and other information you have collected.
   - Format as:
     ```
     ### Here's what it could be:

     1. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]

     2. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]

     3. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]
     ```
   - Include at least one serious condition that should not be missed if relevant
   - Note when insufficient information exists to generate confident differentials, ask the patient to see to book a telehealth appointment.

5. CLEAR RECOMMENDATION (2-3 sentences)
   - Give a clear care plan including a telehealth appointment with our human doctors when appropriate
   - Discuss relevant specific laboratory or imaging studies and treatment options a doctor would order.
   - Provide evidence-based self-care recommendations only when appropriate with real in-line citations and real sources
   - Always list specific warning signs or red flags (relevant to the current consultation) that would require immediate care
- Recommend virtual consultation with a primary care physician for most concerns for safety and reassurance EXCEPT in Emergencies.
   - VIRTUAL CONNECTION
      - Only offer virtual primary care physician appointments (never in-person) for safety and reassurance.
      - Example: Would you like me to help schedule a virtual appointment with a primary care physician to discuss this further?
      - If user agrees collect preferred timing (day/time ranges) and Generate a Referral note as described below.

7. REFERRAL NOTE
   - Generate a concise Referral note using strictly medical terminology that you will share with the human doctor (or physiotherapist/dietician or any other type of provider)  when you make the appointment.
   - Keep entire Referral note under 250 words
   - Clearly label as: "REFERRAL NOTE" to distinguish from patient communication
   - Automatically generate a structured Referral Note for the encounter. Remember to include
     Concise summary of patient's reported symptoms, history, and concerns.
     Note that physical examination and vital signs could not be performed as this is a remote consultation.
     List differential diagnoses with reasoning
     Reason for referral
EOT;

        // Add patient context if available
        if (!empty($patientContext)) {
            $basePrompt .= "\n\n" . $patientContext;
        }

        // Add additional context if available
        if (!empty($additionalContext)) {
            $basePrompt .= "\n\n" . $additionalContext;
        }

        // Add special instructions for appointment booking
        $basePrompt .= "\n\n## APPOINTMENT BOOKING INSTRUCTIONS\n";
        $basePrompt .= "When the user asks to book an appointment or when you determine an appointment might be beneficial:\n";
        $basePrompt .= "1. DO NOT suggest booking an appointment immediately. First, have a thorough conversation to understand the user's symptoms and concerns.\n";
        $basePrompt .= "2. After gathering sufficient information, provide a summary of the user's condition and your assessment.\n";
        $basePrompt .= "3. Only AFTER providing this summary, ask if the user would like to book an appointment with an appropriate specialist.\n";
        $basePrompt .= "4. Wait for explicit user consent before proceeding with appointment booking.\n";
        $basePrompt .= "5. Identify the appropriate medical specialty based on the user's symptoms or condition (e.g., dermatologist for skin issues).\n";
        $basePrompt .= "6. If the user has appointment preferences (location, provider gender, language), acknowledge and use these preferences.\n";
        $basePrompt .= "5. When suggesting specialists, explain why that type of doctor is appropriate for their condition\n";
        $basePrompt .= "6. Include the phrase 'I can help you book an appointment with a [specialist type]' to trigger the appointment booking flow\n";
        $basePrompt .= "7. For skin conditions, suggest dermatologists; for ear issues, suggest ENT specialists; for weight management, suggest nutritionists or primary care; for cholesterol issues, suggest cardiologists or primary care\n";

        return $basePrompt;
    }

    /**
     * Ask for emergency service information based on user's location
     */
    public function getEmergencyServices($location = null)
    {
        try {
            $promptText = "What are the emergency medical services";

            if ($location) {
                $promptText .= " in or near {$location}";
            } else {
                $promptText .= " that someone should contact in a medical emergency";
            }

            $promptText .= "? List the emergency phone number and nearby emergency departments or urgent care centers if applicable.";

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post("{$this->apiUrl}/chat/completions", [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant providing accurate emergency medical service information. Be concise and focus only on providing the most relevant emergency contact information and nearby emergency services.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 0.3,
                'max_tokens' => 10000,
                'top_p' => 1.00,
                'store' => true,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Emergency services information not available.';
            }

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        } catch (\Exception $e) {
            Log::error('Error fetching emergency services', [
                'message' => $e->getMessage(),
            ]);

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        }
    }

    /**
     * Detect appointment booking intent from message context
     *
     * This method has been modified to be more conservative in detecting appointment intent.
     * It now requires both:
     * 1. A clear appointment booking intent from the user
     * 2. Evidence that a proper medical conversation has occurred
     *
     * @param string $message
     * @return bool
     */
    public function detectAppointmentBookingIntent($message, $conversationContext = [])
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('OpenAI API key is missing. Cannot detect appointment intent.');
                return false;
            }

            // First, do a quick check for obvious non-appointment messages
            $lowerMessage = strtolower(trim($message));

            // If it's just a simple yes/no/okay without appointment context, be very conservative
            if (in_array($lowerMessage, ['yes', 'no', 'okay', 'ok', 'sure', 'alright', 'fine'])) {
                // For simple responses, we need much stronger evidence
                // Only return true if the message explicitly mentions appointments
                return false; // Let the conversation flow naturally instead of intercepting
            }

            // Check for explicit appointment-related keywords first
            $appointmentKeywords = [
                'book appointment', 'book an appointment', 'schedule appointment',
                'schedule an appointment', 'make appointment', 'make an appointment',
                'need appointment', 'want appointment', 'appointment booking',
                'book doctor', 'see doctor', 'visit doctor', 'consult doctor',
                'when can i book', 'available slots', 'appointment slots',
                'book consultation', 'schedule consultation'
            ];

            $hasExplicitKeywords = false;
            foreach ($appointmentKeywords as $keyword) {
                if (strpos($lowerMessage, $keyword) !== false) {
                    $hasExplicitKeywords = true;
                    break;
                }
            }

            // If no explicit keywords, don't even call the AI - return false
            if (!$hasExplicitKeywords) {
                return false;
            }

            // Create a more conservative system prompt for intent detection
            $systemPrompt = "You are an intelligent AI assistant that detects appointment booking intent. " .
                           "You must be VERY CONSERVATIVE and only return 'true' when there is CLEAR, EXPLICIT appointment booking intent.\n\n" .
                           "RESPOND WITH 'true' ONLY IF the message contains:\n" .
                           "- Explicit request to book/schedule an appointment\n" .
                           "- Direct questions about appointment availability\n" .
                           "- Clear statements about wanting to see a doctor/provider\n" .
                           "- Specific appointment booking language\n\n" .
                           "RESPOND WITH 'false' FOR:\n" .
                           "- General health questions\n" .
                           "- Symptom discussions\n" .
                           "- Medical advice requests\n" .
                           "- Simple yes/no responses without appointment context\n" .
                           "- Any ambiguous messages\n\n" .
                           "Be extremely conservative. When in doubt, respond 'false'.";

            // Prepare the API request with function calling
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post("{$this->apiUrl}/chat/completions", [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $message]
                ],
                'temperature' => 0.1, // Low temperature for more deterministic responses
                'max_tokens' => 10,
                'tools' => [
                    [
                        'type' => 'function',
                        'function' => [
                            'name' => 'detectAppointmentIntent',
                            'description' => 'Determines if the user message contains explicit appointment booking confirmation after a proper medical conversation',
                            'parameters' => [
                                'type' => 'object',
                                'properties' => [
                                    'hasAppointmentIntent' => [
                                        'type' => 'boolean',
                                        'description' => 'True ONLY if the message contains explicit appointment booking confirmation after a proper medical conversation'
                                    ],
                                    'confidence' => [
                                        'type' => 'number',
                                        'description' => 'Confidence score between 0 and 1'
                                    ],
                                    'reasoning' => [
                                        'type' => 'string',
                                        'description' => 'Brief explanation of why this was classified as appointment intent or not'
                                    ]
                                ],
                                'required' => ['hasAppointmentIntent']
                            ]
                        ]
                    ]
                ],
                'tool_choice' => ['type' => 'function', 'function' => ['name' => 'detectAppointmentIntent']]
            ]);

            if ($response->successful()) {
                $result = $response->json();

                // Check if there's a tool call in the response
                if (isset($result['choices'][0]['message']['tool_calls'][0]['function']['arguments'])) {
                    $arguments = json_decode($result['choices'][0]['message']['tool_calls'][0]['function']['arguments'], true);

                    // Log the detection for analysis
                    Log::info('Appointment intent detection', [
                        'message' => $message,
                        'hasIntent' => $arguments['hasAppointmentIntent'] ?? false,
                        'confidence' => $arguments['confidence'] ?? 0,
                        'reasoning' => $arguments['reasoning'] ?? 'No reasoning provided',
                        'had_explicit_keywords' => $hasExplicitKeywords,
                    ]);

                    return $arguments['hasAppointmentIntent'] ?? false;
                }

                // Fallback to simple text response if tool calling fails
                $content = $result['choices'][0]['message']['content'] ?? '';
                return strtolower(trim($content)) === 'true';
            }

            // Log the specific error from OpenAI
            $errorBody = $response->body();
            Log::error('OpenAI API error in intent detection', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Error in appointment intent detection', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Generate a title for a conversation
     */
    public function generateTitle($prompt)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post("{$this->apiUrl}/chat/completions", [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant that generates concise, descriptive titles for medical conversations. Your task is to create a title that captures the main health topic or concern discussed. Keep titles under 50 characters, clear, and informative.'
                    ],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'temperature' => 1.00,
                'max_tokens' => 10000,
                'top_p' => 1.00,
                'store' => true,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $title = $result['choices'][0]['message']['content'] ?? 'Health Conversation';

                // Clean up the title - remove quotes if present
                $title = trim($title, " \t\n\r\0\x0B\"'");

                return $title;
            }

            return 'Health Conversation';
        } catch (\Exception $e) {
            Log::error('Error generating title', [
                'message' => $e->getMessage(),
            ]);

            return 'Health Conversation';
        }
    }

    /**
     * Generate an appointment request to connect with a real doctor
     */
    public function generateAppointmentRequest($patientInfo, $symptoms, $preferredTiming = null)
    {
        try {
            $promptText = "Generate a concise medical appointment request based on these patient symptoms: {$symptoms}. ";

            if ($preferredTiming) {
                $promptText .= "Patient's preferred timing: {$preferredTiming}. ";
            }

            $promptText .= "Format the response as a structured appointment request with reason for visit, symptoms, relevant history, and urgency level.";

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post("{$this->apiUrl}/chat/completions", [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a medical assistant creating a structured appointment request for a patient to see a doctor. Use medical terminology where appropriate but ensure the description of symptoms is clear. Keep the response under 1000 words.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 1.00,
                'max_tokens' => 10000,
                'top_p' => 1.00,
                'store' => true,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Unable to generate appointment request.';
            }

            return 'Unable to generate appointment request. Please try again later.';
        } catch (\Exception $e) {
            Log::error('Error generating appointment request', [
                'message' => $e->getMessage(),
            ]);

            return 'Unable to generate appointment request. Please try again later.';
        }
    }
}
