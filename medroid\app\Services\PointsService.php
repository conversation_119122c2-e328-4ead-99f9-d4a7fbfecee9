<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserPoint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PointsService
{
    /**
     * Points configuration for different activities
     */
    protected $pointsConfig = [
        'login' => 5,
        'chat' => 10,
        'appointment_booked' => 25,
        'appointment_completed' => 50,
        'story_post' => 15,
        'story_view' => 2,
        'referral_signup' => 100,
        'founder_signup' => 100,
        'badge_earned' => 0, // Variable based on badge
        'profile_complete' => 20,
        'first_chat' => 30,
        'first_appointment' => 40,
    ];

    /**
     * Award points to a user for an activity
     */
    public function awardPoints(User $user, string $activityType, ?int $customPoints = null, array $metadata = []): ?UserPoint
    {
        try {
            $points = $customPoints ?? ($this->pointsConfig[$activityType] ?? 0);
            
            if ($points <= 0) {
                return null;
            }

            // Check for daily limits on certain activities
            if ($this->hasReachedDailyLimit($user, $activityType)) {
                Log::info('Daily points limit reached', [
                    'user_id' => $user->id,
                    'activity_type' => $activityType,
                ]);
                return null;
            }

            DB::beginTransaction();

            $userPoint = UserPoint::create([
                'user_id' => $user->id,
                'activity_type' => $activityType,
                'points_earned' => $points,
                'source' => $metadata['source'] ?? $activityType,
                'reference_type' => $metadata['reference_type'] ?? null,
                'reference_id' => $metadata['reference_id'] ?? null,
                'earned_date' => now()->toDateString(),
                'metadata' => $metadata,
            ]);

            // Update user's total points
            $totalPoints = $user->points()->sum('points_earned');
            $user->update(['total_points' => $totalPoints]);

            DB::commit();

            Log::info('Points awarded', [
                'user_id' => $user->id,
                'activity_type' => $activityType,
                'points' => $points,
                'total_points' => $totalPoints,
            ]);

            return $userPoint;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error awarding points: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'activity_type' => $activityType,
                'points' => $points ?? 0,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check if user has reached daily limit for an activity
     */
    protected function hasReachedDailyLimit(User $user, string $activityType): bool
    {
        $dailyLimits = [
            'login' => 1,
            'story_view' => 50,
            'chat' => 10,
        ];

        if (!isset($dailyLimits[$activityType])) {
            return false;
        }

        $todayCount = UserPoint::where('user_id', $user->id)
            ->where('activity_type', $activityType)
            ->where('earned_date', now()->toDateString())
            ->count();

        return $todayCount >= $dailyLimits[$activityType];
    }

    /**
     * Get user's points summary
     */
    public function getUserPointsSummary(User $user): array
    {
        $totalPoints = $user->points()->sum('points_earned');
        $todayPoints = $user->points()
            ->where('earned_date', now()->toDateString())
            ->sum('points_earned');
        
        $thisWeekPoints = $user->points()
            ->where('earned_date', '>=', now()->startOfWeek()->toDateString())
            ->sum('points_earned');

        $thisMonthPoints = $user->points()
            ->where('earned_date', '>=', now()->startOfMonth()->toDateString())
            ->sum('points_earned');

        $activityBreakdown = UserPoint::getUserPointsSummary($user->id);

        return [
            'total_points' => $totalPoints,
            'today_points' => $todayPoints,
            'week_points' => $thisWeekPoints,
            'month_points' => $thisMonthPoints,
            'activity_breakdown' => $activityBreakdown,
            'rank' => $this->getUserRank($user),
        ];
    }

    /**
     * Get user's rank based on total points
     */
    public function getUserRank(User $user): array
    {
        $userPoints = $user->total_points;
        
        $rank = User::where('total_points', '>', $userPoints)->count() + 1;
        $totalUsers = User::where('total_points', '>', 0)->count();

        $percentile = $totalUsers > 0 ? round((($totalUsers - $rank + 1) / $totalUsers) * 100, 1) : 0;

        return [
            'rank' => $rank,
            'total_users' => $totalUsers,
            'percentile' => $percentile,
        ];
    }

    /**
     * Get leaderboard
     */
    public function getLeaderboard(int $limit = 10): array
    {
        return User::where('total_points', '>', 0)
            ->orderBy('total_points', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($user, $index) {
                return [
                    'rank' => $index + 1,
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'total_points' => $user->total_points,
                    'is_founder' => $user->is_founder_member,
                    'profile_image' => $user->profile_image,
                ];
            })
            ->toArray();
    }

    /**
     * Get points history for a user
     */
    public function getPointsHistory(User $user, int $days = 30): array
    {
        $startDate = now()->subDays($days)->toDateString();
        
        return $user->points()
            ->where('earned_date', '>=', $startDate)
            ->orderBy('earned_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($point) {
                return [
                    'id' => $point->id,
                    'activity_type' => $point->activity_type,
                    'points_earned' => $point->points_earned,
                    'source' => $point->source,
                    'earned_date' => $point->earned_date,
                    'created_at' => $point->created_at,
                    'metadata' => $point->metadata,
                ];
            })
            ->toArray();
    }

    /**
     * Award points for app usage time
     */
    public function awardTimeBasedPoints(User $user, int $minutesSpent): ?UserPoint
    {
        // Award 1 point per 5 minutes spent, max 20 points per day
        $pointsToAward = min(floor($minutesSpent / 5), 20);
        
        if ($pointsToAward <= 0) {
            return null;
        }

        // Check if already awarded time-based points today
        $todayTimePoints = $user->points()
            ->where('activity_type', 'time_spent')
            ->where('earned_date', now()->toDateString())
            ->sum('points_earned');

        if ($todayTimePoints >= 20) {
            return null; // Already reached daily limit
        }

        $remainingPoints = 20 - $todayTimePoints;
        $finalPoints = min($pointsToAward, $remainingPoints);

        return $this->awardPoints($user, 'time_spent', $finalPoints, [
            'minutes_spent' => $minutesSpent,
            'source' => 'app_usage_time',
        ]);
    }

    /**
     * Get points configuration
     */
    public function getPointsConfig(): array
    {
        return $this->pointsConfig;
    }
}
