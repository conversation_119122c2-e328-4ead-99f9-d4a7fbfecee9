<?php

namespace App\Services;

use App\Models\Provider;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class ProviderService
{
    /**
     * Get providers with filters and pagination
     */
    public function getProviders(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Provider::with(['user'])
            ->where('verification_status', 'verified');

        // Apply filters
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        if (!empty($filters['specialization'])) {
            $query->where('specialization', $filters['specialization']);
        }

        if (!empty($filters['location'])) {
            $query->where('location', 'like', "%{$filters['location']}%");
        }

        if (!empty($filters['min_rating'])) {
            $query->where('rating', '>=', $filters['min_rating']);
        }

        // Sort by rating and availability
        $query->orderBy('rating', 'desc')
              ->orderBy('created_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Get a single provider with detailed information
     */
    public function getProvider(int $providerId): ?Provider
    {
        return Provider::with([
            'user',
            'services',
            'availability',
            'reviews.patient.user'
        ])->find($providerId);
    }

    /**
     * Get provider specializations
     */
    public function getSpecializations(): array
    {
        return Provider::distinct()
            ->pluck('specialization')
            ->filter()
            ->sort()
            ->values()
            ->toArray();
    }

    /**
     * Get providers for public listing (no authentication required)
     */
    public function getPublicProviders(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Provider::with(['user'])
            ->where('verification_status', 'verified')
            ->where('is_public', true);

        // Apply same filters as authenticated version
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        if (!empty($filters['specialization'])) {
            $query->where('specialization', $filters['specialization']);
        }

        if (!empty($filters['location'])) {
            $query->where('location', 'like', "%{$filters['location']}%");
        }

        if (!empty($filters['min_rating'])) {
            $query->where('rating', '>=', $filters['min_rating']);
        }

        $query->orderBy('rating', 'desc')
              ->orderBy('created_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Create or update provider profile
     */
    public function createOrUpdateProfile(User $user, array $data): Provider
    {
        $provider = Provider::where('user_id', $user->id)->first();

        if ($provider) {
            $provider->update($data);
        } else {
            $data['user_id'] = $user->id;
            $provider = Provider::create($data);
        }

        return $provider->load('user');
    }

    /**
     * Get provider profile for authenticated user
     */
    public function getProviderProfile(User $user): ?Provider
    {
        return Provider::with(['user', 'services', 'availability'])
            ->where('user_id', $user->id)
            ->first();
    }

    /**
     * Update provider verification status (admin only)
     */
    public function updateVerificationStatus(int $providerId, string $status, ?string $notes = null): bool
    {
        $provider = Provider::find($providerId);
        
        if (!$provider) {
            return false;
        }

        $provider->update([
            'verification_status' => $status,
            'verification_notes' => $notes,
            'verified_at' => $status === 'verified' ? now() : null
        ]);

        return true;
    }

    /**
     * Get provider statistics
     */
    public function getProviderStatistics(int $providerId): array
    {
        $provider = Provider::find($providerId);
        
        if (!$provider) {
            return [];
        }

        return [
            'total_appointments' => $provider->appointments()->count(),
            'completed_appointments' => $provider->appointments()->where('status', 'completed')->count(),
            'upcoming_appointments' => $provider->appointments()
                ->whereIn('status', ['scheduled', 'confirmed'])
                ->where('scheduled_at', '>', now())
                ->count(),
            'average_rating' => $provider->rating,
            'total_reviews' => $provider->reviews()->count(),
            'total_patients' => $provider->appointments()->distinct('patient_id')->count()
        ];
    }

    /**
     * Search providers by various criteria
     */
    public function searchProviders(string $query, array $filters = [], int $limit = 10): Collection
    {
        $providers = Provider::with(['user'])
            ->where('verification_status', 'verified')
            ->where(function ($q) use ($query) {
                $q->whereHas('user', function ($userQuery) use ($query) {
                    $userQuery->where('name', 'like', "%{$query}%");
                })
                ->orWhere('specialization', 'like', "%{$query}%")
                ->orWhere('bio', 'like', "%{$query}%");
            });

        // Apply additional filters
        if (!empty($filters['specialization'])) {
            $providers->where('specialization', $filters['specialization']);
        }

        if (!empty($filters['location'])) {
            $providers->where('location', 'like', "%{$filters['location']}%");
        }

        return $providers->orderBy('rating', 'desc')
                        ->limit($limit)
                        ->get();
    }

    /**
     * Get top-rated providers
     */
    public function getTopRatedProviders(int $limit = 10): Collection
    {
        return Provider::with(['user'])
            ->where('verification_status', 'verified')
            ->where('rating', '>=', 4.0)
            ->orderBy('rating', 'desc')
            ->orderBy('total_reviews', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get providers by specialization
     */
    public function getProvidersBySpecialization(string $specialization, int $limit = 10): Collection
    {
        return Provider::with(['user'])
            ->where('verification_status', 'verified')
            ->where('specialization', $specialization)
            ->orderBy('rating', 'desc')
            ->limit($limit)
            ->get();
    }
}
