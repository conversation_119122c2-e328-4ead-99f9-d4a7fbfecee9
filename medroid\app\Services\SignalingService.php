<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Ratchet\ConnectionInterface;
use Ratchet\MessageComponentInterface;
use Ratchet\WebSocket\WsConnection;
use Illuminate\Support\Str;

/**
 * WebRTC Signaling Service
 * 
 * This service handles WebRTC signaling for video consultations.
 * It uses Ratchet WebSockets to facilitate real-time communication.
 */
class SignalingService implements MessageComponentInterface
{
    /**
     * Active connections
     * 
     * @var \SplObjectStorage
     */
    protected $clients;
    
    /**
     * Rooms for video consultations
     * 
     * @var array
     */
    protected $rooms = [];
    
    /**
     * Connection to user mapping
     * 
     * @var array
     */
    protected $connectionToUser = [];
    
    /**
     * Connection to room mapping
     * 
     * @var array
     */
    protected $connectionToRoom = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->clients = new \SplObjectStorage();
        
        Log::info('Signaling service initialized');
    }
    
    /**
     * Handle new WebSocket connection
     * 
     * @param ConnectionInterface $conn
     * @return void
     */
    public function onOpen(ConnectionInterface $conn)
    {
        // Store the new connection
        $this->clients->attach($conn);
        
        // Extract room ID from the request URI
        $request = $conn->httpRequest;
        $path = $request->getUri()->getPath();
        $pathParts = explode('/', trim($path, '/'));
        $roomId = end($pathParts);
        
        // Extract token from the request headers
        $token = null;
        if ($request->hasHeader('Authorization')) {
            $authHeader = $request->getHeader('Authorization')[0];
            if (Str::startsWith($authHeader, 'Bearer ')) {
                $token = Str::substr($authHeader, 7);
            }
        }
        
        // Validate token and get user
        $user = $this->validateToken($token);
        if (!$user) {
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'Invalid or expired token',
            ]));
            $conn->close();
            return;
        }
        
        // Validate room
        $appointment = $this->validateRoom($roomId, $user);
        if (!$appointment) {
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'Invalid room or unauthorized access',
            ]));
            $conn->close();
            return;
        }
        
        // Store connection to user and room mappings
        $this->connectionToUser[$conn->resourceId] = $user;
        $this->connectionToRoom[$conn->resourceId] = $roomId;
        
        // Create room if it doesn't exist
        if (!isset($this->rooms[$roomId])) {
            $this->rooms[$roomId] = [
                'appointment_id' => $appointment->id,
                'connections' => [],
            ];
        }
        
        // Add connection to room
        $this->rooms[$roomId]['connections'][$conn->resourceId] = [
            'connection' => $conn,
            'user_id' => $user->id,
            'user_role' => $this->getUserRole($user, $appointment),
        ];
        
        // Log connection
        Log::info('New WebSocket connection', [
            'connection_id' => $conn->resourceId,
            'user_id' => $user->id,
            'room_id' => $roomId,
            'user_role' => $this->getUserRole($user, $appointment),
        ]);
        
        // Notify client of successful connection
        $conn->send(json_encode([
            'type' => 'connected',
            'room_id' => $roomId,
            'user_id' => $user->id,
            'user_role' => $this->getUserRole($user, $appointment),
        ]));
        
        // Notify other participants in the room
        $this->notifyRoomParticipants($roomId, $conn->resourceId, 'user_joined', [
            'user_id' => $user->id,
            'user_role' => $this->getUserRole($user, $appointment),
        ]);
    }
    
    /**
     * Handle WebSocket message
     * 
     * @param ConnectionInterface $from
     * @param string $msg
     * @return void
     */
    public function onMessage(ConnectionInterface $from, $msg)
    {
        // Decode message
        $data = json_decode($msg, true);
        if (!$data || !isset($data['type'])) {
            return;
        }
        
        // Get room ID for this connection
        $roomId = $this->connectionToRoom[$from->resourceId] ?? null;
        if (!$roomId || !isset($this->rooms[$roomId])) {
            return;
        }
        
        // Handle message based on type
        switch ($data['type']) {
            case 'offer':
            case 'answer':
            case 'candidate':
                // Forward signaling messages to other participants in the room
                $this->forwardSignalingMessage($roomId, $from->resourceId, $data);
                break;
                
            default:
                // Log unknown message type
                Log::warning('Unknown message type', [
                    'type' => $data['type'],
                    'connection_id' => $from->resourceId,
                ]);
                break;
        }
    }
    
    /**
     * Handle WebSocket connection close
     * 
     * @param ConnectionInterface $conn
     * @return void
     */
    public function onClose(ConnectionInterface $conn)
    {
        // Get room ID for this connection
        $roomId = $this->connectionToRoom[$conn->resourceId] ?? null;
        
        // Remove connection from room
        if ($roomId && isset($this->rooms[$roomId])) {
            // Get user ID for this connection
            $userId = $this->connectionToUser[$conn->resourceId]->id ?? null;
            
            // Notify other participants in the room
            if ($userId) {
                $this->notifyRoomParticipants($roomId, $conn->resourceId, 'user_left', [
                    'user_id' => $userId,
                ]);
            }
            
            // Remove connection from room
            unset($this->rooms[$roomId]['connections'][$conn->resourceId]);
            
            // Remove room if empty
            if (empty($this->rooms[$roomId]['connections'])) {
                unset($this->rooms[$roomId]);
            }
        }
        
        // Remove connection mappings
        unset($this->connectionToUser[$conn->resourceId]);
        unset($this->connectionToRoom[$conn->resourceId]);
        
        // Remove connection from clients
        $this->clients->detach($conn);
        
        // Log disconnection
        Log::info('WebSocket connection closed', [
            'connection_id' => $conn->resourceId,
        ]);
    }
    
    /**
     * Handle WebSocket error
     * 
     * @param ConnectionInterface $conn
     * @param \Exception $e
     * @return void
     */
    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        // Log error
        Log::error('WebSocket error', [
            'connection_id' => $conn->resourceId,
            'error' => $e->getMessage(),
        ]);
        
        // Close connection
        $conn->close();
    }
    
    /**
     * Validate token and get user
     * 
     * @param string|null $token
     * @return User|null
     */
    protected function validateToken($token)
    {
        if (!$token) {
            return null;
        }
        
        // In a real implementation, you would validate the token
        // and return the associated user
        // This is a simplified example
        
        // For now, just return a mock user
        return User::find(1);
    }
    
    /**
     * Validate room and check if user has access
     * 
     * @param string $roomId
     * @param User $user
     * @return Appointment|null
     */
    protected function validateRoom($roomId, User $user)
    {
        // Extract appointment ID from room ID
        // Room ID format: medroid-{hash}
        // The hash is generated in VideoConsultationService::createVideoSession
        
        // Find all telemedicine appointments
        $appointments = Appointment::where('is_telemedicine', true)
            ->where('video_session_id', $roomId)
            ->get();
        
        foreach ($appointments as $appointment) {
            // Check if user has access to this appointment
            if ($this->canAccessAppointment($user, $appointment)) {
                return $appointment;
            }
        }
        
        return null;
    }
    
    /**
     * Check if user has access to appointment
     * 
     * @param User $user
     * @param Appointment $appointment
     * @return bool
     */
    protected function canAccessAppointment(User $user, Appointment $appointment)
    {
        // Check if user is the patient
        if ($appointment->patient && $appointment->patient->user_id == $user->id) {
            return true;
        }
        
        // Check if user is the provider
        if ($appointment->provider && $appointment->provider->user_id == $user->id) {
            return true;
        }
        
        // Check if user is an admin
        if ($user->hasRole('admin')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Get user role for appointment
     * 
     * @param User $user
     * @param Appointment $appointment
     * @return string
     */
    protected function getUserRole(User $user, Appointment $appointment)
    {
        // Check if user is the patient
        if ($appointment->patient && $appointment->patient->user_id == $user->id) {
            return 'patient';
        }
        
        // Check if user is the provider
        if ($appointment->provider && $appointment->provider->user_id == $user->id) {
            return 'provider';
        }
        
        // Default role
        return 'guest';
    }
    
    /**
     * Forward signaling message to other participants in the room
     * 
     * @param string $roomId
     * @param int $fromConnectionId
     * @param array $data
     * @return void
     */
    protected function forwardSignalingMessage($roomId, $fromConnectionId, $data)
    {
        // Get room
        $room = $this->rooms[$roomId] ?? null;
        if (!$room) {
            return;
        }
        
        // Forward message to all other connections in the room
        foreach ($room['connections'] as $connectionId => $connectionData) {
            if ($connectionId != $fromConnectionId) {
                $connectionData['connection']->send(json_encode($data));
            }
        }
    }
    
    /**
     * Notify room participants of an event
     * 
     * @param string $roomId
     * @param int $excludeConnectionId
     * @param string $eventType
     * @param array $eventData
     * @return void
     */
    protected function notifyRoomParticipants($roomId, $excludeConnectionId, $eventType, $eventData)
    {
        // Get room
        $room = $this->rooms[$roomId] ?? null;
        if (!$room) {
            return;
        }
        
        // Prepare message
        $message = json_encode([
            'type' => $eventType,
            'data' => $eventData,
        ]);
        
        // Send message to all other connections in the room
        foreach ($room['connections'] as $connectionId => $connectionData) {
            if ($connectionId != $excludeConnectionId) {
                $connectionData['connection']->send($message);
            }
        }
    }
}
