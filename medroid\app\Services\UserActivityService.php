<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserActivityLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserActivityService
{
    /**
     * Log user activity
     *
     * @param User $user
     * @param string $activityType
     * @param array|null $metadata
     * @return UserActivityLog
     */
    public function logActivity(User $user, string $activityType = 'login', ?array $metadata = null)
    {
        try {
            $today = Carbon::today()->toDateString();

            $activityLog = UserActivityLog::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'activity_date' => $today,
                    'activity_type' => $activityType,
                ],
                [
                    'metadata' => $metadata,
                ]
            );

            // Award points for activity and update streak
            $this->updateUserActivityStats($user, $activityType, $metadata);

            return $activityLog;
        } catch (\Exception $e) {
            Log::error("Error logging user activity: " . $e->getMessage(), [
                'user_id' => $user->id,
                'activity_type' => $activityType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Update user activity stats and award points
     */
    protected function updateUserActivityStats(User $user, string $activityType, ?array $metadata = null)
    {
        try {
            // Update activity streak
            $streak = $this->getUserActivityStreak($user);
            $user->update([
                'activity_streak' => $streak,
                'last_activity_at' => now(),
            ]);

            // Award points for activity
            $pointsService = app(\App\Services\PointsService::class);
            $pointsService->awardPoints($user, $activityType, null, $metadata ?? []);

            // Check for new badges
            $badgeService = app(\App\Services\BadgeService::class);
            $badgeService->checkActivityBadges($user);

            // Update club membership level if applicable
            $clubService = app(\App\Services\ClubService::class);
            $clubService->updateMembershipLevel($user);

        } catch (\Exception $e) {
            Log::error("Error updating user activity stats: " . $e->getMessage(), [
                'user_id' => $user->id,
                'activity_type' => $activityType,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if user has been active for consecutive days
     *
     * @param User $user
     * @param int $days
     * @return bool
     */
    public function hasConsecutiveDaysActivity(User $user, int $days = 3)
    {
        try {
            $today = Carbon::today();
            $consecutiveDays = 0;

            // Check each of the previous days
            for ($i = 0; $i < $days; $i++) {
                $date = $today->copy()->subDays($i)->toDateString();

                $hasActivity = UserActivityLog::where('user_id', $user->id)
                    ->whereDate('activity_date', $date)
                    ->exists();

                if ($hasActivity) {
                    $consecutiveDays++;
                } else {
                    break; // Break the streak if a day is missed
                }
            }

            return $consecutiveDays >= $days;
        } catch (\Exception $e) {
            Log::error("Error checking consecutive days activity: " . $e->getMessage(), [
                'user_id' => $user->id,
                'days' => $days,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Get user's activity streak (number of consecutive days active)
     *
     * @param User $user
     * @return int
     */
    public function getUserActivityStreak(User $user)
    {
        try {
            $today = Carbon::today();
            $streak = 0;

            // Check each previous day until we find a day with no activity
            for ($i = 0; $i < 365; $i++) { // Limit to a year to prevent infinite loops
                $date = $today->copy()->subDays($i)->toDateString();

                $hasActivity = UserActivityLog::where('user_id', $user->id)
                    ->whereDate('activity_date', $date)
                    ->exists();

                if ($hasActivity) {
                    $streak++;
                } else {
                    break;
                }
            }

            return $streak;
        } catch (\Exception $e) {
            Log::error("Error getting user activity streak: " . $e->getMessage(), [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 0;
        }
    }
}
