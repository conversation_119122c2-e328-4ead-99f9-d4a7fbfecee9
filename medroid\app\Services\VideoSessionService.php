<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\User;
use App\Models\VideoConsultation;
use App\Models\VideoSessionParticipant;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use TaylanUnutmaz\AgoraTokenBuilder\RtcTokenBuilder;

class VideoSessionService
{
    protected $appId;
    protected $appCertificate;
    protected $expirationTimeInSeconds;

    public function __construct()
    {
        $this->appId = env('AGORA_APP_ID', '********************************');
        $this->appCertificate = env('AGORA_APP_CERTIFICATE', '********************************');
        $this->expirationTimeInSeconds = 3600; // 1 hour

        Log::info('VideoSessionService initialized', [
            'appId' => $this->appId,
            'hasAppCertificate' => !empty($this->appCertificate),
        ]);
    }

    /**
     * Create a new video session for an appointment.
     * This should be called by the provider to start the session.
     */
    public function createSession(Appointment $appointment, User $creator)
    {
        try {
            // First, end all existing sessions for this user to prevent conflicts
            $this->endAllUserSessions($creator);

            // Check if session already exists for this appointment
            $existingSession = VideoConsultation::where('appointment_id', $appointment->id)
                ->whereIn('status', ['created', 'active'])
                ->first();

            if ($existingSession) {
                // End the existing session and create a new one
                Log::info('Ending existing session before creating new one', [
                    'appointment_id' => $appointment->id,
                    'existing_session_id' => $existingSession->session_id,
                ]);

                $existingSession->end();

                // Clear the appointment's video session ID to force recreation
                $appointment->update(['video_session_id' => null]);
            }

            // Generate unique identifiers
            $sessionId = 'vs_' . Str::uuid();
            $channelName = 'medroid_' . $appointment->id . '_' . time();

            // Create video consultation record
            $videoConsultation = VideoConsultation::create([
                'appointment_id' => $appointment->id,
                'session_id' => $sessionId,
                'channel_name' => $channelName,
                'agora_app_id' => $this->appId,
                'status' => 'created',
                'created_by' => $creator->id,
                'max_participants' => 2,
                'current_participants' => 0,
                'session_config' => [
                    'video_enabled' => true,
                    'audio_enabled' => true,
                    'recording_enabled' => false,
                ],
            ]);

            // Update appointment with session ID only
            $appointment->update([
                'video_session_id' => $sessionId,
            ]);

            Log::info('Video session created successfully', [
                'appointment_id' => $appointment->id,
                'session_id' => $sessionId,
                'channel_name' => $channelName,
                'created_by' => $creator->id,
            ]);

            return [
                'success' => true,
                'session' => $videoConsultation,
                'message' => 'Session created successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Error creating video session', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Join a video session as a participant.
     */
    public function joinSession(Appointment $appointment, User $user)
    {
        try {
            // Get the video consultation
            $videoConsultation = VideoConsultation::where('appointment_id', $appointment->id)
                ->whereIn('status', ['created', 'active'])
                ->first();

            if (!$videoConsultation) {
                return [
                    'success' => false,
                    'message' => 'No active video session found for this appointment',
                ];
            }

            // Clean up any stale participants first
            $this->cleanupStaleParticipants($videoConsultation);

            // Check if session can accept participants
            if (!$videoConsultation->canAcceptParticipants()) {
                // Try to clean up user's existing sessions and retry
                Log::info('Session full, cleaning up user sessions and retrying', [
                    'user_id' => $user->id,
                    'appointment_id' => $appointment->id,
                ]);

                $this->endAllUserSessions($user);

                // Refresh the video consultation data
                $videoConsultation->refresh();

                // Check again after cleanup
                if (!$videoConsultation->canAcceptParticipants()) {
                    return [
                        'success' => false,
                        'message' => 'Session is full or not accepting participants',
                    ];
                }
            }

            // Determine user role
            $role = $this->getUserRole($user, $appointment);
            if (!$role) {
                return [
                    'success' => false,
                    'message' => 'User not authorized for this appointment',
                ];
            }

            // Check if participant already exists
            $participant = VideoSessionParticipant::where('video_consultation_id', $videoConsultation->id)
                ->where('user_id', $user->id)
                ->first();

            if ($participant) {
                // If participant exists but token is expired, refresh it
                if ($participant->isTokenExpired()) {
                    $this->refreshParticipantToken($participant, $videoConsultation->channel_name);
                }

                // Update status to joined if not already
                if (!$participant->isJoined()) {
                    $participant->join();
                }
            } else {
                // Create new participant
                $participant = $this->createParticipant($videoConsultation, $user, $role);
                if (!$participant) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create participant record',
                    ];
                }
            }

            // Update connection info with timestamp
            if ($participant) {
                $participant->update([
                    'connection_info' => array_merge($participant->connection_info ?? [], [
                        'last_join_attempt' => now()->toISOString(),
                        'join_count' => ($participant->connection_info['join_count'] ?? 0) + 1,
                    ])
                ]);
            }

            // Start session if this is the first participant
            if ($videoConsultation->status === 'created') {
                $videoConsultation->start();
            }

            // Clean up any stale participants (disconnected for more than 5 minutes)
            $this->cleanupStaleParticipants($videoConsultation);

            Log::info('User joined video session', [
                'appointment_id' => $appointment->id,
                'session_id' => $videoConsultation->session_id,
                'user_id' => $user->id,
                'role' => $role,
                'agora_uid' => $participant->agora_uid,
                'participant_count' => $videoConsultation->fresh()->current_participants,
                'channel_name' => $videoConsultation->channel_name,
                'app_id' => $this->appId,
                'token_length' => strlen($participant->agora_token),
            ]);

            return [
                'success' => true,
                'session_data' => [
                    'app_id' => $this->appId,
                    'channel' => $videoConsultation->channel_name,
                    'token' => $participant->agora_token,
                    'uid' => $participant->agora_uid,
                    'session_id' => $videoConsultation->session_id,
                ],
                'participant' => $participant,
                'session' => $videoConsultation,
            ];

        } catch (\Exception $e) {
            Log::error('Error joining video session', [
                'appointment_id' => $appointment->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to join session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Leave a video session.
     */
    public function leaveSession(Appointment $appointment, User $user)
    {
        try {
            $videoConsultation = VideoConsultation::where('appointment_id', $appointment->id)
                ->whereIn('status', ['created', 'active'])
                ->first();

            if (!$videoConsultation) {
                return [
                    'success' => false,
                    'message' => 'No active video session found',
                ];
            }

            $participant = VideoSessionParticipant::where('video_consultation_id', $videoConsultation->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$participant) {
                return [
                    'success' => false,
                    'message' => 'User not found in session',
                ];
            }

            // Mark participant as left
            $participant->leave();

            // End session if no participants left
            if ($videoConsultation->current_participants <= 0) {
                $videoConsultation->end();
            }

            Log::info('User left video session', [
                'appointment_id' => $appointment->id,
                'session_id' => $videoConsultation->session_id,
                'user_id' => $user->id,
                'remaining_participants' => $videoConsultation->current_participants,
            ]);

            return [
                'success' => true,
                'message' => 'Left session successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Error leaving video session', [
                'appointment_id' => $appointment->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to leave session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * End a video session completely.
     */
    public function endSession(Appointment $appointment, User $user)
    {
        try {
            $videoConsultation = VideoConsultation::where('appointment_id', $appointment->id)
                ->whereIn('status', ['created', 'active'])
                ->first();

            if (!$videoConsultation) {
                return [
                    'success' => false,
                    'message' => 'No active video session found',
                ];
            }

            // End the session
            $videoConsultation->end();

            // Update appointment status
            $appointment->update([
                'status' => 'completed',
                'consultation_ended_at' => now(),
            ]);

            Log::info('Video session ended', [
                'appointment_id' => $appointment->id,
                'session_id' => $videoConsultation->session_id,
                'ended_by' => $user->id,
            ]);

            return [
                'success' => true,
                'message' => 'Session ended successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Error ending video session', [
                'appointment_id' => $appointment->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to end session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create a participant record.
     */
    protected function createParticipant(VideoConsultation $videoConsultation, User $user, string $role)
    {
        try {
            // Generate unique UID for this participant
            $agoraUid = $this->generateUniqueUid($user->id, $role);

            // Generate token
            $token = $this->generateToken($videoConsultation->channel_name, $agoraUid);
            $expiresAt = now()->addSeconds($this->expirationTimeInSeconds);

            $participant = VideoSessionParticipant::create([
                'video_consultation_id' => $videoConsultation->id,
                'user_id' => $user->id,
                'agora_uid' => $agoraUid,
                'agora_token' => $token,
                'role' => $role,
                'status' => 'invited',
                'token_expires_at' => $expiresAt,
                'connection_info' => [
                    'user_agent' => request()->header('User-Agent'),
                    'ip_address' => request()->ip(),
                ],
            ]);

            return $participant;

        } catch (\Exception $e) {
            Log::error('Error creating participant', [
                'video_consultation_id' => $videoConsultation->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Refresh participant token.
     */
    protected function refreshParticipantToken(VideoSessionParticipant $participant, string $channelName)
    {
        try {
            $newToken = $this->generateToken($channelName, $participant->agora_uid);
            $expiresAt = now()->addSeconds($this->expirationTimeInSeconds);

            $participant->refreshToken($newToken, $expiresAt);

            Log::info('Participant token refreshed', [
                'participant_id' => $participant->id,
                'user_id' => $participant->user_id,
                'agora_uid' => $participant->agora_uid,
            ]);

        } catch (\Exception $e) {
            Log::error('Error refreshing participant token', [
                'participant_id' => $participant->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate Agora token.
     */
    protected function generateToken(string $channelName, int $uid)
    {
        try {
            if (empty($channelName)) {
                throw new \Exception('Channel name cannot be empty');
            }

            if (empty($this->appId) || empty($this->appCertificate)) {
                throw new \Exception('Agora App ID or Certificate not configured');
            }

            $uid = max(1, (int)$uid);
            $expireTime = time() + $this->expirationTimeInSeconds;

            $token = RtcTokenBuilder::buildTokenWithUid(
                $this->appId,
                $this->appCertificate,
                $channelName,
                $uid,
                1, // Role publisher
                $expireTime
            );

            if (empty($token) || substr($token, 0, 3) !== '006') {
                throw new \Exception('Generated token has invalid format');
            }

            return $token;

        } catch (\Exception $e) {
            Log::error('Error generating Agora token', [
                'error' => $e->getMessage(),
                'channel' => $channelName,
                'uid' => $uid,
            ]);

            throw $e;
        }
    }

    /**
     * Generate unique UID for Agora.
     */
    protected function generateUniqueUid(int $userId, string $role)
    {
        $rolePrefix = $role === 'patient' ? 1 : 2;
        $timeComponent = time() % 10000;
        $userComponent = $userId % 1000;

        $uid = ($rolePrefix * 10000000) + ($timeComponent * 1000) + $userComponent;
        $uid = max(1, min($uid, **********));

        return $uid;
    }

    /**
     * Get user role for appointment.
     */
    protected function getUserRole(User $user, Appointment $appointment)
    {
        if ($user->role === 'patient' && $user->patient && $user->patient->id === $appointment->patient_id) {
            return 'patient';
        }

        if ($user->role === 'provider' && $user->provider && $user->provider->id === $appointment->provider_id) {
            return 'provider';
        }

        // Admin/manager can join as provider
        if (in_array($user->role, ['admin', 'manager'])) {
            return 'provider';
        }

        return null;
    }

    /**
     * End all active video sessions for a specific user.
     * This is called when a provider wants to start a new call.
     */
    public function endAllUserSessions(User $user)
    {
        try {
            // Find all active sessions where the user is a participant
            $activeParticipants = VideoSessionParticipant::where('user_id', $user->id)
                ->whereIn('status', ['invited', 'joined'])
                ->with('videoConsultation')
                ->get();

            foreach ($activeParticipants as $participant) {
                $videoConsultation = $participant->videoConsultation;

                if ($videoConsultation && in_array($videoConsultation->status, ['created', 'active'])) {
                    Log::info('Ending user session', [
                        'user_id' => $user->id,
                        'session_id' => $videoConsultation->session_id,
                        'appointment_id' => $videoConsultation->appointment_id,
                    ]);

                    // Mark participant as left
                    $participant->leave();

                    // End the entire session if no participants left
                    if ($videoConsultation->current_participants <= 0) {
                        $videoConsultation->end();

                        // Clear the appointment's video session ID
                        if ($videoConsultation->appointment) {
                            $videoConsultation->appointment->update(['video_session_id' => null]);
                        }
                    }
                }
            }

            Log::info('Completed cleanup of user sessions', [
                'user_id' => $user->id,
                'sessions_processed' => $activeParticipants->count(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error ending user sessions', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Clean up stale participants who have been disconnected for too long.
     */
    protected function cleanupStaleParticipants(VideoConsultation $videoConsultation)
    {
        try {
            $staleThreshold = now()->subMinutes(2); // Reduced to 2 minutes for more aggressive cleanup

            // Find participants that are disconnected or have been "joined" for too long without activity
            $staleParticipants = VideoSessionParticipant::where('video_consultation_id', $videoConsultation->id)
                ->where(function ($query) use ($staleThreshold) {
                    $query->where('status', 'disconnected')
                          ->where('left_at', '<', $staleThreshold);
                })
                ->orWhere(function ($query) use ($staleThreshold, $videoConsultation) {
                    // Also clean up participants who joined but haven't been active
                    $query->where('video_consultation_id', $videoConsultation->id)
                          ->where('status', 'joined')
                          ->where('joined_at', '<', $staleThreshold)
                          ->whereNull('left_at');
                })
                ->get();

            foreach ($staleParticipants as $participant) {
                Log::info('Cleaning up stale participant', [
                    'participant_id' => $participant->id,
                    'user_id' => $participant->user_id,
                    'agora_uid' => $participant->agora_uid,
                    'status' => $participant->status,
                    'joined_at' => $participant->joined_at,
                    'left_at' => $participant->left_at,
                ]);

                // Force leave to update participant count
                if ($participant->status !== 'left') {
                    $participant->leave();
                }
            }

            // Force recalculate participant count after cleanup
            $videoConsultation->recalculateParticipantCount();

        } catch (\Exception $e) {
            Log::error('Error cleaning up stale participants', [
                'video_consultation_id' => $videoConsultation->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
