<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('providers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('specialization');
            $table->string('license_number')->nullable();
            $table->string('verification_status')->default('pending');

            // Practice locations as JSON (consider creating a separate table for this in the future)
            $table->json('practice_locations')->nullable();

            // Rating and basic info
            $table->decimal('rating', 3, 1)->nullable();
            $table->string('gender')->nullable();

            // Pricing information
            $table->json('pricing')->nullable();

            // Availability information
            $table->json('weekly_availability')->nullable();
            $table->json('absences')->nullable();

            // Additional provider fields from later migrations
            $table->text('bio')->nullable();
            $table->string('profile_image')->nullable();
            $table->string('education')->nullable();
            $table->integer('years_of_experience')->nullable();
            $table->boolean('accepts_insurance')->default(false);
            $table->json('insurance_providers')->nullable();

            // Indexes for common queries
            $table->index('user_id');
            $table->index('specialization');
            $table->index('verification_status');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('providers');
    }
};