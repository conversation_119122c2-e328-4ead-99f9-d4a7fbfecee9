<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('duration')->comment('Duration in minutes');
            $table->decimal('price', 10, 2);
            $table->string('category')->nullable();
            
            // Service availability
            $table->json('availability')->nullable()->comment('Service-specific availability overrides');
            
            // Telemedicine options
            $table->boolean('is_telemedicine')->default(false);
            $table->boolean('supports_video')->default(false);
            $table->boolean('supports_audio')->default(false);
            $table->boolean('supports_chat')->default(false);
            
            // Service status
            $table->boolean('active')->default(true);
            
            // Discounts and promotions
            $table->decimal('discount_percentage', 5, 2)->nullable();
            $table->timestamp('discount_valid_until')->nullable();
            
            // Indexes for common queries
            $table->index(['provider_id', 'active']);
            $table->index(['category', 'active']);
            $table->index('is_telemedicine');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('services');
    }
};
