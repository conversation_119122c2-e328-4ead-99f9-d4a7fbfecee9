<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('provider_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->nullable()->constrained()->nullOnDelete();

            // Use timestamp instead of separate date and time_slot
            $table->timestamp('scheduled_at')->nullable();

            // Keep date and time_slot for backward compatibility
            $table->date('date');
            $table->json('time_slot');

            // Consultation timing fields
            $table->timestamp('started_at')->nullable();
            $table->timestamp('ended_at')->nullable();
            $table->integer('duration_minutes')->nullable();

            // Telemedicine fields
            $table->boolean('is_telemedicine')->default(false);
            $table->string('video_session_id')->nullable();

            // Audio recording fields
            $table->boolean('has_recording')->default(false);
            $table->string('recording_url')->nullable();
            $table->string('recording_status')->nullable();

            // Payment fields
            $table->decimal('amount', 10, 2)->nullable();
            $table->string('payment_status')->default('pending');
            $table->string('payment_method')->nullable();
            $table->string('transaction_id')->nullable();

            // Basic fields
            $table->text('reason')->nullable();
            $table->string('status')->default('scheduled');
            $table->text('notes')->nullable();

            // Indexes for common queries
            $table->index(['patient_id', 'scheduled_at']);
            $table->index(['provider_id', 'scheduled_at']);
            $table->index(['scheduled_at', 'status']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('appointments');
    }
};