<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('social_contents', function (Blueprint $table) {
            $table->id();
            $table->string('source');
            $table->string('source_id');
            $table->string('content_type');
            $table->string('media_url')->nullable();
            $table->text('caption')->nullable();
            $table->json('health_topics')->nullable();
            $table->decimal('relevance_score', 5, 2)->nullable();
            $table->json('engagement_metrics')->nullable();
            $table->string('filtered_status')->default('pending');
            $table->timestamps();
        });

        // Create pivot tables for likes and saves
        Schema::create('content_likes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('social_content_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['user_id', 'social_content_id']);
        });

        Schema::create('content_saves', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('social_content_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['user_id', 'social_content_id']);
        });

        // Create comments table for social content
        Schema::create('social_content_comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('social_content_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('content');
            $table->foreignId('parent_id')->nullable()->constrained('social_content_comments')->onDelete('cascade'); // For replies
            $table->timestamps();

            $table->index(['social_content_id', 'created_at']);
        });

        // Create stories table
        Schema::create('stories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('media_url');
            $table->string('media_type')->default('image'); // image or video
            $table->text('caption')->nullable();
            $table->timestamp('expires_at');
            $table->boolean('is_active')->default(true);
            $table->json('viewers')->nullable(); // Array of user IDs who viewed the story
            $table->timestamps();

            $table->index(['user_id', 'is_active', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stories');
        Schema::dropIfExists('social_content_comments');
        Schema::dropIfExists('content_saves');
        Schema::dropIfExists('content_likes');
        Schema::dropIfExists('social_contents');
    }
};