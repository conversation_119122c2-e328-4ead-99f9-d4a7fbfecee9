<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->enum('type', ['earned', 'used', 'expired', 'refunded']);
            $table->string('source')->nullable(); // referral, admin, promotion, etc.
            $table->string('reference_id')->nullable(); // ID of related entity (appointment_id, referral_id, etc.)
            $table->string('reference_type')->nullable(); // Model type of related entity
            $table->text('description')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Indexes for common queries
            $table->index('user_id');
            $table->index('type');
            $table->index('source');
            $table->index(['reference_id', 'reference_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_transactions');
    }
};
