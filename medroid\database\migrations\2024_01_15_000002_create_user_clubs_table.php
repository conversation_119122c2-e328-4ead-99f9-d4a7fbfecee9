<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_clubs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('club_type'); // 'founder', 'premium', 'regular', etc.
            $table->string('membership_level')->default('basic'); // basic, silver, gold, platinum
            $table->boolean('is_verified')->default(false);
            $table->string('verification_method')->nullable(); // 'founder_code', 'manual', 'achievement'
            $table->string('founder_code_used')->nullable();
            $table->timestamp('joined_at');
            $table->json('benefits')->nullable(); // Club-specific benefits
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'club_type']);
            $table->index(['club_type', 'is_verified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_clubs');
    }
};
