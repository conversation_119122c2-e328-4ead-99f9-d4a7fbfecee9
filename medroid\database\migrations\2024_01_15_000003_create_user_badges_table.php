<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_badges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('badge_type'); // 'founder', 'early_adopter', 'active_user', 'referrer', etc.
            $table->string('badge_name');
            $table->string('badge_description')->nullable();
            $table->string('badge_icon')->nullable(); // Icon/image path
            $table->string('badge_color')->default('#FF9F6E'); // Coral Pop color
            $table->integer('points_awarded')->default(0);
            $table->timestamp('earned_at');
            $table->json('criteria_met')->nullable(); // What criteria were met to earn this badge
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'badge_type']);
            $table->index(['badge_type', 'earned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_badges');
    }
};
