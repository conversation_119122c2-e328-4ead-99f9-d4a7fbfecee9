<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('activity_type'); // 'login', 'chat', 'appointment', 'referral', 'story_post', etc.
            $table->integer('points_earned');
            $table->string('source')->nullable(); // What generated these points
            $table->string('reference_type')->nullable(); // Model type (Chat, Appointment, etc.)
            $table->unsignedBigInteger('reference_id')->nullable(); // Model ID
            $table->date('earned_date');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'earned_date']);
            $table->index(['activity_type', 'earned_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_points');
    }
};
