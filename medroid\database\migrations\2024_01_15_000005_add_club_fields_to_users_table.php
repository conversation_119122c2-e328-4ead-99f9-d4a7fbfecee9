<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_founder_member')->default(false)->after('referred_by');
            $table->integer('total_points')->default(0)->after('is_founder_member');
            $table->integer('activity_streak')->default(0)->after('total_points');
            $table->timestamp('last_activity_at')->nullable()->after('activity_streak');
            $table->json('club_memberships')->nullable()->after('last_activity_at'); // Cache for quick access
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_founder_member',
                'total_points',
                'activity_streak',
                'last_activity_at',
                'club_memberships'
            ]);
        });
    }
};
