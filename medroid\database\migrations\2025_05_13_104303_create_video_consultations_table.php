<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('video_consultations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('appointment_id')->constrained()->onDelete('cascade');
            $table->string('session_id')->unique();
            $table->string('channel_name');
            $table->string('agora_app_id');
            $table->enum('status', ['created', 'active', 'ended', 'abandoned'])->default('created');
            $table->foreignId('created_by')->constrained('users');
            $table->dateTime('started_at')->nullable();
            $table->dateTime('ended_at')->nullable();
            $table->integer('max_participants')->default(2);
            $table->integer('current_participants')->default(0);
            $table->json('session_config')->nullable();
            $table->boolean('has_recording')->default(false);
            $table->string('recording_path')->nullable();
            $table->dateTime('recording_started_at')->nullable();
            $table->dateTime('recording_ended_at')->nullable();
            $table->timestamps();

            $table->index(['session_id', 'status']);
            $table->index(['appointment_id', 'status']);
            $table->index('channel_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('video_consultations');
    }
};
