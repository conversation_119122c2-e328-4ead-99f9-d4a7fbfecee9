<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('video_session_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('video_consultation_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('agora_uid');
            $table->text('agora_token');
            $table->enum('role', ['patient', 'provider']);
            $table->enum('status', ['invited', 'joined', 'left', 'disconnected'])->default('invited');
            $table->dateTime('joined_at')->nullable();
            $table->dateTime('left_at')->nullable();
            $table->dateTime('token_expires_at');
            $table->json('connection_info')->nullable();
            $table->timestamps();

            $table->unique(['video_consultation_id', 'user_id']);
            $table->index(['video_consultation_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index('agora_uid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('video_session_participants');
    }
};
