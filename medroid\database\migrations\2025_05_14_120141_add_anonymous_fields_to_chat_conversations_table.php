<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chat_conversations', function (Blueprint $table) {
            $table->string('anonymous_id')->nullable()->after('patient_id');
            $table->boolean('is_anonymous')->default(false)->after('escalated');
            $table->boolean('is_public')->default(false)->after('is_anonymous');
            $table->timestamp('shared_at')->nullable()->after('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chat_conversations', function (Blueprint $table) {
            $table->dropColumn('anonymous_id');
            $table->dropColumn('is_anonymous');
            $table->dropColumn('is_public');
            $table->dropColumn('shared_at');
        });
    }
};
