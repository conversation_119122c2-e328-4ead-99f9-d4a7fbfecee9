<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * List of migrations that are now redundant and should be marked as run
     * without actually running them.
     */
    protected $redundantMigrations = [
        '2023_01_01_000010_add_appointment_preferences_to_patients',
        '2023_06_01_000002_update_appointments_table',
        '2023_06_01_000003_update_providers_table',
        '2023_07_01_000001_add_consultation_timing_fields_to_appointments',
        '2025_05_08_094520_add_telemedicine_fields_to_services_table',
        '2025_05_08_094657_add_video_consultation_fields_to_appointments_table',
        '2025_05_08_101054_add_audio_recording_fields_to_appointments_table',
        '2025_05_13_131027_add_stripe_columns_to_users_table',
        '2025_05_13_131035_add_payment_status_to_appointments_table',
        '2025_05_16_000001_add_scheduled_at_to_appointments_table',
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // This migration doesn't actually modify the database schema
        // It just marks redundant migrations as run in the migrations table
        
        // Get the migrations table name from the config
        $migrationsTable = 'migrations';
        
        // Check if the migrations table exists
        if (Schema::hasTable($migrationsTable)) {
            // Get all migrations that have already been run
            $ranMigrations = DB::table($migrationsTable)->pluck('migration')->toArray();
            
            // Mark each redundant migration as run if it hasn't been run already
            foreach ($this->redundantMigrations as $migration) {
                if (!in_array($migration, $ranMigrations)) {
                    DB::table($migrationsTable)->insert([
                        'migration' => $migration,
                        'batch' => DB::table($migrationsTable)->max('batch') + 1
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Get the migrations table name from the config
        $migrationsTable = 'migrations';
        
        // Check if the migrations table exists
        if (Schema::hasTable($migrationsTable)) {
            // Remove the redundant migrations from the migrations table
            DB::table($migrationsTable)
                ->whereIn('migration', $this->redundantMigrations)
                ->delete();
        }
    }
};
