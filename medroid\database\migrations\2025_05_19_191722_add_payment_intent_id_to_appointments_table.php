<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->string('payment_intent_id')->nullable()->after('transaction_id');
            $table->timestamp('payment_due_by')->nullable()->after('payment_intent_id');
            $table->timestamp('paid_at')->nullable()->after('payment_due_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn(['payment_intent_id', 'payment_due_by', 'paid_at']);
        });
    }
};
