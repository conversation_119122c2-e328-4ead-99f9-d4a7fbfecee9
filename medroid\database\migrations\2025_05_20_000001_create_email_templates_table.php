<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->string('subject');
            $table->text('content');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Insert default templates
        $this->seedDefaultTemplates();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_templates');
    }

    /**
     * Seed default email templates.
     *
     * @return void
     */
    private function seedDefaultTemplates()
    {
        $templates = [
            [
                'name' => 'User Registration',
                'slug' => 'user-registration',
                'subject' => 'Welcome to Medroid',
                'description' => 'Email sent to users when they register',
                'content' => file_get_contents(resource_path('views/emails/user-registration.blade.php')),
            ],
            [
                'name' => 'Password Reset',
                'slug' => 'password-reset',
                'subject' => 'Reset Your Medroid Password',
                'description' => 'Email sent to users when they request a password reset',
                'content' => file_get_contents(resource_path('views/emails/password-reset.blade.php')),
            ],
            [
                'name' => 'Appointment Booked (Patient)',
                'slug' => 'appointment-booked-patient',
                'subject' => 'Your Appointment Has Been Booked',
                'description' => 'Email sent to patients when they book an appointment',
                'content' => file_get_contents(resource_path('views/emails/appointment-booked-patient.blade.php')),
            ],
            [
                'name' => 'Appointment Booked (Provider)',
                'slug' => 'appointment-booked-provider',
                'subject' => 'New Appointment Booked',
                'description' => 'Email sent to providers when a patient books an appointment with them',
                'content' => file_get_contents(resource_path('views/emails/appointment-booked-provider.blade.php')),
            ],
            [
                'name' => 'Appointment Confirmed (Patient)',
                'slug' => 'appointment-confirmed-patient',
                'subject' => 'Your Appointment Payment Has Been Confirmed',
                'description' => 'Email sent to patients when their appointment payment is confirmed',
                'content' => file_get_contents(resource_path('views/emails/appointment-confirmed-patient.blade.php')),
            ],
            [
                'name' => 'Appointment Confirmed (Provider)',
                'slug' => 'appointment-confirmed-provider',
                'subject' => 'Appointment Payment Confirmed',
                'description' => 'Email sent to providers when a patient\'s appointment payment is confirmed',
                'content' => file_get_contents(resource_path('views/emails/appointment-confirmed-provider.blade.php')),
            ],
            [
                'name' => 'Appointment Cancelled (Patient)',
                'slug' => 'appointment-cancelled-patient',
                'subject' => 'Your Appointment Has Been Cancelled',
                'description' => 'Email sent to patients when their appointment is cancelled',
                'content' => file_get_contents(resource_path('views/emails/appointment-cancelled-patient.blade.php')),
            ],
            [
                'name' => 'Appointment Cancelled (Provider)',
                'slug' => 'appointment-cancelled-provider',
                'subject' => 'Appointment Cancelled',
                'description' => 'Email sent to providers when an appointment is cancelled',
                'content' => file_get_contents(resource_path('views/emails/appointment-cancelled-provider.blade.php')),
            ],
            [
                'name' => 'Appointment Reminder (Patient)',
                'slug' => 'appointment-reminder-patient',
                'subject' => 'Reminder: Your Appointment is Tomorrow',
                'description' => 'Email sent to patients to remind them of an upcoming appointment',
                'content' => file_get_contents(resource_path('views/emails/appointment-reminder-patient.blade.php')),
            ],
            [
                'name' => 'Appointment Reminder (Provider)',
                'slug' => 'appointment-reminder-provider',
                'subject' => 'Reminder: Upcoming Appointment Tomorrow',
                'description' => 'Email sent to providers to remind them of an upcoming appointment',
                'content' => file_get_contents(resource_path('views/emails/appointment-reminder-provider.blade.php')),
            ],
        ];

        foreach ($templates as $template) {
            DB::table('email_templates')->insert([
                'name' => $template['name'],
                'slug' => $template['slug'],
                'subject' => $template['subject'],
                'content' => $template['content'],
                'description' => $template['description'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
};
