<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if content_likes table exists
        if (Schema::hasTable('content_likes')) {
            // Check if it has the wrong column name
            if (Schema::hasColumn('content_likes', 'social_content_id') && !Schema::hasColumn('content_likes', 'content_id')) {
                Schema::table('content_likes', function (Blueprint $table) {
                    // Rename social_content_id to content_id
                    $table->renameColumn('social_content_id', 'content_id');
                });
            } 
            // If the table exists but doesn't have either column, add content_id
            elseif (!Schema::hasColumn('content_likes', 'social_content_id') && !Schema::hasColumn('content_likes', 'content_id')) {
                Schema::table('content_likes', function (Blueprint $table) {
                    $table->foreignId('content_id')->after('user_id');
                });
            }
        } 
        // If the table doesn't exist, create it
        else {
            Schema::create('content_likes', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->foreignId('content_id');
                $table->timestamps();
                
                $table->unique(['user_id', 'content_id']);
            });
        }

        // Check if content_saves table exists
        if (Schema::hasTable('content_saves')) {
            // Check if it has the wrong column name
            if (Schema::hasColumn('content_saves', 'social_content_id') && !Schema::hasColumn('content_saves', 'content_id')) {
                Schema::table('content_saves', function (Blueprint $table) {
                    // Rename social_content_id to content_id
                    $table->renameColumn('social_content_id', 'content_id');
                });
            } 
            // If the table exists but doesn't have either column, add content_id
            elseif (!Schema::hasColumn('content_saves', 'social_content_id') && !Schema::hasColumn('content_saves', 'content_id')) {
                Schema::table('content_saves', function (Blueprint $table) {
                    $table->foreignId('content_id')->after('user_id');
                });
            }
        } 
        // If the table doesn't exist, create it
        else {
            Schema::create('content_saves', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->foreignId('content_id');
                $table->timestamps();
                
                $table->unique(['user_id', 'content_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // We don't want to drop the tables in the down method
        // Just revert the column names if needed
        if (Schema::hasTable('content_likes') && Schema::hasColumn('content_likes', 'content_id')) {
            Schema::table('content_likes', function (Blueprint $table) {
                $table->renameColumn('content_id', 'social_content_id');
            });
        }

        if (Schema::hasTable('content_saves') && Schema::hasColumn('content_saves', 'content_id')) {
            Schema::table('content_saves', function (Blueprint $table) {
                $table->renameColumn('content_id', 'social_content_id');
            });
        }
    }
};
