<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the column doesn't exist before adding it
        if (!Schema::hasColumn('chat_conversations', 'anonymous_id')) {
            Schema::table('chat_conversations', function (Blueprint $table) {
                $table->string('anonymous_id')->nullable()->after('patient_id');
            });
        }

        // Check if the column doesn't exist before adding it
        if (!Schema::hasColumn('chat_conversations', 'is_anonymous')) {
            Schema::table('chat_conversations', function (Blueprint $table) {
                $table->boolean('is_anonymous')->default(false)->after('escalated');
            });
        }

        // Check if the column doesn't exist before adding it
        if (!Schema::hasColumn('chat_conversations', 'is_public')) {
            Schema::table('chat_conversations', function (Blueprint $table) {
                $table->boolean('is_public')->default(false)->after('is_anonymous');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to drop these columns in the down method
        // as they might be used by other migrations
    }
};
