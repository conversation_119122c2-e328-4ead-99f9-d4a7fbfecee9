<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('referrals', function (Blueprint $table) {
            $table->integer('consecutive_days_active')->default(0)->after('credit_awarded');
            $table->boolean('eligible_for_credit')->default(false)->after('consecutive_days_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('referrals', function (Blueprint $table) {
            $table->dropColumn('consecutive_days_active');
            $table->dropColumn('eligible_for_credit');
        });
    }
};
