<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create stories table (comments table already exists from earlier migration)
        if (!Schema::hasTable('stories')) {
            Schema::create('stories', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('media_url');
                $table->string('media_type')->default('image'); // image or video
                $table->text('caption')->nullable();
                $table->timestamp('expires_at');
                $table->boolean('is_active')->default(true);
                $table->json('viewers')->nullable(); // Array of user IDs who viewed the story
                $table->timestamps();

                $table->index(['user_id', 'is_active', 'expires_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stories');
    }
};
