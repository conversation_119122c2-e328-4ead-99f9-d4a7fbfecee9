<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('sso_provider')->nullable()->after('password');
            $table->string('sso_provider_id')->nullable()->after('sso_provider');
            $table->text('sso_access_token')->nullable()->after('sso_provider_id');
            $table->text('sso_refresh_token')->nullable()->after('sso_access_token');
            $table->timestamp('sso_token_expires_at')->nullable()->after('sso_refresh_token');
            $table->json('sso_profile_data')->nullable()->after('sso_token_expires_at');

            // Add index for SSO lookups
            $table->index(['sso_provider', 'sso_provider_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['sso_provider', 'sso_provider_id']);
            $table->dropColumn([
                'sso_provider',
                'sso_provider_id',
                'sso_access_token',
                'sso_refresh_token',
                'sso_token_expires_at',
                'sso_profile_data'
            ]);
        });
    }
};
