<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the column doesn't exist before adding it
        // (referral_note already exists in the original chat_conversations table)
        if (!Schema::hasColumn('chat_conversations', 'referral_note')) {
            Schema::table('chat_conversations', function (Blueprint $table) {
                $table->text('referral_note')->nullable()->after('escalation_reason');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chat_conversations', function (Blueprint $table) {
            $table->dropColumn('referral_note');
        });
    }
};
