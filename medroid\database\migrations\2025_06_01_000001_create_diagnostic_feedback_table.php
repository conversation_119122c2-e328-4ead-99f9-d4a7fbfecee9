<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('diagnostic_feedback', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('chat_conversation_id')->nullable()->constrained()->onDelete('set null');
            $table->json('ai_diagnoses')->nullable()->comment('List of AI-suggested diagnoses');
            $table->string('ai_diagnosis')->nullable()->comment('The primary AI diagnosis');
            $table->string('provider_diagnosis')->comment('The diagnosis confirmed by the provider');
            $table->boolean('is_accurate')->default(false)->comment('Whether the AI diagnosis matched the provider diagnosis');
            $table->text('feedback_notes')->nullable()->comment('Additional feedback from the provider');
            $table->timestamps();

            // Indexes for common queries
            $table->index('provider_id');
            $table->index('patient_id');
            $table->index('is_accurate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('diagnostic_feedback');
    }
};
