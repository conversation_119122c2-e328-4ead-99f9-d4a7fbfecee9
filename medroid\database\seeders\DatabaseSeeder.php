<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Provider;
use App\Models\Patient;
use App\Models\Service;
use App\Models\ProviderAvailability;
use App\Models\Appointment;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting database seeding...');

        // Disable foreign key checks to avoid constraint issues
        Schema::disableForeignKeyConstraints();

        // Clear tables before seeding to avoid duplicate data
        $this->truncateTables([
            'appointments', 'services', 'provider_availabilities',
            'providers', 'patients', 'users', 'model_has_roles', 'model_has_permissions'
        ]);

        // Re-enable foreign key checks
        Schema::enableForeignKeyConstraints();

        // Run the roles and permissions seeder first
        $this->call(RolesAndPermissionsSeeder::class);

        // Seed users and their profiles
        $this->seedUsers();

        // Seed provider availabilities
        $this->seedProviderAvailabilities();

        // Seed services
        $this->seedServices();

        // Seed sample appointments
        $this->seedAppointments();

        $this->command->info('Database seeding completed successfully!');
        $this->displayCredentials();
    }

    /**
     * Truncate the specified tables.
     *
     * @param array $tables
     * @return void
     */
    protected function truncateTables(array $tables)
    {
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
            }
        }
    }

    /**
     * Seed users and their profiles.
     *
     * @return void
     */
    protected function seedUsers()
    {
        $this->command->info('Seeding users...');

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'phone_number' => '******-000-0001',
            'is_active' => true,
        ]);
        $admin->assignRole('admin');

        // Also update the role column for compatibility
        $admin->update(['role' => 'admin']);

        // Create multiple healthcare providers
        $this->seedProviders();

        // Create multiple patients
        $this->seedPatients();
    }

    /**
     * Seed healthcare providers.
     *
     * @return void
     */
    protected function seedProviders()
    {
        $providers = [
            [
                'name' => 'Dr. Jane Smith',
                'email' => '<EMAIL>',
                'specialization' => 'General Practitioner',
                'license_number' => 'MD12345',
                'gender' => 'female',
                'education' => 'Harvard Medical School',
                'years_of_experience' => 10,
                'bio' => 'Dr. Jane Smith is a board-certified general practitioner with over 10 years of experience in family medicine.',
                'phone' => '******-000-0002',
            ],
            [
                'name' => 'Dr. Michael Johnson',
                'email' => '<EMAIL>',
                'specialization' => 'Cardiology',
                'license_number' => 'MD67890',
                'gender' => 'male',
                'education' => 'Johns Hopkins University',
                'years_of_experience' => 15,
                'bio' => 'Dr. Michael Johnson is a renowned cardiologist specializing in interventional cardiology and heart disease prevention.',
                'phone' => '******-000-0003',
            ],
            [
                'name' => 'Dr. Sarah Williams',
                'email' => '<EMAIL>',
                'specialization' => 'Dermatology',
                'license_number' => 'MD11111',
                'gender' => 'female',
                'education' => 'Stanford University School of Medicine',
                'years_of_experience' => 8,
                'bio' => 'Dr. Sarah Williams specializes in medical and cosmetic dermatology with expertise in skin cancer detection.',
                'phone' => '******-000-0004',
            ],
        ];

        foreach ($providers as $providerData) {
            $user = User::create([
                'name' => $providerData['name'],
                'email' => $providerData['email'],
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'phone_number' => $providerData['phone'],
                'is_active' => true,
            ]);
            $user->assignRole('provider');

            // Also update the role column for compatibility
            $user->update(['role' => 'provider']);

            Provider::create([
                'user_id' => $user->id,
                'specialization' => $providerData['specialization'],
                'license_number' => $providerData['license_number'],
                'verification_status' => 'verified',
                'gender' => $providerData['gender'],
                'practice_locations' => json_encode([
                    [
                        'address' => '123 Medical Center Dr',
                        'city' => 'Boston',
                        'state' => 'MA',
                        'zip' => '02115',
                        'country' => 'USA',
                        'is_primary' => true,
                    ]
                ]),
                'pricing' => json_encode([
                    'consultation' => rand(100, 250),
                    'follow_up' => rand(50, 125),
                ]),
                'rating' => round(rand(40, 50) / 10, 1),
                'bio' => $providerData['bio'],
                'education' => $providerData['education'],
                'years_of_experience' => $providerData['years_of_experience'],
                'accepts_insurance' => true,
                'insurance_providers' => json_encode(['Blue Cross', 'Aetna', 'Cigna', 'UnitedHealth']),
            ]);
        }
    }

    /**
     * Seed patient users.
     *
     * @return void
     */
    protected function seedPatients()
    {
        $patients = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'gender' => 'male',
                'date_of_birth' => '1985-06-15',
                'phone' => '******-000-0005',
                'health_history' => [
                    'surgeries' => ['Appendectomy (2010)'],
                    'chronic_conditions' => ['Mild asthma'],
                ],
                'allergies' => ['Penicillin', 'Peanuts'],
                'medications' => ['Albuterol inhaler as needed'],
                'insurance_provider' => 'Blue Cross Blue Shield',
                'insurance_policy_number' => 'BCBS12345678',
                'emergency_contact_name' => 'Jane Doe',
                'emergency_contact_phone' => '************',
                'emergency_contact_relationship' => 'Spouse',
            ],
            [
                'name' => 'Emily Johnson',
                'email' => '<EMAIL>',
                'gender' => 'female',
                'date_of_birth' => '1992-03-22',
                'phone' => '******-000-0006',
                'health_history' => [
                    'surgeries' => [],
                    'chronic_conditions' => ['Diabetes Type 2'],
                ],
                'allergies' => ['Shellfish'],
                'medications' => ['Metformin 500mg twice daily'],
                'insurance_provider' => 'Aetna',
                'insurance_policy_number' => 'AET987654321',
                'emergency_contact_name' => 'Robert Johnson',
                'emergency_contact_phone' => '************',
                'emergency_contact_relationship' => 'Father',
            ],
            [
                'name' => 'Robert Chen',
                'email' => '<EMAIL>',
                'gender' => 'male',
                'date_of_birth' => '1978-11-08',
                'phone' => '******-000-0007',
                'health_history' => [
                    'surgeries' => ['Knee replacement (2020)'],
                    'chronic_conditions' => ['Hypertension'],
                ],
                'allergies' => ['None known'],
                'medications' => ['Lisinopril 10mg daily'],
                'insurance_provider' => 'Cigna',
                'insurance_policy_number' => 'CIG456789123',
                'emergency_contact_name' => 'Lisa Chen',
                'emergency_contact_phone' => '************',
                'emergency_contact_relationship' => 'Wife',
            ],
        ];

        foreach ($patients as $patientData) {
            $user = User::create([
                'name' => $patientData['name'],
                'email' => $patientData['email'],
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'phone_number' => $patientData['phone'],
                'date_of_birth' => $patientData['date_of_birth'],
                'is_active' => true,
            ]);
            $user->assignRole('patient');

            // Also update the role column for compatibility
            $user->update(['role' => 'patient']);

            Patient::create([
                'user_id' => $user->id,
                'gender' => $patientData['gender'],
                'date_of_birth' => $patientData['date_of_birth'],
                'health_history' => json_encode($patientData['health_history']),
                'allergies' => json_encode($patientData['allergies']),
                'medications' => json_encode($patientData['medications']),
                'appointment_preferences' => json_encode([
                    'preferred_days' => ['Monday', 'Wednesday', 'Friday'],
                    'preferred_time' => 'morning',
                ]),
                'insurance_provider' => $patientData['insurance_provider'],
                'insurance_policy_number' => $patientData['insurance_policy_number'],
                'emergency_contact_name' => $patientData['emergency_contact_name'],
                'emergency_contact_phone' => $patientData['emergency_contact_phone'],
                'emergency_contact_relationship' => $patientData['emergency_contact_relationship'],
            ]);
        }
    }

    /**
     * Seed provider availabilities.
     *
     * @return void
     */
    protected function seedProviderAvailabilities()
    {
        $this->command->info('Seeding provider availabilities...');

        $providers = Provider::all();

        foreach ($providers as $provider) {
            // Create availability for each weekday (Monday = 1, Sunday = 0)
            for ($day = 1; $day <= 5; $day++) {
                ProviderAvailability::create([
                    'provider_id' => $provider->id,
                    'day_of_week' => $day,
                    'start_time' => '09:00:00',
                    'end_time' => '17:00:00',
                    'is_active' => true,
                ]);
            }

            // Add some weekend availability for some providers
            if ($provider->id % 2 == 0) {
                ProviderAvailability::create([
                    'provider_id' => $provider->id,
                    'day_of_week' => 6, // Saturday
                    'start_time' => '10:00:00',
                    'end_time' => '14:00:00',
                    'is_active' => true,
                ]);
            }
        }
    }

    /**
     * Seed services.
     *
     * @return void
     */
    protected function seedServices()
    {
        $this->command->info('Seeding services...');

        $providers = Provider::all();

        $serviceTemplates = [
            'General Practitioner' => [
                [
                    'name' => 'General Consultation',
                    'description' => 'Initial consultation for general health concerns',
                    'duration' => 30,
                    'price' => 150.00,
                    'category' => 'Consultation',
                ],
                [
                    'name' => 'Follow-up Appointment',
                    'description' => 'Follow-up appointment for existing patients',
                    'duration' => 15,
                    'price' => 75.00,
                    'category' => 'Follow-up',
                ],
                [
                    'name' => 'Annual Physical Exam',
                    'description' => 'Comprehensive annual health check-up',
                    'duration' => 60,
                    'price' => 250.00,
                    'category' => 'Preventive',
                ],
            ],
            'Cardiology' => [
                [
                    'name' => 'Cardiac Consultation',
                    'description' => 'Specialized consultation for heart-related concerns',
                    'duration' => 45,
                    'price' => 300.00,
                    'category' => 'Consultation',
                ],
                [
                    'name' => 'ECG Interpretation',
                    'description' => 'Electrocardiogram reading and interpretation',
                    'duration' => 20,
                    'price' => 150.00,
                    'category' => 'Diagnostic',
                ],
                [
                    'name' => 'Cardiac Follow-up',
                    'description' => 'Follow-up for cardiac patients',
                    'duration' => 30,
                    'price' => 200.00,
                    'category' => 'Follow-up',
                ],
            ],
            'Dermatology' => [
                [
                    'name' => 'Skin Consultation',
                    'description' => 'General dermatological consultation',
                    'duration' => 30,
                    'price' => 200.00,
                    'category' => 'Consultation',
                ],
                [
                    'name' => 'Mole Check',
                    'description' => 'Comprehensive mole and skin cancer screening',
                    'duration' => 45,
                    'price' => 250.00,
                    'category' => 'Screening',
                ],
                [
                    'name' => 'Acne Treatment',
                    'description' => 'Consultation and treatment for acne',
                    'duration' => 25,
                    'price' => 175.00,
                    'category' => 'Treatment',
                ],
            ],
        ];

        foreach ($providers as $provider) {
            $specialization = $provider->specialization;
            $services = $serviceTemplates[$specialization] ?? $serviceTemplates['General Practitioner'];

            foreach ($services as $serviceData) {
                Service::create([
                    'provider_id' => $provider->id,
                    'name' => $serviceData['name'],
                    'description' => $serviceData['description'],
                    'duration' => $serviceData['duration'],
                    'price' => $serviceData['price'],
                    'category' => $serviceData['category'],
                    'is_telemedicine' => true,
                    'supports_video' => true,
                    'supports_audio' => true,
                    'supports_chat' => $serviceData['category'] !== 'Diagnostic',
                    'active' => true,
                ]);
            }
        }
    }

    /**
     * Seed sample appointments.
     *
     * @return void
     */
    protected function seedAppointments()
    {
        $this->command->info('Seeding appointments...');

        // Check if Appointment model exists
        if (!class_exists('App\Models\Appointment')) {
            $this->command->warn('Appointment model not found. Skipping appointment seeding.');
            return;
        }

        $providers = Provider::all();
        $patients = Patient::all();

        if ($providers->isEmpty() || $patients->isEmpty()) {
            $this->command->warn('No providers or patients found. Skipping appointment seeding.');
            return;
        }

        $statuses = ['scheduled', 'completed', 'cancelled', 'no_show'];
        $reasons = [
            'General consultation',
            'Follow-up appointment',
            'Annual check-up',
            'Skin examination',
            'Cardiac consultation',
            'Routine screening'
        ];

        // Create appointments for the next 30 days
        for ($i = 0; $i < 20; $i++) {
            $provider = $providers->random();
            $patient = $patients->random();
            $service = $provider->services()->first();

            if (!$service) continue;

            $appointmentDate = Carbon::now()->addDays(rand(-7, 30));
            $startTime = Carbon::createFromTime(rand(9, 16), [0, 15, 30, 45][rand(0, 3)]);
            $endTime = $startTime->copy()->addMinutes($service->duration);

            try {
                \App\Models\Appointment::create([
                    'patient_id' => $patient->id,
                    'provider_id' => $provider->id,
                    'service_id' => $service->id,
                    'date' => $appointmentDate->format('Y-m-d'),
                    'time_slot' => [
                        'start_time' => $startTime->format('H:i'),
                        'end_time' => $endTime->format('H:i'),
                    ],
                    'scheduled_at' => $appointmentDate->setTime($startTime->hour, $startTime->minute),
                    'reason' => $reasons[rand(0, count($reasons) - 1)],
                    'status' => $statuses[rand(0, 3)],
                    'notes' => 'Sample appointment created by seeder',
                    'is_telemedicine' => rand(0, 1) == 1,
                    'amount' => $service->price,
                    'payment_status' => ['pending', 'paid', 'failed'][rand(0, 2)],
                ]);
            } catch (\Exception $e) {
                $this->command->warn('Could not create appointment: ' . $e->getMessage());
            }
        }
    }

    /**
     * Display login credentials for testing.
     *
     * @return void
     */
    protected function displayCredentials()
    {
        $this->command->info('');
        $this->command->info('=== Test Login Credentials ===');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Provider: <EMAIL> / password');
        $this->command->info('Cardiologist: <EMAIL> / password');
        $this->command->info('Dermatologist: <EMAIL> / password');
        $this->command->info('Patient: <EMAIL> / password');
        $this->command->info('Patient 2: <EMAIL> / password');
        $this->command->info('Patient 3: <EMAIL> / password');
        $this->command->info('');
    }
}