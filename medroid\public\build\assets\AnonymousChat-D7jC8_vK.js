import{d as $,b as d,e as n,f as h,g as e,i as m,u as y,P as x,w as f,j as v,l as O,r as u,o as P,n as M,t as I,m as E,p as F,F as A,q as U,s as L,v as Z,x as S,y as G,z as H,A as K}from"./vendor-CGdKbVnC.js";import{M as j}from"./MedroidLogo-Bx6QLK9u.js";import{_ as R}from"./_plugin-vue_export-helper-DlAUqK2U.js";const W={class:"min-h-screen bg-gray-50"},J={key:0,class:"bg-white shadow-sm border-b border-gray-200"},Y={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Q={class:"flex justify-between items-center h-16"},X={class:"flex items-center space-x-4"},ee={class:"flex-1"},se={class:"bg-white border-t border-gray-200 mt-auto"},te={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},oe={class:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"},ae={class:"flex items-center space-x-3"},re=$({__name:"PublicLayout",props:{title:{},showHeader:{type:Boolean}},setup(_){return(i,r)=>(n(),d("div",W,[i.showHeader!==!1?(n(),d("header",J,[e("div",Y,[e("div",Q,[m(y(x),{href:i.route("home"),class:"flex items-center space-x-3"},{default:f(()=>[m(j,{size:32}),r[0]||(r[0]=e("span",{class:"text-xl font-bold text-medroid-navy"},"Medroid",-1))]),_:1},8,["href"]),e("div",X,[m(y(x),{href:i.route("login"),class:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"},{default:f(()=>r[1]||(r[1]=[v(" Sign In ")])),_:1},8,["href"]),m(y(x),{href:i.route("register"),class:"bg-medroid-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-medroid-primary/90 transition-colors"},{default:f(()=>r[2]||(r[2]=[v(" Sign Up ")])),_:1},8,["href"])])])])])):h("",!0),e("main",ee,[O(i.$slots,"default")]),e("footer",se,[e("div",te,[e("div",oe,[e("div",ae,[m(j,{size:24}),r[3]||(r[3]=e("span",{class:"text-sm text-gray-600"},"© 2024 Medroid. All rights reserved.",-1))]),r[4]||(r[4]=e("div",{class:"flex space-x-6 text-sm text-gray-600"},[e("a",{href:"#",class:"hover:text-gray-900 transition-colors"},"Privacy Policy"),e("a",{href:"#",class:"hover:text-gray-900 transition-colors"},"Terms of Service"),e("a",{href:"#",class:"hover:text-gray-900 transition-colors"},"Contact")],-1))])])])]))}}),le={key:0,class:"flex-shrink-0 mr-3"},ne={class:"text-sm leading-relaxed"},ie={key:0},de={key:0,class:"inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse"},ue={key:1},me={key:0,class:"absolute top-3 -right-1 w-3 h-3 bg-medroid-primary transform rotate-45"},ce={key:1,class:"absolute top-3 -left-1 w-3 h-3 bg-white border-l border-b border-gray-200 transform rotate-45"},pe={key:1,class:"flex-shrink-0 ml-3"},ge=$({__name:"ChatMessage",props:{message:{},animate:{type:Boolean,default:!0},delay:{default:0}},setup(_){const i=_,r=u(),c=u(!1),p=u(!1),g=u("");P(()=>{i.animate?setTimeout(()=>{c.value=!0,i.message.type==="ai"?C():g.value=i.message.content},i.delay):(c.value=!0,g.value=i.message.content)});const C=()=>{p.value=!0;const a=i.message.content;let l=0;const k=setInterval(()=>{l<a.length?(g.value+=a[l],l++):(clearInterval(k),p.value=!1)},30)},b=a=>a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return(a,l)=>(n(),d("div",{ref_key:"messageRef",ref:r,class:M(["flex mb-4 transition-all duration-500 ease-out",a.message.type==="user"?"justify-end":"justify-start",c.value?"opacity-100 translate-y-0":"opacity-0 translate-y-4"])},[a.message.type==="ai"?(n(),d("div",le,l[0]||(l[0]=[e("div",{class:"w-8 h-8 rounded-full bg-medroid-primary flex items-center justify-center shadow-md"},[e("svg",{class:"w-5 h-5 text-white",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 2C13.1 2 14 2.9 14 4V8H18C19.1 8 20 8.9 20 10V14C20 15.1 19.1 16 18 16H14V20C14 21.1 13.1 22 12 22C10.9 22 10 21.1 10 20V16H6C4.9 16 4 15.1 4 14V10C4 8.9 4.9 8 6 8H10V4C10 2.9 10.9 2 12 2Z"})])],-1)]))):h("",!0),e("div",{class:M(["max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-md relative",a.message.type==="user"?"bg-medroid-primary text-white rounded-br-md":"bg-white text-gray-800 border border-gray-200 rounded-bl-md"])},[e("div",ne,[a.message.type==="ai"&&a.animate?(n(),d("span",ie,[v(I(g.value)+" ",1),p.value?(n(),d("span",de)):h("",!0)])):(n(),d("span",ue,I(a.message.content),1))]),e("div",{class:M(["text-xs mt-1 opacity-70",a.message.type==="user"?"text-white":"text-gray-500"])},I(b(a.message.timestamp)),3),a.message.type==="user"?(n(),d("div",me)):(n(),d("div",ce))],2),a.message.type==="user"?(n(),d("div",pe,l[1]||(l[1]=[e("div",{class:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center shadow-md"},[e("svg",{class:"w-5 h-5 text-gray-600",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 6C13.1 6 14 6.9 14 8C14 9.1 13.1 10 12 10C10.9 10 10 9.1 10 8C10 6.9 10.9 6 12 6ZM12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4ZM12 13C9.33 13 4 14.33 4 17V20H20V17C20 14.33 14.67 13 12 13ZM12 14.9C14.97 14.9 18.1 16.36 18.1 17V18.1H5.9V17C5.9 16.36 9.03 14.9 12 14.9Z"})])],-1)]))):h("",!0)],2))}}),ye=R(ge,[["__scopeId","data-v-f036e25f"]]),fe={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},ve={class:"bg-white rounded-lg shadow-lg flex flex-col h-[600px]"},xe={key:0,class:"flex justify-start"},he={class:"border-t p-4"},be=["disabled"],we=["disabled"],Ce={class:"mt-8 grid grid-cols-1 md:grid-cols-2 gap-6"},_e={class:"bg-medroid-primary/5 p-6 rounded-lg border border-medroid-primary/20"},ke={class:"space-y-2"},Me={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},Ie={class:"bg-white rounded-lg max-w-md w-full p-6"},Se={class:"space-y-4"},$e={class:"flex space-x-3 mt-6"},Te={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},Ve={class:"bg-white rounded-lg max-w-md w-full p-6"},Ae={class:"space-y-3"},ze=$({__name:"AnonymousChat",setup(_){const i=u([]),r=u(""),c=u(!1),p=u(null),g=u(null),C=u(""),b=u(!1),a=u(!1),l=u({age:"",gender:""}),k=()=>{let o=localStorage.getItem("medroid_anonymous_id");o||(o="anon_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("medroid_anonymous_id",o)),C.value=o},T=E(()=>r.value.trim()&&!c.value),z=()=>{p.value&&K(()=>{p.value.scrollTop=p.value.scrollHeight})},w=(o,s)=>{const t={id:Date.now(),type:o,content:s,timestamp:new Date};i.value.push(t),z()},B=async o=>{try{const s=await fetch("/api/anonymous/chat/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:o,anonymous_id:C.value,gender:l.value.gender||null,age:l.value.age||null})}),t=await s.json();if(s.ok)return g.value=t.conversation_id,t.message&&w("ai",t.message),t.requires_auth&&(b.value=!0),t;throw new Error(t.message||"Failed to start conversation")}catch(s){throw console.error("Error starting conversation:",s),w("ai","Sorry, I encountered an error. Please try again."),s}},V=async()=>{if(!T.value)return;const o=r.value.trim();w("user",o),r.value="",c.value=!0;try{let s;if(!g.value)s=await B(o);else{const t=await fetch("/api/anonymous/chat/message",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:g.value,message:o,anonymous_id:C.value,gender:l.value.gender||null,age:l.value.age||null})});if(s=await t.json(),t.ok)s.message&&w("ai",s.message),s.requires_auth&&(b.value=!0);else throw new Error(s.message||"Failed to send message")}}catch(s){console.error("Error sending message:",s),w("ai","Sorry, I encountered an error. Please try again.")}finally{c.value=!1}},D=o=>{o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),V())},q=()=>{a.value=!1};return P(()=>{k(),w("ai","Hello! I'm your AI health assistant. I can help you with health questions, symptoms, and general medical advice. How can I help you today?"),setTimeout(()=>{a.value=!0},2e3)}),(o,s)=>(n(),d(A,null,[m(y(F),{title:"Anonymous AI Health Chat - Medroid"}),m(re,null,{default:f(()=>[e("div",fe,[s[11]||(s[11]=e("div",{class:"text-center mb-8"},[e("h1",{class:"text-3xl font-bold text-gray-900 mb-2"}," Anonymous AI Health Chat "),e("p",{class:"text-gray-600 max-w-2xl mx-auto"}," Get instant, private health advice from our AI assistant. No account required. ")],-1)),e("div",ve,[e("div",{ref_key:"chatContainer",ref:p,class:"flex-1 p-6 overflow-y-auto space-y-4"},[(n(!0),d(A,null,U(i.value,(t,N)=>(n(),L(ye,{key:t.id,message:t,animate:!0,delay:N*100},null,8,["message","delay"]))),128)),c.value?(n(),d("div",xe,s[5]||(s[5]=[e("div",{class:"bg-gray-100 text-gray-800 rounded-lg px-4 py-2"},[e("div",{class:"flex space-x-1"},[e("div",{class:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),e("div",{class:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{"animation-delay":"0.1s"}}),e("div",{class:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{"animation-delay":"0.2s"}})])],-1)]))):h("",!0)],512),e("div",he,[e("form",{onSubmit:Z(V,["prevent"]),class:"flex space-x-2"},[S(e("input",{"onUpdate:modelValue":s[0]||(s[0]=t=>r.value=t),type:"text",placeholder:"Type your health question...",class:"flex-1 border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-medroid-primary focus:border-transparent",disabled:c.value,onKeypress:D},null,40,be),[[G,r.value]]),e("button",{type:"submit",disabled:!T.value,class:"bg-medroid-primary text-white px-6 py-3 rounded-lg hover:bg-medroid-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Send ",8,we)],32)])]),e("div",Ce,[s[10]||(s[10]=e("div",{class:"bg-white p-6 rounded-lg shadow"},[e("h3",{class:"font-semibold text-lg mb-4 text-gray-900"},"What I can help with:"),e("ul",{class:"space-y-2 text-gray-600"},[e("li",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-medroid-teal rounded-full"}),e("span",null,"Symptom analysis and insights")]),e("li",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-medroid-teal rounded-full"}),e("span",null,"Medication information")]),e("li",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-medroid-teal rounded-full"}),e("span",null,"General health advice")]),e("li",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-medroid-teal rounded-full"}),e("span",null,"Wellness recommendations")])])],-1)),e("div",_e,[s[8]||(s[8]=e("h3",{class:"font-semibold text-lg mb-2 text-medroid-primary"},"Want more features?",-1)),s[9]||(s[9]=e("p",{class:"text-gray-600 mb-4"}," Create an account to save your conversations, book appointments with real doctors, and access your health history. ",-1)),e("div",ke,[m(y(x),{href:o.route("register"),class:"block w-full bg-medroid-primary text-white text-center px-4 py-2 rounded-lg hover:bg-medroid-primary/90 transition-colors"},{default:f(()=>s[6]||(s[6]=[v(" Sign Up Free ")])),_:1},8,["href"]),m(y(x),{href:o.route("login"),class:"block w-full text-medroid-primary text-center px-4 py-2 rounded-lg border border-medroid-primary hover:bg-medroid-primary/5 transition-colors"},{default:f(()=>s[7]||(s[7]=[v(" Sign In ")])),_:1},8,["href"])])])]),s[12]||(s[12]=e("div",{class:"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4"},[e("div",{class:"flex"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("p",{class:"text-sm text-yellow-800"},[e("strong",null,"Medical Disclaimer:"),v(" This AI assistant provides general health information only and should not replace professional medical advice. Always consult with a qualified healthcare provider for medical concerns. ")])])])],-1))]),a.value?(n(),d("div",Me,[e("div",Ie,[s[17]||(s[17]=e("h3",{class:"text-lg font-semibold mb-4"},"Help us provide better advice (Optional)",-1)),s[18]||(s[18]=e("p",{class:"text-gray-600 mb-6"}," Providing your age and gender helps our AI give more personalized health advice. This information is anonymous and not stored. ",-1)),e("div",Se,[e("div",null,[s[14]||(s[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Age Group",-1)),S(e("select",{"onUpdate:modelValue":s[1]||(s[1]=t=>l.value.age=t),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-medroid-primary"},s[13]||(s[13]=[e("option",{value:""},"Prefer not to say",-1),e("option",{value:"under_18"},"Under 18",-1),e("option",{value:"18_25"},"18-25",-1),e("option",{value:"26_35"},"26-35",-1),e("option",{value:"36_45"},"36-45",-1),e("option",{value:"46_55"},"46-55",-1),e("option",{value:"56_65"},"56-65",-1),e("option",{value:"over_65"},"Over 65",-1)]),512),[[H,l.value.age]])]),e("div",null,[s[16]||(s[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Gender",-1)),S(e("select",{"onUpdate:modelValue":s[2]||(s[2]=t=>l.value.gender=t),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-medroid-primary"},s[15]||(s[15]=[e("option",{value:""},"Prefer not to say",-1),e("option",{value:"male"},"Male",-1),e("option",{value:"female"},"Female",-1),e("option",{value:"other"},"Other",-1)]),512),[[H,l.value.gender]])])]),e("div",$e,[e("button",{onClick:q,class:"flex-1 bg-medroid-primary text-white px-4 py-2 rounded-lg hover:bg-medroid-primary/90 transition-colors"}," Continue "),e("button",{onClick:s[3]||(s[3]=t=>a.value=!1),class:"flex-1 text-gray-600 px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"}," Skip ")])])])):h("",!0),b.value?(n(),d("div",Te,[e("div",Ve,[s[21]||(s[21]=e("h3",{class:"text-lg font-semibold mb-4"},"Sign in required",-1)),s[22]||(s[22]=e("p",{class:"text-gray-600 mb-6"}," To book appointments and access additional features, you'll need to create an account or sign in. Your conversation will be saved. ",-1)),e("div",Ae,[m(y(x),{href:o.route("register"),class:"block w-full bg-medroid-primary text-white text-center px-4 py-3 rounded-lg hover:bg-medroid-primary/90 transition-colors"},{default:f(()=>s[19]||(s[19]=[v(" Create Account ")])),_:1},8,["href"]),m(y(x),{href:o.route("login"),class:"block w-full text-medroid-primary text-center px-4 py-3 rounded-lg border border-medroid-primary hover:bg-medroid-primary/5 transition-colors"},{default:f(()=>s[20]||(s[20]=[v(" Sign In ")])),_:1},8,["href"]),e("button",{onClick:s[4]||(s[4]=t=>b.value=!1),class:"block w-full text-gray-600 text-center px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"}," Continue Anonymous Chat ")])])])):h("",!0)]),_:1})],64))}});export{ze as default};
