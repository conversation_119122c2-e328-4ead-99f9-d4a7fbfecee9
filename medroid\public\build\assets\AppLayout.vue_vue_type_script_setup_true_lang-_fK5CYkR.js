import{r as q,m as h,o as D,E as U,b as l,e as s,g as r,s as y,f as i,n as u,i as c,w as o,u as n,P as p,F as b,d as v,l as x,q as H,j as E,t as k,H as O}from"./vendor-CGdKbVnC.js";import{M as j}from"./MedroidLogo-Bx6QLK9u.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as J,a as w,P as K}from"./Primitive-D-1s9QYo.js";const Q={class:"h-full bg-gray-800 text-white flex flex-col"},X={class:"flex items-center justify-between p-4 border-b border-gray-700"},Y={class:"flex items-center"},Z={key:2,class:"ml-3 text-xl font-semibold"},ee={class:"flex-1 overflow-y-auto py-4 flex flex-col justify-between"},te={class:"space-y-1"},se={key:0,class:"ml-3"},ae={key:0,class:"ml-3"},re={key:0,class:"ml-3"},le={key:0,class:"ml-3"},ie={key:0,class:"ml-3"},ne={key:0,class:"ml-3"},oe={key:0,class:"px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-4"},ue={key:0,class:"ml-3"},ce={key:0,class:"ml-3"},de={key:0,class:"ml-3"},pe={key:0,class:"ml-3"},ge={key:0,class:"ml-3"},fe={key:0,class:"ml-3"},he={key:0,class:"ml-3"},me={key:0,class:"px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-4"},ye={key:1},ve={key:0,class:"ml-3"},xe={key:2},be={key:0,class:"ml-3"},we={key:3},_e={key:0,class:"ml-3"},ke={key:4},$e={key:0,class:"ml-3"},We={key:5},Pe={key:0,class:"ml-3"},Se={key:6},Ae={key:0,class:"ml-3"},Me={key:7},Ce={key:0,class:"ml-3"},Be={key:8},Ve={key:0,class:"ml-3"},Le={key:9,class:"px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-4"},Ie={key:10},ze={key:0,class:"ml-3"},Ee={key:11},je={key:0,class:"ml-3"},Ne={key:12},Re={key:0,class:"ml-3"},Fe={key:0,class:"ml-3"},Te={key:0,class:"ml-3"},qe={key:0,class:"ml-3"},De={key:3,class:"px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider"},Ue={key:0,class:"ml-3"},He={class:"mt-auto pt-4 border-t border-gray-700"},Oe={key:0,class:"ml-3"},Ge={__name:"AppSidebar",props:{user:{type:Object,required:!0}},setup(g){const d=g,a=q(!1),m=h(()=>{var t;return((t=d.user)==null?void 0:t.role)||"patient"}),f=h(()=>m.value==="admin"),$=h(()=>m.value==="provider"),_=h(()=>m.value==="patient"),W=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view users"))}),P=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view providers"))}),S=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view patients"))}),N=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view analytics"))}),A=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view appointments"))}),M=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view payments"))}),C=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view chats"))}),B=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage notifications"))}),V=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage email templates"))}),L=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage permissions"))}),I=h(()=>{var t,e;return f.value||((e=(t=d.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage services"))}),R=h(()=>W.value||P.value||S.value||A.value||M.value||C.value||I.value),F=h(()=>L.value||V.value||B.value),T=()=>{a.value=!a.value,localStorage.setItem("app-sidebar-collapsed",a.value)},z=()=>{window.innerWidth<768&&!a.value&&(a.value=!0,localStorage.setItem("app-sidebar-collapsed","true"))};return D(()=>{const t=localStorage.getItem("app-sidebar-collapsed");t!==null&&(a.value=t==="true"),window.innerWidth<768&&(a.value=!0),window.addEventListener("resize",z)}),U(()=>{window.removeEventListener("resize",z)}),(t,e)=>(s(),l("div",{class:u(["h-screen flex-shrink-0",[a.value?"w-16":"w-64","transition-all duration-300"]])},[r("div",Q,[r("div",X,[r("div",Y,[a.value?(s(),y(j,{key:1,size:24})):(s(),y(j,{key:0,size:32})),a.value?i("",!0):(s(),l("span",Z,"Medroid"))]),r("button",{onClick:T,class:"text-gray-400 hover:text-white focus:outline-none"},[r("i",{class:u(["fas",a.value?"fa-angle-right":"fa-angle-left"])},null,2)])]),r("div",ee,[r("nav",null,[r("ul",te,[r("li",null,[c(n(p),{href:"/dashboard",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url==="/dashboard"}])},{default:o(()=>[e[0]||(e[0]=r("i",{class:"fas fa-tachometer-alt w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",se,"Dashboard"))]),_:1},8,["class"])]),_.value?(s(),l(b,{key:0},[r("li",null,[c(n(p),{href:"/chat",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url==="/chat"}])},{default:o(()=>[e[1]||(e[1]=r("i",{class:"fas fa-robot w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ae,"AI Chat"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/providers",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/providers")}])},{default:o(()=>[e[2]||(e[2]=r("i",{class:"fas fa-user-md w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",re,"Find Doctors"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/appointments",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/appointments")}])},{default:o(()=>[e[3]||(e[3]=r("i",{class:"fas fa-calendar-alt w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",le,"My Appointments"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/feed",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url==="/feed"}])},{default:o(()=>[e[4]||(e[4]=r("i",{class:"fas fa-home w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ie,"Health Feed"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/credits",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/credits")}])},{default:o(()=>[e[5]||(e[5]=r("i",{class:"fas fa-coins w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ne,"Credits"))]),_:1},8,["class"])])],64)):i("",!0),$.value?(s(),l(b,{key:1},[a.value?i("",!0):(s(),l("li",oe," Provider Tools ")),r("li",null,[c(n(p),{href:"/provider/availability",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/provider/availability")}])},{default:o(()=>[e[6]||(e[6]=r("i",{class:"fas fa-calendar-alt w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ue,"Availability"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/provider/services",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/provider/services")}])},{default:o(()=>[e[7]||(e[7]=r("i",{class:"fas fa-concierge-bell w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ce,"My Services"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/provider/schedule",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/provider/schedule")}])},{default:o(()=>[e[8]||(e[8]=r("i",{class:"fas fa-calendar-check w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",de,"My Schedule"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/appointments",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/appointments")}])},{default:o(()=>[e[9]||(e[9]=r("i",{class:"fas fa-calendar-plus w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",pe,"Appointments"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/provider/patients",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/provider/patients")}])},{default:o(()=>[e[10]||(e[10]=r("i",{class:"fas fa-users w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ge,"My Patients"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/provider/earnings",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/provider/earnings")}])},{default:o(()=>[e[11]||(e[11]=r("i",{class:"fas fa-dollar-sign w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",fe,"Earnings"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/provider/profile",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/provider/profile")}])},{default:o(()=>[e[12]||(e[12]=r("i",{class:"fas fa-user-md w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",he,"Provider Profile"))]),_:1},8,["class"])])],64)):i("",!0),R.value?(s(),l(b,{key:2},[a.value?i("",!0):(s(),l("li",me," Management ")),W.value?(s(),l("li",ye,[c(n(p),{href:"/users",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/users")}])},{default:o(()=>[e[13]||(e[13]=r("i",{class:"fas fa-users w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ve,"Users"))]),_:1},8,["class"])])):i("",!0),P.value?(s(),l("li",xe,[c(n(p),{href:"/providers",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/providers")&&!t.$page.url.startsWith("/providers/")}])},{default:o(()=>[e[14]||(e[14]=r("i",{class:"fas fa-user-md w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",be,"Providers"))]),_:1},8,["class"])])):i("",!0),S.value?(s(),l("li",we,[c(n(p),{href:"/patients",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/patients")}])},{default:o(()=>[e[15]||(e[15]=r("i",{class:"fas fa-user-injured w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",_e,"Patients"))]),_:1},8,["class"])])):i("",!0),A.value?(s(),l("li",ke,[c(n(p),{href:"/appointments",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/appointments")&&!t.$page.url.startsWith("/appointments/")}])},{default:o(()=>[e[16]||(e[16]=r("i",{class:"fas fa-calendar-alt w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",$e,"Appointments"))]),_:1},8,["class"])])):i("",!0),N.value?(s(),l("li",We,[c(n(p),{href:"/admin/analytics",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/admin/analytics")}])},{default:o(()=>[e[17]||(e[17]=r("i",{class:"fas fa-chart-bar w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Pe,"Analytics"))]),_:1},8,["class"])])):i("",!0),M.value?(s(),l("li",Se,[c(n(p),{href:"/payments",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/payments")}])},{default:o(()=>[e[18]||(e[18]=r("i",{class:"fas fa-credit-card w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Ae,"Payments"))]),_:1},8,["class"])])):i("",!0),C.value?(s(),l("li",Me,[c(n(p),{href:"/chats",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/chats")&&!t.$page.url.startsWith("/chat")}])},{default:o(()=>[e[19]||(e[19]=r("i",{class:"fas fa-comments w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Ce,"Chats"))]),_:1},8,["class"])])):i("",!0),I.value?(s(),l("li",Be,[c(n(p),{href:"/services",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/services")}])},{default:o(()=>[e[20]||(e[20]=r("i",{class:"fas fa-concierge-bell w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Ve,"Services"))]),_:1},8,["class"])])):i("",!0),!a.value&&F.value?(s(),l("li",Le," System ")):i("",!0),L.value?(s(),l("li",Ie,[c(n(p),{href:"/permissions",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/permissions")}])},{default:o(()=>[e[21]||(e[21]=r("i",{class:"fas fa-key w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",ze,"Permissions"))]),_:1},8,["class"])])):i("",!0),V.value?(s(),l("li",Ee,[c(n(p),{href:"/email-templates",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/email-templates")}])},{default:o(()=>[e[22]||(e[22]=r("i",{class:"fas fa-envelope w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",je,"Email Templates"))]),_:1},8,["class"])])):i("",!0),B.value?(s(),l("li",Ne,[c(n(p),{href:"/notifications",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/notifications")}])},{default:o(()=>[e[23]||(e[23]=r("i",{class:"fas fa-bell w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Re,"Notifications"))]),_:1},8,["class"])])):i("",!0),r("li",null,[c(n(p),{href:"/admin/clubs",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/admin/clubs")}])},{default:o(()=>[e[24]||(e[24]=r("i",{class:"fas fa-trophy w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Fe,"Club Management"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/admin/referrals",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/admin/referrals")}])},{default:o(()=>[e[25]||(e[25]=r("i",{class:"fas fa-share-alt w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Te,"Referral Management"))]),_:1},8,["class"])]),r("li",null,[c(n(p),{href:"/admin/settings",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url.startsWith("/admin/settings")}])},{default:o(()=>[e[26]||(e[26]=r("i",{class:"fas fa-cog w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",qe,"System Settings"))]),_:1},8,["class"])])],64)):i("",!0),a.value?i("",!0):(s(),l("li",De," Account ")),r("li",null,[c(n(p),{href:"/settings/profile",class:u(["flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",{"bg-gray-700 text-white":t.$page.url==="/settings/profile"}])},{default:o(()=>[e[27]||(e[27]=r("i",{class:"fas fa-user w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Ue,"Profile"))]),_:1},8,["class"])])])]),r("div",He,[c(n(p),{href:"/logout",method:"post",class:"w-full flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"},{default:o(()=>[e[28]||(e[28]=r("i",{class:"fas fa-sign-out-alt w-6 text-center"},null,-1)),a.value?i("",!0):(s(),l("span",Oe,"Logout"))]),_:1})])])])],2))}},Je=G(Ge,[["__scopeId","data-v-26a96af2"]]),Ke=v({__name:"Breadcrumb",props:{class:{}},setup(g){const d=g;return(a,m)=>(s(),l("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:u(d.class)},[x(a.$slots,"default")],2))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qe=J("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),Xe=v({__name:"BreadcrumbItem",props:{class:{}},setup(g){const d=g;return(a,m)=>(s(),l("li",{"data-slot":"breadcrumb-item",class:u(n(w)("inline-flex items-center gap-1.5",d.class))},[x(a.$slots,"default")],2))}}),Ye=v({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(g){const d=g;return(a,m)=>(s(),y(n(K),{"data-slot":"breadcrumb-link",as:a.as,"as-child":a.asChild,class:u(n(w)("hover:text-foreground transition-colors",d.class))},{default:o(()=>[x(a.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Ze=v({__name:"BreadcrumbList",props:{class:{}},setup(g){const d=g;return(a,m)=>(s(),l("ol",{"data-slot":"breadcrumb-list",class:u(n(w)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",d.class))},[x(a.$slots,"default")],2))}}),et=v({__name:"BreadcrumbPage",props:{class:{}},setup(g){const d=g;return(a,m)=>(s(),l("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:u(n(w)("text-foreground font-normal",d.class))},[x(a.$slots,"default")],2))}}),tt=v({__name:"BreadcrumbSeparator",props:{class:{}},setup(g){const d=g;return(a,m)=>(s(),l("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:u(n(w)("[&>svg]:size-3.5",d.class))},[x(a.$slots,"default",{},()=>[c(n(Qe))])],2))}}),st=v({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(g){return(d,a)=>(s(),y(n(Ke),null,{default:o(()=>[c(n(Ze),null,{default:o(()=>[(s(!0),l(b,null,H(d.breadcrumbs,(m,f)=>(s(),l(b,{key:f},[c(n(Xe),null,{default:o(()=>[f===d.breadcrumbs.length-1?(s(),y(n(et),{key:0},{default:o(()=>[E(k(m.title),1)]),_:2},1024)):(s(),y(n(Ye),{key:1,"as-child":""},{default:o(()=>[c(n(p),{href:m.href??"#"},{default:o(()=>[E(k(m.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),f!==d.breadcrumbs.length-1?(s(),y(n(tt),{key:0})):i("",!0)],64))),128))]),_:1})]),_:1}))}}),at={class:"flex h-screen bg-gray-100"},rt={class:"flex-1 flex flex-col overflow-hidden"},lt={class:"bg-white shadow-sm border-b border-gray-200"},it={class:"px-6 py-4"},nt={class:"flex items-center justify-between"},ot={class:"flex items-center space-x-4"},ut={class:"text-sm text-gray-700"},ct={class:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100"},dt={__name:"AppSidebarLayout",props:{breadcrumbs:{type:Array,default:()=>[]}},setup(g){var m;const a=(m=O().props.auth)==null?void 0:m.user;return(f,$)=>{var _;return s(),l("div",at,[c(Je,{user:n(a)},null,8,["user"]),r("div",rt,[r("header",lt,[r("div",it,[r("div",nt,[r("div",null,[g.breadcrumbs.length>0?(s(),y(st,{key:0,breadcrumbs:g.breadcrumbs},null,8,["breadcrumbs"])):i("",!0)]),r("div",ot,[r("span",ut,"Welcome, "+k((_=n(a))==null?void 0:_.name),1)])])])]),r("main",ct,[x(f.$slots,"default")])])])}}},mt=v({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(g){return(d,a)=>(s(),y(dt,{breadcrumbs:d.breadcrumbs},{default:o(()=>[x(d.$slots,"default")]),_:3},8,["breadcrumbs"]))}});export{mt as _};
