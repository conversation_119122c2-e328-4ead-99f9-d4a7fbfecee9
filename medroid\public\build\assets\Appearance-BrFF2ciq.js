import{d as m,b as i,e as t,F as k,q as y,g as s,s as d,Q as g,t as x,n as b,u as r,w as l,i as e,p as f}from"./vendor-CGdKbVnC.js";import{u as v}from"./app-B5KLD1sc.js";import{c as o}from"./Primitive-D-1s9QYo.js";import{_ as M,a as w}from"./Layout.vue_vue_type_script_setup_true_lang-WlaPwVGG.js";import{_ as I}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import"./index-l_ndanDW.js";import"./index-CgBriE2E.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=o("MonitorIcon",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=o("MoonIcon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=o("SunIcon",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),B={class:"inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800"},S=["onClick"],q={class:"ml-1.5 text-sm"},z=m({__name:"AppearanceTabs",setup(u){const{appearance:a,updateAppearance:c}=v(),p=[{value:"light",Icon:$,label:"Light"},{value:"dark",Icon:C,label:"Dark"},{value:"system",Icon:A,label:"System"}];return(L,j)=>(t(),i("div",B,[(t(),i(k,null,y(p,({value:n,Icon:_,label:h})=>s("button",{key:n,onClick:F=>r(c)(n),class:b(["flex items-center rounded-md px-3.5 py-1.5 transition-colors",r(a)===n?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"])},[(t(),d(g(_),{class:"-ml-1 h-4 w-4"})),s("span",q,x(h),1)],10,S)),64))]))}}),D={class:"space-y-6"},J=m({__name:"Appearance",setup(u){const a=[{title:"Appearance settings",href:"/settings/appearance"}];return(c,p)=>(t(),d(I,{breadcrumbs:a},{default:l(()=>[e(r(f),{title:"Appearance settings"}),e(M,null,{default:l(()=>[s("div",D,[e(w,{title:"Appearance settings",description:"Update your account's appearance settings"}),e(z)])]),_:1})]),_:1}))}});export{J as default};
