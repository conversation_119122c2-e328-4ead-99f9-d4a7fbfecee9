import{r as u,o as L,b as l,e as i,i as j,u as W,p as z,w as B,g as t,f as w,t as v,j as A,F as g,q as h,x as b,z as R,y as x,a as T}from"./vendor-B07q4Gx1.js";import{_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js";import"./MedroidLogo-B0q18fAd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B2yccHbY.js";const G={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},H={key:0,class:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4"},I={class:"flex"},J={class:"text-red-700"},K={key:1,class:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4"},Q={class:"flex"},X={class:"text-green-700"},Y={key:2,class:"text-center py-12"},Z={key:3,class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ee={class:"lg:col-span-2"},te={class:"bg-white rounded-lg shadow-sm border p-6"},se={class:"flex justify-between items-center mb-6"},ae={class:"space-y-4"},le={class:"font-medium text-gray-900 mb-3"},ie={key:0,class:"text-gray-500 text-sm"},oe={key:1,class:"space-y-2"},ne={class:"text-sm"},re=["onClick"],de={class:"space-y-6"},ue={class:"bg-white rounded-lg shadow-sm border p-6"},ve={class:"flex justify-between items-center mb-4"},me={key:0,class:"text-gray-500 text-sm"},ce={key:1,class:"space-y-3"},ye={class:"flex justify-between items-start"},be={class:"text-sm font-medium"},pe={class:"text-xs text-gray-600 mt-1"},fe=["onClick"],ge=["disabled"],xe={key:0,class:"fas fa-spinner fa-spin mr-2"},_e={key:1,class:"fas fa-save mr-2"},ke={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},we={class:"bg-white rounded-lg p-6 w-full max-w-md mx-4"},he={class:"space-y-4"},Te=["value"],De={class:"flex justify-end space-x-3 mt-6"},Se={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ae={class:"bg-white rounded-lg p-6 w-full max-w-md mx-4"},Ce={class:"space-y-4"},Me={class:"flex justify-end space-x-3 mt-6"},Fe={__name:"Availability",setup(Ve){const E=[{title:"Dashboard",href:"/dashboard"},{title:"Availability Management",href:"/provider/availability"}],D=u(!1),p=u(!1),r=u(null),f=u(""),S=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],m=u({Monday:[],Tuesday:[],Wednesday:[],Thursday:[],Friday:[],Saturday:[],Sunday:[]}),y=u([]),d=u({day:"Monday",startTime:"09:00",endTime:"17:00"}),o=u({startDate:"",endDate:"",reason:""}),_=u(!1),k=u(!1),O=async()=>{D.value=!0,r.value=null;try{const[a,e]=await Promise.all([T.get("/provider/get-availability"),T.get("/provider/get-absences")]);if(a.data.weekly_availability){const s={};S.forEach(n=>{s[n]=[]}),a.data.weekly_availability.forEach(n=>{n.day&&n.slots&&(s[n.day]=n.slots)}),m.value=s}e.data.absences&&(y.value=e.data.absences)}catch(a){console.error("Error fetching availability data:",a),r.value="Failed to load availability data. Please try again."}finally{D.value=!1}},U=()=>{const a=d.value.day,e={start_time:d.value.startTime,end_time:d.value.endTime};if(e.start_time>=e.end_time){r.value="End time must be after start time";return}if(m.value[a].some(c=>e.start_time<c.end_time&&e.end_time>c.start_time)){r.value="Time slot overlaps with existing slot";return}m.value[a].push(e),m.value[a].sort((c,V)=>c.start_time.localeCompare(V.start_time)),d.value={day:"Monday",startTime:"09:00",endTime:"17:00"},_.value=!1,r.value=null},$=(a,e)=>{m.value[a].splice(e,1)},F=()=>{if(!o.value.startDate||!o.value.endDate){r.value="Please select start and end dates";return}if(o.value.startDate>o.value.endDate){r.value="End date must be after start date";return}y.value.push({start_date:o.value.startDate,end_date:o.value.endDate,reason:o.value.reason||"Personal time off"}),o.value={startDate:"",endDate:"",reason:""},k.value=!1,r.value=null},N=a=>{y.value.splice(a,1)},P=async()=>{p.value=!0,r.value=null,f.value="";try{const a=Object.keys(m.value).map(e=>({day:e,slots:m.value[e]}));await T.post("/api/provider/availability",{weekly_availability:a}),await T.post("/api/provider/absences",{absences:y.value}),f.value="Availability updated successfully!",setTimeout(()=>{f.value=""},3e3)}catch(a){console.error("Error saving availability:",a),r.value="Failed to save availability. Please try again."}finally{p.value=!1}},C=a=>new Date(`2000-01-01T${a}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),M=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return L(()=>{O()}),(a,e)=>(i(),l(g,null,[j(W(z),{title:"Availability Management"}),j(q,{breadcrumbs:E},{default:B(()=>[t("div",G,[e[19]||(e[19]=t("div",{class:"mb-8"},[t("h1",{class:"text-3xl font-bold text-gray-900"},"Availability Management"),t("p",{class:"mt-2 text-gray-600"},"Manage your working hours and time off")],-1)),r.value?(i(),l("div",H,[t("div",I,[e[10]||(e[10]=t("i",{class:"fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"},null,-1)),t("p",J,v(r.value),1)])])):w("",!0),f.value?(i(),l("div",K,[t("div",Q,[e[11]||(e[11]=t("i",{class:"fas fa-check-circle text-green-400 mr-3 mt-0.5"},null,-1)),t("p",X,v(f.value),1)])])):w("",!0),D.value?(i(),l("div",Y,e[12]||(e[12]=[t("i",{class:"fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"},null,-1),t("p",{class:"text-gray-600"},"Loading availability data...",-1)]))):(i(),l("div",Z,[t("div",ee,[t("div",te,[t("div",se,[e[14]||(e[14]=t("h2",{class:"text-xl font-semibold text-gray-900"},"Weekly Availability",-1)),t("button",{onClick:e[0]||(e[0]=s=>_.value=!0),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"},e[13]||(e[13]=[t("i",{class:"fas fa-plus mr-2"},null,-1),A(" Add Time Slot ")]))]),t("div",ae,[(i(),l(g,null,h(S,s=>t("div",{key:s,class:"border rounded-lg p-4"},[t("h3",le,v(s),1),m.value[s].length===0?(i(),l("div",ie," No availability set for this day ")):(i(),l("div",oe,[(i(!0),l(g,null,h(m.value[s],(n,c)=>(i(),l("div",{key:c,class:"flex justify-between items-center bg-gray-50 rounded p-3"},[t("span",ne,v(C(n.start_time))+" - "+v(C(n.end_time)),1),t("button",{onClick:V=>$(s,c),class:"text-red-600 hover:text-red-800"},e[15]||(e[15]=[t("i",{class:"fas fa-trash text-sm"},null,-1)]),8,re)]))),128))]))])),64))])])]),t("div",de,[t("div",ue,[t("div",ve,[e[17]||(e[17]=t("h2",{class:"text-lg font-semibold text-gray-900"},"Time Off",-1)),t("button",{onClick:e[1]||(e[1]=s=>k.value=!0),class:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"},e[16]||(e[16]=[t("i",{class:"fas fa-plus mr-1"},null,-1),A(" Add ")]))]),y.value.length===0?(i(),l("div",me," No time off scheduled ")):(i(),l("div",ce,[(i(!0),l(g,null,h(y.value,(s,n)=>(i(),l("div",{key:n,class:"border rounded p-3"},[t("div",ye,[t("div",null,[t("p",be,v(M(s.start_date))+" - "+v(M(s.end_date)),1),t("p",pe,v(s.reason),1)]),t("button",{onClick:c=>N(n),class:"text-red-600 hover:text-red-800"},e[18]||(e[18]=[t("i",{class:"fas fa-trash text-sm"},null,-1)]),8,fe)])]))),128))]))]),t("button",{onClick:P,disabled:p.value,class:"w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"},[p.value?(i(),l("i",xe)):(i(),l("i",_e)),A(" "+v(p.value?"Saving...":"Save Changes"),1)],8,ge)])]))]),_.value?(i(),l("div",ke,[t("div",we,[e[23]||(e[23]=t("h3",{class:"text-lg font-semibold mb-4"},"Add Time Slot",-1)),t("div",he,[t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Day",-1)),b(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>d.value.day=s),class:"w-full border rounded-lg px-3 py-2"},[(i(),l(g,null,h(S,s=>t("option",{key:s,value:s},v(s),9,Te)),64))],512),[[R,d.value.day]])]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Start Time",-1)),b(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>d.value.startTime=s),type:"time",class:"w-full border rounded-lg px-3 py-2"},null,512),[[x,d.value.startTime]])]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"End Time",-1)),b(t("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>d.value.endTime=s),type:"time",class:"w-full border rounded-lg px-3 py-2"},null,512),[[x,d.value.endTime]])])]),t("div",De,[t("button",{onClick:e[5]||(e[5]=s=>_.value=!1),class:"px-4 py-2 text-gray-600 hover:text-gray-800"}," Cancel "),t("button",{onClick:U,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"}," Add Slot ")])])])):w("",!0),k.value?(i(),l("div",Se,[t("div",Ae,[e[27]||(e[27]=t("h3",{class:"text-lg font-semibold mb-4"},"Add Time Off",-1)),t("div",Ce,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Start Date",-1)),b(t("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>o.value.startDate=s),type:"date",class:"w-full border rounded-lg px-3 py-2"},null,512),[[x,o.value.startDate]])]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"End Date",-1)),b(t("input",{"onUpdate:modelValue":e[7]||(e[7]=s=>o.value.endDate=s),type:"date",class:"w-full border rounded-lg px-3 py-2"},null,512),[[x,o.value.endDate]])]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Reason (Optional)",-1)),b(t("input",{"onUpdate:modelValue":e[8]||(e[8]=s=>o.value.reason=s),type:"text",placeholder:"e.g., Vacation, Conference, etc.",class:"w-full border rounded-lg px-3 py-2"},null,512),[[x,o.value.reason]])])]),t("div",Me,[t("button",{onClick:e[9]||(e[9]=s=>k.value=!1),class:"px-4 py-2 text-gray-600 hover:text-gray-800"}," Cancel "),t("button",{onClick:F,class:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"}," Add Time Off ")])])])):w("",!0)]),_:1})],64))}};export{Fe as default};
