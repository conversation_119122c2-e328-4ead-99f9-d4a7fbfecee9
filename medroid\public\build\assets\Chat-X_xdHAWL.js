import{r as d,o as y,b as o,e as r,i as u,u as x,p as b,w,g as e,f as k,F as h,q as _,t as p,v as M,x as A,y as j,j as C,A as I}from"./vendor-B07q4Gx1.js";import{_ as B}from"./AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js";import"./MedroidLogo-B0q18fAd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B2yccHbY.js";const H={class:"flex h-full flex-1 flex-col gap-6 rounded-xl p-6"},T={class:"bg-white rounded-lg shadow-lg flex-1 flex flex-col min-h-96"},z={key:0,class:"flex justify-end"},S={class:"bg-blue-600 text-white rounded-lg px-4 py-2 max-w-xs lg:max-w-md"},V={key:1,class:"flex justify-start"},L={class:"bg-gray-100 text-gray-800 rounded-lg px-4 py-2 max-w-xs lg:max-w-md"},N={key:0,class:"flex justify-start"},q={class:"border-t p-4"},D=["disabled"],F=["disabled"],U={__name:"Chat",setup(P){const l=d([]),a=d(""),n=d(!1),i=d(null),g=[{title:"AI Chat",href:"/chat"}],m=async()=>{if(!a.value.trim())return;const c=a.value.trim();l.value.push({type:"user",content:c}),a.value="",n.value=!0;try{const s=await fetch("/api/chat/start",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${$page.props.auth.token}`},body:JSON.stringify({message:c})}),t=await s.json();s.ok&&t.response?l.value.push({type:"ai",content:t.response}):(console.error("Failed to get AI response"),l.value.push({type:"ai",content:"Sorry, I encountered an error. Please try again."}))}catch{console.error("An error occurred while sending your message"),l.value.push({type:"ai",content:"Sorry, I encountered an error. Please try again."})}finally{n.value=!1,await I(),f()}},f=()=>{i.value&&(i.value.scrollTop=i.value.scrollHeight)};return y(()=>{l.value.push({type:"ai",content:"Hello! I'm your AI health assistant. How can I help you today? You can ask me about symptoms, medications, general health advice, or anything health-related."})}),(c,s)=>(r(),o(h,null,[u(x(b),{title:"AI Health Assistant"}),u(B,{breadcrumbs:g},{default:w(()=>[e("div",H,[s[2]||(s[2]=e("div",{class:"bg-white overflow-hidden shadow-sm rounded-lg"},[e("div",{class:"p-6"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])]),e("div",null,[e("h2",{class:"text-xl font-semibold text-gray-800"},"AI Health Assistant"),e("p",{class:"text-sm text-gray-600"},"Get instant answers to your health questions")])])])],-1)),e("div",T,[e("div",{class:"flex-1 p-4 overflow-y-auto",ref_key:"chatContainer",ref:i},[(r(!0),o(h,null,_(l.value,(t,v)=>(r(),o("div",{key:v,class:"mb-4"},[t.type==="user"?(r(),o("div",z,[e("div",S,p(t.content),1)])):(r(),o("div",V,[e("div",L,p(t.content),1)]))]))),128)),n.value?(r(),o("div",N,s[1]||(s[1]=[e("div",{class:"bg-gray-100 text-gray-800 rounded-lg px-4 py-2"},[e("div",{class:"flex space-x-1"},[e("div",{class:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),e("div",{class:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{"animation-delay":"0.1s"}}),e("div",{class:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{"animation-delay":"0.2s"}})])],-1)]))):k("",!0)],512),e("div",q,[e("form",{onSubmit:M(m,["prevent"]),class:"flex space-x-2"},[A(e("input",{"onUpdate:modelValue":s[0]||(s[0]=t=>a.value=t),type:"text",placeholder:"Type your health question...",class:"flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:n.value},null,8,D),[[j,a.value]]),e("button",{type:"submit",disabled:!a.value.trim()||n.value,class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Send ",8,F)],32)])]),s[3]||(s[3]=e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-6"},[e("div",{class:"bg-white p-6 rounded-lg shadow"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mb-3"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])]),e("h3",{class:"font-semibold text-lg mb-2"},"Symptom Analysis"),e("p",{class:"text-gray-600"},"Describe your symptoms and get preliminary insights about possible conditions.")]),e("div",{class:"bg-white p-6 rounded-lg shadow"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mb-3"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"})])]),e("h3",{class:"font-semibold text-lg mb-2"},"Medication Info"),e("p",{class:"text-gray-600"},"Ask about medications, dosages, side effects, and drug interactions.")]),e("div",{class:"bg-white p-6 rounded-lg shadow"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mb-3"},[e("svg",{class:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])]),e("h3",{class:"font-semibold text-lg mb-2"},"Health Advice"),e("p",{class:"text-gray-600"},"Get general health tips, lifestyle recommendations, and wellness guidance.")])],-1)),s[4]||(s[4]=e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},[e("div",{class:"flex"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("p",{class:"text-sm text-yellow-800"},[e("strong",null,"Medical Disclaimer:"),C(" This AI assistant provides general health information only and should not replace professional medical advice. Always consult with a qualified healthcare provider for medical concerns. ")])])])],-1))])]),_:1})],64))}};export{U as default};
