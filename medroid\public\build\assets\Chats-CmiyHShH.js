import{_ as b}from"./AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js";import{r as m,o as w,b as d,e as r,i as u,u as y,p as C,w as g,g as t,t as a,F as c,q as p,n as _,f as h,s as B,P as M,j as T}from"./vendor-B07q4Gx1.js";import"./MedroidLogo-B0q18fAd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B2yccHbY.js";const A={class:"flex items-center justify-between"},V={class:"flex mt-2","aria-label":"Breadcrumb"},D={class:"inline-flex items-center space-x-1 md:space-x-3"},E={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},N={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},S={class:"py-12"},j={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},I={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},J={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},L={class:"p-6"},P={class:"flex items-center"},z={class:"ml-4"},F={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},q={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},$={class:"p-6"},G={class:"flex items-center"},H={class:"ml-4"},K={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},O={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Q={class:"p-6"},R={class:"flex items-center"},U={class:"ml-4"},W={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},X={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Y={class:"p-6"},Z={class:"flex items-center"},tt={class:"ml-4"},et={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},st={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},at={class:"p-6 text-gray-900 dark:text-gray-100"},rt={key:0,class:"text-center py-8"},dt={key:1,class:"overflow-x-auto"},lt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ot={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},it={class:"px-6 py-4 whitespace-nowrap"},nt={class:"flex items-center"},xt={class:"ml-4"},gt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ct={class:"text-sm text-gray-500 dark:text-gray-400"},mt={class:"px-6 py-4"},ut={class:"text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate"},yt={class:"text-sm text-gray-500 dark:text-gray-400"},pt={class:"px-6 py-4 whitespace-nowrap"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ht={class:"px-6 py-4 whitespace-nowrap"},ft={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},vt={key:0,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Tt={__name:"Chats",setup(kt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Chats",href:"/chats"}],x=m(!1),l=m([]),f=async()=>{x.value=!0;try{l.value=[{id:1,patient_name:"John Doe",provider_name:"Dr. Jane Smith",last_message:"Thank you for the consultation",last_message_time:"2024-06-02 14:30",status:"active",message_count:15,type:"consultation"},{id:2,patient_name:"Emily Johnson",provider_name:"AI Assistant",last_message:"I need help with my symptoms",last_message_time:"2024-06-02 13:45",status:"active",message_count:8,type:"ai_chat"}]}catch(o){console.error("Error fetching chats:",o)}finally{x.value=!1}},v=o=>({active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",ended:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",archived:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",k=o=>({consultation:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",ai_chat:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",support:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return w(()=>{f()}),(o,e)=>(r(),d(c,null,[u(y(C),{title:"Chat Management"}),u(b,null,{header:g(()=>[t("div",A,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Chat Management ",-1)),t("nav",V,[t("ol",D,[(r(),d(c,null,p(n,(s,i)=>t("li",{key:i,class:"inline-flex items-center"},[i<n.length-1?(r(),B(y(M),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:g(()=>[T(a(s.title),1)]),_:2},1032,["href"])):(r(),d("span",E,a(s.title),1)),i<n.length-1?(r(),d("svg",N,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):h("",!0)])),64))])])])])]),default:g(()=>[t("div",S,[t("div",j,[t("div",I,[t("div",J,[t("div",L,[t("div",P,[e[3]||(e[3]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-comments text-2xl text-blue-500"})],-1)),t("div",z,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Chats",-1)),t("p",F,a(l.value.length),1)])])])]),t("div",q,[t("div",$,[t("div",G,[e[5]||(e[5]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-circle text-2xl text-green-500"})],-1)),t("div",H,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Active Chats",-1)),t("p",K,a(l.value.filter(s=>s.status==="active").length),1)])])])]),t("div",O,[t("div",Q,[t("div",R,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-robot text-2xl text-purple-500"})],-1)),t("div",U,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"AI Chats",-1)),t("p",W,a(l.value.filter(s=>s.type==="ai_chat").length),1)])])])]),t("div",X,[t("div",Y,[t("div",Z,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-message text-2xl text-orange-500"})],-1)),t("div",tt,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Messages",-1)),t("p",et,a(l.value.reduce((s,i)=>s+i.message_count,0)),1)])])])])]),t("div",st,[t("div",at,[x.value?(r(),d("div",rt,e[10]||(e[10]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(r(),d("div",dt,[t("table",lt,[e[14]||(e[14]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Participants "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Last Message "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Messages "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",ot,[(r(!0),d(c,null,p(l.value,s=>(r(),d("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",it,[t("div",nt,[e[11]||(e[11]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center"},[t("i",{class:"fas fa-comment text-blue-600 dark:text-blue-400"})])],-1)),t("div",xt,[t("div",gt,a(s.patient_name),1),t("div",ct," with "+a(s.provider_name),1)])])]),t("td",mt,[t("div",ut,a(s.last_message),1),t("div",yt,a(s.last_message_time),1)]),t("td",pt,[t("span",{class:_([k(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.type.replace("_"," ")),3)]),t("td",_t,a(s.message_count),1),t("td",ht,[t("span",{class:_([v(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.status),3)]),t("td",ft,[e[12]||(e[12]=t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View ",-1)),e[13]||(e[13]=t("button",{class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"}," Monitor ",-1)),s.status==="active"?(r(),d("button",vt," End ")):h("",!0)])]))),128))])])]))])])])])]),_:1})],64))}};export{Tt as default};
