import{d as l,M as f,s as i,e as d,w as t,i as a,g as r,u as s,p as c,v as u,j as n,f as _}from"./vendor-CGdKbVnC.js";import{_ as w}from"./InputError.vue_vue_type_script_setup_true_lang-BY1lF_tM.js";import{_ as C}from"./index-l_ndanDW.js";import{_ as g,a as V}from"./Label.vue_vue_type_script_setup_true_lang-DnpYyBxB.js";import{L as b,_ as h}from"./AuthLayout.vue_vue_type_script_setup_true_lang-Bx2q0EL9.js";import"./Primitive-D-1s9QYo.js";import"./index-CgBriE2E.js";const v={class:"space-y-6"},x={class:"grid gap-2"},y={class:"flex items-center"},j=l({__name:"ConfirmPassword",setup($){const o=f({password:""}),m=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset()}})};return(k,e)=>(d(),i(h,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[a(s(c),{title:"Confirm password"}),r("form",{onSubmit:u(m,["prevent"])},[r("div",v,[r("div",x,[a(s(g),{htmlFor:"password"},{default:t(()=>e[1]||(e[1]=[n("Password")])),_:1}),a(s(V),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(o).password,"onUpdate:modelValue":e[0]||(e[0]=p=>s(o).password=p),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(w,{message:s(o).errors.password},null,8,["message"])]),r("div",y,[a(s(C),{class:"w-full",disabled:s(o).processing},{default:t(()=>[s(o).processing?(d(),i(s(b),{key:0,class:"h-4 w-4 animate-spin"})):_("",!0),e[2]||(e[2]=n(" Confirm Password "))]),_:1},8,["disabled"])])])],32)]),_:1}))}});export{j as default};
