import{r as h,m as f,o as L,b as l,e as n,i as _,u as w,p as z,w as k,g as e,f as b,t as r,x as B,z as I,j as K,F as A,q as C,n as D,s as X,P as R,a as U}from"./vendor-CGdKbVnC.js";import{_ as H}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-D-1s9QYo.js";const Q={class:"unified-dashboard"},W={class:"dashboard-container"},G={class:"dashboard-header"},Y={class:"header-content"},J={class:"dashboard-title"},Z={class:"dashboard-subtitle"},ee={class:"header-actions"},te=["disabled"],se={key:0,class:"fas fa-spinner fa-spin"},ae={key:1,class:"fas fa-sync-alt"},oe={key:0,class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ne={class:"bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500"},le={class:"flex justify-between"},ie={class:"text-2xl font-bold text-blue-600"},de={class:"bg-green-50 rounded-lg p-4 border-l-4 border-green-500"},ce={class:"flex justify-between"},ue={class:"text-2xl font-bold text-green-600"},pe={class:"bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500"},ge={class:"flex justify-between"},me={class:"text-2xl font-bold text-purple-600"},ve={class:"bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500"},he={class:"flex justify-between"},be={class:"text-2xl font-bold text-yellow-600"},fe={key:1,class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},xe={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ye={class:"bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500"},_e={class:"flex justify-between"},we={class:"text-2xl font-bold text-blue-600"},ke={class:"text-xs text-gray-400"},Ae={key:2,class:"bg-white overflow-hidden shadow-sm rounded-lg mb-6"},De={class:"p-6"},Pe={class:"space-y-4"},Me={class:"flex justify-between items-start"},je={class:"font-medium text-gray-900"},Ce={class:"text-sm text-gray-600"},Re={class:"text-sm text-gray-500 mt-1"},Ue={key:3,class:"bg-white overflow-hidden shadow-sm rounded-lg"},Se={class:"p-6"},Ve={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Te={class:"text-sm font-medium text-gray-900 text-center"},qe={class:"text-xs text-gray-500 text-center mt-1"},Ee={key:4,class:"bg-white overflow-hidden shadow-sm rounded-lg"},Ne={class:"p-6"},Oe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ie={__name:"Dashboard",props:{stats:{type:Object,default:()=>({upcomingAppointments:0,completedAppointments:0,healthRecords:0,totalPatients:0,monthlyEarnings:0,completionRate:0})},recentAppointments:{type:Array,default:()=>[]},quickActions:{type:Array,default:()=>[]},kpiData:{type:Object,default:()=>({})},user:{type:Object,default:()=>null},providerData:{type:Object,default:()=>({weeklyAvailability:[],nextAppointments:[],earnings:{},patientCount:0})}},setup(i){const a=i,S=[{title:"Dashboard",href:"/dashboard"}],c=h(!1),V=h("Never"),g=h("admin"),m=h(null),P=h({total_users:0,monthly_active_users:0,daily_active_users:0,user_growth_rate:0,total_appointments:0,monthly_appointments:0,daily_appointments:0,appointment_completion_rate:0,total_consults:0,monthly_consults:0,daily_consults:0,consult_to_appointment_ratio:0,repeat_consult_users:0,diagnostic_accuracy:0,diagnostic_feedback_count:0,top_accurate_diagnoses:[],top_inaccurate_diagnoses:[],user_locations:[],total_revenue:0,monthly_revenue:0,average_appointment_value:0,...a.kpiData}),v=f(()=>a.user&&a.user.role==="admin"),u=f(()=>a.user&&a.user.role==="provider"),x=f(()=>a.user&&a.user.role==="patient"),y=f(()=>{var o;return a.user&&(a.user.role==="admin"||((o=a.user.user_permissions)==null?void 0:o.includes("view analytics")))}),T=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),q=o=>({scheduled:"bg-blue-100 text-blue-800",confirmed:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-gray-100 text-gray-800",cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",E=()=>u.value?"Provider Dashboard":x.value?"Patient Dashboard":v.value?{admin:"Admin Dashboard",provider:"Provider Dashboard",patient:"Patient Dashboard",care_manager:"Care Manager Dashboard"}[g.value]||"Admin Dashboard":"Dashboard",N=()=>u.value?"Manage your patients, availability, and appointments":x.value?"Track your health and appointments":v.value?{admin:"Manage your healthcare platform from one place",provider:"Manage your patients and appointments",patient:"Track your health and appointments",care_manager:"Monitor patient care and compliance"}[g.value]||"Manage your healthcare platform":"Manage your healthcare services",O=()=>{M(!0)},M=async(o=!1)=>{if(!y.value){m.value="You do not have permission to view analytics data.";return}c.value=!0,m.value=null;try{const t=await U.get("/management/dashboard/kpi",{params:{refresh:o},headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});P.value={...P.value,...t.data},V.value=new Date().toLocaleString()}catch(t){console.error("Error fetching KPI data:",t),t.response?m.value=`Failed to load KPI data: ${t.response.data.message||t.message}`:t.request?m.value="Failed to load KPI data: No response from server":m.value=`Failed to load KPI data: ${t.message}`}finally{c.value=!1}},$=async()=>{var o,t,s;if(u.value){c.value=!0;try{const d=(o=document.querySelector('meta[name="csrf-token"]'))==null?void 0:o.getAttribute("content"),p=await U.get("/provider/dashboard-data",{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":d||""},withCredentials:!0});p.data&&(Object.assign(a.stats,p.data.stats),p.data.todayAppointments&&a.recentAppointments.splice(0,a.recentAppointments.length,...p.data.todayAppointments))}catch(d){console.error("Error fetching provider data:",d),(((t=d.response)==null?void 0:t.status)===401||((s=d.response)==null?void 0:s.status)===403)&&console.warn("User does not have provider access - this is expected for non-provider users")}finally{c.value=!1}}},F=()=>[{title:"Manage Availability",href:"/provider/availability",icon:"calendar-alt",description:"Set your working hours and time slots",color:"blue"},{title:"View Appointments",href:"/appointments",icon:"calendar-check",description:"See your upcoming appointments",color:"green"},{title:"Patient List",href:"/provider/patients",icon:"users",description:"Manage your patient records",color:"purple"},{title:"Earnings",href:"/provider/earnings",icon:"dollar-sign",description:"Track your earnings and payments",color:"yellow"}];return L(()=>{var o;console.log("Dashboard mounted with user:",a.user),console.log("User role:",(o=a.user)==null?void 0:o.role),console.log("isProvider:",u.value),console.log("isAdmin:",v.value),console.log("isPatient:",x.value),a.user&&(g.value=a.user.role||"admin"),u.value?(console.log("Fetching provider data..."),$()):y.value?console.log("User can view analytics, but KPI fetching is disabled"):console.log("User cannot view analytics")}),(o,t)=>(n(),l(A,null,[_(w(z),{title:"Dashboard"}),_(H,{breadcrumbs:S},{default:k(()=>[e("div",Q,[e("div",W,[e("div",G,[e("div",Y,[e("h1",J,r(E()),1),e("p",Z,r(N()),1)]),e("div",ee,[v.value?B((n(),l("select",{key:0,"onUpdate:modelValue":t[0]||(t[0]=s=>g.value=s),onChange:O,class:"role-selector"},t[2]||(t[2]=[e("option",{value:"admin"},"Admin View",-1),e("option",{value:"provider"},"Provider View",-1),e("option",{value:"patient"},"Patient View",-1),e("option",{value:"care_manager"},"Care Manager View",-1)]),544)),[[I,g.value]]):b("",!0),y.value?(n(),l("button",{key:1,onClick:t[1]||(t[1]=s=>M(!0)),class:"refresh-btn",disabled:c.value},[c.value?(n(),l("i",se)):(n(),l("i",ae)),K(" "+r(c.value?"Refreshing...":"Refresh Data"),1)],8,te)):b("",!0)])]),u.value?(n(),l("div",oe,[t[15]||(t[15]=e("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Provider Overview",-1)),e("div",re,[e("div",ne,[e("div",le,[e("div",null,[t[3]||(t[3]=e("p",{class:"text-sm text-gray-500"},"Total Patients",-1)),e("p",ie,r(i.stats.totalPatients||0),1),t[4]||(t[4]=e("p",{class:"text-xs text-gray-400"},"Active patients",-1))]),t[5]||(t[5]=e("div",{class:"text-blue-500"},[e("i",{class:"fas fa-users text-2xl"})],-1))])]),e("div",de,[e("div",ce,[e("div",null,[t[6]||(t[6]=e("p",{class:"text-sm text-gray-500"},"Upcoming Appointments",-1)),e("p",ue,r(i.stats.upcomingAppointments||0),1),t[7]||(t[7]=e("p",{class:"text-xs text-gray-400"},"Next 7 days",-1))]),t[8]||(t[8]=e("div",{class:"text-green-500"},[e("i",{class:"fas fa-calendar-check text-2xl"})],-1))])]),e("div",pe,[e("div",ge,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm text-gray-500"},"Completion Rate",-1)),e("p",me,r(i.stats.completionRate||0)+"%",1),t[10]||(t[10]=e("p",{class:"text-xs text-gray-400"},"This month",-1))]),t[11]||(t[11]=e("div",{class:"text-purple-500"},[e("i",{class:"fas fa-chart-line text-2xl"})],-1))])]),e("div",ve,[e("div",he,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm text-gray-500"},"Monthly Earnings",-1)),e("p",be,"$"+r(i.stats.monthlyEarnings||0),1),t[13]||(t[13]=e("p",{class:"text-xs text-gray-400"},"Current month",-1))]),t[14]||(t[14]=e("div",{class:"text-yellow-500"},[e("i",{class:"fas fa-dollar-sign text-2xl"})],-1))])])])])):b("",!0),v.value?(n(),l("div",fe,[t[18]||(t[18]=e("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Platform Overview",-1)),e("div",xe,[e("div",ye,[e("div",_e,[e("div",null,[t[16]||(t[16]=e("p",{class:"text-sm text-gray-500"},"Total Users",-1)),e("p",we,r(i.stats.totalUsers||0),1),e("p",ke,r(i.stats.newUsersThisMonth||0)+" new this month",1)]),t[17]||(t[17]=e("div",{class:"text-blue-500"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})])],-1))])])])])):b("",!0),i.recentAppointments.length>0?(n(),l("div",Ae,[e("div",De,[t[19]||(t[19]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Recent Appointments",-1)),e("div",Pe,[(n(!0),l(A,null,C(i.recentAppointments,s=>{var d,p,j;return n(),l("div",{key:s.id,class:"border border-gray-200 rounded-lg p-4"},[e("div",Me,[e("div",null,[e("h4",je," Dr. "+r(((p=(d=s.provider)==null?void 0:d.user)==null?void 0:p.name)||"Unknown Provider"),1),e("p",Ce,r(((j=s.provider)==null?void 0:j.specialization)||"General Practice"),1),e("p",Re,r(T(s.scheduled_at)),1)]),e("span",{class:D([q(s.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(s.status),3)])])}),128))])])])):b("",!0),u.value?(n(),l("div",Ue,[e("div",Se,[t[20]||(t[20]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1)),e("div",Ve,[(n(!0),l(A,null,C(F(),s=>(n(),X(w(R),{key:s.title,href:s.href,class:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:k(()=>[e("div",{class:D(`w-12 h-12 bg-${s.color}-100 rounded-full flex items-center justify-center mb-3`)},[e("i",{class:D(`fas fa-${s.icon} text-${s.color}-600 text-xl`)},null,2)],2),e("span",Te,r(s.title),1),e("span",qe,r(s.description),1)]),_:2},1032,["href"]))),128))])])])):(n(),l("div",Ee,[e("div",Ne,[t[22]||(t[22]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1)),e("div",Oe,[_(w(R),{href:"/chat",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:k(()=>t[21]||(t[21]=[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])],-1),e("span",{class:"text-sm font-medium text-gray-900"},"AI Chat",-1)])),_:1})])])]))])])]),_:1})],64))}};export{Ie as default};
