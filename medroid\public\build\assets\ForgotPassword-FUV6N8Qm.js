import{d as u,M as _,s as n,e as m,w as r,i as a,b as c,f as d,g as o,u as s,p as g,t as w,v as x,j as l}from"./vendor-B07q4Gx1.js";import{_ as v}from"./InputError.vue_vue_type_script_setup_true_lang-DsycJfG5.js";import{_ as y}from"./TextLink.vue_vue_type_script_setup_true_lang-riXAqVrZ.js";import{_ as k}from"./index-DUhngxR1.js";import{_ as V,a as b}from"./Label.vue_vue_type_script_setup_true_lang-BRhg_Suh.js";import{L as $,_ as C}from"./AuthLayout.vue_vue_type_script_setup_true_lang-BQzxigG1.js";import"./Primitive-B2yccHbY.js";import"./index-_OBvq0Oi.js";const B={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E={class:"space-y-6"},N={class:"grid gap-2"},F={class:"my-6 flex items-center justify-start"},h={class:"space-x-1 text-center text-sm text-muted-foreground"},q=u({__name:"ForgotPassword",props:{status:{}},setup(j){const t=_({email:""}),p=()=>{t.post(route("password.email"))};return(i,e)=>(m(),n(C,{title:"Forgot password",description:"Enter your email to receive a password reset link"},{default:r(()=>[a(s(g),{title:"Forgot password"}),i.status?(m(),c("div",B,w(i.status),1)):d("",!0),o("div",E,[o("form",{onSubmit:x(p,["prevent"])},[o("div",N,[a(s(V),{for:"email"},{default:r(()=>e[1]||(e[1]=[l("Email address")])),_:1}),a(s(b),{id:"email",type:"email",name:"email",autocomplete:"off",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=f=>s(t).email=f),autofocus:"",placeholder:"<EMAIL>"},null,8,["modelValue"]),a(v,{message:s(t).errors.email},null,8,["message"])]),o("div",F,[a(s(k),{class:"w-full",disabled:s(t).processing},{default:r(()=>[s(t).processing?(m(),n(s($),{key:0,class:"h-4 w-4 animate-spin"})):d("",!0),e[2]||(e[2]=l(" Email password reset link "))]),_:1},8,["disabled"])])],32),o("div",h,[e[4]||(e[4]=o("span",null,"Or, return to",-1)),a(y,{href:i.route("login")},{default:r(()=>e[3]||(e[3]=[l("log in")])),_:1},8,["href"])])])]),_:1}))}});export{q as default};
