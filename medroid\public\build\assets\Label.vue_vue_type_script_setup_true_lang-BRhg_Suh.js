import{Z as $,r as S,X as M,A as I,m as h,a5 as m,a6 as J,o as T,u as p,d as w,s as N,e as O,w as B,l as L,O as A,x as K,y as U,b as W,n as z,a7 as X}from"./vendor-B07q4Gx1.js";import{P as Z,a as D}from"./Primitive-B2yccHbY.js";import{d as q,i as G,e as E,w as H,f as Q,g as Y}from"./index-_OBvq0Oi.js";const R=G?window:void 0;function k(e){var t;const o=m(e);return(t=o==null?void 0:o.$el)!=null?t:o}function F(...e){const t=[],o=()=>{t.forEach(l=>l()),t.length=0},a=(l,r,i,u)=>(l.addEventListener(r,i,u),()=>l.removeEventListener(r,i,u)),n=h(()=>{const l=E(m(e[0])).filter(r=>r!=null);return l.every(r=>typeof r!="string")?l:void 0}),f=H(()=>{var l,r;return[(r=(l=n.value)==null?void 0:l.map(i=>k(i)))!=null?r:[R].filter(i=>i!=null),E(m(n.value?e[1]:e[0])),E(p(n.value?e[2]:e[1])),m(n.value?e[3]:e[2])]},([l,r,i,u])=>{if(o(),!(l!=null&&l.length)||!(r!=null&&r.length)||!(i!=null&&i.length))return;const _=Y(u)?{...u}:u;t.push(...l.flatMap(b=>r.flatMap(d=>i.map(v=>a(b,d,v,_)))))},{flush:"post"}),s=()=>{f(),o()};return Q(o),s}function ie(){const e=J(!1),t=$();return t&&T(()=>{e.value=!0},t),e}function ee(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ue(...e){let t,o,a={};e.length===3?(t=e[0],o=e[1],a=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,o=e[0],a=e[1]):(t=e[0],o=e[1]):(t=!0,o=e[0]);const{target:n=R,eventName:f="keydown",passive:s=!1,dedupe:l=!1}=a,r=ee(t);return F(n,f,u=>{u.repeat&&m(l)||r(u)&&o(u)},s)}function te(e){return JSON.parse(JSON.stringify(e))}function ne(e,t,o,a={}){var n,f,s;const{clone:l=!1,passive:r=!1,eventName:i,deep:u=!1,defaultValue:_,shouldEmit:b}=a,d=$(),v=o||(d==null?void 0:d.emit)||((n=d==null?void 0:d.$emit)==null?void 0:n.bind(d))||((s=(f=d==null?void 0:d.proxy)==null?void 0:f.$emit)==null?void 0:s.bind(d==null?void 0:d.proxy));let g=i;t||(t="modelValue"),g=g||`update:${t.toString()}`;const P=c=>l?typeof l=="function"?l(c):te(c):c,j=()=>q(e[t])?P(e[t]):_,C=c=>{b?b(c)&&v(g,c):v(g,c)};if(r){const c=j(),V=S(c);let x=!1;return M(()=>e[t],y=>{x||(x=!0,V.value=P(y),I(()=>x=!1))}),M(V,y=>{!x&&(y!==e[t]||u)&&C(y)},{deep:u}),V}else return h({get(){return j()},set(c){C(c)}})}function oe(){const e=$(),t=S(),o=h(()=>{var s,l;return["#text","#comment"].includes((s=t.value)==null?void 0:s.$el.nodeName)?(l=t.value)==null?void 0:l.$el.nextElementSibling:k(t)}),a=Object.assign({},e.exposed),n={};for(const s in e.props)Object.defineProperty(n,s,{enumerable:!0,configurable:!0,get:()=>e.props[s]});if(Object.keys(a).length>0)for(const s in a)Object.defineProperty(n,s,{enumerable:!0,configurable:!0,get:()=>a[s]});Object.defineProperty(n,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=n;function f(s){t.value=s,s&&(Object.defineProperty(n,"$el",{enumerable:!0,configurable:!0,get:()=>s instanceof Element?s:s.$el}),e.exposed=n)}return{forwardRef:f,currentRef:t,currentElement:o}}const le=w({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(e){const t=e;return oe(),(o,a)=>(O(),N(p(Z),A(t,{onMousedown:a[0]||(a[0]=n=>{!n.defaultPrevented&&n.detail>1&&n.preventDefault()})}),{default:B(()=>[L(o.$slots,"default")]),_:3},16))}}),de=w({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(e,{emit:t}){const o=e,n=ne(o,"modelValue",t,{passive:!0,defaultValue:o.defaultValue});return(f,s)=>K((O(),W("input",{"onUpdate:modelValue":s[0]||(s[0]=l=>X(n)?n.value=l:null),"data-slot":"input",class:z(p(D)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",o.class))},null,2)),[[U,p(n)]])}}),ce=w({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,o=h(()=>{const{class:a,...n}=t;return n});return(a,n)=>(O(),N(p(le),A({"data-slot":"label"},o.value,{class:p(D)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t.class)}),{default:B(()=>[L(a.$slots,"default")]),_:3},16,["class"]))}});export{ce as _,de as a,ne as b,oe as c,R as d,ie as e,F as f,ue as o,k as u};
