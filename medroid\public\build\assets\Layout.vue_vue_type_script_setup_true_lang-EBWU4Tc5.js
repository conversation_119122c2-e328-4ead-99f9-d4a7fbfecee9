import{d as c,m,s as h,e as s,w as _,l as g,O as y,u as r,a3 as w,a4 as $,b as l,g as n,f as x,t as p,G as P,i as u,F as z,q as B,P as b,j as k,n as C}from"./vendor-B07q4Gx1.js";import{_ as S}from"./index-DUhngxR1.js";import{P as N,a as O}from"./Primitive-B2yccHbY.js";import{r as V}from"./index-_OBvq0Oi.js";const I=c({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o,t=["horizontal","vertical"];function d(a){return t.includes(a)}const i=m(()=>d(e.orientation)?e.orientation:"horizontal"),f=m(()=>i.value==="vertical"?e.orientation:void 0),v=m(()=>e.decorative?{role:"none"}:{"aria-orientation":f.value,role:"separator"});return(a,K)=>(s(),h(r(N),y({as:a.as,"as-child":a.asChild,"data-orientation":i.value},v.value),{default:_(()=>[g(a.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),L=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o;return(t,d)=>(s(),h(I,w($(e)),{default:_(()=>[g(t.$slots,"default")]),_:3},16))}}),R={class:"mb-0.5 text-base font-medium"},T={key:0,class:"text-sm text-muted-foreground"},Z=c({__name:"HeadingSmall",props:{title:{},description:{}},setup(o){return(e,t)=>(s(),l("header",null,[n("h3",R,p(e.title),1),e.description?(s(),l("p",T,p(e.description),1)):x("",!0)]))}}),j={class:"mb-8 space-y-0.5"},A={class:"text-xl font-semibold tracking-tight"},E={key:0,class:"text-sm text-muted-foreground"},F=c({__name:"Heading",props:{title:{},description:{}},setup(o){return(e,t)=>(s(),l("div",j,[n("h2",A,p(e.title),1),e.description?(s(),l("p",E,p(e.description),1)):x("",!0)]))}}),H=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(o){const e=o,t=V(e,"class");return(d,i)=>(s(),h(r(L),y({"data-slot":"separator-root"},r(t),{class:r(O)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e.class)}),null,16,["class"]))}}),q={class:"px-4 py-6"},D={class:"flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-x-12 lg:space-y-0"},G={class:"w-full max-w-xl lg:w-48"},M={class:"flex flex-col space-x-0 space-y-1"},U={class:"flex-1 md:max-w-2xl"},J={class:"max-w-xl space-y-12"},ee=c({__name:"Layout",setup(o){var i;const e=[{title:"Profile",href:"/settings/profile"},{title:"Password",href:"/settings/password"},{title:"Appearance",href:"/settings/appearance"}],t=P(),d=(i=t.props.ziggy)!=null&&i.location?new URL(t.props.ziggy.location).pathname:"";return(f,v)=>(s(),l("div",q,[u(F,{title:"Settings",description:"Manage your profile and account settings"}),n("div",D,[n("aside",G,[n("nav",M,[(s(),l(z,null,B(e,a=>u(r(S),{key:a.href,variant:"ghost",class:C(["w-full justify-start",{"bg-muted":r(d)===a.href}]),"as-child":""},{default:_(()=>[u(r(b),{href:a.href},{default:_(()=>[k(p(a.title),1)]),_:2},1032,["href"])]),_:2},1032,["class"])),64))])]),u(r(H),{class:"my-6 md:hidden"}),n("div",U,[n("section",J,[g(f.$slots,"default")])])])]))}});export{ee as _,Z as a};
