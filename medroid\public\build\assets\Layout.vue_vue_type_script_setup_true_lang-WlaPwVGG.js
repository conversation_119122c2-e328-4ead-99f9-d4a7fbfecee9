import{d as c,m,s as h,e as s,w as _,l as g,O as y,u as r,a3 as w,a4 as $,b as l,g as n,f as x,t as p,H as P,i as u,F as z,q as B,P as b,j as k,n as C}from"./vendor-CGdKbVnC.js";import{_ as S}from"./index-l_ndanDW.js";import{P as N,a as O}from"./Primitive-D-1s9QYo.js";import{r as V}from"./index-CgBriE2E.js";const H=c({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o,t=["horizontal","vertical"];function d(a){return t.includes(a)}const i=m(()=>d(e.orientation)?e.orientation:"horizontal"),f=m(()=>i.value==="vertical"?e.orientation:void 0),v=m(()=>e.decorative?{role:"none"}:{"aria-orientation":f.value,role:"separator"});return(a,K)=>(s(),h(r(N),y({as:a.as,"as-child":a.asChild,"data-orientation":i.value},v.value),{default:_(()=>[g(a.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),I=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o;return(t,d)=>(s(),h(H,w($(e)),{default:_(()=>[g(t.$slots,"default")]),_:3},16))}}),L={class:"mb-0.5 text-base font-medium"},R={key:0,class:"text-sm text-muted-foreground"},Z=c({__name:"HeadingSmall",props:{title:{},description:{}},setup(o){return(e,t)=>(s(),l("header",null,[n("h3",L,p(e.title),1),e.description?(s(),l("p",R,p(e.description),1)):x("",!0)]))}}),T={class:"mb-8 space-y-0.5"},j={class:"text-xl font-semibold tracking-tight"},A={key:0,class:"text-sm text-muted-foreground"},E=c({__name:"Heading",props:{title:{},description:{}},setup(o){return(e,t)=>(s(),l("div",T,[n("h2",j,p(e.title),1),e.description?(s(),l("p",A,p(e.description),1)):x("",!0)]))}}),F=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(o){const e=o,t=V(e,"class");return(d,i)=>(s(),h(r(I),y({"data-slot":"separator-root"},r(t),{class:r(O)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e.class)}),null,16,["class"]))}}),q={class:"px-4 py-6"},D={class:"flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-x-12 lg:space-y-0"},M={class:"w-full max-w-xl lg:w-48"},U={class:"flex flex-col space-x-0 space-y-1"},G={class:"flex-1 md:max-w-2xl"},J={class:"max-w-xl space-y-12"},ee=c({__name:"Layout",setup(o){var i;const e=[{title:"Profile",href:"/settings/profile"},{title:"Password",href:"/settings/password"},{title:"Appearance",href:"/settings/appearance"}],t=P(),d=(i=t.props.ziggy)!=null&&i.location?new URL(t.props.ziggy.location).pathname:"";return(f,v)=>(s(),l("div",q,[u(E,{title:"Settings",description:"Manage your profile and account settings"}),n("div",D,[n("aside",M,[n("nav",U,[(s(),l(z,null,B(e,a=>u(r(S),{key:a.href,variant:"ghost",class:C(["w-full justify-start",{"bg-muted":r(d)===a.href}]),"as-child":""},{default:_(()=>[u(r(b),{href:a.href},{default:_(()=>[k(p(a.title),1)]),_:2},1032,["href"])]),_:2},1032,["class"])),64))])]),u(r(F),{class:"my-6 md:hidden"}),n("div",G,[n("section",J,[g(f.$slots,"default")])])])]))}});export{ee as _,Z as a};
