import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r,m as o,b as a,e as l,I as s,n as x,t as b}from"./vendor-CGdKbVnC.js";const w=["src","alt"],v={__name:"MedroidLogo",props:{size:{type:Number,default:32},color:{type:String,default:null},useDarkVersion:{type:Boolean,default:!1},showShadow:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},iconClass:{type:String,default:"fas fa-heart"},fallbackText:{type:String,default:"M"},alt:{type:String,default:"Medroid Logo"}},setup(t){const e=t,n=r(!1),i=r(!1),c=o(()=>e.useDarkVersion?"/images/medroid_icon_dark.png":"/images/medroid_icon.png"),d=o(()=>({display:"inline-block",width:`${e.size}px`,height:`${e.size}px`})),u=o(()=>({width:"100%",height:"100%",objectFit:"contain",display:"block"})),g=o(()=>({width:"100%",height:"100%",borderRadius:"50%",background:`linear-gradient(135deg, ${e.color||"#17C3B2"} 0%, #8BE9C8 100%)`,display:"flex",alignItems:"center",justifyContent:"center",boxShadow:e.showShadow?`0 ${e.size*.1}px ${e.size*.15}px rgba(23, 195, 178, 0.3)`:"none"})),y=o(()=>({fontSize:`${e.size*.5}px`,color:"white"})),f=o(()=>({fontSize:`${e.size*.6}px`,fontWeight:"bold",color:"white",fontFamily:"Arial, sans-serif"})),h=()=>{n.value=!0},m=()=>{i.value=!0,n.value=!1};return(p,k)=>(l(),a("div",{class:"medroid-logo",style:s(d.value)},[n.value?(l(),a("div",{key:1,class:"logo-fallback",style:s(g.value)},[t.showIcon?(l(),a("i",{key:0,class:x(t.iconClass),style:s(y.value)},null,6)):(l(),a("span",{key:1,class:"logo-text",style:s(f.value)},b(t.fallbackText),5))],4)):(l(),a("img",{key:0,src:c.value,alt:t.alt,style:s(u.value),onError:h,onLoad:m},null,44,w))],4))}},_=S(v,[["__scopeId","data-v-1886283a"]]);export{_ as M};
