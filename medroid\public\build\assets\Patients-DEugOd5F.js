import{_ as v}from"./AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js";import{r as p,o as k,b as i,e as o,i as h,u,p as b,w as c,g as t,t as a,F as g,q as y,s as w,f as P,P as D,j as A}from"./vendor-B07q4Gx1.js";import"./MedroidLogo-B0q18fAd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B2yccHbY.js";const M={class:"flex items-center justify-between"},B={class:"flex mt-2","aria-label":"Breadcrumb"},V={class:"inline-flex items-center space-x-1 md:space-x-3"},C={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},E={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},F={class:"py-12"},N={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},j={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},L={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},T={class:"p-6"},z={class:"flex items-center"},J={class:"ml-4"},Y={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},q={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},G={class:"p-6"},H={class:"flex items-center"},I={class:"ml-4"},R={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},S={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},$={class:"p-6"},K={class:"flex items-center"},O={class:"ml-4"},Q={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},U={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},W={class:"p-6"},X={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6 text-gray-900 dark:text-gray-100"},at={key:0,class:"text-center py-8"},rt={key:1,class:"overflow-x-auto"},ot={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},it={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"px-6 py-4 whitespace-nowrap"},lt={class:"flex items-center"},nt={class:"ml-4"},xt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ct={class:"text-sm text-gray-500 dark:text-gray-400"},gt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm text-gray-900 dark:text-gray-100"},pt={class:"text-sm text-gray-500 dark:text-gray-400"},ht={class:"px-6 py-4 whitespace-nowrap"},ut={class:"text-sm text-gray-900 dark:text-gray-100"},yt={class:"text-sm text-gray-500 dark:text-gray-400 capitalize"},_t={class:"px-6 py-4 whitespace-nowrap"},ft={class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"},vt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Mt={__name:"Patients",setup(kt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Patients",href:"/patients"}],x=p(!1),d=p([]),_=async()=>{x.value=!0;try{d.value=[{id:1,name:"John Doe",email:"<EMAIL>",phone:"+1-************",date_of_birth:"1985-06-15",gender:"male",last_appointment:"2024-05-30",total_appointments:8,status:"active"},{id:2,name:"Emily Johnson",email:"<EMAIL>",phone:"+1-************",date_of_birth:"1992-03-22",gender:"female",last_appointment:"2024-06-01",total_appointments:5,status:"active"},{id:3,name:"Robert Chen",email:"<EMAIL>",phone:"+1-************",date_of_birth:"1978-11-08",gender:"male",last_appointment:"2024-05-28",total_appointments:12,status:"active"}]}catch(l){console.error("Error fetching patients:",l)}finally{x.value=!1}},f=l=>{const e=new Date,s=new Date(l);let r=e.getFullYear()-s.getFullYear();const m=e.getMonth()-s.getMonth();return(m<0||m===0&&e.getDate()<s.getDate())&&r--,r};return k(()=>{_()}),(l,e)=>(o(),i(g,null,[h(u(b),{title:"Patient Management"}),h(v,null,{header:c(()=>[t("div",M,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Patient Management ",-1)),t("nav",B,[t("ol",V,[(o(),i(g,null,y(n,(s,r)=>t("li",{key:r,class:"inline-flex items-center"},[r<n.length-1?(o(),w(u(D),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[A(a(s.title),1)]),_:2},1032,["href"])):(o(),i("span",C,a(s.title),1)),r<n.length-1?(o(),i("svg",E,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):P("",!0)])),64))])])]),e[2]||(e[2]=t("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Patient ",-1))])]),default:c(()=>[t("div",F,[t("div",N,[t("div",j,[t("div",L,[t("div",T,[t("div",z,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-users text-2xl text-green-500"})],-1)),t("div",J,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Patients",-1)),t("p",Y,a(d.value.length),1)])])])]),t("div",q,[t("div",G,[t("div",H,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-check text-2xl text-blue-500"})],-1)),t("div",I,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Active Patients",-1)),t("p",R,a(d.value.filter(s=>s.status==="active").length),1)])])])]),t("div",S,[t("div",$,[t("div",K,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-calendar-check text-2xl text-purple-500"})],-1)),t("div",O,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Appointments",-1)),t("p",Q,a(d.value.reduce((s,r)=>s+r.total_appointments,0)),1)])])])]),t("div",U,[t("div",W,[t("div",X,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-chart-line text-2xl text-orange-500"})],-1)),t("div",Z,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Avg Appointments",-1)),t("p",tt,a(Math.round(d.value.reduce((s,r)=>s+r.total_appointments,0)/d.value.length)),1)])])])])]),t("div",et,[t("div",st,[x.value?(o(),i("div",at,e[11]||(e[11]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(o(),i("div",rt,[t("table",ot,[e[14]||(e[14]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patient "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contact "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Age/Gender "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Appointments "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Last Visit "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",it,[(o(!0),i(g,null,y(d.value,s=>(o(),i("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",dt,[t("div",lt,[e[12]||(e[12]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center"},[t("i",{class:"fas fa-user text-green-600 dark:text-green-400"})])],-1)),t("div",nt,[t("div",xt,a(s.name),1),t("div",ct," ID: "+a(s.id),1)])])]),t("td",gt,[t("div",mt,a(s.email),1),t("div",pt,a(s.phone),1)]),t("td",ht,[t("div",ut,a(f(s.date_of_birth))+" years",1),t("div",yt,a(s.gender),1)]),t("td",_t,[t("span",ft,a(s.total_appointments),1)]),t("td",vt,a(s.last_appointment),1),e[13]||(e[13]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},[t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View "),t("button",{class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"}," Edit "),t("button",{class:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"}," History ")],-1))]))),128))])])]))])])])])]),_:1})],64))}};export{Mt as default};
