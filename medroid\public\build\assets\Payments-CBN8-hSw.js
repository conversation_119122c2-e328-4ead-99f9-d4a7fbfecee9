import{_ as k}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import{r as m,o as v,b as d,e as r,i as y,u as p,p as w,w as c,g as t,t as a,F as g,q as u,n as b,f as _,s as P,P as B,j as D}from"./vendor-CGdKbVnC.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-D-1s9QYo.js";const C={class:"flex items-center justify-between"},F={class:"flex mt-2","aria-label":"Breadcrumb"},M={class:"inline-flex items-center space-x-1 md:space-x-3"},V={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},J={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},N={class:"py-12"},S={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},E={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},T={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},$={class:"p-6"},j={class:"flex items-center"},z={class:"ml-4"},A={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},L={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},R={class:"p-6"},q={class:"flex items-center"},G={class:"ml-4"},H={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},I={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},O={class:"flex items-center"},Q={class:"ml-4"},U={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},W={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6 text-gray-900 dark:text-gray-100"},at={key:0,class:"text-center py-8"},rt={key:1,class:"overflow-x-auto"},dt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},it={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ot={class:"px-6 py-4 whitespace-nowrap"},lt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},nt={class:"text-sm text-gray-500 dark:text-gray-400"},xt={class:"px-6 py-4 whitespace-nowrap"},ct={class:"text-sm text-gray-900 dark:text-gray-100"},gt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm text-gray-900 dark:text-gray-100"},yt={class:"px-6 py-4 whitespace-nowrap"},pt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ut={class:"px-6 py-4 whitespace-nowrap"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ht={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},ft={key:0,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Dt={__name:"Payments",setup(kt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Payments",href:"/payments"}],x=m(!1),i=m([]),h=async()=>{x.value=!0;try{i.value=[{id:1,patient_name:"John Doe",provider_name:"Dr. Jane Smith",appointment_id:1,amount:150,status:"paid",payment_method:"credit_card",transaction_id:"txn_123456",date:"2024-06-02"},{id:2,patient_name:"Emily Johnson",provider_name:"Dr. Michael Johnson",appointment_id:2,amount:300,status:"pending",payment_method:"stripe",transaction_id:"txn_789012",date:"2024-06-01"}]}catch(l){console.error("Error fetching payments:",l)}finally{x.value=!1}},f=l=>({paid:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",failed:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",refunded:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"})[l]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return v(()=>{h()}),(l,e)=>(r(),d(g,null,[y(p(w),{title:"Payment Management"}),y(k,null,{header:c(()=>[t("div",C,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Payment Management ",-1)),t("nav",F,[t("ol",M,[(r(),d(g,null,u(n,(s,o)=>t("li",{key:o,class:"inline-flex items-center"},[o<n.length-1?(r(),P(p(B),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[D(a(s.title),1)]),_:2},1032,["href"])):(r(),d("span",V,a(s.title),1)),o<n.length-1?(r(),d("svg",J,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):_("",!0)])),64))])])])])]),default:c(()=>[t("div",N,[t("div",S,[t("div",E,[t("div",T,[t("div",$,[t("div",j,[e[3]||(e[3]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-dollar-sign text-2xl text-green-500"})],-1)),t("div",z,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Revenue",-1)),t("p",A," $"+a(i.value.reduce((s,o)=>s+o.amount,0).toFixed(2)),1)])])])]),t("div",L,[t("div",R,[t("div",q,[e[5]||(e[5]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-check-circle text-2xl text-green-600"})],-1)),t("div",G,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Paid",-1)),t("p",H,a(i.value.filter(s=>s.status==="paid").length),1)])])])]),t("div",I,[t("div",K,[t("div",O,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-clock text-2xl text-yellow-500"})],-1)),t("div",Q,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending",-1)),t("p",U,a(i.value.filter(s=>s.status==="pending").length),1)])])])]),t("div",W,[t("div",X,[t("div",Y,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-times-circle text-2xl text-red-500"})],-1)),t("div",Z,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Failed",-1)),t("p",tt,a(i.value.filter(s=>s.status==="failed").length),1)])])])])]),t("div",et,[t("div",st,[x.value?(r(),d("div",at,e[10]||(e[10]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(r(),d("div",rt,[t("table",dt,[e[12]||(e[12]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Transaction "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patient "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Provider "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Amount "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Date "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",it,[(r(!0),d(g,null,u(i.value,s=>(r(),d("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ot,[t("div",lt,a(s.transaction_id),1),t("div",nt,a(s.payment_method),1)]),t("td",xt,[t("div",ct,a(s.patient_name),1)]),t("td",gt,[t("div",mt,a(s.provider_name),1)]),t("td",yt,[t("div",pt," $"+a(s.amount.toFixed(2)),1)]),t("td",ut,[t("span",{class:b([f(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.status),3)]),t("td",_t,a(s.date),1),t("td",ht,[e[11]||(e[11]=t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View ",-1)),s.status==="paid"?(r(),d("button",ft," Refund ")):_("",!0)])]))),128))])])]))])])])])]),_:1})],64))}};export{Dt as default};
