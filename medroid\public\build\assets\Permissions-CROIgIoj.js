import{_ as h}from"./AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js";import{r as m,o as _,b as r,e as s,i as y,u as v,p as k,w as u,g as e,F as n,q as c,t as i,f,s as w,P,j}from"./vendor-B07q4Gx1.js";import"./MedroidLogo-B0q18fAd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B2yccHbY.js";const B={class:"flex items-center justify-between"},M={class:"flex mt-2","aria-label":"Breadcrumb"},D={class:"inline-flex items-center space-x-1 md:space-x-3"},E={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},N={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},R={class:"py-12"},V={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},A={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},C={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},F={class:"p-6 text-gray-900 dark:text-gray-100"},L={key:0,class:"text-center py-4"},T={key:1,class:"space-y-3"},q={class:"font-medium"},z={class:"text-sm text-gray-500 dark:text-gray-400"},G={class:"flex space-x-2"},H={key:0,class:"text-red-600 hover:text-red-900 text-sm"},S={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},$={class:"p-6 text-gray-900 dark:text-gray-100"},I={key:0,class:"text-center py-4"},J={key:1,class:"space-y-3"},K={class:"font-medium"},O={class:"text-sm text-gray-500 dark:text-gray-400"},ee={__name:"Permissions",setup(Q){const l=[{title:"Dashboard",href:"/dashboard"},{title:"Permissions",href:"/permissions"}],o=m(!1),g=m([]),x=m([]),b=async()=>{o.value=!0;try{g.value=[{id:1,name:"admin",display_name:"Administrator",permissions_count:42},{id:2,name:"manager",display_name:"Manager",permissions_count:22},{id:3,name:"provider",display_name:"Healthcare Provider",permissions_count:12},{id:4,name:"patient",display_name:"Patient",permissions_count:8}],x.value=[{id:1,name:"view users",group:"users"},{id:2,name:"create users",group:"users"},{id:3,name:"edit users",group:"users"},{id:4,name:"delete users",group:"users"},{id:5,name:"view providers",group:"providers"},{id:6,name:"create providers",group:"providers"},{id:7,name:"view patients",group:"patients"},{id:8,name:"view appointments",group:"appointments"}]}catch(p){console.error("Error fetching data:",p)}finally{o.value=!1}};return _(()=>{b()}),(p,t)=>(s(),r(n,null,[y(v(k),{title:"Permissions Management"}),y(h,null,{header:u(()=>[e("div",B,[e("div",null,[t[1]||(t[1]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Permissions Management ",-1)),e("nav",M,[e("ol",D,[(s(),r(n,null,c(l,(a,d)=>e("li",{key:d,class:"inline-flex items-center"},[d<l.length-1?(s(),w(v(P),{key:0,href:a.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:u(()=>[j(i(a.title),1)]),_:2},1032,["href"])):(s(),r("span",E,i(a.title),1)),d<l.length-1?(s(),r("svg",N,t[0]||(t[0]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):f("",!0)])),64))])])])])]),default:u(()=>[e("div",R,[e("div",V,[e("div",A,[e("div",C,[e("div",F,[t[4]||(t[4]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium"},"Roles"),e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm"}," Add Role ")],-1)),o.value?(s(),r("div",L,t[2]||(t[2]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),r("div",T,[(s(!0),r(n,null,c(g.value,a=>(s(),r("div",{key:a.id,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"},[e("div",null,[e("h4",q,i(a.display_name),1),e("p",z,i(a.permissions_count)+" permissions ",1)]),e("div",G,[t[3]||(t[3]=e("button",{class:"text-blue-600 hover:text-blue-900 text-sm"},"Edit",-1)),a.name!=="admin"?(s(),r("button",H,"Delete")):f("",!0)])]))),128))]))])]),e("div",S,[e("div",$,[t[7]||(t[7]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium"},"Permissions"),e("button",{class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"}," Add Permission ")],-1)),o.value?(s(),r("div",I,t[5]||(t[5]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),r("div",J,[(s(!0),r(n,null,c(x.value,a=>(s(),r("div",{key:a.id,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"},[e("div",null,[e("h4",K,i(a.name),1),e("p",O," Group: "+i(a.group),1)]),t[6]||(t[6]=e("div",{class:"flex space-x-2"},[e("button",{class:"text-blue-600 hover:text-blue-900 text-sm"},"Edit"),e("button",{class:"text-red-600 hover:text-red-900 text-sm"},"Delete")],-1))]))),128))]))])])]),t[8]||(t[8]=e("div",{class:"mt-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},[e("div",{class:"p-6 text-gray-900 dark:text-gray-100"},[e("h3",{class:"text-lg font-medium mb-4"},"Role-Permission Matrix"),e("div",{class:"text-center py-8 text-gray-500 dark:text-gray-400"},[e("i",{class:"fas fa-cogs text-4xl mb-4"}),e("p",null,"Role-Permission matrix will be implemented here"),e("p",{class:"text-sm"},"This will allow you to assign permissions to roles")])])],-1))])])]),_:1})],64))}};export{ee as default};
