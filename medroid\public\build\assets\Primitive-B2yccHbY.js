import{h as j,F as Ee,d as ke,ac as Ne,O as Le,ad as je}from"./vendor-B07q4Gx1.js";function ye(e){var r,t,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(r=0;r<a;r++)e[r]&&(t=ye(e[r]))&&(o&&(o+=" "),o+=t)}else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function Ve(){for(var e,r,t=0,o="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=ye(e))&&(o&&(o+=" "),o+=r);return o}const ie="-",Fe=e=>{const r=_e(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const u=i.split(ie);return u[0]===""&&u.length!==1&&u.shift(),ve(u,r)||Oe(i)},getConflictingClassGroupIds:(i,u)=>{const m=t[i]||[];return u&&o[i]?[...m,...o[i]]:m}}},ve=(e,r)=>{var i;if(e.length===0)return r.classGroupId;const t=e[0],o=r.nextPart.get(t),a=o?ve(e.slice(1),o):void 0;if(a)return a;if(r.validators.length===0)return;const c=e.join(ie);return(i=r.validators.find(({validator:u})=>u(c)))==null?void 0:i.classGroupId},be=/^\[(.+)\]$/,Oe=e=>{if(be.test(e)){const r=be.exec(e)[1],t=r==null?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},_e=e=>{const{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(const a in t)se(t[a],o,a,r);return o},se=(e,r,t,o)=>{e.forEach(a=>{if(typeof a=="string"){const c=a===""?r:he(r,a);c.classGroupId=t;return}if(typeof a=="function"){if(Be(a)){se(a(o),r,t,o);return}r.validators.push({validator:a,classGroupId:t});return}Object.entries(a).forEach(([c,i])=>{se(i,he(r,c),t,o)})})},he=(e,r)=>{let t=e;return r.split(ie).forEach(o=>{t.nextPart.has(o)||t.nextPart.set(o,{nextPart:new Map,validators:[]}),t=t.nextPart.get(o)}),t},Be=e=>e.isThemeGetter,$e=e=>{if(e<1)return{get:()=>{},set:()=>{}};let r=0,t=new Map,o=new Map;const a=(c,i)=>{t.set(c,i),r++,r>e&&(r=0,o=t,t=new Map)};return{get(c){let i=t.get(c);if(i!==void 0)return i;if((i=o.get(c))!==void 0)return a(c,i),i},set(c,i){t.has(c)?t.set(c,i):a(c,i)}}},ne="!",ae=":",We=ae.length,Ue=e=>{const{prefix:r,experimentalParseClassName:t}=e;let o=a=>{const c=[];let i=0,u=0,m=0,g;for(let k=0;k<a.length;k++){let y=a[k];if(i===0&&u===0){if(y===ae){c.push(a.slice(m,k)),m=k+We;continue}if(y==="/"){g=k;continue}}y==="["?i++:y==="]"?i--:y==="("?u++:y===")"&&u--}const f=c.length===0?a:a.substring(m),S=qe(f),O=S!==f,_=g&&g>m?g-m:void 0;return{modifiers:c,hasImportantModifier:O,baseClassName:S,maybePostfixModifierPosition:_}};if(r){const a=r+ae,c=o;o=i=>i.startsWith(a)?c(i.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(t){const a=o;o=c=>t({className:c,parseClassName:a})}return o},qe=e=>e.endsWith(ne)?e.substring(0,e.length-1):e.startsWith(ne)?e.substring(1):e,He=e=>{const r=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const a=[];let c=[];return o.forEach(i=>{i[0]==="["||r[i]?(a.push(...c.sort(),i),c=[]):c.push(i)}),a.push(...c.sort()),a}},Je=e=>({cache:$e(e.cacheSize),parseClassName:Ue(e),sortModifiers:He(e),...Fe(e)}),Ke=/\s+/,Xe=(e,r)=>{const{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:c}=r,i=[],u=e.trim().split(Ke);let m="";for(let g=u.length-1;g>=0;g-=1){const f=u[g],{isExternal:S,modifiers:O,hasImportantModifier:_,baseClassName:k,maybePostfixModifierPosition:y}=t(f);if(S){m=f+(m.length>0?" "+m:m);continue}let G=!!y,M=o(G?k.substring(0,y):k);if(!M){if(!G){m=f+(m.length>0?" "+m:m);continue}if(M=o(k),!M){m=f+(m.length>0?" "+m:m);continue}G=!1}const U=c(O).join(":"),B=_?U+ne:U,T=B+M;if(i.includes(T))continue;i.push(T);const E=a(M,G);for(let I=0;I<E.length;++I){const $=E[I];i.push(B+$)}m=f+(m.length>0?" "+m:m)}return m};function Ze(){let e=0,r,t,o="";for(;e<arguments.length;)(r=arguments[e++])&&(t=ze(r))&&(o&&(o+=" "),o+=t);return o}const ze=e=>{if(typeof e=="string")return e;let r,t="";for(let o=0;o<e.length;o++)e[o]&&(r=ze(e[o]))&&(t&&(t+=" "),t+=r);return t};function De(e,...r){let t,o,a,c=i;function i(m){const g=r.reduce((f,S)=>S(f),e());return t=Je(g),o=t.cache.get,a=t.cache.set,c=u,u(m)}function u(m){const g=o(m);if(g)return g;const f=Xe(m,t);return a(m,f),f}return function(){return c(Ze.apply(null,arguments))}}const b=e=>{const r=t=>t[e]||[];return r.isThemeGetter=!0,r},Ce=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Se=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Qe=/^\d+\/\d+$/,Ye=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,eo=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,oo=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ro=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,to=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=e=>Qe.test(e),p=e=>!!e&&!Number.isNaN(Number(e)),A=e=>!!e&&Number.isInteger(Number(e)),re=e=>e.endsWith("%")&&p(e.slice(0,-1)),C=e=>Ye.test(e),so=()=>!0,no=e=>eo.test(e)&&!oo.test(e),Ae=()=>!1,ao=e=>ro.test(e),io=e=>to.test(e),lo=e=>!s(e)&&!n(e),co=e=>V(e,Pe,Ae),s=e=>Ce.test(e),R=e=>V(e,Re,no),te=e=>V(e,go,p),we=e=>V(e,Me,Ae),mo=e=>V(e,Ie,io),Z=e=>V(e,Ge,ao),n=e=>Se.test(e),W=e=>F(e,Re),po=e=>F(e,bo),xe=e=>F(e,Me),uo=e=>F(e,Pe),fo=e=>F(e,Ie),D=e=>F(e,Ge,!0),V=(e,r,t)=>{const o=Ce.exec(e);return o?o[1]?r(o[1]):t(o[2]):!1},F=(e,r,t=!1)=>{const o=Se.exec(e);return o?o[1]?r(o[1]):t:!1},Me=e=>e==="position"||e==="percentage",Ie=e=>e==="image"||e==="url",Pe=e=>e==="length"||e==="size"||e==="bg-size",Re=e=>e==="length",go=e=>e==="number",bo=e=>e==="family-name",Ge=e=>e==="shadow",ho=()=>{const e=b("color"),r=b("font"),t=b("text"),o=b("font-weight"),a=b("tracking"),c=b("leading"),i=b("breakpoint"),u=b("container"),m=b("spacing"),g=b("radius"),f=b("shadow"),S=b("inset-shadow"),O=b("text-shadow"),_=b("drop-shadow"),k=b("blur"),y=b("perspective"),G=b("aspect"),M=b("ease"),U=b("animate"),B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],T=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...T(),n,s],I=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],d=()=>[n,s,m],v=()=>[L,"full","auto",...d()],le=()=>[A,"none","subgrid",n,s],ce=()=>["auto",{span:["full",A,n,s]},A,n,s],q=()=>[A,"auto",n,s],de=()=>["auto","min","max","fr",n,s],Y=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...d()],P=()=>[L,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...d()],l=()=>[e,n,s],me=()=>[...T(),xe,we,{position:[n,s]}],pe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ue=()=>["auto","cover","contain",uo,co,{size:[n,s]}],ee=()=>[re,W,R],w=()=>["","none","full",g,n,s],x=()=>["",p,W,R],H=()=>["solid","dashed","dotted","double"],fe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],h=()=>[p,re,xe,we],ge=()=>["","none",k,n,s],J=()=>["none",p,n,s],K=()=>["none",p,n,s],oe=()=>[p,n,s],X=()=>[L,"full",...d()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[so],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[lo],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",p],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",L,s,n,G]}],container:["container"],columns:[{columns:[p,s,n,u]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:v()}],"inset-x":[{"inset-x":v()}],"inset-y":[{"inset-y":v()}],start:[{start:v()}],end:[{end:v()}],top:[{top:v()}],right:[{right:v()}],bottom:[{bottom:v()}],left:[{left:v()}],visibility:["visible","invisible","collapse"],z:[{z:[A,"auto",n,s]}],basis:[{basis:[L,"full","auto",u,...d()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[p,L,"auto","initial","none",s]}],grow:[{grow:["",p,n,s]}],shrink:[{shrink:["",p,n,s]}],order:[{order:[A,"first","last","none",n,s]}],"grid-cols":[{"grid-cols":le()}],"col-start-end":[{col:ce()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":le()}],"row-start-end":[{row:ce()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":de()}],"auto-rows":[{"auto-rows":de()}],gap:[{gap:d()}],"gap-x":[{"gap-x":d()}],"gap-y":[{"gap-y":d()}],"justify-content":[{justify:[...Y(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...Y()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":Y()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:d()}],px:[{px:d()}],py:[{py:d()}],ps:[{ps:d()}],pe:[{pe:d()}],pt:[{pt:d()}],pr:[{pr:d()}],pb:[{pb:d()}],pl:[{pl:d()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":d()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":d()}],"space-y-reverse":["space-y-reverse"],size:[{size:P()}],w:[{w:[u,"screen",...P()]}],"min-w":[{"min-w":[u,"screen","none",...P()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[i]},...P()]}],h:[{h:["screen",...P()]}],"min-h":[{"min-h":["screen","none",...P()]}],"max-h":[{"max-h":["screen",...P()]}],"font-size":[{text:["base",t,W,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,n,te]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",re,s]}],"font-family":[{font:[po,s,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,n,s]}],"line-clamp":[{"line-clamp":[p,"none",n,te]}],leading:[{leading:[c,...d()]}],"list-image":[{"list-image":["none",n,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",n,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:l()}],"text-color":[{text:l()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:[p,"from-font","auto",n,R]}],"text-decoration-color":[{decoration:l()}],"underline-offset":[{"underline-offset":[p,"auto",n,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:d()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",n,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",n,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:me()}],"bg-repeat":[{bg:pe()}],"bg-size":[{bg:ue()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},A,n,s],radial:["",n,s],conic:[A,n,s]},fo,mo]}],"bg-color":[{bg:l()}],"gradient-from-pos":[{from:ee()}],"gradient-via-pos":[{via:ee()}],"gradient-to-pos":[{to:ee()}],"gradient-from":[{from:l()}],"gradient-via":[{via:l()}],"gradient-to":[{to:l()}],rounded:[{rounded:w()}],"rounded-s":[{"rounded-s":w()}],"rounded-e":[{"rounded-e":w()}],"rounded-t":[{"rounded-t":w()}],"rounded-r":[{"rounded-r":w()}],"rounded-b":[{"rounded-b":w()}],"rounded-l":[{"rounded-l":w()}],"rounded-ss":[{"rounded-ss":w()}],"rounded-se":[{"rounded-se":w()}],"rounded-ee":[{"rounded-ee":w()}],"rounded-es":[{"rounded-es":w()}],"rounded-tl":[{"rounded-tl":w()}],"rounded-tr":[{"rounded-tr":w()}],"rounded-br":[{"rounded-br":w()}],"rounded-bl":[{"rounded-bl":w()}],"border-w":[{border:x()}],"border-w-x":[{"border-x":x()}],"border-w-y":[{"border-y":x()}],"border-w-s":[{"border-s":x()}],"border-w-e":[{"border-e":x()}],"border-w-t":[{"border-t":x()}],"border-w-r":[{"border-r":x()}],"border-w-b":[{"border-b":x()}],"border-w-l":[{"border-l":x()}],"divide-x":[{"divide-x":x()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":x()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...H(),"hidden","none"]}],"divide-style":[{divide:[...H(),"hidden","none"]}],"border-color":[{border:l()}],"border-color-x":[{"border-x":l()}],"border-color-y":[{"border-y":l()}],"border-color-s":[{"border-s":l()}],"border-color-e":[{"border-e":l()}],"border-color-t":[{"border-t":l()}],"border-color-r":[{"border-r":l()}],"border-color-b":[{"border-b":l()}],"border-color-l":[{"border-l":l()}],"divide-color":[{divide:l()}],"outline-style":[{outline:[...H(),"none","hidden"]}],"outline-offset":[{"outline-offset":[p,n,s]}],"outline-w":[{outline:["",p,W,R]}],"outline-color":[{outline:l()}],shadow:[{shadow:["","none",f,D,Z]}],"shadow-color":[{shadow:l()}],"inset-shadow":[{"inset-shadow":["none",S,D,Z]}],"inset-shadow-color":[{"inset-shadow":l()}],"ring-w":[{ring:x()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:l()}],"ring-offset-w":[{"ring-offset":[p,R]}],"ring-offset-color":[{"ring-offset":l()}],"inset-ring-w":[{"inset-ring":x()}],"inset-ring-color":[{"inset-ring":l()}],"text-shadow":[{"text-shadow":["none",O,D,Z]}],"text-shadow-color":[{"text-shadow":l()}],opacity:[{opacity:[p,n,s]}],"mix-blend":[{"mix-blend":[...fe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":fe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[p]}],"mask-image-linear-from-pos":[{"mask-linear-from":h()}],"mask-image-linear-to-pos":[{"mask-linear-to":h()}],"mask-image-linear-from-color":[{"mask-linear-from":l()}],"mask-image-linear-to-color":[{"mask-linear-to":l()}],"mask-image-t-from-pos":[{"mask-t-from":h()}],"mask-image-t-to-pos":[{"mask-t-to":h()}],"mask-image-t-from-color":[{"mask-t-from":l()}],"mask-image-t-to-color":[{"mask-t-to":l()}],"mask-image-r-from-pos":[{"mask-r-from":h()}],"mask-image-r-to-pos":[{"mask-r-to":h()}],"mask-image-r-from-color":[{"mask-r-from":l()}],"mask-image-r-to-color":[{"mask-r-to":l()}],"mask-image-b-from-pos":[{"mask-b-from":h()}],"mask-image-b-to-pos":[{"mask-b-to":h()}],"mask-image-b-from-color":[{"mask-b-from":l()}],"mask-image-b-to-color":[{"mask-b-to":l()}],"mask-image-l-from-pos":[{"mask-l-from":h()}],"mask-image-l-to-pos":[{"mask-l-to":h()}],"mask-image-l-from-color":[{"mask-l-from":l()}],"mask-image-l-to-color":[{"mask-l-to":l()}],"mask-image-x-from-pos":[{"mask-x-from":h()}],"mask-image-x-to-pos":[{"mask-x-to":h()}],"mask-image-x-from-color":[{"mask-x-from":l()}],"mask-image-x-to-color":[{"mask-x-to":l()}],"mask-image-y-from-pos":[{"mask-y-from":h()}],"mask-image-y-to-pos":[{"mask-y-to":h()}],"mask-image-y-from-color":[{"mask-y-from":l()}],"mask-image-y-to-color":[{"mask-y-to":l()}],"mask-image-radial":[{"mask-radial":[n,s]}],"mask-image-radial-from-pos":[{"mask-radial-from":h()}],"mask-image-radial-to-pos":[{"mask-radial-to":h()}],"mask-image-radial-from-color":[{"mask-radial-from":l()}],"mask-image-radial-to-color":[{"mask-radial-to":l()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":T()}],"mask-image-conic-pos":[{"mask-conic":[p]}],"mask-image-conic-from-pos":[{"mask-conic-from":h()}],"mask-image-conic-to-pos":[{"mask-conic-to":h()}],"mask-image-conic-from-color":[{"mask-conic-from":l()}],"mask-image-conic-to-color":[{"mask-conic-to":l()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:me()}],"mask-repeat":[{mask:pe()}],"mask-size":[{mask:ue()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",n,s]}],filter:[{filter:["","none",n,s]}],blur:[{blur:ge()}],brightness:[{brightness:[p,n,s]}],contrast:[{contrast:[p,n,s]}],"drop-shadow":[{"drop-shadow":["","none",_,D,Z]}],"drop-shadow-color":[{"drop-shadow":l()}],grayscale:[{grayscale:["",p,n,s]}],"hue-rotate":[{"hue-rotate":[p,n,s]}],invert:[{invert:["",p,n,s]}],saturate:[{saturate:[p,n,s]}],sepia:[{sepia:["",p,n,s]}],"backdrop-filter":[{"backdrop-filter":["","none",n,s]}],"backdrop-blur":[{"backdrop-blur":ge()}],"backdrop-brightness":[{"backdrop-brightness":[p,n,s]}],"backdrop-contrast":[{"backdrop-contrast":[p,n,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",p,n,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[p,n,s]}],"backdrop-invert":[{"backdrop-invert":["",p,n,s]}],"backdrop-opacity":[{"backdrop-opacity":[p,n,s]}],"backdrop-saturate":[{"backdrop-saturate":[p,n,s]}],"backdrop-sepia":[{"backdrop-sepia":["",p,n,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":d()}],"border-spacing-x":[{"border-spacing-x":d()}],"border-spacing-y":[{"border-spacing-y":d()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",n,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[p,"initial",n,s]}],ease:[{ease:["linear","initial",M,n,s]}],delay:[{delay:[p,n,s]}],animate:[{animate:["none",U,n,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,n,s]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:J()}],"rotate-x":[{"rotate-x":J()}],"rotate-y":[{"rotate-y":J()}],"rotate-z":[{"rotate-z":J()}],scale:[{scale:K()}],"scale-x":[{"scale-x":K()}],"scale-y":[{"scale-y":K()}],"scale-z":[{"scale-z":K()}],"scale-3d":["scale-3d"],skew:[{skew:oe()}],"skew-x":[{"skew-x":oe()}],"skew-y":[{"skew-y":oe()}],transform:[{transform:[n,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:X()}],"translate-x":[{"translate-x":X()}],"translate-y":[{"translate-y":X()}],"translate-z":[{"translate-z":X()}],"translate-none":["translate-none"],accent:[{accent:l()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:l()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",n,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":d()}],"scroll-mx":[{"scroll-mx":d()}],"scroll-my":[{"scroll-my":d()}],"scroll-ms":[{"scroll-ms":d()}],"scroll-me":[{"scroll-me":d()}],"scroll-mt":[{"scroll-mt":d()}],"scroll-mr":[{"scroll-mr":d()}],"scroll-mb":[{"scroll-mb":d()}],"scroll-ml":[{"scroll-ml":d()}],"scroll-p":[{"scroll-p":d()}],"scroll-px":[{"scroll-px":d()}],"scroll-py":[{"scroll-py":d()}],"scroll-ps":[{"scroll-ps":d()}],"scroll-pe":[{"scroll-pe":d()}],"scroll-pt":[{"scroll-pt":d()}],"scroll-pr":[{"scroll-pr":d()}],"scroll-pb":[{"scroll-pb":d()}],"scroll-pl":[{"scroll-pl":d()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",n,s]}],fill:[{fill:["none",...l()]}],"stroke-w":[{stroke:[p,W,R,te]}],stroke:[{stroke:["none",...l()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},wo=De(ho);function Co(...e){return wo(Ve(e))}/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Q={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ko=({size:e,strokeWidth:r=2,absoluteStrokeWidth:t,color:o,iconNode:a,name:c,class:i,...u},{slots:m})=>j("svg",{...Q,width:e||Q.width,height:e||Q.height,stroke:o||Q.stroke,"stroke-width":t?Number(r)*24/Number(e):r,class:["lucide",`lucide-${xo(c??"icon")}`],...u},[...a.map(g=>j(...g)),...m.default?[m.default()]:[]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const So=(e,r)=>(t,{slots:o})=>j(ko,{...t,iconNode:r,name:e},o);function Te(e){return e?e.flatMap(r=>r.type===Ee?Te(r.children):[r]):[]}const yo=ke({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:r,slots:t}){return()=>{var m,g;if(!t.default)return null;const o=Te(t.default()),a=o.findIndex(f=>f.type!==Ne);if(a===-1)return o;const c=o[a];(m=c.props)==null||delete m.ref;const i=c.props?Le(r,c.props):r;r.class&&((g=c.props)!=null&&g.class)&&delete c.props.class;const u=je(c,i);for(const f in i)f.startsWith("on")&&(u.props||(u.props={}),u.props[f]=i[f]);return o.length===1?u:(o[a]=u,o)}}}),vo=["area","img","input"],Ao=ke({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:r,slots:t}){const o=e.asChild?"template":e.as;return typeof o=="string"&&vo.includes(o)?()=>j(o,r):o!=="template"?()=>j(e.as,r,{default:t.default}):()=>j(yo,r,{default:t.default})}});export{Ao as P,Co as a,Ve as b,So as c,Te as r};
