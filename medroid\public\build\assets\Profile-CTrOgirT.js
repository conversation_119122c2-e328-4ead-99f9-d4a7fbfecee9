import{r as u,o as M,b as n,e as i,i as k,u as N,p as B,w as D,g as e,f as C,t as c,v as T,x as o,y as r,F as x,q as V,j as P,J as U,a as z}from"./vendor-CGdKbVnC.js";import{_ as $}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-D-1s9QYo.js";const j={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},I={key:0,class:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4"},K={class:"flex"},q={class:"text-red-700"},Y={key:1,class:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4"},J={class:"flex"},Z={class:"text-green-700"},G={key:2,class:"text-center py-12"},H={key:3,class:"bg-white rounded-lg shadow-sm border"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={class:"mt-4"},R={class:"mt-4"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},X={class:"flex flex-wrap gap-2 mb-4"},h=["onClick"],ee={class:"flex space-x-2"},te={class:"space-y-2 mb-4"},le={class:"text-sm"},se=["onClick"],oe={class:"flex space-x-2"},re={class:"flex justify-end pt-6 border-t"},ae=["disabled"],ne={key:0,class:"fas fa-spinner fa-spin mr-2"},ie={key:1,class:"fas fa-save mr-2"},me={__name:"Profile",setup(ue){const L=[{title:"Dashboard",href:"/dashboard"},{title:"Provider Profile",href:"/provider/profile"}],v=u(!1),f=u(!1),b=u(null),g=u(""),l=u({specialization:"",bio:"",education:"",years_of_experience:0,license_number:"",phone:"",address:"",city:"",state:"",zip_code:"",consultation_fee:0,languages:[],certifications:[]}),d=u(""),p=u(""),_=async()=>{v.value=!0;try{const a=await z.get("/api/provider/profile");a.data.provider&&(l.value={...l.value,...a.data.provider})}catch(a){console.error("Error fetching profile:",a),b.value="Failed to load profile data"}finally{v.value=!1}},S=async()=>{f.value=!0,b.value=null,g.value="";try{await z.post("/api/provider/profile",l.value),g.value="Profile updated successfully!",setTimeout(()=>{g.value=""},3e3)}catch(a){console.error("Error saving profile:",a),b.value="Failed to save profile. Please try again."}finally{f.value=!1}},y=()=>{d.value.trim()&&!l.value.languages.includes(d.value.trim())&&(l.value.languages.push(d.value.trim()),d.value="")},A=a=>{l.value.languages.splice(a,1)},w=()=>{p.value.trim()&&!l.value.certifications.includes(p.value.trim())&&(l.value.certifications.push(p.value.trim()),p.value="")},E=a=>{l.value.certifications.splice(a,1)};return M(()=>{_()}),(a,t)=>(i(),n(x,null,[k(N(B),{title:"Provider Profile"}),k($,{breadcrumbs:L},{default:D(()=>[e("div",j,[t[33]||(t[33]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-bold text-gray-900"},"Provider Profile"),e("p",{class:"mt-2 text-gray-600"},"Manage your professional information and credentials")],-1)),b.value?(i(),n("div",I,[e("div",K,[t[13]||(t[13]=e("i",{class:"fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"},null,-1)),e("p",q,c(b.value),1)])])):C("",!0),g.value?(i(),n("div",Y,[e("div",J,[t[14]||(t[14]=e("i",{class:"fas fa-check-circle text-green-400 mr-3 mt-0.5"},null,-1)),e("p",Z,c(g.value),1)])])):C("",!0),v.value?(i(),n("div",G,t[15]||(t[15]=[e("i",{class:"fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"},null,-1),e("p",{class:"text-gray-600"},"Loading profile...",-1)]))):(i(),n("div",H,[e("form",{onSubmit:T(S,["prevent"]),class:"p-6 space-y-6"},[e("div",null,[t[22]||(t[22]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Professional Information",-1)),e("div",O,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Specialization",-1)),o(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>l.value.specialization=s),type:"text",placeholder:"e.g., Cardiology, Dermatology",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.specialization]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Years of Experience",-1)),o(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>l.value.years_of_experience=s),type:"number",min:"0",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.years_of_experience,void 0,{number:!0}]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"License Number",-1)),o(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>l.value.license_number=s),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.license_number]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Consultation Fee ($)",-1)),o(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>l.value.consultation_fee=s),type:"number",min:"0",step:"0.01",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.consultation_fee,void 0,{number:!0}]])])]),e("div",Q,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Education",-1)),o(e("textarea",{"onUpdate:modelValue":t[4]||(t[4]=s=>l.value.education=s),rows:"3",placeholder:"Your educational background and qualifications",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.education]])]),e("div",R,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Bio",-1)),o(e("textarea",{"onUpdate:modelValue":t[5]||(t[5]=s=>l.value.bio=s),rows:"4",placeholder:"Tell patients about yourself and your practice",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.bio]])])]),e("div",null,[t[28]||(t[28]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Contact Information",-1)),e("div",W,[e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone",-1)),o(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>l.value.phone=s),type:"tel",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.phone]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Address",-1)),o(e("input",{"onUpdate:modelValue":t[7]||(t[7]=s=>l.value.address=s),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.address]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"City",-1)),o(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>l.value.city=s),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.city]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"State",-1)),o(e("input",{"onUpdate:modelValue":t[9]||(t[9]=s=>l.value.state=s),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.state]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"ZIP Code",-1)),o(e("input",{"onUpdate:modelValue":t[10]||(t[10]=s=>l.value.zip_code=s),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[r,l.value.zip_code]])])])]),e("div",null,[t[30]||(t[30]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Languages",-1)),e("div",X,[(i(!0),n(x,null,V(l.value.languages,(s,m)=>(i(),n("span",{key:m,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[P(c(s)+" ",1),e("button",{onClick:F=>A(m),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},t[29]||(t[29]=[e("i",{class:"fas fa-times text-xs"},null,-1)]),8,h)]))),128))]),e("div",ee,[o(e("input",{"onUpdate:modelValue":t[11]||(t[11]=s=>d.value=s),onKeyup:U(y,["enter"]),type:"text",placeholder:"Add a language",class:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[r,d.value]]),e("button",{onClick:y,type:"button",class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"}," Add ")])]),e("div",null,[t[32]||(t[32]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Certifications",-1)),e("div",te,[(i(!0),n(x,null,V(l.value.certifications,(s,m)=>(i(),n("div",{key:m,class:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},[e("span",le,c(s),1),e("button",{onClick:F=>E(m),type:"button",class:"text-red-600 hover:text-red-800"},t[31]||(t[31]=[e("i",{class:"fas fa-trash text-sm"},null,-1)]),8,se)]))),128))]),e("div",oe,[o(e("input",{"onUpdate:modelValue":t[12]||(t[12]=s=>p.value=s),onKeyup:U(w,["enter"]),type:"text",placeholder:"Add a certification",class:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[r,p.value]]),e("button",{onClick:w,type:"button",class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"}," Add ")])]),e("div",re,[e("button",{type:"submit",disabled:f.value,class:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},[f.value?(i(),n("i",ne)):(i(),n("i",ie)),P(" "+c(f.value?"Saving...":"Save Profile"),1)],8,ae)])],32)]))])]),_:1})],64))}};export{me as default};
