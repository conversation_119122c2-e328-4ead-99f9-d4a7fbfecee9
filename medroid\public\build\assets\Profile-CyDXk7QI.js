import{S as xe,U as Le,V as ce,r as P,X as W,A as R,E as ye,m as B,d as _,Y as ge,Z as ue,h as Ne,_ as Re,$ as he,a0 as Ue,l as E,u as a,o as _e,s as w,e as g,w as p,O as $,f as Z,a1 as je,a2 as U,B as be,H as Ke,i as y,a3 as We,a4 as Ve,g as A,b as V,n as Ee,M as we,j as S,G as ze,p as He,v as qe,P as Ye,T as Xe,x as Ge,R as Ze}from"./vendor-B07q4Gx1.js";import{a as Oe,_ as Je}from"./Layout.vue_vue_type_script_setup_true_lang-EBWU4Tc5.js";import{_ as ne}from"./InputError.vue_vue_type_script_setup_true_lang-DsycJfG5.js";import{_ as G}from"./index-DUhngxR1.js";import{d as Qe,u as De,b as et,c as F,e as tt,o as st,f as ot,_ as ae,a as re}from"./Label.vue_vue_type_script_setup_true_lang-BRhg_Suh.js";import{c as nt,r as at,P as L,a as j}from"./Primitive-B2yccHbY.js";import{i as z,c as rt,a as it,t as lt,b as fe}from"./index-_OBvq0Oi.js";import{_ as ut}from"./AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js";import"./MedroidLogo-B0q18fAd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dt=nt("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function Ce(s,e){const t=typeof s=="string"?`${s}Context`:e,o=Symbol(t);return[r=>{const l=xe(o,r);if(l||l===null)return l;throw new Error(`Injection \`${o.toString()}\` not found. Component must be used within ${Array.isArray(s)?`one of the following components: ${s.join(", ")}`:`\`${s}\``}`)},r=>(Le(o,r),r)]}const[Pe,As]=Ce("ConfigProvider");let ct=0;function ie(s,e="reka"){const t=Pe({useId:void 0});return ce?`${e}-${ce()}`:t.useId?`${e}-${t.useId()}`:`${e}-${++ct}`}function ft(s,e){const t=P(s);function o(i){return e[t.value][i]??t.value}return{state:t,dispatch:i=>{t.value=o(i)}}}function pt(s,e){var b;const t=P({}),o=P("none"),n=P(s),i=s.value?"mounted":"unmounted";let r;const l=((b=e.value)==null?void 0:b.ownerDocument.defaultView)??Qe,{state:c,dispatch:f}=ft(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),d=h=>{var C;if(z){const K=new CustomEvent(h,{bubbles:!1,cancelable:!1});(C=e.value)==null||C.dispatchEvent(K)}};W(s,async(h,C)=>{var H;const K=C!==h;if(await R(),K){const Q=o.value,x=q(e.value);h?(f("MOUNT"),d("enter"),x==="none"&&d("after-enter")):x==="none"||x==="undefined"||((H=t.value)==null?void 0:H.display)==="none"?(f("UNMOUNT"),d("leave"),d("after-leave")):C&&Q!==x?(f("ANIMATION_OUT"),d("leave")):(f("UNMOUNT"),d("after-leave"))}},{immediate:!0});const u=h=>{const C=q(e.value),K=C.includes(h.animationName),H=c.value==="mounted"?"enter":"leave";if(h.target===e.value&&K&&(d(`after-${H}`),f("ANIMATION_END"),!n.value)){const Q=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",r=l==null?void 0:l.setTimeout(()=>{var x;((x=e.value)==null?void 0:x.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=Q)})}h.target===e.value&&C==="none"&&f("ANIMATION_END")},v=h=>{h.target===e.value&&(o.value=q(e.value))},D=W(e,(h,C)=>{h?(t.value=getComputedStyle(h),h.addEventListener("animationstart",v),h.addEventListener("animationcancel",u),h.addEventListener("animationend",u)):(f("ANIMATION_END"),r!==void 0&&(l==null||l.clearTimeout(r)),C==null||C.removeEventListener("animationstart",v),C==null||C.removeEventListener("animationcancel",u),C==null||C.removeEventListener("animationend",u))},{immediate:!0}),m=W(c,()=>{const h=q(e.value);o.value=c.value==="mounted"?h:"none"});return ye(()=>{D(),m()}),{isPresent:B(()=>["mounted","unmountSuspended"].includes(c.value))}}function q(s){return s&&getComputedStyle(s).animationName||"none"}const $e=_({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(s,{slots:e,expose:t}){var f;const{present:o,forceMount:n}=ge(s),i=P(),{isPresent:r}=pt(o,i);t({present:r});let l=e.default({present:r.value});l=at(l||[]);const c=ue();if(l&&(l==null?void 0:l.length)>1){const d=(f=c==null?void 0:c.parent)!=null&&f.type.name?`<${c.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${d}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(u=>`  - ${u}`).join(`
`)].join(`
`))}return()=>n.value||o.value||r.value?Ne(e.default({present:r.value})[0],{ref:d=>{const u=De(d);return typeof(u==null?void 0:u.hasAttribute)>"u"||(u!=null&&u.hasAttribute("data-reka-popper-content-wrapper")?i.value=u.firstElementChild:i.value=u),u}}):null}});function J(s){const e=ue(),t=e==null?void 0:e.type.emits,o={};return t!=null&&t.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),t==null||t.forEach(n=>{o[Re(he(n))]=(...i)=>s(n,...i)}),o}function de(s){const e=ue(),t=Object.keys((e==null?void 0:e.type.props)??{}).reduce((n,i)=>{const r=(e==null?void 0:e.type.props[i]).default;return r!==void 0&&(n[i]=r),n},{}),o=Ue(s);return B(()=>{const n={},i=(e==null?void 0:e.vnode.props)??{};return Object.keys(i).forEach(r=>{n[he(r)]=i[r]}),Object.keys({...t,...n}).reduce((r,l)=>(o.value[l]!==void 0&&(r[l]=o.value[l]),r),{})})}function Ae(s,e){const t=de(s),o=e?J(e):{};return B(()=>({...t.value,...o}))}const[k,mt]=Ce("DialogRoot"),vt=_({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(s,{emit:e}){const t=s,n=et(t,"open",e,{defaultValue:t.defaultOpen,passive:t.open===void 0}),i=P(),r=P(),{modal:l}=ge(t);return mt({open:n,modal:l,openModal:()=>{n.value=!0},onOpenChange:c=>{n.value=c},onOpenToggle:()=>{n.value=!n.value},contentId:"",titleId:"",descriptionId:"",triggerElement:i,contentElement:r}),(c,f)=>E(c.$slots,"default",{open:a(n)})}}),yt=_({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(s){const e=s,t=k(),{forwardRef:o,currentElement:n}=F();return t.contentId||(t.contentId=ie(void 0,"reka-dialog-content")),_e(()=>{t.triggerElement.value=n.value}),(i,r)=>(g(),w(a(L),$(e,{ref:a(o),type:i.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":a(t).open.value||!1,"aria-controls":a(t).open.value?a(t).contentId:void 0,"data-state":a(t).open.value?"open":"closed",onClick:a(t).onOpenToggle}),{default:p(()=>[E(i.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),gt=_({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(s){const e=tt();return(t,o)=>a(e)||t.forceMount?(g(),w(je,{key:0,to:t.to,disabled:t.disabled,defer:t.defer},[E(t.$slots,"default")],8,["to","disabled","defer"])):Z("",!0)}});function Be(s,e,t){const o=t.originalEvent.target,n=new CustomEvent(s,{bubbles:!1,cancelable:!0,detail:t});e&&o.addEventListener(s,e,{once:!0}),o.dispatchEvent(n)}const ht="dismissableLayer.pointerDownOutside",_t="dismissableLayer.focusOutside";function Fe(s,e){const t=e.closest("[data-dismissable-layer]"),o=s.dataset.dismissableLayer===""?s:s.querySelector("[data-dismissable-layer]"),n=Array.from(s.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(t&&o===t||n.indexOf(o)<n.indexOf(t))}function bt(s,e){var i;const t=((i=e==null?void 0:e.value)==null?void 0:i.ownerDocument)??(globalThis==null?void 0:globalThis.document),o=P(!1),n=P(()=>{});return U(r=>{if(!z)return;const l=async f=>{const d=f.target;if(e!=null&&e.value){if(Fe(e.value,d)){o.value=!1;return}if(f.target&&!o.value){let u=function(){Be(ht,s,v)};const v={originalEvent:f};f.pointerType==="touch"?(t.removeEventListener("click",n.value),n.value=u,t.addEventListener("click",n.value,{once:!0})):u()}else t.removeEventListener("click",n.value);o.value=!1}},c=window.setTimeout(()=>{t.addEventListener("pointerdown",l)},0);r(()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",l),t.removeEventListener("click",n.value)})}),{onPointerDownCapture:()=>o.value=!0}}function Et(s,e){var n;const t=((n=e==null?void 0:e.value)==null?void 0:n.ownerDocument)??(globalThis==null?void 0:globalThis.document),o=P(!1);return U(i=>{if(!z)return;const r=async l=>{e!=null&&e.value&&(await R(),await R(),!(!e.value||Fe(e.value,l.target))&&l.target&&!o.value&&Be(_t,s,{originalEvent:l}))};t.addEventListener("focusin",r),i(()=>t.removeEventListener("focusin",r))}),{onFocusCapture:()=>o.value=!0,onBlurCapture:()=>o.value=!1}}const I=be({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),wt=_({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(s,{emit:e}){const t=s,o=e,{forwardRef:n,currentElement:i}=F(),r=B(()=>{var m;return((m=i.value)==null?void 0:m.ownerDocument)??globalThis.document}),l=B(()=>I.layersRoot),c=B(()=>i.value?Array.from(l.value).indexOf(i.value):-1),f=B(()=>I.layersWithOutsidePointerEventsDisabled.size>0),d=B(()=>{const m=Array.from(l.value),[O]=[...I.layersWithOutsidePointerEventsDisabled].slice(-1),b=m.indexOf(O);return c.value>=b}),u=bt(async m=>{const O=[...I.branches].some(b=>b==null?void 0:b.contains(m.target));!d.value||O||(o("pointerDownOutside",m),o("interactOutside",m),await R(),m.defaultPrevented||o("dismiss"))},i),v=Et(m=>{[...I.branches].some(b=>b==null?void 0:b.contains(m.target))||(o("focusOutside",m),o("interactOutside",m),m.defaultPrevented||o("dismiss"))},i);st("Escape",m=>{c.value===l.value.size-1&&(o("escapeKeyDown",m),m.defaultPrevented||o("dismiss"))});let D;return U(m=>{i.value&&(t.disableOutsidePointerEvents&&(I.layersWithOutsidePointerEventsDisabled.size===0&&(D=r.value.body.style.pointerEvents,r.value.body.style.pointerEvents="none"),I.layersWithOutsidePointerEventsDisabled.add(i.value)),l.value.add(i.value),m(()=>{t.disableOutsidePointerEvents&&I.layersWithOutsidePointerEventsDisabled.size===1&&(r.value.body.style.pointerEvents=D)}))}),U(m=>{m(()=>{i.value&&(l.value.delete(i.value),I.layersWithOutsidePointerEventsDisabled.delete(i.value))})}),(m,O)=>(g(),w(a(L),{ref:a(n),"as-child":m.asChild,as:m.as,"data-dismissable-layer":"",style:Ke({pointerEvents:f.value?d.value?"auto":"none":void 0}),onFocusCapture:a(v).onFocusCapture,onBlurCapture:a(v).onBlurCapture,onPointerdownCapture:a(u).onPointerDownCapture},{default:p(()=>[E(m.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}});function M(){let s=document.activeElement;if(s==null)return null;for(;s!=null&&s.shadowRoot!=null&&s.shadowRoot.activeElement!=null;)s=s.shadowRoot.activeElement;return s}function Ot(s){return s?"open":"closed"}const ee="focusScope.autoFocusOnMount",te="focusScope.autoFocusOnUnmount",pe={bubbles:!1,cancelable:!0};function Dt(s,{select:e=!1}={}){const t=M();for(const o of s)if(T(o,{select:e}),M()!==t)return!0}function Ct(s){const e=Se(s),t=me(e,s),o=me(e.reverse(),s);return[t,o]}function Se(s){const e=[],t=document.createTreeWalker(s,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const n=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||n?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)e.push(t.currentNode);return e}function me(s,e){for(const t of s)if(!Pt(t,{upTo:e}))return t}function Pt(s,{upTo:e}){if(getComputedStyle(s).visibility==="hidden")return!0;for(;s;){if(e!==void 0&&s===e)return!1;if(getComputedStyle(s).display==="none")return!0;s=s.parentElement}return!1}function $t(s){return s instanceof HTMLInputElement&&"select"in s}function T(s,{select:e=!1}={}){if(s&&s.focus){const t=M();s.focus({preventScroll:!0}),s!==t&&$t(s)&&e&&s.select()}}const At=rt(()=>P([]));function Bt(){const s=At();return{add(e){const t=s.value[0];e!==t&&(t==null||t.pause()),s.value=ve(s.value,e),s.value.unshift(e)},remove(e){var t;s.value=ve(s.value,e),(t=s.value[0])==null||t.resume()}}}function ve(s,e){const t=[...s],o=t.indexOf(e);return o!==-1&&t.splice(o,1),t}function Ft(s){return s.filter(e=>e.tagName!=="A")}const St=_({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(s,{emit:e}){const t=s,o=e,{currentRef:n,currentElement:i}=F(),r=P(null),l=Bt(),c=be({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});U(d=>{if(!z)return;const u=i.value;if(!t.trapped)return;function v(b){if(c.paused||!u)return;const h=b.target;u.contains(h)?r.value=h:T(r.value,{select:!0})}function D(b){if(c.paused||!u)return;const h=b.relatedTarget;h!==null&&(u.contains(h)||T(r.value,{select:!0}))}function m(b){u.contains(r.value)||T(u)}document.addEventListener("focusin",v),document.addEventListener("focusout",D);const O=new MutationObserver(m);u&&O.observe(u,{childList:!0,subtree:!0}),d(()=>{document.removeEventListener("focusin",v),document.removeEventListener("focusout",D),O.disconnect()})}),U(async d=>{const u=i.value;if(await R(),!u)return;l.add(c);const v=M();if(!u.contains(v)){const m=new CustomEvent(ee,pe);u.addEventListener(ee,O=>o("mountAutoFocus",O)),u.dispatchEvent(m),m.defaultPrevented||(Dt(Ft(Se(u)),{select:!0}),M()===v&&T(u))}d(()=>{u.removeEventListener(ee,b=>o("mountAutoFocus",b));const m=new CustomEvent(te,pe),O=b=>{o("unmountAutoFocus",b)};u.addEventListener(te,O),u.dispatchEvent(m),setTimeout(()=>{m.defaultPrevented||T(v??document.body,{select:!0}),u.removeEventListener(te,O),l.remove(c)},0)})});function f(d){if(!t.loop&&!t.trapped||c.paused)return;const u=d.key==="Tab"&&!d.altKey&&!d.ctrlKey&&!d.metaKey,v=M();if(u&&v){const D=d.currentTarget,[m,O]=Ct(D);m&&O?!d.shiftKey&&v===O?(d.preventDefault(),t.loop&&T(m,{select:!0})):d.shiftKey&&v===m&&(d.preventDefault(),t.loop&&T(O,{select:!0})):v===D&&d.preventDefault()}}return(d,u)=>(g(),w(a(L),{ref_key:"currentRef",ref:n,tabindex:"-1","as-child":d.asChild,as:d.as,onKeydown:f},{default:p(()=>[E(d.$slots,"default")]),_:3},8,["as-child","as"]))}}),ke=_({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{emit:e}){const t=s,o=e,n=k(),{forwardRef:i,currentElement:r}=F();return n.titleId||(n.titleId=ie(void 0,"reka-dialog-title")),n.descriptionId||(n.descriptionId=ie(void 0,"reka-dialog-description")),_e(()=>{n.contentElement=r,M()!==document.body&&(n.triggerElement.value=M())}),(l,c)=>(g(),w(a(St),{"as-child":"",loop:"",trapped:t.trapFocus,onMountAutoFocus:c[5]||(c[5]=f=>o("openAutoFocus",f)),onUnmountAutoFocus:c[6]||(c[6]=f=>o("closeAutoFocus",f))},{default:p(()=>[y(a(wt),$({id:a(n).contentId,ref:a(i),as:l.as,"as-child":l.asChild,"disable-outside-pointer-events":l.disableOutsidePointerEvents,role:"dialog","aria-describedby":a(n).descriptionId,"aria-labelledby":a(n).titleId,"data-state":a(Ot)(a(n).open.value)},l.$attrs,{onDismiss:c[0]||(c[0]=f=>a(n).onOpenChange(!1)),onEscapeKeyDown:c[1]||(c[1]=f=>o("escapeKeyDown",f)),onFocusOutside:c[2]||(c[2]=f=>o("focusOutside",f)),onInteractOutside:c[3]||(c[3]=f=>o("interactOutside",f)),onPointerDownOutside:c[4]||(c[4]=f=>o("pointerDownOutside",f))}),{default:p(()=>[E(l.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}});var kt=function(s){if(typeof document>"u")return null;var e=Array.isArray(s)?s[0]:s;return e.ownerDocument.body},N=new WeakMap,Y=new WeakMap,X={},se=0,Ie=function(s){return s&&(s.host||Ie(s.parentNode))},It=function(s,e){return e.map(function(t){if(s.contains(t))return t;var o=Ie(t);return o&&s.contains(o)?o:(console.error("aria-hidden",t,"in not contained inside",s,". Doing nothing"),null)}).filter(function(t){return!!t})},Tt=function(s,e,t,o){var n=It(e,Array.isArray(s)?s:[s]);X[t]||(X[t]=new WeakMap);var i=X[t],r=[],l=new Set,c=new Set(n),f=function(u){!u||l.has(u)||(l.add(u),f(u.parentNode))};n.forEach(f);var d=function(u){!u||c.has(u)||Array.prototype.forEach.call(u.children,function(v){if(l.has(v))d(v);else try{var D=v.getAttribute(o),m=D!==null&&D!=="false",O=(N.get(v)||0)+1,b=(i.get(v)||0)+1;N.set(v,O),i.set(v,b),r.push(v),O===1&&m&&Y.set(v,!0),b===1&&v.setAttribute(t,"true"),m||v.setAttribute(o,"true")}catch(h){console.error("aria-hidden: cannot operate on ",v,h)}})};return d(e),l.clear(),se++,function(){r.forEach(function(u){var v=N.get(u)-1,D=i.get(u)-1;N.set(u,v),i.set(u,D),v||(Y.has(u)||u.removeAttribute(o),Y.delete(u)),D||u.removeAttribute(t)}),se--,se||(N=new WeakMap,N=new WeakMap,Y=new WeakMap,X={})}},Mt=function(s,e,t){t===void 0&&(t="data-aria-hidden");var o=Array.from(Array.isArray(s)?s:[s]),n=kt(s);return n?(o.push.apply(o,Array.from(n.querySelectorAll("[aria-live]"))),Tt(o,n,t,"aria-hidden")):function(){return null}};function xt(s){let e;W(()=>De(s),t=>{t?e=Mt(t):e&&e()}),ye(()=>{e&&e()})}const Lt=_({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{emit:e}){const t=s,o=e,n=k(),i=J(o),{forwardRef:r,currentElement:l}=F();return xt(l),(c,f)=>(g(),w(ke,$({...t,...a(i)},{ref:a(r),"trap-focus":a(n).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:f[0]||(f[0]=d=>{var u;d.defaultPrevented||(d.preventDefault(),(u=a(n).triggerElement.value)==null||u.focus())}),onPointerDownOutside:f[1]||(f[1]=d=>{const u=d.detail.originalEvent,v=u.button===0&&u.ctrlKey===!0;(u.button===2||v)&&d.preventDefault()}),onFocusOutside:f[2]||(f[2]=d=>{d.preventDefault()})}),{default:p(()=>[E(c.$slots,"default")]),_:3},16,["trap-focus"]))}}),Nt=_({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{emit:e}){const t=s,n=J(e);F();const i=k(),r=P(!1),l=P(!1);return(c,f)=>(g(),w(ke,$({...t,...a(n)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:f[0]||(f[0]=d=>{var u;d.defaultPrevented||(r.value||(u=a(i).triggerElement.value)==null||u.focus(),d.preventDefault()),r.value=!1,l.value=!1}),onInteractOutside:f[1]||(f[1]=d=>{var D;d.defaultPrevented||(r.value=!0,d.detail.originalEvent.type==="pointerdown"&&(l.value=!0));const u=d.target;((D=a(i).triggerElement.value)==null?void 0:D.contains(u))&&d.preventDefault(),d.detail.originalEvent.type==="focusin"&&l.value&&d.preventDefault()})}),{default:p(()=>[E(c.$slots,"default")]),_:3},16))}}),Rt=_({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{emit:e}){const t=s,o=e,n=k(),i=J(o),{forwardRef:r}=F();return(l,c)=>(g(),w(a($e),{present:l.forceMount||a(n).open.value},{default:p(()=>[a(n).modal.value?(g(),w(Lt,$({key:0,ref:a(r)},{...t,...a(i),...l.$attrs}),{default:p(()=>[E(l.$slots,"default")]),_:3},16)):(g(),w(Nt,$({key:1,ref:a(r)},{...t,...a(i),...l.$attrs}),{default:p(()=>[E(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}});function oe(s){if(s===null||typeof s!="object")return!1;const e=Object.getPrototypeOf(s);return e!==null&&e!==Object.prototype&&Object.getPrototypeOf(e)!==null||Symbol.iterator in s?!1:Symbol.toStringTag in s?Object.prototype.toString.call(s)==="[object Module]":!0}function le(s,e,t=".",o){if(!oe(e))return le(s,{},t,o);const n=Object.assign({},e);for(const i in s){if(i==="__proto__"||i==="constructor")continue;const r=s[i];r!=null&&(o&&o(n,i,r,t)||(Array.isArray(r)&&Array.isArray(n[i])?n[i]=[...r,...n[i]]:oe(r)&&oe(n[i])?n[i]=le(r,n[i],(t?`${t}.`:"")+i.toString(),o):n[i]=r))}return n}function Ut(s){return(...e)=>e.reduce((t,o)=>le(t,o,"",s),{})}const jt=Ut(),Kt=it(()=>{const s=P(new Map),e=P(),t=B(()=>{for(const r of s.value.values())if(r)return!0;return!1}),o=Pe({scrollBody:P(!0)});let n=null;const i=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.body.style.removeProperty("--scrollbar-width"),document.body.style.overflow=e.value??"",fe&&(n==null||n()),e.value=void 0};return W(t,(r,l)=>{var u;if(!z)return;if(!r){l&&i();return}e.value===void 0&&(e.value=document.body.style.overflow);const c=window.innerWidth-document.documentElement.clientWidth,f={padding:c,margin:0},d=(u=o.scrollBody)!=null&&u.value?typeof o.scrollBody.value=="object"?jt({padding:o.scrollBody.value.padding===!0?c:o.scrollBody.value.padding,margin:o.scrollBody.value.margin===!0?c:o.scrollBody.value.margin},f):f:{padding:0,margin:0};c>0&&(document.body.style.paddingRight=typeof d.padding=="number"?`${d.padding}px`:String(d.padding),document.body.style.marginRight=typeof d.margin=="number"?`${d.margin}px`:String(d.margin),document.body.style.setProperty("--scrollbar-width",`${c}px`),document.body.style.overflow="hidden"),fe&&(n=ot(document,"touchmove",v=>Vt(v),{passive:!1})),R(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),s});function Wt(s){const e=Math.random().toString(36).substring(2,7),t=Kt();t.value.set(e,s);const o=B({get:()=>t.value.get(e)??!1,set:n=>t.value.set(e,n)});return lt(()=>{t.value.delete(e)}),o}function Te(s){const e=window.getComputedStyle(s);if(e.overflowX==="scroll"||e.overflowY==="scroll"||e.overflowX==="auto"&&s.clientWidth<s.scrollWidth||e.overflowY==="auto"&&s.clientHeight<s.scrollHeight)return!0;{const t=s.parentNode;return!(t instanceof Element)||t.tagName==="BODY"?!1:Te(t)}}function Vt(s){const e=s||window.event,t=e.target;return t instanceof Element&&Te(t)?!1:e.touches.length>1?!0:(e.preventDefault&&e.cancelable&&e.preventDefault(),!1)}const zt=_({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(s){const e=k();return Wt(!0),F(),(t,o)=>(g(),w(a(L),{as:t.as,"as-child":t.asChild,"data-state":a(e).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:p(()=>[E(t.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Ht=_({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(s){const e=k(),{forwardRef:t}=F();return(o,n)=>{var i;return(i=a(e))!=null&&i.modal.value?(g(),w(a($e),{key:0,present:o.forceMount||a(e).open.value},{default:p(()=>[y(zt,$(o.$attrs,{ref:a(t),as:o.as,"as-child":o.asChild}),{default:p(()=>[E(o.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):Z("",!0)}}}),Me=_({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(s){const e=s;F();const t=k();return(o,n)=>(g(),w(a(L),$(e,{type:o.as==="button"?"button":void 0,onClick:n[0]||(n[0]=i=>a(t).onOpenChange(!1))}),{default:p(()=>[E(o.$slots,"default")]),_:3},16,["type"]))}}),qt=_({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(s){const e=s,t=k();return F(),(o,n)=>(g(),w(a(L),$(e,{id:a(t).titleId}),{default:p(()=>[E(o.$slots,"default")]),_:3},16,["id"]))}}),Yt=_({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(s){const e=s;F();const t=k();return(o,n)=>(g(),w(a(L),$(e,{id:a(t).descriptionId}),{default:p(()=>[E(o.$slots,"default")]),_:3},16,["id"]))}}),Xt=_({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(s){const e=s;return(t,o)=>(g(),w(a(gt),We(Ve(e)),{default:p(()=>[E(t.$slots,"default")]),_:3},16))}}),Gt=_({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:e}){const n=Ae(s,e);return(i,r)=>(g(),w(a(vt),$({"data-slot":"dialog"},a(n)),{default:p(()=>[E(i.$slots,"default")]),_:3},16))}}),Zt=_({__name:"DialogClose",props:{asChild:{type:Boolean},as:{}},setup(s){const e=s;return(t,o)=>(g(),w(a(Me),$({"data-slot":"dialog-close"},e),{default:p(()=>[E(t.$slots,"default")]),_:3},16))}}),Jt=_({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,t=B(()=>{const{class:o,...n}=e;return n});return(o,n)=>(g(),w(a(Ht),$({"data-slot":"dialog-overlay"},t.value,{class:a(j)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e.class)}),{default:p(()=>[E(o.$slots,"default")]),_:3},16,["class"]))}}),Qt=_({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{emit:e}){const t=s,o=e,n=B(()=>{const{class:r,...l}=t;return l}),i=Ae(n,o);return(r,l)=>(g(),w(a(Xt),null,{default:p(()=>[y(Jt),y(a(Rt),$({"data-slot":"dialog-content"},a(i),{class:a(j)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t.class)}),{default:p(()=>[E(r.$slots,"default"),y(a(Me),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"},{default:p(()=>[y(a(dt)),l[0]||(l[0]=A("span",{class:"sr-only"},"Close",-1))]),_:1})]),_:3},16,["class"])]),_:3}))}}),es=_({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,t=B(()=>{const{class:n,...i}=e;return i}),o=de(t);return(n,i)=>(g(),w(a(Yt),$({"data-slot":"dialog-description"},a(o),{class:a(j)("text-muted-foreground text-sm",e.class)}),{default:p(()=>[E(n.$slots,"default")]),_:3},16,["class"]))}}),ts=_({__name:"DialogFooter",props:{class:{}},setup(s){const e=s;return(t,o)=>(g(),V("div",{"data-slot":"dialog-footer",class:Ee(a(j)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e.class))},[E(t.$slots,"default")],2))}}),ss=_({__name:"DialogHeader",props:{class:{}},setup(s){const e=s;return(t,o)=>(g(),V("div",{"data-slot":"dialog-header",class:Ee(a(j)("flex flex-col gap-2 text-center sm:text-left",e.class))},[E(t.$slots,"default")],2))}}),os=_({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,t=B(()=>{const{class:n,...i}=e;return i}),o=de(t);return(n,i)=>(g(),w(a(qt),$({"data-slot":"dialog-title"},a(o),{class:a(j)("text-lg leading-none font-semibold",e.class)}),{default:p(()=>[E(n.$slots,"default")]),_:3},16,["class"]))}}),ns=_({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{}},setup(s){const e=s;return(t,o)=>(g(),w(a(yt),$({"data-slot":"dialog-trigger"},e),{default:p(()=>[E(t.$slots,"default")]),_:3},16))}}),as={class:"space-y-6"},rs={class:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},is={class:"grid gap-2"},ls=_({__name:"DeleteUser",setup(s){const e=P(null),t=we({password:""}),o=i=>{i.preventDefault(),t.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>n(),onError:()=>{var r;return(r=e.value)==null?void 0:r.focus()},onFinish:()=>t.reset()})},n=()=>{t.clearErrors(),t.reset()};return(i,r)=>(g(),V("div",as,[y(Oe,{title:"Delete account",description:"Delete your account and all of its resources"}),A("div",rs,[r[7]||(r[7]=A("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[A("p",{class:"font-medium"},"Warning"),A("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),y(a(Gt),null,{default:p(()=>[y(a(ns),{"as-child":""},{default:p(()=>[y(a(G),{variant:"destructive"},{default:p(()=>r[1]||(r[1]=[S("Delete account")])),_:1})]),_:1}),y(a(Qt),null,{default:p(()=>[A("form",{class:"space-y-6",onSubmit:o},[y(a(ss),{class:"space-y-3"},{default:p(()=>[y(a(os),null,{default:p(()=>r[2]||(r[2]=[S("Are you sure you want to delete your account?")])),_:1}),y(a(es),null,{default:p(()=>r[3]||(r[3]=[S(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1})]),_:1}),A("div",is,[y(a(ae),{for:"password",class:"sr-only"},{default:p(()=>r[4]||(r[4]=[S("Password")])),_:1}),y(a(re),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:e,modelValue:a(t).password,"onUpdate:modelValue":r[0]||(r[0]=l=>a(t).password=l),placeholder:"Password"},null,8,["modelValue"]),y(ne,{message:a(t).errors.password},null,8,["message"])]),y(a(ts),{class:"gap-2"},{default:p(()=>[y(a(Zt),{"as-child":""},{default:p(()=>[y(a(G),{variant:"secondary",onClick:n},{default:p(()=>r[5]||(r[5]=[S(" Cancel ")])),_:1})]),_:1}),y(a(G),{variant:"destructive",disabled:a(t).processing},{default:p(()=>r[6]||(r[6]=[A("button",{type:"submit"},"Delete account",-1)])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),us={class:"flex flex-col space-y-6"},ds={class:"grid gap-2"},cs={class:"grid gap-2"},fs={key:0},ps={class:"-mt-4 text-sm text-muted-foreground"},ms={key:0,class:"mt-2 text-sm font-medium text-green-600"},vs={class:"flex items-center gap-4"},ys={class:"text-sm text-neutral-600"},Bs=_({__name:"Profile",props:{mustVerifyEmail:{type:Boolean},status:{}},setup(s){const e=[{title:"Profile settings",href:"/settings/profile"}],o=ze().props.auth.user,n=we({name:o.name,email:o.email}),i=()=>{n.patch(route("profile.update"),{preserveScroll:!0})};return(r,l)=>(g(),w(ut,{breadcrumbs:e},{default:p(()=>[y(a(He),{title:"Profile settings"}),y(Je,null,{default:p(()=>[A("div",us,[y(Oe,{title:"Profile information",description:"Update your name and email address"}),A("form",{onSubmit:qe(i,["prevent"]),class:"space-y-6"},[A("div",ds,[y(a(ae),{for:"name"},{default:p(()=>l[2]||(l[2]=[S("Name")])),_:1}),y(a(re),{id:"name",class:"mt-1 block w-full",modelValue:a(n).name,"onUpdate:modelValue":l[0]||(l[0]=c=>a(n).name=c),required:"",autocomplete:"name",placeholder:"Full name"},null,8,["modelValue"]),y(ne,{class:"mt-2",message:a(n).errors.name},null,8,["message"])]),A("div",cs,[y(a(ae),{for:"email"},{default:p(()=>l[3]||(l[3]=[S("Email address")])),_:1}),y(a(re),{id:"email",type:"email",class:"mt-1 block w-full",modelValue:a(n).email,"onUpdate:modelValue":l[1]||(l[1]=c=>a(n).email=c),required:"",autocomplete:"username",placeholder:"Email address"},null,8,["modelValue"]),y(ne,{class:"mt-2",message:a(n).errors.email},null,8,["message"])]),r.mustVerifyEmail&&!a(o).email_verified_at?(g(),V("div",fs,[A("p",ps,[l[5]||(l[5]=S(" Your email address is unverified. ")),y(a(Ye),{href:r.route("verification.send"),method:"post",as:"button",class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:p(()=>l[4]||(l[4]=[S(" Click here to resend the verification email. ")])),_:1},8,["href"])]),r.status==="verification-link-sent"?(g(),V("div",ms," A new verification link has been sent to your email address. ")):Z("",!0)])):Z("",!0),A("div",vs,[y(a(G),{disabled:a(n).processing},{default:p(()=>l[6]||(l[6]=[S("Save")])),_:1},8,["disabled"]),y(Xe,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[Ge(A("p",ys,"Saved.",512),[[Ze,a(n).recentlySuccessful]])]),_:1})])],32)]),y(ls)]),_:1})]),_:1}))}});export{Bs as default};
