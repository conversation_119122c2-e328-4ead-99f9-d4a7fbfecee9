import{_ as v}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import{r as m,o as k,b as i,e as r,i as u,u as p,p as b,w as c,g as t,t as a,F as g,q as y,n as w,f,s as P,P as D,j as M}from"./vendor-CGdKbVnC.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-D-1s9QYo.js";const z={class:"flex items-center justify-between"},B={class:"flex mt-2","aria-label":"Breadcrumb"},V={class:"inline-flex items-center space-x-1 md:space-x-3"},C={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},S={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},j={class:"py-12"},N={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},A={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},E={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},F={class:"p-6"},J={class:"flex items-center"},L={class:"ml-4"},R={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},T={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},q={class:"p-6"},G={class:"flex items-center"},W={class:"ml-4"},$={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},H={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},I={class:"p-6"},K={class:"flex items-center"},O={class:"ml-4"},Q={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},U={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6 text-gray-900 dark:text-gray-100"},at={key:0,class:"text-center py-8"},rt={key:1,class:"overflow-x-auto"},it={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},lt={class:"px-6 py-4 whitespace-nowrap"},ot={class:"flex items-center"},nt={class:"ml-4"},xt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ct={class:"text-sm text-gray-500 dark:text-gray-400"},gt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm text-gray-900 dark:text-gray-100"},ut={class:"text-sm text-gray-500 dark:text-gray-400"},pt={class:"px-6 py-4 whitespace-nowrap"},yt={class:"px-6 py-4 whitespace-nowrap"},ft={class:"flex items-center"},_t={class:"text-sm text-gray-900 dark:text-gray-100"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},vt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},kt={key:0,class:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"},Bt={__name:"Providers",setup(bt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Providers",href:"/providers"}],x=m(!1),d=m([]),_=async()=>{x.value=!0;try{d.value=[{id:1,name:"Dr. Jane Smith",email:"<EMAIL>",specialization:"General Practitioner",license_number:"MD12345",verification_status:"verified",rating:4.8,years_of_experience:10,patients_count:45,appointments_count:120},{id:2,name:"Dr. Michael Johnson",email:"<EMAIL>",specialization:"Cardiology",license_number:"MD67890",verification_status:"verified",rating:4.9,years_of_experience:15,patients_count:38,appointments_count:95},{id:3,name:"Dr. Sarah Williams",email:"<EMAIL>",specialization:"Dermatology",license_number:"MD11111",verification_status:"pending",rating:4.7,years_of_experience:8,patients_count:32,appointments_count:78}]}catch(o){console.error("Error fetching providers:",o)}finally{x.value=!1}},h=o=>({verified:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return k(()=>{_()}),(o,e)=>(r(),i(g,null,[u(p(b),{title:"Provider Management"}),u(v,null,{header:c(()=>[t("div",z,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Provider Management ",-1)),t("nav",B,[t("ol",V,[(r(),i(g,null,y(n,(s,l)=>t("li",{key:l,class:"inline-flex items-center"},[l<n.length-1?(r(),P(p(D),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[M(a(s.title),1)]),_:2},1032,["href"])):(r(),i("span",C,a(s.title),1)),l<n.length-1?(r(),i("svg",S,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):f("",!0)])),64))])])]),e[2]||(e[2]=t("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Provider ",-1))])]),default:c(()=>[t("div",j,[t("div",N,[t("div",A,[t("div",E,[t("div",F,[t("div",J,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-md text-2xl text-blue-500"})],-1)),t("div",L,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Providers",-1)),t("p",R,a(d.value.length),1)])])])]),t("div",T,[t("div",q,[t("div",G,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-check-circle text-2xl text-green-500"})],-1)),t("div",W,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Verified",-1)),t("p",$,a(d.value.filter(s=>s.verification_status==="verified").length),1)])])])]),t("div",H,[t("div",I,[t("div",K,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-clock text-2xl text-yellow-500"})],-1)),t("div",O,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending",-1)),t("p",Q,a(d.value.filter(s=>s.verification_status==="pending").length),1)])])])]),t("div",U,[t("div",X,[t("div",Y,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-star text-2xl text-yellow-400"})],-1)),t("div",Z,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Avg Rating",-1)),t("p",tt,a((d.value.reduce((s,l)=>s+l.rating,0)/d.value.length).toFixed(1)),1)])])])])]),t("div",et,[t("div",st,[x.value?(r(),i("div",at,e[11]||(e[11]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(r(),i("div",rt,[t("table",it,[e[16]||(e[16]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Provider "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Specialization "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Rating "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patients "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",dt,[(r(!0),i(g,null,y(d.value,s=>(r(),i("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",lt,[t("div",ot,[e[12]||(e[12]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center"},[t("i",{class:"fas fa-user-md text-blue-600 dark:text-blue-400"})])],-1)),t("div",nt,[t("div",xt,a(s.name),1),t("div",ct,a(s.email),1)])])]),t("td",gt,[t("div",mt,a(s.specialization),1),t("div",ut,a(s.license_number),1)]),t("td",pt,[t("span",{class:w([h(s.verification_status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.verification_status),3)]),t("td",yt,[t("div",ft,[e[13]||(e[13]=t("i",{class:"fas fa-star text-yellow-400 mr-1"},null,-1)),t("span",_t,a(s.rating),1)])]),t("td",ht,a(s.patients_count),1),t("td",vt,[e[14]||(e[14]=t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View ",-1)),e[15]||(e[15]=t("button",{class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"}," Edit ",-1)),s.verification_status==="pending"?(r(),i("button",kt," Verify ")):f("",!0)])]))),128))])])]))])])])])]),_:1})],64))}};export{Bt as default};
