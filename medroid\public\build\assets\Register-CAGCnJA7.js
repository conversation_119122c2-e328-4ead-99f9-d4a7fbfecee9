import{d as S,M as L,r as d,b as r,e as n,i,g as e,u as a,p as P,G as b,f as u,t as c,v as T,x as m,y as w,N as k,j as x,w as q,P as E,F as _,q as F,n as M}from"./vendor-CGdKbVnC.js";import{_ as y}from"./InputError.vue_vue_type_script_setup_true_lang-BY1lF_tM.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";const R={class:"min-h-screen bg-gray-50 flex"},H={class:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-white"},U={class:"max-w-md w-full"},D={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},Y={key:0},G=["placeholder"],O={class:"relative"},W=["type","autocomplete","placeholder"],$={key:0,class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},J={key:1,class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},K={key:1},Q={class:"relative"},X=["type"],Z={key:0,class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ee={key:1,class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},te={class:"pt-4"},se=["disabled"],oe={class:"text-center pt-4"},ae={class:"text-sm text-gray-600"},re={key:2,class:"text-center"},ne={key:3,class:"text-center"},le={class:"hidden lg:flex lg:w-1/2 bg-gray-100 flex-col"},ie={class:"flex-1 p-6 overflow-y-auto space-y-4"},de=S({__name:"Register",props:{canResetPassword:{type:Boolean,default:!1},status:{type:String,default:null},isLoginMode:{type:Boolean,default:!1}},setup(p){const C=p,s=L({name:"",email:"",password:"",password_confirmation:""}),g=d(!1),f=d(!1),v=d(!1),o=d(C.isLoginMode),B=d([{type:"bot",text:"Hi! I'm your AI health assistant. How are you feeling today?"},{type:"user",text:"I've been having trouble sleeping lately. Any suggestions?"},{type:"bot",text:"Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep."},{type:"user",text:"That makes sense. I'll try that tonight. Thank you!"},{type:"bot",text:"You're welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?"},{type:"user",text:"Yes, that would be helpful. I think stress might be affecting my sleep."},{type:"bot",text:"Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system."},{type:"user",text:"I'll definitely try that breathing technique. Thank you for the personalized advice!"}]),j=()=>{v.value=!0;const h=o.value?"login":"register";s.post(route(h),{onFinish:()=>{o.value?s.reset("password"):s.reset("password","password_confirmation"),v.value=!1}})},I=()=>{g.value=!g.value},V=()=>{f.value=!f.value},z=()=>{o.value=!o.value,s.reset()};return(h,t)=>(n(),r(_,null,[i(a(P),{title:o.value?"Sign In":"Register"},null,8,["title"]),e("div",R,[e("div",H,[e("div",U,[t[10]||(t[10]=b('<div class="text-left mb-8" data-v-cdf013a5><h1 class="text-4xl font-bold text-gray-900 mb-2" data-v-cdf013a5> Your health, </h1><h2 class="text-4xl font-bold text-gray-900 mb-4" data-v-cdf013a5> our priority </h2><p class="text-gray-600 mb-8" data-v-cdf013a5> AI-powered healthcare that puts your wellness first. </p></div><button type="button" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 mb-6" data-v-cdf013a5><svg class="w-5 h-5 mr-3" viewBox="0 0 24 24" data-v-cdf013a5><path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" data-v-cdf013a5></path><path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" data-v-cdf013a5></path><path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" data-v-cdf013a5></path><path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" data-v-cdf013a5></path></svg> Continue with Google </button><div class="relative mb-6" data-v-cdf013a5><div class="absolute inset-0 flex items-center" data-v-cdf013a5><div class="w-full border-t border-gray-300" data-v-cdf013a5></div></div><div class="relative flex justify-center text-sm" data-v-cdf013a5><span class="px-2 bg-white text-gray-500" data-v-cdf013a5>OR</span></div></div>',3)),p.status?(n(),r("div",D,c(p.status),1)):u("",!0),e("form",{class:"space-y-4",onSubmit:T(j,["prevent"])},[o.value?u("",!0):(n(),r("div",Y,[m(e("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=l=>a(s).name=l),name:"name",type:"text",autocomplete:"name",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500",placeholder:"Enter your full name"},null,512),[[w,a(s).name]]),i(y,{class:"mt-2",message:a(s).errors.name},null,8,["message"])])),e("div",null,[m(e("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=l=>a(s).email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500",placeholder:o.value?"Enter your personal or work email":"Enter your email address"},null,8,G),[[w,a(s).email]]),i(y,{class:"mt-2",message:a(s).errors.email},null,8,["message"])]),e("div",null,[e("div",O,[m(e("input",{id:"password","onUpdate:modelValue":t[2]||(t[2]=l=>a(s).password=l),name:"password",type:g.value?"text":"password",autocomplete:o.value?"current-password":"new-password",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500",placeholder:o.value?"Enter your password":"Create a strong password"},null,8,W),[[k,a(s).password]]),e("button",{type:"button",onClick:I,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-gray-600 transition-colors duration-200"},[g.value?(n(),r("svg",$,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),r("svg",J,t[5]||(t[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),i(y,{class:"mt-2",message:a(s).errors.password},null,8,["message"])]),o.value?u("",!0):(n(),r("div",K,[e("div",Q,[m(e("input",{id:"password_confirmation","onUpdate:modelValue":t[3]||(t[3]=l=>a(s).password_confirmation=l),name:"password_confirmation",type:f.value?"text":"password",autocomplete:"new-password",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500",placeholder:"Confirm your password"},null,8,X),[[k,a(s).password_confirmation]]),e("button",{type:"button",onClick:V,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-gray-600 transition-colors duration-200"},[f.value?(n(),r("svg",Z,t[6]||(t[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),r("svg",ee,t[7]||(t[7]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),i(y,{class:"mt-2",message:a(s).errors.password_confirmation},null,8,["message"])])),e("div",te,[e("button",{type:"submit",disabled:a(s).processing||v.value,class:"w-full bg-orange-400 hover:bg-orange-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"},c(a(s).processing||v.value?o.value?"Signing In...":"Creating Account...":o.value?"Sign In":"Sign Up"),9,se)]),e("div",oe,[e("p",ae,[x(c(o.value?"Don't have an account?":"Already have an account?")+" ",1),e("button",{type:"button",onClick:z,class:"font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200 underline"},c(o.value?"Sign up":"Sign in here"),1)])]),o.value&&p.canResetPassword?(n(),r("div",re,[i(a(E),{href:h.route("password.request"),class:"text-sm text-gray-600 hover:text-gray-500 transition-colors duration-200 underline"},{default:q(()=>t[8]||(t[8]=[x(" Forgot password? ")])),_:1},8,["href"])])):u("",!0),o.value?u("",!0):(n(),r("div",ne,t[9]||(t[9]=[e("p",{class:"text-xs text-gray-500"},[x(" By continuing, you acknowledge Medroid's "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500 underline"},"Privacy Policy")],-1)])))],32)])]),e("div",le,[t[11]||(t[11]=e("div",{class:"bg-white p-6 border-b border-gray-200"},[e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Experience Medroid"),e("p",{class:"text-gray-600 text-sm"},"Chat with our AI doctor for instant health insights")],-1)),e("div",ie,[(n(!0),r(_,null,F(B.value,(l,A)=>(n(),r("div",{key:A,class:M(l.type==="user"?"flex justify-end":"flex justify-start")},[e("div",{class:M(["max-w-xs lg:max-w-md px-4 py-3 rounded-2xl text-sm",l.type==="user"?"bg-blue-600 text-white rounded-br-md":"bg-white text-gray-800 border border-gray-200 rounded-bl-md shadow-sm"])},c(l.text),3)],2))),128))]),t[12]||(t[12]=b('<div class="p-6 bg-white border-t border-gray-200" data-v-cdf013a5><div class="flex items-center space-x-3" data-v-cdf013a5><input type="text" placeholder="Type your health question here..." class="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" disabled data-v-cdf013a5><button class="bg-orange-400 hover:bg-orange-500 text-white p-3 rounded-full transition-colors duration-200 disabled:opacity-50" disabled data-v-cdf013a5><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-cdf013a5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" data-v-cdf013a5></path></svg></button></div><div class="mt-4 text-center" data-v-cdf013a5><a href="#" class="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200" data-v-cdf013a5> Learn more about Medroid → </a></div></div><div class="absolute bottom-4 right-4 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded" data-v-cdf013a5> v1.0.27 (2025-05-30) </div>',2))])])],64))}}),ge=N(de,[["__scopeId","data-v-cdf013a5"]]);export{ge as default};
