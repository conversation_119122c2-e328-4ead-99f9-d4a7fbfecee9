import{_ as D}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import{r as g,o as S,b as u,e as n,g as e,t as m,v as j,x as d,y as v,f as b,K as _,s as $,w as E,F as M,q as A,n as C,j as N}from"./vendor-CGdKbVnC.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-D-1s9QYo.js";const O={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},P={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},T={class:"mt-3"},q={class:"flex justify-between items-center mb-4"},B={class:"text-lg font-medium text-gray-900"},F={class:"grid grid-cols-2 gap-4"},z={class:"border border-gray-200 rounded-md p-4"},I={class:"flex items-center mb-3"},L={key:0,class:"ml-6 space-y-2"},Y={class:"flex items-center"},G={class:"flex items-center"},K={class:"flex items-center"},H={class:"border border-gray-200 rounded-md p-4"},J={class:"grid grid-cols-2 gap-4"},Q={class:"flex items-center"},R={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200"},W=["disabled"],X={__name:"ProviderServiceModal",props:{service:{type:Object,default:null}},emits:["close","saved"],setup(h,{emit:w}){const c=h,x=w,p=g(!1),s=g({name:"",description:"",category:"",price:0,duration:30,is_telemedicine:!1,supports_video:!1,supports_audio:!1,supports_chat:!1,active:!0,discount_percentage:null,discount_valid_until:""}),f=async()=>{var a,t;p.value=!0;try{const o=c.service?`/api/services/${c.service.id}`:"/api/services",y=c.service?"put":"post";await window.axios[y](o,s.value),x("saved")}catch(o){if(console.error("Error saving service:",o),(t=(a=o.response)==null?void 0:a.data)!=null&&t.errors){const y=Object.values(o.response.data.errors).flat();alert(`Validation errors:
`+y.join(`
`))}else alert("Error saving service. Please try again.")}finally{p.value=!1}};return S(()=>{if(c.service&&(Object.keys(s.value).forEach(a=>{c.service[a]!==void 0&&(s.value[a]=c.service[a])}),c.service.discount_valid_until)){const a=new Date(c.service.discount_valid_until);s.value.discount_valid_until=a.toISOString().slice(0,16)}}),(a,t)=>(n(),u("div",O,[e("div",P,[e("div",T,[e("div",q,[e("h3",B,m(h.service?"Edit Service":"Create New Service"),1),e("button",{onClick:t[0]||(t[0]=o=>a.$emit("close")),class:"text-gray-400 hover:text-gray-600"},t[14]||(t[14]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("form",{onSubmit:j(f,["prevent"]),class:"space-y-4"},[e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700"},"Service Name *",-1)),d(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.value.name=o),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., General Consultation, Cardiology Check-up"},null,512),[[v,s.value.name]])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700"},"Description",-1)),d(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=o=>s.value.description=o),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Describe what this service includes..."},null,512),[[v,s.value.description]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700"},"Category",-1)),d(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.value.category=o),type:"text",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., Consultation, Therapy, Diagnostic"},null,512),[[v,s.value.category]])]),e("div",F,[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700"},"Price ($) *",-1)),d(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.price=o),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"},null,512),[[v,s.value.price,void 0,{number:!0}]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700"},"Duration (minutes) *",-1)),d(e("input",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.value.duration=o),type:"number",min:"5",step:"5",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"30"},null,512),[[v,s.value.duration,void 0,{number:!0}]])])]),e("div",z,[e("div",I,[d(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>s.value.is_telemedicine=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.is_telemedicine]]),t[20]||(t[20]=e("label",{class:"ml-2 block text-sm font-medium text-gray-900"}," Offer as Telemedicine Service ",-1))]),s.value.is_telemedicine?(n(),u("div",L,[t[24]||(t[24]=e("div",{class:"text-sm text-gray-600 mb-2"},"Select supported communication methods:",-1)),e("div",Y,[d(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>s.value.supports_video=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.supports_video]]),t[21]||(t[21]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Video Calls ",-1))]),e("div",G,[d(e("input",{"onUpdate:modelValue":t[8]||(t[8]=o=>s.value.supports_audio=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.supports_audio]]),t[22]||(t[22]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Audio Calls ",-1))]),e("div",K,[d(e("input",{"onUpdate:modelValue":t[9]||(t[9]=o=>s.value.supports_chat=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.supports_chat]]),t[23]||(t[23]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Text Chat ",-1))])])):b("",!0)]),e("div",H,[t[27]||(t[27]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Promotional Pricing (Optional)",-1)),e("div",J,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700"},"Discount (%)",-1)),d(e("input",{"onUpdate:modelValue":t[10]||(t[10]=o=>s.value.discount_percentage=o),type:"number",step:"0.01",min:"0",max:"100",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"},null,512),[[v,s.value.discount_percentage,void 0,{number:!0}]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700"},"Valid Until",-1)),d(e("input",{"onUpdate:modelValue":t[11]||(t[11]=o=>s.value.discount_valid_until=o),type:"datetime-local",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[v,s.value.discount_valid_until]])])])]),e("div",Q,[d(e("input",{"onUpdate:modelValue":t[12]||(t[12]=o=>s.value.active=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.active]]),t[28]||(t[28]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Make this service active and available for booking ",-1))]),e("div",R,[e("button",{type:"button",onClick:t[13]||(t[13]=o=>a.$emit("close")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:p.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},m(p.value?"Saving...":h.service?"Update Service":"Create Service"),9,W)])],32)])])]))}},Z={class:"py-12"},ee={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},te={class:"bg-white overflow-hidden shadow-xl sm:rounded-lg"},se={class:"p-6 lg:p-8 bg-white border-b border-gray-200"},oe={class:"flex justify-between items-center mb-6"},le={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},re={class:"p-6"},ie={class:"flex justify-between items-start mb-4"},ne={class:"text-lg font-semibold text-gray-900"},ae={class:"text-gray-600 text-sm mb-4"},de={class:"space-y-2 mb-4"},ue={class:"flex justify-between text-sm"},ce={class:"font-medium"},me={class:"flex justify-between text-sm"},pe={class:"font-medium text-green-600"},be={class:"flex justify-between text-sm"},ve={class:"font-medium"},ge={key:0,class:"mb-4"},xe={class:"flex flex-wrap gap-1"},fe={key:0,class:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"},ye={key:1,class:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded"},we={key:2,class:"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded"},_e={key:1,class:"mb-4 p-2 bg-yellow-50 rounded"},he={class:"text-xs text-yellow-800"},ke={key:0},$e={class:"flex justify-between items-center pt-4 border-t border-gray-100"},Ce=["onClick"],Se=["onClick"],Ve=["onClick"],Ue={key:1,class:"text-center py-12"},Ne={__name:"Services",setup(h){const w=g([]),c=g(!1),x=g(!1),p=g(!1),s=g(null),f=async()=>{c.value=!0;try{const i=await window.axios.get("/api/services");w.value=i.data}catch(i){console.error("Error loading services:",i)}finally{c.value=!1}},a=i=>{s.value=i,p.value=!0},t=async i=>{try{await window.axios.put(`/api/services/${i.id}`,{...i,active:!i.active}),await f()}catch(r){console.error("Error updating service status:",r),alert("Error updating service status")}},o=async i=>{if(confirm(`Are you sure you want to delete "${i.name}"?`))try{await window.axios.delete(`/api/services/${i.id}`),await f()}catch(r){console.error("Error deleting service:",r),alert("Error deleting service")}},y=()=>{x.value=!1,p.value=!1,s.value=null},V=()=>{y(),f()},U=i=>new Date(i).toLocaleDateString();return S(()=>{f()}),(i,r)=>(n(),$(D,{title:"My Services"},{default:E(()=>[e("div",Z,[e("div",ee,[e("div",te,[e("div",se,[e("div",oe,[r[2]||(r[2]=e("h1",{class:"text-2xl font-medium text-gray-900"},"My Services",-1)),e("button",{onClick:r[0]||(r[0]=l=>x.value=!0),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add New Service ")]),w.value.length>0?(n(),u("div",le,[(n(!0),u(M,null,A(w.value,l=>(n(),u("div",{key:l.id,class:"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"},[e("div",re,[e("div",ie,[e("h3",ne,m(l.name),1),e("span",{class:C(["px-2 py-1 text-xs font-semibold rounded-full",l.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},m(l.active?"Active":"Inactive"),3)]),e("p",ae,m(l.description),1),e("div",de,[e("div",ue,[r[3]||(r[3]=e("span",{class:"text-gray-500"},"Category:",-1)),e("span",ce,m(l.category||"Uncategorized"),1)]),e("div",me,[r[4]||(r[4]=e("span",{class:"text-gray-500"},"Price:",-1)),e("span",pe,"$"+m(l.price),1)]),e("div",be,[r[5]||(r[5]=e("span",{class:"text-gray-500"},"Duration:",-1)),e("span",ve,m(l.duration)+" minutes",1)])]),l.is_telemedicine?(n(),u("div",ge,[r[6]||(r[6]=e("div",{class:"text-xs text-gray-500 mb-2"},"Telemedicine Features:",-1)),e("div",xe,[l.supports_video?(n(),u("span",fe," Video ")):b("",!0),l.supports_audio?(n(),u("span",ye," Audio ")):b("",!0),l.supports_chat?(n(),u("span",we," Chat ")):b("",!0)])])):b("",!0),l.discount_percentage?(n(),u("div",_e,[e("div",he,[N(m(l.discount_percentage)+"% discount ",1),l.discount_valid_until?(n(),u("span",ke," until "+m(U(l.discount_valid_until)),1)):b("",!0)])])):b("",!0),e("div",$e,[e("button",{onClick:k=>a(l),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"}," Edit ",8,Ce),e("button",{onClick:k=>t(l),class:C(["text-sm font-medium",l.active?"text-red-600 hover:text-red-800":"text-green-600 hover:text-green-800"])},m(l.active?"Deactivate":"Activate"),11,Se),e("button",{onClick:k=>o(l),class:"text-red-600 hover:text-red-800 text-sm font-medium"}," Delete ",8,Ve)])])]))),128))])):(n(),u("div",Ue,[r[7]||(r[7]=e("div",{class:"text-gray-500 mb-4"},"You haven't created any services yet.",-1)),e("button",{onClick:r[1]||(r[1]=l=>x.value=!0),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Create Your First Service ")]))])])])]),x.value||p.value?(n(),$(X,{key:0,service:s.value,onClose:y,onSaved:V},null,8,["service"])):b("",!0)]),_:1}))}};export{Ne as default};
