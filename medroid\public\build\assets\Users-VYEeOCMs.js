import{r as o,o as w,b as r,e as a,i as y,u as f,p as U,w as g,g as e,x as h,y as B,z as M,F as c,q as b,t as l,n as D,f as v,s as V,P as A,j as C}from"./vendor-CGdKbVnC.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js";import"./MedroidLogo-Bx6QLK9u.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-D-1s9QYo.js";const L={class:"flex items-center justify-between"},N={class:"flex mt-2","aria-label":"Breadcrumb"},P={class:"inline-flex items-center space-x-1 md:space-x-3"},R={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},j={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},z={class:"py-12"},E={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},F={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},J={class:"p-6"},T={class:"flex flex-col sm:flex-row gap-4"},q={class:"flex-1"},Q={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},$={class:"p-6 text-gray-900 dark:text-gray-100"},G={key:0,class:"text-center py-8"},H={key:1,class:"overflow-x-auto"},I={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},K={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},O={class:"px-6 py-4 whitespace-nowrap"},W={class:"flex items-center"},X={class:"ml-4"},Y={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Z={class:"text-sm text-gray-500 dark:text-gray-400"},ee={class:"px-6 py-4 whitespace-nowrap"},te={class:"px-6 py-4 whitespace-nowrap"},se={class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"},ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},re={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},le={key:0,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},ce={__name:"Users",setup(de){const i=[{title:"Dashboard",href:"/dashboard"},{title:"Users",href:"/users"}],n=o(!1),u=o([]),p=o(""),m=o("all"),k=async()=>{n.value=!0;try{u.value=[{id:1,name:"Admin User",email:"<EMAIL>",role:"admin",status:"active",created_at:"2024-01-15",last_login:"2024-06-02"},{id:2,name:"Dr. Jane Smith",email:"<EMAIL>",role:"provider",status:"active",created_at:"2024-01-20",last_login:"2024-06-01"},{id:3,name:"John Doe",email:"<EMAIL>",role:"patient",status:"active",created_at:"2024-02-01",last_login:"2024-05-30"}]}catch(d){console.error("Error fetching users:",d)}finally{n.value=!1}},_=d=>({admin:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",provider:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",patient:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",manager:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"})[d]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return w(()=>{k()}),(d,t)=>(a(),r(c,null,[y(f(U),{title:"User Management"}),y(S,null,{header:g(()=>[e("div",L,[e("div",null,[t[3]||(t[3]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," User Management ",-1)),e("nav",N,[e("ol",P,[(a(),r(c,null,b(i,(s,x)=>e("li",{key:x,class:"inline-flex items-center"},[x<i.length-1?(a(),V(f(A),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:g(()=>[C(l(s.title),1)]),_:2},1032,["href"])):(a(),r("span",R,l(s.title),1)),x<i.length-1?(a(),r("svg",j,t[2]||(t[2]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):v("",!0)])),64))])])]),t[4]||(t[4]=e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add User ",-1))])]),default:g(()=>[e("div",z,[e("div",E,[e("div",F,[e("div",J,[e("div",T,[e("div",q,[h(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>p.value=s),type:"text",placeholder:"Search users...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[B,p.value]])]),e("div",null,[h(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>m.value=s),class:"px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},t[5]||(t[5]=[e("option",{value:"all"},"All Roles",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"provider"},"Provider",-1),e("option",{value:"patient"},"Patient",-1),e("option",{value:"manager"},"Manager",-1)]),512),[[M,m.value]])])])])]),e("div",Q,[e("div",$,[n.value?(a(),r("div",G,t[6]||(t[6]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(a(),r("div",H,[e("table",I,[t[9]||(t[9]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," User "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Role "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Last Login "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",K,[(a(!0),r(c,null,b(u.value,s=>(a(),r("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",O,[e("div",W,[t[7]||(t[7]=e("div",{class:"flex-shrink-0 h-10 w-10"},[e("div",{class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},[e("i",{class:"fas fa-user text-gray-500 dark:text-gray-400"})])],-1)),e("div",X,[e("div",Y,l(s.name),1),e("div",Z,l(s.email),1)])])]),e("td",ee,[e("span",{class:D([_(s.role),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(s.role),3)]),e("td",te,[e("span",se,l(s.status),1)]),e("td",ae,l(s.last_login),1),e("td",re,[t[8]||(t[8]=e("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," Edit ",-1)),s.role!=="admin"?(a(),r("button",le," Delete ")):v("",!0)])]))),128))])])]))])])])])]),_:1})],64))}};export{ce as default};
