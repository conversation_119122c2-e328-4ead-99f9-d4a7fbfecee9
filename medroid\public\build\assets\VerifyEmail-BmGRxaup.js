import{d as c,M as f,s as n,e as i,w as a,i as o,b as u,f as m,g as p,u as e,p as _,v,j as d}from"./vendor-B07q4Gx1.js";import{_ as y}from"./TextLink.vue_vue_type_script_setup_true_lang-riXAqVrZ.js";import{_ as b}from"./index-DUhngxR1.js";import{L as k,_ as g}from"./AuthLayout.vue_vue_type_script_setup_true_lang-BQzxigG1.js";import"./Primitive-B2yccHbY.js";const x={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E=c({__name:"VerifyEmail",props:{status:{}},setup(h){const s=f({}),l=()=>{s.post(route("verification.send"))};return(r,t)=>(i(),n(g,{title:"Verify email",description:"Please verify your email address by clicking on the link we just emailed to you."},{default:a(()=>[o(e(_),{title:"Email verification"}),r.status==="verification-link-sent"?(i(),u("div",x," A new verification link has been sent to the email address you provided during registration. ")):m("",!0),p("form",{onSubmit:v(l,["prevent"]),class:"space-y-6 text-center"},[o(e(b),{disabled:e(s).processing,variant:"secondary"},{default:a(()=>[e(s).processing?(i(),n(e(k),{key:0,class:"h-4 w-4 animate-spin"})):m("",!0),t[0]||(t[0]=d(" Resend verification email "))]),_:1},8,["disabled"]),o(y,{href:r.route("logout"),method:"post",as:"button",class:"mx-auto block text-sm"},{default:a(()=>t[1]||(t[1]=[d(" Log out ")])),_:1},8,["href"])],32)]),_:1}))}});export{E as default};
