const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AnonymousChat-D7jC8_vK.js","assets/vendor-CGdKbVnC.js","assets/MedroidLogo-Bx6QLK9u.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/MedroidLogo-Bg8BK-pN.css","assets/AnonymousChat-Fde6JZe3.css","assets/AppointmentDetail-DWL41PY-.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-_fK5CYkR.js","assets/Primitive-D-1s9QYo.js","assets/AppLayout-tn0RQdqM.css","assets/AppointmentEdit-BZIVBk_i.js","assets/Appointments-Ct_GHfrH.js","assets/Chat-3nwqN-wA.js","assets/Chats-BuGCnoOR.js","assets/Dashboard-BNRZigZm.js","assets/Dashboard_backup-D-eB35p9.js","assets/Dashboard_backup-rPoQq-Jj.css","assets/Patients-CGfRH0zq.js","assets/Payments-CBN8-hSw.js","assets/Permissions-BO036f7l.js","assets/Availability-CXNM3wuU.js","assets/Earnings-B-yIsAs2.js","assets/Patients-DaNUJfRj.js","assets/Profile-CTrOgirT.js","assets/Schedule-BliKrB0r.js","assets/Services-B8PSkwlT.js","assets/Providers-EWNOw3O-.js","assets/Services-DniJE8Tp.js","assets/Users-VYEeOCMs.js","assets/Welcome-EsipVBdW.js","assets/Welcome-qlx7atrY.css","assets/ConfirmPassword-CXtmX4d9.js","assets/InputError.vue_vue_type_script_setup_true_lang-BY1lF_tM.js","assets/index-l_ndanDW.js","assets/Label.vue_vue_type_script_setup_true_lang-DnpYyBxB.js","assets/index-CgBriE2E.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-Bx2q0EL9.js","assets/ForgotPassword-2VOyDu1H.js","assets/TextLink.vue_vue_type_script_setup_true_lang-C1OqCDkC.js","assets/Register-CAGCnJA7.js","assets/Register-DkVUU-h8.css","assets/ResetPassword-CpOp0h-b.js","assets/VerifyEmail-rb-oZAef.js","assets/Appearance-BrFF2ciq.js","assets/Layout.vue_vue_type_script_setup_true_lang-WlaPwVGG.js","assets/Password-CNK8khhL.js","assets/Profile-BSteYeI4.js"])))=>i.map(i=>d[i]);
import{r as A,o as h,a as d,L,c as R,k as T,h as D}from"./vendor-CGdKbVnC.js";const I="modulepreload",O=function(e){return"/build/"+e},v={},t=function(o,r,i){let p=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),s=(n==null?void 0:n.nonce)||(n==null?void 0:n.getAttribute("nonce"));p=Promise.allSettled(r.map(a=>{if(a=O(a),a in v)return;v[a]=!0;const _=a.endsWith(".css"),g=_?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${g}`))return;const u=document.createElement("link");if(u.rel=_?"stylesheet":I,_||(u.as="script"),u.crossOrigin="",u.href=a,s&&u.setAttribute("nonce",s),document.head.appendChild(u),_)return new Promise((f,P)=>{u.addEventListener("load",f),u.addEventListener("error",()=>P(new Error(`Unable to preload CSS for ${a}`)))})}))}function m(n){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=n,window.dispatchEvent(s),!s.defaultPrevented)throw n}return p.then(n=>{for(const s of n||[])s.status==="rejected"&&m(s.reason);return o().catch(m)})};async function y(e,o){for(const r of Array.isArray(e)?e:[e]){const i=o[r];if(!(typeof i>"u"))return typeof i=="function"?i():i}throw new Error(`Page not found: ${e}`)}function c(e){if(!(typeof window>"u"))if(e==="system"){const r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",r==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const V=(e,o,r=365)=>{if(typeof document>"u")return;const i=r*24*60*60;document.cookie=`${e}=${o};path=/;max-age=${i};SameSite=Lax`},w=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),E=()=>typeof window>"u"?null:localStorage.getItem("appearance"),k=()=>{const e=E();c(e||"system")};function S(){var o;if(typeof window>"u")return;const e=E();c(e||"system"),(o=w())==null||o.addEventListener("change",k)}function $(){const e=A("system");h(()=>{const r=localStorage.getItem("appearance");r&&(e.value=r)});function o(r){e.value=r,localStorage.setItem("appearance",r),V("appearance",r),c(r)}return{appearance:e,updateAppearance:o}}const C="Laravel";d.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";d.defaults.withCredentials=!0;const l=document.head.querySelector('meta[name="csrf-token"]');l&&(d.defaults.headers.common["X-CSRF-TOKEN"]=l.content);window.axios=d;L({title:e=>`${e} - ${C}`,resolve:e=>y(`./pages/${e}.vue`,Object.assign({"./pages/AnonymousChat.vue":()=>t(()=>import("./AnonymousChat-D7jC8_vK.js"),__vite__mapDeps([0,1,2,3,4,5])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-DWL41PY-.js"),__vite__mapDeps([6,7,1,2,3,4,8,9])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-BZIVBk_i.js"),__vite__mapDeps([10,1,7,2,3,4,8,9])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-Ct_GHfrH.js"),__vite__mapDeps([11,7,1,2,3,4,8,9])),"./pages/Chat.vue":()=>t(()=>import("./Chat-3nwqN-wA.js"),__vite__mapDeps([12,1,7,2,3,4,8,9])),"./pages/Chats.vue":()=>t(()=>import("./Chats-BuGCnoOR.js"),__vite__mapDeps([13,7,1,2,3,4,8,9])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-BNRZigZm.js"),__vite__mapDeps([14,1,7,2,3,4,8,9])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-D-eB35p9.js"),__vite__mapDeps([15,1,7,2,3,4,8,9,16])),"./pages/Patients.vue":()=>t(()=>import("./Patients-CGfRH0zq.js"),__vite__mapDeps([17,7,1,2,3,4,8,9])),"./pages/Payments.vue":()=>t(()=>import("./Payments-CBN8-hSw.js"),__vite__mapDeps([18,7,1,2,3,4,8,9])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-BO036f7l.js"),__vite__mapDeps([19,7,1,2,3,4,8,9])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-CXNM3wuU.js"),__vite__mapDeps([20,1,7,2,3,4,8,9])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-B-yIsAs2.js"),__vite__mapDeps([21,7,1,2,3,4,8,9])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-DaNUJfRj.js"),__vite__mapDeps([22,1,7,2,3,4,8,9])),"./pages/Provider/Profile.vue":()=>t(()=>import("./Profile-CTrOgirT.js"),__vite__mapDeps([23,1,7,2,3,4,8,9])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-BliKrB0r.js"),__vite__mapDeps([24,1,7,2,3,4,8,9])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-B8PSkwlT.js"),__vite__mapDeps([25,7,1,2,3,4,8,9])),"./pages/Providers.vue":()=>t(()=>import("./Providers-EWNOw3O-.js"),__vite__mapDeps([26,7,1,2,3,4,8,9])),"./pages/Services.vue":()=>t(()=>import("./Services-DniJE8Tp.js"),__vite__mapDeps([27,1,7,2,3,4,8,9])),"./pages/Users.vue":()=>t(()=>import("./Users-VYEeOCMs.js"),__vite__mapDeps([28,1,7,2,3,4,8,9])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-EsipVBdW.js"),__vite__mapDeps([29,1,3,30])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-CXtmX4d9.js"),__vite__mapDeps([31,1,32,33,8,34,35,36])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-2VOyDu1H.js"),__vite__mapDeps([37,1,32,38,33,8,34,35,36])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-CAGCnJA7.js"),__vite__mapDeps([39,1,32,3,40])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-CpOp0h-b.js"),__vite__mapDeps([41,1,32,33,8,34,35,36])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-rb-oZAef.js"),__vite__mapDeps([42,1,38,33,8,36])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-BrFF2ciq.js"),__vite__mapDeps([43,1,8,44,33,35,7,2,3,4,9])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-CNK8khhL.js"),__vite__mapDeps([45,1,32,7,2,3,4,8,9,44,33,35,34])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-BSteYeI4.js"),__vite__mapDeps([46,1,44,33,8,35,32,34,7,2,3,4,9]))})),setup({el:e,App:o,props:r,plugin:i}){R({render:()=>D(o,r)}).use(i).use(T).mount(e)},progress:{color:"#4B5563"}});S();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(let o of e)(o.scope.includes("datadog")||o.scope.includes("sw.js"))&&o.unregister()}).catch(function(e){});export{$ as u};
