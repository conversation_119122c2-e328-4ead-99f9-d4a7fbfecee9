const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AnonymousChat-Bm6bBEOe.js","assets/vendor-B07q4Gx1.js","assets/MedroidLogo-B0q18fAd.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/MedroidLogo-Bg8BK-pN.css","assets/AnonymousChat-Fde6JZe3.css","assets/AppointmentDetail-BSaZcHqF.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-5u6-YWsx.js","assets/Primitive-B2yccHbY.js","assets/AppLayout-tn0RQdqM.css","assets/AppointmentEdit-DkXDorJ5.js","assets/Appointments-DlF0a5th.js","assets/Chat-X_xdHAWL.js","assets/Chats-CmiyHShH.js","assets/Dashboard-DO-ms-Ic.js","assets/Dashboard_backup-BreF5rlw.js","assets/Dashboard_backup-rPoQq-Jj.css","assets/Patients-DEugOd5F.js","assets/Payments-DbfEstzg.js","assets/Permissions-CROIgIoj.js","assets/Availability-CwsYN9sr.js","assets/Earnings-C0Y2vwKc.js","assets/Patients-UrS-X7Cc.js","assets/Profile-g4hsr0bJ.js","assets/Schedule-BN6RXFoL.js","assets/Services-DvpWvYTa.js","assets/Providers-CMyv-MIa.js","assets/Services-Dha0G_IH.js","assets/Users-CxPfCeJB.js","assets/Welcome-CEY1P1Sn.js","assets/Welcome-qlx7atrY.css","assets/ConfirmPassword-Bhv_Y-CS.js","assets/InputError.vue_vue_type_script_setup_true_lang-DsycJfG5.js","assets/index-DUhngxR1.js","assets/Label.vue_vue_type_script_setup_true_lang-BRhg_Suh.js","assets/index-_OBvq0Oi.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-BQzxigG1.js","assets/ForgotPassword-FUV6N8Qm.js","assets/TextLink.vue_vue_type_script_setup_true_lang-riXAqVrZ.js","assets/Register-QnUCRnAl.js","assets/Register-DkVUU-h8.css","assets/ResetPassword-BSfkDdkU.js","assets/VerifyEmail-BmGRxaup.js","assets/Appearance-cdvQXMCr.js","assets/Layout.vue_vue_type_script_setup_true_lang-EBWU4Tc5.js","assets/Password-D7vMwAiV.js","assets/Profile-CyDXk7QI.js"])))=>i.map(i=>d[i]);
import{r as A,o as h,a as d,L,c as R,k as T,h as D}from"./vendor-B07q4Gx1.js";const I="modulepreload",O=function(e){return"/build/"+e},v={},t=function(o,r,i){let p=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),s=(n==null?void 0:n.nonce)||(n==null?void 0:n.getAttribute("nonce"));p=Promise.allSettled(r.map(a=>{if(a=O(a),a in v)return;v[a]=!0;const _=a.endsWith(".css"),g=_?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${g}`))return;const u=document.createElement("link");if(u.rel=_?"stylesheet":I,_||(u.as="script"),u.crossOrigin="",u.href=a,s&&u.setAttribute("nonce",s),document.head.appendChild(u),_)return new Promise((f,P)=>{u.addEventListener("load",f),u.addEventListener("error",()=>P(new Error(`Unable to preload CSS for ${a}`)))})}))}function m(n){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=n,window.dispatchEvent(s),!s.defaultPrevented)throw n}return p.then(n=>{for(const s of n||[])s.status==="rejected"&&m(s.reason);return o().catch(m)})};async function y(e,o){for(const r of Array.isArray(e)?e:[e]){const i=o[r];if(!(typeof i>"u"))return typeof i=="function"?i():i}throw new Error(`Page not found: ${e}`)}function c(e){if(!(typeof window>"u"))if(e==="system"){const r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",r==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const V=(e,o,r=365)=>{if(typeof document>"u")return;const i=r*24*60*60;document.cookie=`${e}=${o};path=/;max-age=${i};SameSite=Lax`},w=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),E=()=>typeof window>"u"?null:localStorage.getItem("appearance"),k=()=>{const e=E();c(e||"system")};function S(){var o;if(typeof window>"u")return;const e=E();c(e||"system"),(o=w())==null||o.addEventListener("change",k)}function $(){const e=A("system");h(()=>{const r=localStorage.getItem("appearance");r&&(e.value=r)});function o(r){e.value=r,localStorage.setItem("appearance",r),V("appearance",r),c(r)}return{appearance:e,updateAppearance:o}}const C="Laravel";d.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";d.defaults.withCredentials=!0;const l=document.head.querySelector('meta[name="csrf-token"]');l&&(d.defaults.headers.common["X-CSRF-TOKEN"]=l.content);window.axios=d;L({title:e=>`${e} - ${C}`,resolve:e=>y(`./pages/${e}.vue`,Object.assign({"./pages/AnonymousChat.vue":()=>t(()=>import("./AnonymousChat-Bm6bBEOe.js"),__vite__mapDeps([0,1,2,3,4,5])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-BSaZcHqF.js"),__vite__mapDeps([6,7,1,2,3,4,8,9])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-DkXDorJ5.js"),__vite__mapDeps([10,1,7,2,3,4,8,9])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-DlF0a5th.js"),__vite__mapDeps([11,7,1,2,3,4,8,9])),"./pages/Chat.vue":()=>t(()=>import("./Chat-X_xdHAWL.js"),__vite__mapDeps([12,1,7,2,3,4,8,9])),"./pages/Chats.vue":()=>t(()=>import("./Chats-CmiyHShH.js"),__vite__mapDeps([13,7,1,2,3,4,8,9])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-DO-ms-Ic.js"),__vite__mapDeps([14,1,7,2,3,4,8,9])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-BreF5rlw.js"),__vite__mapDeps([15,1,7,2,3,4,8,9,16])),"./pages/Patients.vue":()=>t(()=>import("./Patients-DEugOd5F.js"),__vite__mapDeps([17,7,1,2,3,4,8,9])),"./pages/Payments.vue":()=>t(()=>import("./Payments-DbfEstzg.js"),__vite__mapDeps([18,7,1,2,3,4,8,9])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-CROIgIoj.js"),__vite__mapDeps([19,7,1,2,3,4,8,9])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-CwsYN9sr.js"),__vite__mapDeps([20,1,7,2,3,4,8,9])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-C0Y2vwKc.js"),__vite__mapDeps([21,7,1,2,3,4,8,9])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-UrS-X7Cc.js"),__vite__mapDeps([22,1,7,2,3,4,8,9])),"./pages/Provider/Profile.vue":()=>t(()=>import("./Profile-g4hsr0bJ.js"),__vite__mapDeps([23,1,7,2,3,4,8,9])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-BN6RXFoL.js"),__vite__mapDeps([24,1,7,2,3,4,8,9])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-DvpWvYTa.js"),__vite__mapDeps([25,7,1,2,3,4,8,9])),"./pages/Providers.vue":()=>t(()=>import("./Providers-CMyv-MIa.js"),__vite__mapDeps([26,7,1,2,3,4,8,9])),"./pages/Services.vue":()=>t(()=>import("./Services-Dha0G_IH.js"),__vite__mapDeps([27,1,7,2,3,4,8,9])),"./pages/Users.vue":()=>t(()=>import("./Users-CxPfCeJB.js"),__vite__mapDeps([28,1,7,2,3,4,8,9])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-CEY1P1Sn.js"),__vite__mapDeps([29,1,3,30])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-Bhv_Y-CS.js"),__vite__mapDeps([31,1,32,33,8,34,35,36])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-FUV6N8Qm.js"),__vite__mapDeps([37,1,32,38,33,8,34,35,36])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-QnUCRnAl.js"),__vite__mapDeps([39,1,32,3,40])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-BSfkDdkU.js"),__vite__mapDeps([41,1,32,33,8,34,35,36])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-BmGRxaup.js"),__vite__mapDeps([42,1,38,33,8,36])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-cdvQXMCr.js"),__vite__mapDeps([43,1,8,44,33,35,7,2,3,4,9])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-D7vMwAiV.js"),__vite__mapDeps([45,1,32,7,2,3,4,8,9,44,33,35,34])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-CyDXk7QI.js"),__vite__mapDeps([46,1,44,33,8,35,32,34,7,2,3,4,9]))})),setup({el:e,App:o,props:r,plugin:i}){R({render:()=>D(o,r)}).use(i).use(T).mount(e)},progress:{color:"#4B5563"}});S();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(let o of e)(o.scope.includes("datadog")||o.scope.includes("sw.js"))&&o.unregister()}).catch(function(e){});export{$ as u};
