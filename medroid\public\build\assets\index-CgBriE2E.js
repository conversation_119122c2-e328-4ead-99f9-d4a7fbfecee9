import{m as l,a7 as i,B as s,u as f,a8 as c,a9 as d,aa as p,ab as w,Z as g,X as v,Y as u,a5 as y}from"./vendor-CGdKbVnC.js";function m(e){return p()?(w(e),!0):!1}function _(e){let n=!1,r;const t=c(!0);return(...o)=>(n||(r=t.run(()=>e(...o)),n=!0),r)}function I(e){let n=0,r,t;const o=()=>{n-=1,t&&n<=0&&(t.stop(),r=void 0,t=void 0)};return(...a)=>(n+=1,t||(t=c(!0),r=t.run(()=>e(...a))),m(o),r)}function O(e){if(!i(e))return s(e);const n=new Proxy({},{get(r,t,o){return f(Reflect.get(e.value,t,o))},set(r,t,o){return i(e.value[t])&&!i(o)?e.value[t].value=o:e.value[t]=o,!0},deleteProperty(r,t){return Reflect.deleteProperty(e.value,t)},has(r,t){return Reflect.has(e.value,t)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return s(n)}function b(e){return O(l(e))}function x(e,...n){const r=n.flat(),t=r[0];return b(()=>Object.fromEntries(typeof t=="function"?Object.entries(u(e)).filter(([o,a])=>!t(y(a),o)):Object.entries(u(e)).filter(o=>!r.includes(o[0]))))}const S=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const D=e=>typeof e<"u",h=Object.prototype.toString,k=e=>h.call(e)==="[object Object]",B=P();function P(){var e,n;return S&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((n=window==null?void 0:window.navigator)==null?void 0:n.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function A(e){return g()}function G(e){return Array.isArray(e)?e:[e]}function T(e,n){A()&&d(e,n)}function E(e,n,r){return v(e,n,{...r,immediate:!0})}export{I as a,B as b,_ as c,D as d,G as e,m as f,k as g,S as i,x as r,T as t,E as w};
