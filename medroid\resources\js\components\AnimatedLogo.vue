<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
    size?: number;
    showText?: boolean;
    animate?: boolean;
    className?: string;
}

const props = withDefaults(defineProps<Props>(), {
    size: 120,
    showText: true,
    animate: true,
    className: ''
});

const logoRef = ref<HTMLElement>();
const textRef = ref<HTMLElement>();
const isVisible = ref(false);

onMounted(() => {
    if (props.animate) {
        setTimeout(() => {
            isVisible.value = true;
        }, 100);
    } else {
        isVisible.value = true;
    }
});
</script>

<template>
    <div :class="['flex flex-col items-center justify-center', className]">
        <!-- Animated Logo Icon -->
        <div
            ref="logoRef"
            :class="[
                'relative transition-all duration-1000 ease-out',
                isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-6 scale-95'
            ]"
            :style="{ width: `${size}px`, height: `${size}px` }"
        >
            <!-- Glow Effect -->
            <div
                v-if="animate"
                class="absolute inset-0 rounded-full bg-medroid-primary opacity-20 animate-pulse-glow"
            ></div>
            
            <!-- Main Logo Container -->
            <div
                class="relative w-full h-full rounded-full bg-medroid-primary flex items-center justify-center shadow-lg"
                :class="{ 'animate-pulse-glow': animate }"
            >
                <!-- Medical Cross Icon -->
                <svg
                    :width="size * 0.5"
                    :height="size * 0.5"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="text-white"
                >
                    <path
                        d="M12 2C13.1 2 14 2.9 14 4V8H18C19.1 8 20 8.9 20 10V14C20 15.1 19.1 16 18 16H14V20C14 21.1 13.1 22 12 22C10.9 22 10 21.1 10 20V16H6C4.9 16 4 15.1 4 14V10C4 8.9 4.9 8 6 8H10V4C10 2.9 10.9 2 12 2Z"
                        fill="currentColor"
                    />
                </svg>
                
                <!-- Pulse Rings -->
                <div
                    v-if="animate"
                    class="absolute inset-0 rounded-full border-2 border-white/30 animate-ping"
                ></div>
                <div
                    v-if="animate"
                    class="absolute inset-2 rounded-full border border-white/20 animate-ping"
                    style="animation-delay: 0.5s"
                ></div>
            </div>
        </div>

        <!-- Animated Text -->
        <div
            v-if="showText"
            ref="textRef"
            :class="[
                'mt-6 text-center transition-all duration-1000 ease-out',
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
            ]"
            style="animation-delay: 0.3s"
        >
            <h1 class="text-4xl font-bold text-medroid-primary mb-2 tracking-wide">
                Medroid
            </h1>
            <p class="text-lg text-slate-600 tracking-wide">
                Your AI Doctor
            </p>
        </div>
    </div>
</template>

<style scoped>
/* Additional custom animations for the logo */
@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: logoFloat 3s ease-in-out infinite;
}
</style>
