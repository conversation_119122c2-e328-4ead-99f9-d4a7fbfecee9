<script setup>
import { Link } from '@inertiajs/vue3';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import MedroidLogo from '@/components/MedroidLogo.vue';

const props = defineProps({
  user: {
    type: Object,
    required: true
  }
});

const collapsed = ref(false);

const userRole = computed(() => {
  return props.user?.role || 'patient';
});

// Permission checks based on backend structure
const isAdmin = computed(() => userRole.value === 'admin');
const isProvider = computed(() => userRole.value === 'provider');
const isPatient = computed(() => userRole.value === 'patient');

const canViewUsers = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view users');
});

const canViewProviders = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view providers');
});

const canViewPatients = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view patients');
});

const canViewAnalytics = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view analytics');
});

const canViewAppointments = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view appointments');
});

const canViewPayments = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view payments');
});

const canViewChats = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view chats');
});

const canManageNotifications = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage notifications');
});

const canManageEmailTemplates = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage email templates');
});

const canManagePermissions = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage permissions');
});

const canManageServices = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage services');
});

// Group permission checks
const hasAnyManagementPermission = computed(() => {
  return canViewUsers.value || canViewProviders.value || canViewPatients.value ||
         canViewAppointments.value || canViewPayments.value || canViewChats.value || canManageServices.value;
});

const hasAnySystemPermission = computed(() => {
  return canManagePermissions.value || canManageEmailTemplates.value || canManageNotifications.value;
});

const toggleSidebar = () => {
  collapsed.value = !collapsed.value;
  localStorage.setItem('app-sidebar-collapsed', collapsed.value);
};

const handleResize = () => {
  if (window.innerWidth < 768 && !collapsed.value) {
    collapsed.value = true;
    localStorage.setItem('app-sidebar-collapsed', 'true');
  }
};

onMounted(() => {
  const savedState = localStorage.getItem('app-sidebar-collapsed');
  if (savedState !== null) {
    collapsed.value = savedState === 'true';
  }

  if (window.innerWidth < 768) {
    collapsed.value = true;
  }

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="h-screen flex-shrink-0" :class="[collapsed ? 'w-16' : 'w-64', 'transition-all duration-300']">
    <div class="h-full bg-gray-800 text-white flex flex-col">
      <div class="flex items-center justify-between p-4 border-b border-gray-700">
        <div class="flex items-center">
          <MedroidLogo v-if="!collapsed" :size="32" />
          <MedroidLogo v-else :size="24" />
          <span v-if="!collapsed" class="ml-3 text-xl font-semibold">Medroid</span>
        </div>
        <button @click="toggleSidebar" class="text-gray-400 hover:text-white focus:outline-none">
          <i class="fas" :class="collapsed ? 'fa-angle-right' : 'fa-angle-left'"></i>
        </button>
      </div>

      <div class="flex-1 overflow-y-auto py-4 flex flex-col justify-between">
        <nav>
          <ul class="space-y-1">
            <!-- Dashboard -->
            <li>
              <Link href="/dashboard"
                class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                :class="{ 'bg-gray-700 text-white': $page.url === '/dashboard' }">
                <i class="fas fa-tachometer-alt w-6 text-center"></i>
                <span v-if="!collapsed" class="ml-3">Dashboard</span>
              </Link>
            </li>

            <!-- Patient-specific menu items -->
            <template v-if="isPatient">
              <li>
                <Link href="/chat"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url === '/chat' }">
                  <i class="fas fa-robot w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">AI Chat</span>
                </Link>
              </li>

              <li>
                <Link href="/providers"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/providers') }">
                  <i class="fas fa-user-md w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Find Doctors</span>
                </Link>
              </li>

              <li>
                <Link href="/appointments"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/appointments') }">
                  <i class="fas fa-calendar-alt w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">My Appointments</span>
                </Link>
              </li>

              <li>
                <Link href="/feed"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url === '/feed' }">
                  <i class="fas fa-home w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Health Feed</span>
                </Link>
              </li>

              <li>
                <Link href="/credits"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/credits') }">
                  <i class="fas fa-coins w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Credits</span>
                </Link>
              </li>
            </template>

            <!-- Provider-specific menu items -->
            <template v-if="isProvider">
              <!-- Provider Section Header -->
              <li v-if="!collapsed" class="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-4">
                Provider Tools
              </li>

              <li>
                <Link href="/provider/availability"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/provider/availability') }">
                  <i class="fas fa-calendar-alt w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Availability</span>
                </Link>
              </li>

              <li>
                <Link href="/provider/services"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/provider/services') }">
                  <i class="fas fa-concierge-bell w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">My Services</span>
                </Link>
              </li>

              <li>
                <Link href="/provider/schedule"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/provider/schedule') }">
                  <i class="fas fa-calendar-check w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">My Schedule</span>
                </Link>
              </li>

              <li>
                <Link href="/appointments"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/appointments') }">
                  <i class="fas fa-calendar-plus w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Appointments</span>
                </Link>
              </li>

              <li>
                <Link href="/provider/patients"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/provider/patients') }">
                  <i class="fas fa-users w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">My Patients</span>
                </Link>
              </li>

              <li>
                <Link href="/provider/earnings"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/provider/earnings') }">
                  <i class="fas fa-dollar-sign w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Earnings</span>
                </Link>
              </li>

              <li>
                <Link href="/provider/profile"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/provider/profile') }">
                  <i class="fas fa-user-md w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Provider Profile</span>
                </Link>
              </li>
            </template>

            <!-- Management menu items - permission-based visibility -->
            <template v-if="hasAnyManagementPermission">
              <!-- Management Section Header -->
              <li v-if="!collapsed" class="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-4">
                Management
              </li>

              <li v-if="canViewUsers">
                <Link href="/users"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/users') }">
                  <i class="fas fa-users w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Users</span>
                </Link>
              </li>

              <li v-if="canViewProviders">
                <Link href="/providers"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/providers') && !$page.url.startsWith('/providers/') }">
                  <i class="fas fa-user-md w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Providers</span>
                </Link>
              </li>

              <li v-if="canViewPatients">
                <Link href="/patients"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/patients') }">
                  <i class="fas fa-user-injured w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Patients</span>
                </Link>
              </li>

              <li v-if="canViewAppointments">
                <Link href="/appointments"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/appointments') && !$page.url.startsWith('/appointments/') }">
                  <i class="fas fa-calendar-alt w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Appointments</span>
                </Link>
              </li>

              <li v-if="canViewAnalytics">
                <Link href="/admin/analytics"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/admin/analytics') }">
                  <i class="fas fa-chart-bar w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Analytics</span>
                </Link>
              </li>

              <li v-if="canViewPayments">
                <Link href="/payments"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/payments') }">
                  <i class="fas fa-credit-card w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Payments</span>
                </Link>
              </li>

              <li v-if="canViewChats">
                <Link href="/chats"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/chats') && !$page.url.startsWith('/chat') }">
                  <i class="fas fa-comments w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Chats</span>
                </Link>
              </li>

              <li v-if="canManageServices">
                <Link href="/services"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/services') }">
                  <i class="fas fa-concierge-bell w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Services</span>
                </Link>
              </li>

              <!-- System Section Header -->
              <li v-if="!collapsed && hasAnySystemPermission" class="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-4">
                System
              </li>

              <li v-if="canManagePermissions">
                <Link href="/permissions"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/permissions') }">
                  <i class="fas fa-key w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Permissions</span>
                </Link>
              </li>

              <li v-if="canManageEmailTemplates">
                <Link href="/email-templates"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/email-templates') }">
                  <i class="fas fa-envelope w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Email Templates</span>
                </Link>
              </li>

              <li v-if="canManageNotifications">
                <Link href="/notifications"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/notifications') }">
                  <i class="fas fa-bell w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Notifications</span>
                </Link>
              </li>

              <li>
                <Link href="/admin/clubs"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/admin/clubs') }">
                  <i class="fas fa-trophy w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Club Management</span>
                </Link>
              </li>

              <li>
                <Link href="/admin/referrals"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/admin/referrals') }">
                  <i class="fas fa-share-alt w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">Referral Management</span>
                </Link>
              </li>

              <li>
                <Link href="/admin/settings"
                  class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                  :class="{ 'bg-gray-700 text-white': $page.url.startsWith('/admin/settings') }">
                  <i class="fas fa-cog w-6 text-center"></i>
                  <span v-if="!collapsed" class="ml-3">System Settings</span>
                </Link>
              </li>
            </template>

            <!-- User Section -->
            <li v-if="!collapsed" class="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Account
            </li>

            <li>
              <Link href="/settings/profile"
                class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200"
                :class="{ 'bg-gray-700 text-white': $page.url === '/settings/profile' }">
                <i class="fas fa-user w-6 text-center"></i>
                <span v-if="!collapsed" class="ml-3">Profile</span>
              </Link>
            </li>
          </ul>
        </nav>

        <!-- Logout button at the bottom -->
        <div class="mt-auto pt-4 border-t border-gray-700">
          <Link href="/logout" method="post"
            class="w-full flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
            <i class="fas fa-sign-out-alt w-6 text-center"></i>
            <span v-if="!collapsed" class="ml-3">Logout</span>
          </Link>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* All styling is handled by Tailwind CSS classes */
</style>
