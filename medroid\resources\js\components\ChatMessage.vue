<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
    message: {
        id: number;
        type: 'user' | 'ai';
        content: string;
        timestamp: Date;
    };
    animate?: boolean;
    delay?: number;
}

const props = withDefaults(defineProps<Props>(), {
    animate: true,
    delay: 0
});

const messageRef = ref<HTMLElement>();
const isVisible = ref(false);
const isTyping = ref(false);
const displayedText = ref('');

onMounted(() => {
    if (props.animate) {
        setTimeout(() => {
            isVisible.value = true;
            if (props.message.type === 'ai') {
                startTypingAnimation();
            } else {
                displayedText.value = props.message.content;
            }
        }, props.delay);
    } else {
        isVisible.value = true;
        displayedText.value = props.message.content;
    }
});

const startTypingAnimation = () => {
    isTyping.value = true;
    const text = props.message.content;
    let currentIndex = 0;
    
    const typeInterval = setInterval(() => {
        if (currentIndex < text.length) {
            displayedText.value += text[currentIndex];
            currentIndex++;
        } else {
            clearInterval(typeInterval);
            isTyping.value = false;
        }
    }, 30); // Typing speed
};

const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};
</script>

<template>
    <div
        ref="messageRef"
        :class="[
            'flex mb-4 transition-all duration-500 ease-out',
            message.type === 'user' ? 'justify-end' : 'justify-start',
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        ]"
    >
        <!-- AI Avatar -->
        <div
            v-if="message.type === 'ai'"
            class="flex-shrink-0 mr-3"
        >
            <div class="w-8 h-8 rounded-full bg-medroid-primary flex items-center justify-center shadow-md">
                <svg
                    class="w-5 h-5 text-white"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <path d="M12 2C13.1 2 14 2.9 14 4V8H18C19.1 8 20 8.9 20 10V14C20 15.1 19.1 16 18 16H14V20C14 21.1 13.1 22 12 22C10.9 22 10 21.1 10 20V16H6C4.9 16 4 15.1 4 14V10C4 8.9 4.9 8 6 8H10V4C10 2.9 10.9 2 12 2Z" />
                </svg>
            </div>
        </div>

        <!-- Message Bubble -->
        <div
            :class="[
                'max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-md relative',
                message.type === 'user'
                    ? 'bg-medroid-primary text-white rounded-br-md'
                    : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md'
            ]"
        >
            <!-- Message Content -->
            <div class="text-sm leading-relaxed">
                <span v-if="message.type === 'ai' && animate">
                    {{ displayedText }}
                    <span
                        v-if="isTyping"
                        class="inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse"
                    ></span>
                </span>
                <span v-else>{{ message.content }}</span>
            </div>

            <!-- Timestamp -->
            <div
                :class="[
                    'text-xs mt-1 opacity-70',
                    message.type === 'user' ? 'text-white' : 'text-gray-500'
                ]"
            >
                {{ formatTime(message.timestamp) }}
            </div>

            <!-- Message Tail -->
            <div
                v-if="message.type === 'user'"
                class="absolute top-3 -right-1 w-3 h-3 bg-medroid-primary transform rotate-45"
            ></div>
            <div
                v-else
                class="absolute top-3 -left-1 w-3 h-3 bg-white border-l border-b border-gray-200 transform rotate-45"
            ></div>
        </div>

        <!-- User Avatar -->
        <div
            v-if="message.type === 'user'"
            class="flex-shrink-0 ml-3"
        >
            <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center shadow-md">
                <svg
                    class="w-5 h-5 text-gray-600"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <path d="M12 6C13.1 6 14 6.9 14 8C14 9.1 13.1 10 12 10C10.9 10 10 9.1 10 8C10 6.9 10.9 6 12 6ZM12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4ZM12 13C9.33 13 4 14.33 4 17V20H20V17C20 14.33 14.67 13 12 13ZM12 14.9C14.97 14.9 18.1 16.36 18.1 17V18.1H5.9V17C5.9 16.36 9.03 14.9 12 14.9Z" />
                </svg>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Typing indicator animation */
@keyframes typing-cursor {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.animate-typing-cursor {
    animation: typing-cursor 1s infinite;
}
</style>
