<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface FeatureSlide {
    icon: string;
    title: string;
    subtitle: string;
    description: string;
    color: string;
}

interface Props {
    slides: FeatureSlide[];
    autoPlay?: boolean;
    interval?: number;
}

const props = withDefaults(defineProps<Props>(), {
    autoPlay: true,
    interval: 4000
});

const currentSlide = ref(0);
const isAnimating = ref(false);
const slideContainer = ref<HTMLElement>();

let autoPlayTimer: NodeJS.Timeout | null = null;

const nextSlide = () => {
    if (isAnimating.value) return;
    
    isAnimating.value = true;
    currentSlide.value = (currentSlide.value + 1) % props.slides.length;
    
    setTimeout(() => {
        isAnimating.value = false;
    }, 500);
};

const goToSlide = (index: number) => {
    if (isAnimating.value || index === currentSlide.value) return;
    
    isAnimating.value = true;
    currentSlide.value = index;
    
    setTimeout(() => {
        isAnimating.value = false;
    }, 500);
};

const startAutoPlay = () => {
    if (props.autoPlay && !autoPlayTimer) {
        autoPlayTimer = setInterval(nextSlide, props.interval);
    }
};

const stopAutoPlay = () => {
    if (autoPlayTimer) {
        clearInterval(autoPlayTimer);
        autoPlayTimer = null;
    }
};

onMounted(() => {
    startAutoPlay();
});

// Icon mapping for common icons
const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, string> = {
        'psychology_outlined': 'M12 2C13.1 2 14 2.9 14 4V8H18C19.1 8 20 8.9 20 10V14C20 15.1 19.1 16 18 16H14V20C14 21.1 13.1 22 12 22C10.9 22 10 21.1 10 20V16H6C4.9 16 4 15.1 4 14V10C4 8.9 4.9 8 6 8H10V4C10 2.9 10.9 2 12 2Z',
        'calendar_month_outlined': 'M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H12V15H7V10Z',
        'local_hospital_outlined': 'M19 8H17V6C17 5.45 16.55 5 16 5H8C7.45 5 7 5.45 7 6V8H5C4.45 8 4 8.45 4 9V15C4 15.55 4.45 16 5 16H7V18C7 18.55 7.45 19 8 19H16C16.55 19 17 18.55 17 18V16H19C19.55 16 20 15.55 20 15V9C20 8.45 19.55 8 19 8Z',
        'people_outline': 'M16 4C18.21 4 20 5.79 20 8C20 10.21 18.21 12 16 12C13.79 12 12 10.21 12 8C12 5.79 13.79 4 16 4ZM16 6C14.9 6 14 6.9 14 8C14 9.1 14.9 10 16 10C17.1 10 18 9.1 18 8C18 6.9 17.1 6 16 6ZM8 4C10.21 4 12 5.79 12 8C12 10.21 10.21 12 8 12C5.79 12 4 10.21 4 8C4 5.79 5.79 4 8 4ZM8 6C6.9 6 6 6.9 6 8C6 9.1 6.9 10 8 10C9.1 10 10 9.1 10 8C10 6.9 9.1 6 8 6ZM8 13C10.67 13 16 14.33 16 17V20H0V17C0 14.33 5.33 13 8 13ZM8 14.9C5.03 14.9 1.9 16.36 1.9 17V18.1H14.1V17C14.1 16.36 10.97 14.9 8 14.9ZM16 13C18.67 13 24 14.33 24 17V20H18V18.1H22.1V17C22.1 16.36 18.97 14.9 16 14.9C15.64 14.9 15.23 14.93 14.77 14.99C15.32 15.59 15.6 16.27 15.6 17V20H16V13Z',
        'person_outline': 'M12 6C13.1 6 14 6.9 14 8C14 9.1 13.1 10 12 10C10.9 10 10 9.1 10 8C10 6.9 10.9 6 12 6ZM12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4ZM12 13C9.33 13 4 14.33 4 17V20H20V17C20 14.33 14.67 13 12 13ZM12 14.9C14.97 14.9 18.1 16.36 18.1 17V18.1H5.9V17C5.9 16.36 9.03 14.9 12 14.9Z'
    };
    
    return iconMap[iconName] || iconMap['psychology_outlined'];
};
</script>

<template>
    <div 
        class="relative w-full h-80 overflow-hidden rounded-xl bg-gradient-to-br from-gray-50 to-white shadow-lg"
        @mouseenter="stopAutoPlay"
        @mouseleave="startAutoPlay"
    >
        <!-- Slides Container -->
        <div 
            ref="slideContainer"
            class="relative w-full h-full"
        >
            <div
                v-for="(slide, index) in slides"
                :key="index"
                :class="[
                    'absolute inset-0 flex flex-col items-center justify-center p-8 text-center transition-all duration-500 ease-in-out',
                    index === currentSlide 
                        ? 'opacity-100 translate-x-0' 
                        : index < currentSlide 
                            ? 'opacity-0 -translate-x-full' 
                            : 'opacity-0 translate-x-full'
                ]"
            >
                <!-- Icon -->
                <div 
                    class="w-16 h-16 rounded-full flex items-center justify-center mb-4 shadow-lg"
                    :style="{ backgroundColor: slide.color }"
                >
                    <svg
                        class="w-8 h-8 text-white"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                    >
                        <path :d="getIconComponent(slide.icon)" />
                    </svg>
                </div>

                <!-- Content -->
                <h3 class="text-2xl font-bold text-gray-800 mb-2">
                    {{ slide.title }}
                </h3>
                <p class="text-lg font-medium mb-3" :style="{ color: slide.color }">
                    {{ slide.subtitle }}
                </p>
                <p class="text-gray-600 leading-relaxed max-w-md">
                    {{ slide.description }}
                </p>
            </div>
        </div>

        <!-- Dots Indicator -->
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <button
                v-for="(slide, index) in slides"
                :key="index"
                @click="goToSlide(index)"
                :class="[
                    'w-3 h-3 rounded-full transition-all duration-200',
                    index === currentSlide 
                        ? 'bg-gray-800 scale-110' 
                        : 'bg-gray-400 hover:bg-gray-600'
                ]"
            />
        </div>

        <!-- Navigation Arrows -->
        <button
            @click="goToSlide((currentSlide - 1 + slides.length) % slides.length)"
            class="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/80 hover:bg-white shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
        >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>
        
        <button
            @click="nextSlide"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/80 hover:bg-white shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
        >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>
</template>
