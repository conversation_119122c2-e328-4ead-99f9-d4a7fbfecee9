<script setup lang="ts">
import { computed } from 'vue';

interface Props {
    variant?: 'primary' | 'secondary' | 'accent';
    size?: 'sm' | 'md' | 'lg';
    disabled?: boolean;
    loading?: boolean;
    fullWidth?: boolean;
    className?: string;
}

const props = withDefaults(defineProps<Props>(), {
    variant: 'primary',
    size: 'md',
    disabled: false,
    loading: false,
    fullWidth: false,
    className: ''
});

const emit = defineEmits<{
    click: [event: MouseEvent];
}>();

const buttonClasses = computed(() => {
    const baseClasses = [
        'relative',
        'inline-flex',
        'items-center',
        'justify-center',
        'font-medium',
        'rounded-lg',
        'transition-all',
        'duration-200',
        'transform',
        'hover:scale-105',
        'active:scale-95',
        'focus:outline-none',
        'focus:ring-2',
        'focus:ring-offset-2',
        'disabled:opacity-50',
        'disabled:cursor-not-allowed',
        'disabled:transform-none'
    ];

    // Size classes
    const sizeClasses = {
        sm: ['px-4', 'py-2', 'text-sm'],
        md: ['px-6', 'py-3', 'text-base'],
        lg: ['px-8', 'py-4', 'text-lg']
    };

    // Variant classes
    const variantClasses = {
        primary: [
            'bg-medroid-primary',
            'text-white',
            'shadow-lg',
            'hover:shadow-xl',
            'focus:ring-[var(--color-teal-surge)]'
        ],
        secondary: [
            'bg-medroid-secondary',
            'text-white',
            'shadow-lg',
            'hover:shadow-xl',
            'focus:ring-[var(--color-midnight-navy)]'
        ],
        accent: [
            'bg-medroid-accent',
            'text-white',
            'shadow-lg',
            'hover:shadow-xl',
            'focus:ring-[var(--color-coral-pop)]'
        ]
    };

    const widthClasses = props.fullWidth ? ['w-full'] : [];

    return [
        ...baseClasses,
        ...sizeClasses[props.size],
        ...variantClasses[props.variant],
        ...widthClasses,
        props.className
    ].filter(Boolean);
});

const handleClick = (event: MouseEvent) => {
    if (!props.disabled && !props.loading) {
        emit('click', event);
    }
};
</script>

<template>
    <button
        :class="buttonClasses"
        :disabled="disabled || loading"
        @click="handleClick"
    >
        <!-- Loading Spinner -->
        <div
            v-if="loading"
            class="absolute inset-0 flex items-center justify-center"
        >
            <svg
                class="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg>
        </div>

        <!-- Button Content -->
        <span :class="{ 'opacity-0': loading }">
            <slot />
        </span>

        <!-- Glow Effect on Hover -->
        <div
            class="absolute inset-0 rounded-lg opacity-0 transition-opacity duration-200 hover:opacity-20"
            :class="{
                'bg-white': variant === 'primary' || variant === 'secondary' || variant === 'accent'
            }"
        ></div>
    </button>
</template>

<style scoped>
/* Additional hover effects */
button:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

button:active {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
