<template>
  <div class="medroid-logo" :style="containerStyle">
    <!-- Try to load the actual logo image first -->
    <img
      v-if="!imageError"
      :src="logoPath"
      :alt="alt"
      :style="imageStyle"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    
    <!-- Fallback to gradient circle with icon if image fails to load -->
    <div
      v-else
      class="logo-fallback"
      :style="fallbackStyle"
    >
      <i 
        v-if="showIcon"
        :class="iconClass"
        :style="iconStyle"
      ></i>
      <span
        v-else
        class="logo-text"
        :style="textStyle"
      >
        {{ fallbackText }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props definition
const props = defineProps({
  size: {
    type: Number,
    default: 32
  },
  color: {
    type: String,
    default: null
  },
  useDarkVersion: {
    type: Boolean,
    default: false
  },
  showShadow: {
    type: Boolean,
    default: true
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  iconClass: {
    type: String,
    default: 'fas fa-heart'
  },
  fallbackText: {
    type: String,
    default: 'M'
  },
  alt: {
    type: String,
    default: 'Medroid Logo'
  }
})

// Reactive data
const imageError = ref(false)
const imageLoaded = ref(false)

// Computed properties
const logoPath = computed(() => {
  return props.useDarkVersion 
    ? '/images/medroid_icon_dark.png'
    : '/images/medroid_icon.png'
})

const containerStyle = computed(() => ({
  display: 'inline-block',
  width: `${props.size}px`,
  height: `${props.size}px`,
}))

const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
  display: 'block'
}))

const fallbackStyle = computed(() => {
  const primaryColor = props.color || '#17C3B2' // tealSurge
  const shadowColor = 'rgba(23, 195, 178, 0.3)' // tealSurge with opacity
  
  return {
    width: '100%',
    height: '100%',
    borderRadius: '50%',
    background: `linear-gradient(135deg, ${primaryColor} 0%, #8BE9C8 100%)`, // tealSurge to mintGlow
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: props.showShadow 
      ? `0 ${props.size * 0.1}px ${props.size * 0.15}px ${shadowColor}`
      : 'none'
  }
})

const iconStyle = computed(() => ({
  fontSize: `${props.size * 0.5}px`,
  color: 'white'
}))

const textStyle = computed(() => ({
  fontSize: `${props.size * 0.6}px`,
  fontWeight: 'bold',
  color: 'white',
  fontFamily: 'Arial, sans-serif'
}))

// Methods
const handleImageError = () => {
  imageError.value = true
}

const handleImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
}
</script>

<style scoped>
.medroid-logo {
  display: inline-block;
}

.logo-fallback {
  position: relative;
}

.logo-text {
  user-select: none;
  line-height: 1;
}

/* Animation for the fallback logo */
.logo-fallback {
  transition: transform 0.2s ease-in-out;
}

.logo-fallback:hover {
  transform: scale(1.05);
}
</style>
