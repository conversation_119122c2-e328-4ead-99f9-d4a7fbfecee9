<template>
  <div class="medroid-logo-with-text" :style="containerStyle">
    <!-- Logo -->
    <MedroidLogo
      :size="logoSize"
      :color="logoColor"
      :use-dark-version="useDarkVersion"
      :show-shadow="showShadow"
      :show-icon="showIcon"
      :icon-class="iconClass"
    />
    
    <!-- Text -->
    <div class="logo-text-container" :style="textContainerStyle">
      <h1 class="logo-title" :style="titleStyle">
        {{ title }}
      </h1>
      <p class="logo-subtitle" :style="subtitleStyle">
        {{ subtitle }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import MedroidLogo from './MedroidLogo.vue'

// Props definition
const props = defineProps({
  logoSize: {
    type: Number,
    default: 60
  },
  fontSize: {
    type: Number,
    default: 24
  },
  showShadow: {
    type: Boolean,
    default: true
  },
  useDarkVersion: {
    type: Boolean,
    default: false
  },
  textColor: {
    type: String,
    default: null
  },
  logoColor: {
    type: String,
    default: null
  },
  title: {
    type: String,
    default: 'Medroid'
  },
  subtitle: {
    type: String,
    default: 'Your AI Doctor'
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  iconClass: {
    type: String,
    default: 'fas fa-heart'
  },
  spacing: {
    type: Number,
    default: 12
  },
  alignment: {
    type: String,
    default: 'center', // 'left', 'center', 'right'
    validator: (value) => ['left', 'center', 'right'].includes(value)
  }
})

// Computed properties
const containerStyle = computed(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: props.alignment === 'left' ? 'flex-start' : 
              props.alignment === 'right' ? 'flex-end' : 'center',
  gap: `${props.spacing}px`
}))

const textContainerStyle = computed(() => ({
  textAlign: props.alignment
}))

const defaultTextColor = computed(() => {
  return props.textColor || '#032B3E' // midnightNavy
})

const titleStyle = computed(() => ({
  fontSize: `${props.fontSize}px`,
  fontWeight: 'bold',
  color: defaultTextColor.value,
  letterSpacing: '0.5px',
  margin: '0',
  lineHeight: '1.2'
}))

const subtitleStyle = computed(() => ({
  fontSize: `${props.fontSize * 0.5}px`,
  color: props.textColor ? '#6E7A8A' : '#6E7A8A', // slateGrey
  letterSpacing: '0.5px',
  margin: '0',
  lineHeight: '1.2'
}))
</script>

<style scoped>
.medroid-logo-with-text {
  display: inline-flex;
}

.logo-text-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.logo-title {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.logo-subtitle {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Animation */
.medroid-logo-with-text {
  transition: transform 0.2s ease-in-out;
}

.medroid-logo-with-text:hover {
  transform: translateY(-1px);
}
</style>
