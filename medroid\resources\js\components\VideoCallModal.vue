<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import AgoraRTC from 'agora-rtc-sdk-ng';

const props = defineProps({
    isOpen: {
        type: Boolean,
        required: true
    },
    appointmentId: {
        type: [String, Number],
        required: true
    },
    userRole: {
        type: String,
        required: true
    }
});

const emit = defineEmits(['close']);

// Agora client and tracks
const client = ref(null);
const localAudioTrack = ref(null);
const localVideoTrack = ref(null);
const remoteUsers = ref({});
const joinedUsers = ref(new Set()); // Track users who have joined the channel

// UI state
const isConnecting = ref(false);
const isAudioMuted = ref(false);
const isVideoMuted = ref(false);
const connectionError = ref('');
const connectionTimeout = ref(null);

// Session data
const sessionData = ref(null);

// Polling for session status
const statusPollingInterval = ref(null);
const sessionStatus = ref(null);

// Track health monitoring
const trackHealthInterval = ref(null);

// Initialize video call
const initializeCall = async () => {
    if (isConnecting.value) return;

    try {
        isConnecting.value = true;
        connectionError.value = '';
        console.log('Initializing video call for appointment:', props.appointmentId);

        // Get session data from backend
        const response = await fetch(`/video/session/${props.appointmentId}`, {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const data = await response.json();
        console.log('Session data received:', data);
        console.log('Raw session_data:', data.session_data);
        console.log('UID from session_data:', data.session_data?.uid);
        sessionData.value = data.session_data;

        // Create Agora client with cross-platform compatibility
        client.value = AgoraRTC.createClient({
            mode: 'rtc',
            codec: 'h264'  // Use H.264 for better cross-platform compatibility
        });
        console.log('Agora client created');

        // Set up event listeners
        setupEventListeners();

        // Validate UID before joining
        if (!sessionData.value.uid || sessionData.value.uid === 0) {
            throw new Error(`Invalid UID received: ${sessionData.value.uid}`);
        }

        // Join channel
        console.log('Joining Agora channel:', sessionData.value.channel, 'with UID:', sessionData.value.uid);
        const joinResult = await client.value.join(
            sessionData.value.app_id,
            sessionData.value.channel,
            sessionData.value.token,
            parseInt(sessionData.value.uid) // Ensure UID is an integer
        );
        console.log('Successfully joined Agora channel, result:', joinResult);

        // Wait a moment for connection to stabilize, then verify and notify backend
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

        if (client.value.connectionState === 'CONNECTED') {
            console.log('Agora connection verified, notifying backend');
            await notifyBackendJoined();
        } else {
            console.error('Agora connection not established, connection state:', client.value.connectionState);
            throw new Error('Failed to establish Agora connection');
        }

        // Create and publish local tracks immediately
        await createLocalTracks();
        await publishLocalTracks();
        console.log('Local tracks created and published');

        // Force publish again to ensure Agora detects us
        setTimeout(async () => {
            console.log('Force republishing tracks to ensure visibility...');
            await publishLocalTracks();
        }, 2000);

        // Set up periodic track health check
        startTrackHealthCheck();

        // Debug: Log our own UID and channel info
        console.log('=== DEBUG INFO ===');
        console.log('My UID:', sessionData.value.uid);
        console.log('Channel:', sessionData.value.channel);
        console.log('Client connection state:', client.value.connectionState);
        console.log('Local UID in channel:', client.value.uid);
        console.log('Channel name:', client.value.channelName);
        console.log('==================');

        // Start polling for session status immediately
        startSessionStatusPolling();

        // Check for existing remote users in the channel
        const remoteUsers = client.value.remoteUsers;
        console.log('Existing remote users in channel:', remoteUsers.map(u => u.uid));

        // Process existing users who might already be publishing
        for (const user of remoteUsers) {
            if (user.hasVideo) {
                console.log('Found existing user with video:', user.uid);
                await handleUserPublished(user, 'video');
            }
            if (user.hasAudio) {
                console.log('Found existing user with audio:', user.uid);
                await handleUserPublished(user, 'audio');
            }
        }

    } catch (error) {
        console.error('Error initializing video call:', error);
        connectionError.value = error.message;
    } finally {
        isConnecting.value = false;
    }
};

// Setup event listeners
const setupEventListeners = () => {
    client.value.on('user-joined', handleUserJoined);
    client.value.on('user-published', handleUserPublished);
    client.value.on('user-unpublished', handleUserUnpublished);
    client.value.on('user-left', handleUserLeft);
    client.value.on('connection-state-changed', handleConnectionStateChanged);

    console.log('Agora event listeners set up');
};

// Handle user joined (before they publish media)
const handleUserJoined = (user) => {
    console.log('User joined channel:', user.uid);
    joinedUsers.value.add(user.uid);

    // Clear connection timeout since someone joined
    if (connectionTimeout.value) {
        clearTimeout(connectionTimeout.value);
        connectionTimeout.value = null;
    }

    // Log current state for debugging
    console.log('Current joined users:', Array.from(joinedUsers.value));
    console.log('Current remote users:', Object.keys(remoteUsers.value));
};

// Handle connection state changes
const handleConnectionStateChanged = (curState, revState) => {
    console.log('Connection state changed:', curState, 'from:', revState);

    if (curState === 'CONNECTED') {
        // Start polling for session status
        startSessionStatusPolling();

        // Set a timeout to wait for other participants
        connectionTimeout.value = setTimeout(() => {
            console.log('Connection timeout - checking for participants');
        }, 10000); // 10 second timeout
    }
};

// Start polling session status
const startSessionStatusPolling = () => {
    if (statusPollingInterval.value) return; // Already polling

    console.log('Starting session status polling');
    statusPollingInterval.value = setInterval(async () => {
        await checkSessionStatus();
        // Also check for any missed remote users
        await checkForMissedRemoteUsers();
    }, 2000); // Poll every 2 seconds
};

// Check for remote users that might have been missed
const checkForMissedRemoteUsers = async () => {
    if (!client.value) return;

    const agoraRemoteUsers = client.value.remoteUsers || [];

    for (const agoraUser of agoraRemoteUsers) {
        // If we don't have this user in our remoteUsers but they're publishing media
        if (!remoteUsers.value[agoraUser.uid]) {
            console.log('🔍 Found missed remote user:', agoraUser.uid);
            console.log('🔍 User hasVideo:', agoraUser.hasVideo, 'hasAudio:', agoraUser.hasAudio);
            console.log('🔍 User videoTrack:', !!agoraUser.videoTrack, 'audioTrack:', !!agoraUser.audioTrack);

            // Try to subscribe to video if user has published video
            if (agoraUser.hasVideo) {
                console.log('🎬 User has video, attempting to subscribe to missed video for user:', agoraUser.uid);
                await handleUserPublished(agoraUser, 'video');
            } else {
                console.log('⚠️ User does not have video published:', agoraUser.uid);
            }

            // Try to subscribe to audio if user has published audio
            if (agoraUser.hasAudio) {
                console.log('🔊 User has audio, attempting to subscribe to missed audio for user:', agoraUser.uid);
                await handleUserPublished(agoraUser, 'audio');
            } else {
                console.log('⚠️ User does not have audio published:', agoraUser.uid);
            }
        }
    }
};

// Stop polling session status
const stopSessionStatusPolling = () => {
    if (statusPollingInterval.value) {
        console.log('Stopping session status polling');
        clearInterval(statusPollingInterval.value);
        statusPollingInterval.value = null;
    }
};

// Start track health monitoring
const startTrackHealthCheck = () => {
    if (trackHealthInterval.value) return; // Already running

    console.log('Starting track health monitoring');
    trackHealthInterval.value = setInterval(async () => {
        await checkTrackHealth();
    }, 5000); // Check every 5 seconds
};

// Stop track health monitoring
const stopTrackHealthCheck = () => {
    if (trackHealthInterval.value) {
        console.log('Stopping track health monitoring');
        clearInterval(trackHealthInterval.value);
        trackHealthInterval.value = null;
    }
};

// Check if tracks are healthy and republish if needed
const checkTrackHealth = async () => {
    try {
        if (!client.value || !localAudioTrack.value || !localVideoTrack.value) {
            return;
        }

        // Check if tracks are still published
        const publishedTracks = client.value.localTracks || [];
        const hasAudio = publishedTracks.some(t => t.trackMediaType === 'audio');
        const hasVideo = publishedTracks.some(t => t.trackMediaType === 'video');

        if (!hasAudio || !hasVideo) {
            console.log('Detected missing published tracks, republishing...', { hasAudio, hasVideo });
            await publishLocalTracks();
        }

        // Check if local video is still playing
        const localVideoElement = document.getElementById('local-video');
        if (localVideoElement && localVideoTrack.value) {
            if (localVideoElement.srcObject === null) {
                console.log('Local video element lost source, reattaching...');
                localVideoTrack.value.play(localVideoElement);
            }
        }

        // Check remote video connections
        for (const [uid, user] of Object.entries(remoteUsers.value)) {
            if (user.videoTrack) {
                const remoteVideoElement = document.getElementById('remote-video');
                if (remoteVideoElement && remoteVideoElement.srcObject === null) {
                    console.log('Remote video element lost source, reattaching for user:', uid);
                    user.videoTrack.play(remoteVideoElement);
                }
            }
        }

    } catch (error) {
        console.error('Error in track health check:', error);
    }
};

// Check session status via API
const checkSessionStatus = async () => {
    try {
        const response = await fetch(`/video/status/${props.appointmentId}`, {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            const data = await response.json();
            sessionStatus.value = data;

            console.log('Session status:', data);

            // Handle participant changes
            if (data.success && data.participants) {
                await handleParticipantUpdates(data.participants);
            }
        }
    } catch (error) {
        console.error('Error checking session status:', error);
    }
};

// Handle participant updates from API
const handleParticipantUpdates = async (participants) => {
    const activeParticipants = participants.filter(p => p.status === 'joined');
    console.log('Raw participants from API:', participants);
    console.log('Active participants from API:', activeParticipants.map(p => ({
        user_id: p.user_id,
        agora_uid: p.agora_uid,
        agora_uid_type: typeof p.agora_uid,
        name: p.user_name
    })));

    // Check if we have new participants that we haven't detected via Agora events
    for (const participant of activeParticipants) {
        const agoraUid = participant.agora_uid; // Backend now returns integer
        const userId = participant.user_id; // Keep user ID for reference

        // Check if agora_uid is valid
        if (!agoraUid || typeof agoraUid !== 'number' || agoraUid === 0) {
            console.warn('Participant has invalid agora_uid:', participant);
            continue;
        }

        // Skip our own user
        if (agoraUid === sessionData.value?.uid) continue;

        // Add to joinedUsers if not already there (this helps with UI state)
        if (!joinedUsers.value.has(agoraUid)) {
            console.log('Adding participant to joinedUsers from API:', agoraUid, participant.user_name, '(user_id:', userId, ')');
            joinedUsers.value.add(agoraUid);
        }

        // If we don't have this user in our remote users, try to find them in Agora
        if (!remoteUsers.value[agoraUid] && client.value) {
            console.log('Found participant not in remoteUsers, checking Agora:', agoraUid, '(user_id:', userId, ')');

            // Check if this user exists in Agora's remote users
            const agoraRemoteUsers = client.value.remoteUsers || [];
            const agoraUser = agoraRemoteUsers.find(u => u.uid == agoraUid);

            if (agoraUser) {
                console.log('Found user in Agora remote users:', agoraUid);

                // Subscribe to their media if they're publishing
                if (agoraUser.hasVideo && agoraUser.videoTrack) {
                    console.log('Subscribing to existing video for user:', agoraUid);
                    await handleUserPublished(agoraUser, 'video');
                }
                if (agoraUser.hasAudio && agoraUser.audioTrack) {
                    console.log('Subscribing to existing audio for user:', agoraUid);
                    await handleUserPublished(agoraUser, 'audio');
                }
            } else {
                // User is marked as joined in backend but not found in Agora remote users
                // This indicates a sync issue between backend and Agora
                console.log('Participant marked as joined in backend but not found in Agora remote users:', agoraUid, '(user_id:', userId, ')');

                // Try to trigger a refresh of remote users
                if (client.value.remoteUsers) {
                    console.log('Current Agora remote users:', client.value.remoteUsers.map(u => u.uid));
                }

                // Force check all users in the channel
                console.log('Forcing check for user:', agoraUid);
                await forceCheckRemoteUser(agoraUid);

                // If user is still not found after force check, they're likely disconnected
                // We should notify the backend to update their status
                setTimeout(async () => {
                    const stillNotFound = !client.value?.remoteUsers?.find(u => u.uid == agoraUid);
                    if (stillNotFound) {
                        console.log('User still not found in Agora after force check, notifying backend of disconnect:', agoraUid);
                        await notifyBackendUserDisconnected(userId);
                    }
                }, 3000); // Wait 3 seconds before marking as disconnected
            }
        }
    }
};

// Cleanup call resources
const cleanupCall = async () => {
    console.log('Starting call cleanup');

    // Stop polling
    stopSessionStatusPolling();
    stopTrackHealthCheck();

    // Clear any timeouts
    if (connectionTimeout.value) {
        clearTimeout(connectionTimeout.value);
        connectionTimeout.value = null;
    }

    // Notify backend that user is leaving (but not ending the session)
    try {
        await leaveSessionOnBackend();
    } catch (error) {
        console.error('Error leaving session on backend:', error);
    }

    if (localAudioTrack.value) {
        localAudioTrack.value.stop();
        localAudioTrack.value.close();
        localAudioTrack.value = null;
    }
    if (localVideoTrack.value) {
        localVideoTrack.value.stop();
        localVideoTrack.value.close();
        localVideoTrack.value = null;
    }
    if (client.value) {
        await client.value.leave();
        client.value = null;
    }
    remoteUsers.value = {};
    joinedUsers.value.clear();

    console.log('Call cleanup completed');
};

// Notify backend that user has successfully joined the Agora channel
const notifyBackendJoined = async () => {
    try {
        const response = await fetch(`/video/join/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Successfully notified backend of join:', data);
        } else {
            console.error('Failed to notify backend of join:', response.status);
        }
    } catch (error) {
        console.error('Error notifying backend of join:', error);
    }
};

// Leave session on backend (participant leaves but session continues)
const leaveSessionOnBackend = async () => {
    try {
        const response = await fetch(`/video/leave/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Left session on backend:', data);
        } else {
            console.error('Failed to leave session on backend:', response.status);
        }
    } catch (error) {
        console.error('Error leaving session on backend:', error);
    }
};

// Notify backend that a user has disconnected from Agora
const notifyBackendUserDisconnected = async (userId) => {
    try {
        const response = await fetch(`/video/participant-disconnected/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                user_id: userId
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Notified backend of user disconnect:', data);
        } else {
            console.error('Failed to notify backend of user disconnect:', response.status);
        }
    } catch (error) {
        console.error('Error notifying backend of user disconnect:', error);
    }
};

// Create local audio and video tracks
const createLocalTracks = async () => {
    try {
        localAudioTrack.value = await AgoraRTC.createMicrophoneAudioTrack();
        localVideoTrack.value = await AgoraRTC.createCameraVideoTrack();

        // Play local video
        await nextTick();
        const localVideoElement = document.getElementById('local-video');
        if (localVideoElement && localVideoTrack.value) {
            localVideoTrack.value.play(localVideoElement);
        }
    } catch (error) {
        console.error('Error creating local tracks:', error);
        throw error;
    }
};

// Publish local tracks
const publishLocalTracks = async () => {
    try {
        if (!client.value) {
            console.error('Cannot publish: Agora client not available');
            return false;
        }

        if (!localAudioTrack.value || !localVideoTrack.value) {
            console.error('Cannot publish: Local tracks not available');
            return false;
        }

        console.log('Publishing local audio and video tracks...');
        await client.value.publish([localAudioTrack.value, localVideoTrack.value]);
        console.log('Successfully published local tracks');

        // Verify tracks are published by checking client state
        const publishedTracks = client.value.localTracks || [];
        console.log('Published tracks count:', publishedTracks.length);
        console.log('Published track types:', publishedTracks.map(t => t.trackMediaType));

        return true;
    } catch (error) {
        console.error('Error publishing local tracks:', error);
        return false;
    }
};

// Force check for a specific remote user
const forceCheckRemoteUser = async (uid) => {
    try {
        if (!client.value) return;

        const agoraUid = uid; // UID should already be integer from backend
        console.log('Force checking for remote user:', agoraUid);
        const remoteUsers = client.value.remoteUsers || [];
        console.log('Current remote users in client:', remoteUsers.map(u => u.uid));

        const targetUser = remoteUsers.find(u => u.uid == agoraUid);
        if (targetUser) {
            console.log('Found target user:', agoraUid);

            if (!joinedUsers.value.has(agoraUid)) {
                handleUserJoined(targetUser);
            }

            if (targetUser.hasVideo && targetUser.videoTrack) {
                await handleUserPublished(targetUser, 'video');
            }
            if (targetUser.hasAudio && targetUser.audioTrack) {
                await handleUserPublished(targetUser, 'audio');
            }
        } else {
            console.log('User not found in Agora remote users:', agoraUid);
        }

    } catch (error) {
        console.error('Error in force check remote user:', error);
    }
};

// Handle remote user published
const handleUserPublished = async (user, mediaType) => {
    console.log('🎥 [HANDLE USER PUBLISHED] User published media:', user.uid, 'mediaType:', mediaType);
    console.log('🎥 [HANDLE USER PUBLISHED] User object:', { uid: user.uid, hasVideo: user.hasVideo, hasAudio: user.hasAudio });
    console.log('🎥 [HANDLE USER PUBLISHED] Current user role:', props.userRole);
    console.log('🎥 [HANDLE USER PUBLISHED] My UID:', sessionData.value?.uid);

    try {
        // Always subscribe to get the media track - don't skip if track exists
        console.log('🔄 [HANDLE USER PUBLISHED] Subscribing to user:', user.uid, 'mediaType:', mediaType);
        await client.value.subscribe(user, mediaType);
        console.log('✅ [HANDLE USER PUBLISHED] Successfully subscribed to user:', user.uid, 'mediaType:', mediaType);

        if (mediaType === 'video') {
            console.log('🎬 Processing video media for user:', user.uid);
            console.log('🎬 User videoTrack exists:', !!user.videoTrack);

            if (user.videoTrack) {
                await nextTick();
                const remoteVideoElement = document.getElementById('remote-video');
                if (remoteVideoElement) {
                    console.log('🎯 Found remote video element, preparing to play...');

                    // Clear any existing content first
                    remoteVideoElement.srcObject = null;

                    // Play the remote user's video in the main area
                    user.videoTrack.play(remoteVideoElement);
                    console.log('🎬 Video track play() called for user:', user.uid);
                    console.log('🎬 This should show:', props.userRole === 'provider' ? 'PATIENT video' : 'PROVIDER video', 'in main area');
                    console.log('Remote video element:', remoteVideoElement);

                // Verify video is actually playing
                setTimeout(() => {
                    if (remoteVideoElement.videoWidth > 0 && remoteVideoElement.videoHeight > 0) {
                        console.log('✅ Remote video confirmed playing:', remoteVideoElement.videoWidth + 'x' + remoteVideoElement.videoHeight);

                        // Show success message based on user role
                        if (props.userRole === 'provider') {
                            console.log('🏥 Provider now seeing patient video in main area');
                        } else {
                            console.log('👤 Patient now seeing provider video in main area');
                        }
                    } else {
                        console.warn('⚠️ Remote video may not be playing properly');
                        // Try to play again
                        setTimeout(() => {
                            user.videoTrack.play(remoteVideoElement);
                        }, 1000);
                    }
                }, 1000);
            } else {
                console.error('❌ Remote video element not found!');
            }
        } else {
            console.error('❌ No video track found after subscription for user:', user.uid);
        }
        }

        if (mediaType === 'audio' && user.audioTrack) {
            user.audioTrack.play();
            console.log('🔊 Playing remote audio for user:', user.uid);
        }

        remoteUsers.value[user.uid] = user;
        console.log('📊 Remote users updated:', Object.keys(remoteUsers.value));
        console.log('📈 Total remote users count:', Object.keys(remoteUsers.value).length);

        // Log the current video setup for debugging
        console.log('🎯 Current video setup:');
        console.log('  - Local video (small): Your own video');
        console.log('  - Remote video (main):', props.userRole === 'provider' ? 'Patient video' : 'Provider video');

    } catch (error) {
        console.error('❌ Error handling user published:', error);
        console.error('Error details:', error.message, error.stack);

        // Retry subscription after a delay
        setTimeout(async () => {
            console.log('🔄 Retrying subscription for user:', user.uid, 'mediaType:', mediaType);
            try {
                await client.value.subscribe(user, mediaType);
                console.log('✅ Retry subscription successful for user:', user.uid);

                // Try to play video again if it's video media
                if (mediaType === 'video' && user.videoTrack) {
                    const remoteVideoElement = document.getElementById('remote-video');
                    if (remoteVideoElement) {
                        user.videoTrack.play(remoteVideoElement);
                    }
                }
            } catch (retryError) {
                console.error('❌ Retry subscription failed:', retryError);
            }
        }, 2000);
    }
};

// Handle remote user unpublished
const handleUserUnpublished = (user, mediaType) => {
    console.log('User unpublished media:', user.uid, 'type:', mediaType);

    if (mediaType === 'video') {
        const remoteVideoElement = document.getElementById('remote-video');
        if (remoteVideoElement) {
            remoteVideoElement.srcObject = null;
        }
    }
};

// Handle remote user left
const handleUserLeft = (user) => {
    console.log('User left channel:', user.uid);
    delete remoteUsers.value[user.uid];
    joinedUsers.value.delete(user.uid);

    const remoteVideoElement = document.getElementById('remote-video');
    if (remoteVideoElement) {
        remoteVideoElement.srcObject = null;
    }

    // If all remote users have left, consider ending the session
    if (Object.keys(remoteUsers.value).length === 0 && joinedUsers.value.size === 0) {
        console.log('All participants have left, ending call in 3 seconds');
        setTimeout(() => {
            endCall();
        }, 3000); // Give 3 seconds for potential reconnection
    }
};

// Toggle audio
const toggleAudio = async () => {
    if (localAudioTrack.value) {
        await localAudioTrack.value.setEnabled(!isAudioMuted.value);
        isAudioMuted.value = !isAudioMuted.value;
    }
};

// Toggle video
const toggleVideo = async () => {
    if (localVideoTrack.value) {
        await localVideoTrack.value.setEnabled(!isVideoMuted.value);
        isVideoMuted.value = !isVideoMuted.value;
    }
};

// End call
const endCall = async () => {
    console.log('Ending video call and cleaning up session');

    try {
        // Notify backend to end the session
        await endSessionOnBackend();
    } catch (error) {
        console.error('Error ending session on backend:', error);
    }

    await cleanupCall();
    emit('close');
};

// End session on backend
const endSessionOnBackend = async () => {
    try {
        const response = await fetch(`/video/end/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Session ended on backend:', data);
        } else {
            console.error('Failed to end session on backend:', response.status);
        }
    } catch (error) {
        console.error('Error ending session on backend:', error);
    }
};

// Initialize when modal opens
onMounted(() => {
    if (props.isOpen) {
        initializeCall();
    }
});

// Cleanup on unmount
onUnmounted(() => {
    endCall();
});
</script>

<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl h-3/4 flex flex-col">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    Video Consultation
                </h3>
                <button @click="endCall" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Video Container -->
            <div class="flex-1 relative bg-gray-900">
                <!-- Remote Video (Main) -->
                <div class="w-full h-full relative">
                    <video
                        id="remote-video"
                        class="w-full h-full object-cover"
                        autoplay
                        playsinline
                    ></video>
                    <!-- Waiting for other participant -->
                    <div v-if="Object.keys(remoteUsers).length === 0 && joinedUsers.size === 0" class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center text-white">
                            <i class="fas fa-user-circle text-6xl mb-4"></i>
                            <p v-if="userRole === 'provider'">Waiting for patient to join...</p>
                            <p v-else>Waiting for provider to start the call...</p>
                            <p v-if="sessionStatus && sessionStatus.session" class="text-sm mt-2 opacity-75">
                                Session: {{ sessionStatus.session.status }} | Participants: {{ sessionStatus.session.participants }}
                            </p>
                            <div class="mt-4">
                                <div class="animate-pulse flex space-x-1 justify-center">
                                    <div class="w-2 h-2 bg-white rounded-full"></div>
                                    <div class="w-2 h-2 bg-white rounded-full animation-delay-200"></div>
                                    <div class="w-2 h-2 bg-white rounded-full animation-delay-400"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Show when users joined but haven't published media yet -->
                    <div v-if="Object.keys(remoteUsers).length === 0 && joinedUsers.size > 0" class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center text-white">
                            <i class="fas fa-video text-6xl mb-4 animate-pulse"></i>
                            <p v-if="userRole === 'provider'">Patient connected, waiting for video...</p>
                            <p v-else>Provider connected, waiting for video...</p>
                            <p v-if="sessionStatus && sessionStatus.session" class="text-sm mt-2 opacity-75">
                                Session: {{ sessionStatus.session.status }} | Participants: {{ sessionStatus.session.participants }}
                            </p>
                            <div class="mt-4">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Show participant info when video is active -->
                    <div v-if="Object.keys(remoteUsers).length > 0" class="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg text-sm">
                        <i class="fas fa-video mr-1"></i>
                        <span v-if="userRole === 'provider'">Patient Video</span>
                        <span v-else>Provider Video</span>
                    </div>
                </div>

                <!-- Local Video (Picture-in-Picture) -->
                <div class="absolute top-4 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden border-2 border-white">
                    <!-- Local video label -->
                    <div class="absolute top-1 left-1 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs z-10">
                        <i class="fas fa-user mr-1"></i>
                        You
                    </div>
                    <video
                        id="local-video"
                        class="w-full h-full object-cover"
                        autoplay
                        playsinline
                        muted
                    ></video>
                    <div v-if="isVideoMuted" class="absolute inset-0 flex items-center justify-center bg-gray-800">
                        <i class="fas fa-video-slash text-white text-2xl"></i>
                    </div>
                </div>

                <!-- Connection Status -->
                <div v-if="isConnecting" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="text-center text-white">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                        <p>Connecting...</p>
                    </div>
                </div>

                <!-- Error Message -->
                <div v-if="connectionError" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="text-center text-white">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4 text-red-400"></i>
                        <p class="mb-4">{{ connectionError }}</p>
                        <button @click="endCall" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                            Close
                        </button>
                    </div>
                </div>
            </div>

            <!-- Controls -->
            <div class="p-4 bg-gray-50 flex items-center justify-center space-x-4">
                <button
                    @click="toggleAudio"
                    :class="[
                        'p-3 rounded-full transition-colors text-white',
                        isAudioMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'
                    ]"
                >
                    <i :class="isAudioMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
                </button>

                <button
                    @click="toggleVideo"
                    :class="[
                        'p-3 rounded-full transition-colors text-white',
                        isVideoMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'
                    ]"
                >
                    <i :class="isVideoMuted ? 'fas fa-video-slash' : 'fas fa-video'"></i>
                </button>

                <button
                    @click="endCall"
                    class="p-3 rounded-full bg-red-600 hover:bg-red-700 text-white"
                >
                    <i class="fas fa-phone-slash"></i>
                </button>
            </div>
        </div>
    </div>
</template>
