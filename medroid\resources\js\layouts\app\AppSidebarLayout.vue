<script setup>
import AppSidebar from '@/components/AppSidebar.vue';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { usePage } from '@inertiajs/vue3';

const props = defineProps({
    breadcrumbs: {
        type: Array,
        default: () => []
    }
});

const page = usePage();
const user = page.props.auth?.user;
</script>

<template>
    <div class="flex h-screen bg-gray-100">
        <AppSidebar :user="user" />

        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <Breadcrumbs v-if="breadcrumbs.length > 0" :breadcrumbs="breadcrumbs" />
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-700">Welcome, {{ user?.name }}</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <slot />
            </main>
        </div>
    </div>
</template>
