<script setup lang="ts">
import { ref, nextTick, onMounted, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import PublicLayout from '@/layouts/PublicLayout.vue';
import ChatMessage from '@/components/ChatMessage.vue';

// Types
interface Message {
    id: number;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
}

// Reactive data
const messages = ref<Message[]>([]);
const newMessage = ref('');
const isLoading = ref(false);
const chatContainer = ref<HTMLElement | null>(null);
const conversationId = ref<string | null>(null);
const anonymousId = ref<string>('');
const showAuthPrompt = ref(false);
const showDemographics = ref(false);
const demographics = ref({
    age: '',
    gender: ''
});

// Generate or retrieve anonymous ID
const initializeAnonymousId = () => {
    let storedId = localStorage.getItem('medroid_anonymous_id');
    if (!storedId) {
        storedId = 'anon_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        localStorage.setItem('medroid_anonymous_id', storedId);
    }
    anonymousId.value = storedId;
};

// Computed
const canSendMessage = computed(() => {
    return newMessage.value.trim() && !isLoading.value;
});

// Methods
const scrollToBottom = () => {
    if (chatContainer.value) {
        nextTick(() => {
            chatContainer.value!.scrollTop = chatContainer.value!.scrollHeight;
        });
    }
};

const addMessage = (type: 'user' | 'ai', content: string) => {
    const message: Message = {
        id: Date.now(),
        type,
        content,
        timestamp: new Date()
    };
    messages.value.push(message);
    scrollToBottom();
};

const startConversation = async (message: string) => {
    try {
        const response = await fetch('/api/anonymous/chat/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message,
                anonymous_id: anonymousId.value,
                gender: demographics.value.gender || null,
                age: demographics.value.age || null
            })
        });

        const data = await response.json();

        if (response.ok) {
            conversationId.value = data.conversation_id;
            
            if (data.message) {
                addMessage('ai', data.message);
            }

            // Handle auth requirements
            if (data.requires_auth) {
                showAuthPrompt.value = true;
            }

            return data;
        } else {
            throw new Error(data.message || 'Failed to start conversation');
        }
    } catch (error) {
        console.error('Error starting conversation:', error);
        addMessage('ai', 'Sorry, I encountered an error. Please try again.');
        throw error;
    }
};

const sendMessage = async () => {
    if (!canSendMessage.value) return;

    const userMessage = newMessage.value.trim();
    addMessage('user', userMessage);
    newMessage.value = '';
    isLoading.value = true;

    try {
        let response;
        
        if (!conversationId.value) {
            // Start new conversation
            response = await startConversation(userMessage);
        } else {
            // Continue existing conversation
            const apiResponse = await fetch('/api/anonymous/chat/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    conversation_id: conversationId.value,
                    message: userMessage,
                    anonymous_id: anonymousId.value,
                    gender: demographics.value.gender || null,
                    age: demographics.value.age || null
                })
            });

            response = await apiResponse.json();

            if (apiResponse.ok) {
                if (response.message) {
                    addMessage('ai', response.message);
                }

                // Handle auth requirements
                if (response.requires_auth) {
                    showAuthPrompt.value = true;
                }
            } else {
                throw new Error(response.message || 'Failed to send message');
            }
        }
    } catch (error) {
        console.error('Error sending message:', error);
        addMessage('ai', 'Sorry, I encountered an error. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const handleKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
};

const saveDemographics = () => {
    showDemographics.value = false;
    // Demographics will be sent with the next message
};

// Initialize on mount
onMounted(() => {
    initializeAnonymousId();
    
    // Add welcome message
    addMessage('ai', "Hello! I'm your AI health assistant. I can help you with health questions, symptoms, and general medical advice. How can I help you today?");
    
    // Show demographics modal after a short delay
    setTimeout(() => {
        showDemographics.value = true;
    }, 2000);
});
</script>

<template>
    <Head title="Anonymous AI Health Chat - Medroid" />

    <PublicLayout>
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Anonymous AI Health Chat
                </h1>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Get instant, private health advice from our AI assistant. No account required.
                </p>
            </div>

            <!-- Chat Container -->
            <div class="bg-white rounded-lg shadow-lg flex flex-col h-[600px]">
                <!-- Chat Messages -->
                <div 
                    ref="chatContainer"
                    class="flex-1 p-6 overflow-y-auto space-y-4"
                >
                    <ChatMessage
                        v-for="(message, index) in messages"
                        :key="message.id"
                        :message="message"
                        :animate="true"
                        :delay="index * 100"
                    />
                    
                    <!-- Loading indicator -->
                    <div v-if="isLoading" class="flex justify-start">
                        <div class="bg-gray-100 text-gray-800 rounded-lg px-4 py-2">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="border-t p-4">
                    <form @submit.prevent="sendMessage" class="flex space-x-2">
                        <input
                            v-model="newMessage"
                            type="text"
                            placeholder="Type your health question..."
                            class="flex-1 border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-medroid-primary focus:border-transparent"
                            :disabled="isLoading"
                            @keypress="handleKeyPress"
                        />
                        <button
                            type="submit"
                            :disabled="!canSendMessage"
                            class="bg-medroid-primary text-white px-6 py-3 rounded-lg hover:bg-medroid-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            Send
                        </button>
                    </form>
                </div>
            </div>

            <!-- Features and Disclaimer -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Features -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="font-semibold text-lg mb-4 text-gray-900">What I can help with:</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-medroid-teal rounded-full"></div>
                            <span>Symptom analysis and insights</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-medroid-teal rounded-full"></div>
                            <span>Medication information</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-medroid-teal rounded-full"></div>
                            <span>General health advice</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-medroid-teal rounded-full"></div>
                            <span>Wellness recommendations</span>
                        </li>
                    </ul>
                </div>

                <!-- Sign Up Prompt -->
                <div class="bg-medroid-primary/5 p-6 rounded-lg border border-medroid-primary/20">
                    <h3 class="font-semibold text-lg mb-2 text-medroid-primary">Want more features?</h3>
                    <p class="text-gray-600 mb-4">
                        Create an account to save your conversations, book appointments with real doctors, and access your health history.
                    </p>
                    <div class="space-y-2">
                        <Link
                            :href="route('register')"
                            class="block w-full bg-medroid-primary text-white text-center px-4 py-2 rounded-lg hover:bg-medroid-primary/90 transition-colors"
                        >
                            Sign Up Free
                        </Link>
                        <Link
                            :href="route('login')"
                            class="block w-full text-medroid-primary text-center px-4 py-2 rounded-lg border border-medroid-primary hover:bg-medroid-primary/5 transition-colors"
                        >
                            Sign In
                        </Link>
                    </div>
                </div>
            </div>

            <!-- Medical Disclaimer -->
            <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-800">
                            <strong>Medical Disclaimer:</strong> This AI assistant provides general health information only and should not replace professional medical advice. Always consult with a qualified healthcare provider for medical concerns.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demographics Modal -->
        <div v-if="showDemographics" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Help us provide better advice (Optional)</h3>
                <p class="text-gray-600 mb-6">
                    Providing your age and gender helps our AI give more personalized health advice. This information is anonymous and not stored.
                </p>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Age Group</label>
                        <select v-model="demographics.age" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-medroid-primary">
                            <option value="">Prefer not to say</option>
                            <option value="under_18">Under 18</option>
                            <option value="18_25">18-25</option>
                            <option value="26_35">26-35</option>
                            <option value="36_45">36-45</option>
                            <option value="46_55">46-55</option>
                            <option value="56_65">56-65</option>
                            <option value="over_65">Over 65</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                        <select v-model="demographics.gender" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-medroid-primary">
                            <option value="">Prefer not to say</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <div class="flex space-x-3 mt-6">
                    <button
                        @click="saveDemographics"
                        class="flex-1 bg-medroid-primary text-white px-4 py-2 rounded-lg hover:bg-medroid-primary/90 transition-colors"
                    >
                        Continue
                    </button>
                    <button
                        @click="showDemographics = false"
                        class="flex-1 text-gray-600 px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
                    >
                        Skip
                    </button>
                </div>
            </div>
        </div>

        <!-- Auth Prompt Modal -->
        <div v-if="showAuthPrompt" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Sign in required</h3>
                <p class="text-gray-600 mb-6">
                    To book appointments and access additional features, you'll need to create an account or sign in. Your conversation will be saved.
                </p>

                <div class="space-y-3">
                    <Link
                        :href="route('register')"
                        class="block w-full bg-medroid-primary text-white text-center px-4 py-3 rounded-lg hover:bg-medroid-primary/90 transition-colors"
                    >
                        Create Account
                    </Link>
                    <Link
                        :href="route('login')"
                        class="block w-full text-medroid-primary text-center px-4 py-3 rounded-lg border border-medroid-primary hover:bg-medroid-primary/5 transition-colors"
                    >
                        Sign In
                    </Link>
                    <button
                        @click="showAuthPrompt = false"
                        class="block w-full text-gray-600 text-center px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        Continue Anonymous Chat
                    </button>
                </div>
            </div>
        </div>
    </PublicLayout>
</template>
