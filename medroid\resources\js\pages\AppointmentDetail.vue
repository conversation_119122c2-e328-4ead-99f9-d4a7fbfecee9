<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    appointment: {
        type: Object,
        required: true
    }
});

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Appointments', href: '/appointments' },
    { title: 'Appointment Details', href: '#' },
];

const loading = ref(false);

const getStatusBadgeClass = (status) => {
    const classes = {
        scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        no_show: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const getTypeBadgeClass = (type) => {
    const classes = {
        telemedicine: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
        'in-person': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };
    return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const editAppointment = () => {
    router.visit(`/appointments/${props.appointment.id}/edit`);
};

const cancelAppointment = async () => {
    if (!confirm('Are you sure you want to cancel this appointment?')) {
        return;
    }

    loading.value = true;

    try {
        // Get CSRF token from meta tag
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch(`/api/appointments/${props.appointment.id}`, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            alert('Appointment cancelled successfully.');
            router.visit('/appointments');
        } else {
            const errorData = await response.json().catch(() => ({}));
            alert(errorData.message || 'Failed to cancel appointment. Please try again.');
        }
    } catch (error) {
        console.error('Error cancelling appointment:', error);
        alert('Failed to cancel appointment. Please try again.');
    } finally {
        loading.value = false;
    }
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

const formatTime = (time) => {
    return new Date(`2000-01-01 ${time}`).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
};
</script>

<template>
    <Head title="Appointment Details" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Appointment Details
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="flex space-x-3">
                    <Link 
                        href="/appointments"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Appointments
                    </Link>
                    <button 
                        v-if="appointment.status === 'scheduled'"
                        @click="editAppointment"
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"
                    >
                        <i class="fas fa-edit mr-2"></i>
                        Edit Appointment
                    </button>
                    <button 
                        v-if="appointment.status === 'scheduled'"
                        @click="cancelAppointment"
                        :disabled="loading"
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
                        <i v-else class="fas fa-times mr-2"></i>
                        {{ loading ? 'Cancelling...' : 'Cancel Appointment' }}
                    </button>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <!-- Appointment Details Card -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Left Column -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                    Appointment Information
                                </h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Service</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.service?.name || appointment.reason || 'Consultation' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Type</label>
                                        <span :class="getTypeBadgeClass(appointment.is_telemedicine ? 'telemedicine' : 'in-person')" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                            {{ appointment.is_telemedicine ? 'telemedicine' : 'in-person' }}
                                        </span>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                                        <span :class="getStatusBadgeClass(appointment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                            {{ appointment.status }}
                                        </span>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Date</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ formatDate(appointment.date || appointment.scheduled_at) }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Time</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ formatTime(appointment.time || appointment.scheduled_at) }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Amount</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">${{ typeof appointment.amount === 'number' ? appointment.amount.toFixed(2) : parseFloat(appointment.amount || 0).toFixed(2) }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Right Column -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                    Participants
                                </h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Patient</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.patient_name || appointment.patient?.name || 'N/A' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Provider</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.provider_name || appointment.provider?.name || 'N/A' }}</p>
                                    </div>
                                    
                                    <div v-if="appointment.notes">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Notes</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.notes }}</p>
                                    </div>
                                    
                                    <div v-if="appointment.cancellation_reason">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Cancellation Reason</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.cancellation_reason }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
