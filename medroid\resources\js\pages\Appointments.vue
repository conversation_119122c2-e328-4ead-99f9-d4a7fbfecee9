<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import VideoCallModal from '@/components/VideoCallModal.vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Appointments', href: '/appointments' },
];

const loading = ref(false);
const appointments = ref([]);
const actionLoading = ref({});

// Get current user information
const page = usePage();
const currentUser = computed(() => page.props.auth?.user);

// Video call state
const showVideoCall = ref(false);
const selectedAppointment = ref(null);
const userRole = ref('patient'); // Will be determined based on user

// Helper function to determine user's role for a specific appointment
const getUserRoleForAppointment = (appointment) => {
    if (!currentUser.value) return null;

    // Check if current user is the provider for this appointment
    if (currentUser.value.role === 'provider' &&
        appointment.provider?.user?.id === currentUser.value.id) {
        return 'provider';
    }

    // Check if current user is the patient for this appointment
    if (currentUser.value.role === 'patient' &&
        appointment.patient?.user?.id === currentUser.value.id) {
        return 'patient';
    }

    // Admin/manager can act as provider
    if (['admin', 'manager'].includes(currentUser.value.role)) {
        return 'provider';
    }

    return null;
};

// Helper function to check if user can start a video call (providers only)
const canStartVideoCall = (appointment) => {
    const role = getUserRoleForAppointment(appointment);
    return role === 'provider' &&
           appointment.is_telemedicine &&
           ['scheduled', 'confirmed', 'in_progress'].includes(appointment.status);
};

// Helper function to check if user can join a video call (patients only)
const canJoinVideoCall = (appointment) => {
    const role = getUserRoleForAppointment(appointment);
    return role === 'patient' &&
           appointment.is_telemedicine &&
           ['scheduled', 'confirmed', 'in_progress'].includes(appointment.status) &&
           appointment.video_session_id; // Session must exist
};

const fetchAppointments = async () => {
    loading.value = true;
    try {
        // Get CSRF token from meta tag
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch('/appointments-list', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            const data = await response.json();
            // The API returns appointments directly as an array
            appointments.value = Array.isArray(data) ? data : (data.appointments || data.data || []);
        } else if (response.status === 401) {
            // User not authenticated, redirect to login
            router.visit('/login');
            return;
        } else {
            console.error('Failed to fetch appointments:', response.status, await response.text());
            appointments.value = [];
        }
    } catch (error) {
        console.error('Error fetching appointments:', error);
        appointments.value = [];
    } finally {
        loading.value = false;
    }
};

const getStatusBadgeClass = (status) => {
    const classes = {
        scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        no_show: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const getTypeBadgeClass = (type) => {
    const classes = {
        telemedicine: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
        'in-person': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };
    return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

// Action methods
const viewAppointment = (appointment) => {
    router.visit(`/appointments/${appointment.id}`);
};

const editAppointment = (appointment) => {
    // Navigate to edit page or open edit modal
    router.visit(`/appointments/${appointment.id}/edit`);
};

const cancelAppointment = async (appointment) => {
    if (!confirm(`Are you sure you want to cancel the appointment with ${appointment.provider_name || appointment.provider?.name || 'the provider'}?`)) {
        return;
    }

    actionLoading.value[appointment.id] = true;

    try {
        // Get CSRF token from meta tag
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch(`/api/appointments/${appointment.id}`, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            // Update the appointment status locally
            const appointmentIndex = appointments.value.findIndex(a => a.id === appointment.id);
            if (appointmentIndex !== -1) {
                appointments.value[appointmentIndex].status = 'cancelled';
            }

            // Show success message
            alert('Appointment cancelled successfully.');
        } else {
            const errorData = await response.json().catch(() => ({}));
            alert(errorData.message || 'Failed to cancel appointment. Please try again.');
        }
    } catch (error) {
        console.error('Error cancelling appointment:', error);
        alert('Failed to cancel appointment. Please try again.');
    } finally {
        actionLoading.value[appointment.id] = false;
    }
};

const isActionLoading = (appointmentId) => {
    return actionLoading.value[appointmentId] || false;
};

// Video call functions
const startVideoCall = async (appointment) => {
    console.log('Attempting to start video call for appointment:', appointment.id);

    // Check user's role for this appointment
    const role = getUserRoleForAppointment(appointment);

    if (!role) {
        alert('You are not authorized to start this video call.');
        return;
    }

    // Only providers can start video calls
    if (role !== 'provider') {
        alert('Only the provider can start the video call. Please wait for the provider to begin the session.');
        return;
    }

    try {
        // Initialize video session
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const response = await fetch(`/video/initialize/${appointment.id}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            credentials: 'same-origin'
        });

        const responseData = await response.json().catch(() => ({}));

        if (response.ok && responseData.success) {
            console.log('Video session initialized successfully');

            // Update appointment with session data if provided
            if (responseData.session_data) {
                appointment.video_session_id = responseData.session_id;
                appointment.session_data = responseData.session_data;
                appointment.user_role = responseData.user_role;
            }

            selectedAppointment.value = appointment;
            userRole.value = role; // Use determined role
            showVideoCall.value = true;
        } else {
            const errorMessage = responseData.message || 'Failed to start video call. Please try again.';
            console.error('Failed to initialize video session:', errorMessage);
            alert(errorMessage);
        }
    } catch (error) {
        console.error('Error starting video call:', error);
        alert('Network error occurred. Please check your connection and try again.');
    }
};

const joinVideoCall = (appointment) => {
    console.log('Attempting to join video call for appointment:', appointment.id);

    // Check user's role for this appointment
    const role = getUserRoleForAppointment(appointment);

    if (!role) {
        alert('You are not authorized to join this video call.');
        return;
    }

    // If user is a provider, they should start the call instead
    if (role === 'provider') {
        alert('As the provider, please use "Start Call" to begin the video session.');
        return;
    }

    // Validate appointment has video session
    if (!appointment.video_session_id) {
        console.error('No video session found for appointment');
        alert('Video session not available yet. Please wait for the provider to start the call first.');
        return;
    }

    selectedAppointment.value = appointment;
    userRole.value = role; // Use determined role
    showVideoCall.value = true;
};

const closeVideoCall = () => {
    console.log('Closing video call');
    showVideoCall.value = false;
    selectedAppointment.value = null;
    // Refresh appointments to get updated status
    fetchAppointments();
};

const handleVideoCallError = (error) => {
    console.error('Video call error:', error);

    // Provide more specific error messages
    let errorMessage = 'Video call error occurred';
    if (typeof error === 'string') {
        errorMessage = error;
    } else if (error && error.message) {
        errorMessage = error.message;
    }

    // Check for common error types
    if (errorMessage.includes('INVALID_REMOTE_USER')) {
        errorMessage = 'Unable to connect to the other participant. Please try again.';
    } else if (errorMessage.includes('NETWORK_ERROR')) {
        errorMessage = 'Network connection issue. Please check your internet connection.';
    } else if (errorMessage.includes('permission')) {
        errorMessage = 'Camera or microphone permission denied. Please allow access and try again.';
    }

    alert(errorMessage);
    closeVideoCall();
};

// Date and time formatting functions
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Check if it's today
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        }

        // Check if it's tomorrow
        if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow';
        }

        // Format as readable date
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
        });
    } catch (error) {
        return dateString;
    }
};

const formatTime = (appointment) => {
    // Try to get time from time_slot first
    if (appointment.time_slot?.start_time) {
        const startTime = appointment.time_slot.start_time;
        const endTime = appointment.time_slot.end_time;

        try {
            // Convert 24-hour format to 12-hour format
            const formatTime12Hour = (time24) => {
                const [hours, minutes] = time24.split(':');
                const hour = parseInt(hours);
                const ampm = hour >= 12 ? 'PM' : 'AM';
                const hour12 = hour % 12 || 12;
                return `${hour12}:${minutes} ${ampm}`;
            };

            if (endTime) {
                return `${formatTime12Hour(startTime)} - ${formatTime12Hour(endTime)}`;
            } else {
                return formatTime12Hour(startTime);
            }
        } catch (error) {
            return `${startTime}${endTime ? ` - ${endTime}` : ''}`;
        }
    }

    // Fallback to other time fields
    if (appointment.time) {
        return appointment.time;
    }

    if (appointment.scheduled_at) {
        try {
            const date = new Date(appointment.scheduled_at);
            return date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            return appointment.scheduled_at.split('T')[1]?.substring(0, 5) || 'N/A';
        }
    }

    return 'N/A';
};

onMounted(() => {
    fetchAppointments();
});
</script>

<template>
    <Head title="Appointment Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Appointment Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-calendar-alt text-2xl text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Appointments</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ appointments.length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-clock text-2xl text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Scheduled</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ appointments.filter(a => a.status === 'scheduled').length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-2xl text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ appointments.filter(a => a.status === 'completed').length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-dollar-sign text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        ${{ appointments.reduce((sum, a) => sum + (parseFloat(a.amount || a.service?.price || a.price) || 0), 0).toFixed(2) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appointments Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>
                        
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Appointment
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Patient
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Provider
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="appointment in appointments" :key="appointment.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ appointment.service?.name || appointment.reason || 'Consultation' }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <span :class="getTypeBadgeClass(appointment.is_telemedicine ? 'telemedicine' : 'in-person')" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                    {{ appointment.is_telemedicine ? 'telemedicine' : 'in-person' }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ appointment.patient_name || appointment.patient?.name || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ appointment.provider_name || appointment.provider?.name || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ formatDate(appointment.date || appointment.scheduled_at) }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ formatTime(appointment) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(appointment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ appointment.status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            ${{ typeof appointment.amount === 'number' ? appointment.amount.toFixed(2) : parseFloat(appointment.amount || appointment.service?.price || appointment.price || 0).toFixed(2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex flex-wrap gap-2">
                                                <button
                                                    @click="viewAppointment(appointment)"
                                                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
                                                    title="View appointment details"
                                                >
                                                    <i class="fas fa-eye mr-1"></i>
                                                    View
                                                </button>

                                                <!-- Video Call Buttons for Telemedicine - Role-based -->
                                                <template v-if="appointment.is_telemedicine && ['scheduled', 'confirmed', 'in_progress'].includes(appointment.status)">
                                                    <!-- Start Call Button - Only for Providers -->
                                                    <button
                                                        v-if="canStartVideoCall(appointment)"
                                                        @click="startVideoCall(appointment)"
                                                        class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 transition-colors duration-200"
                                                        title="Start video call"
                                                    >
                                                        <i class="fas fa-video mr-1"></i>
                                                        Start Call
                                                    </button>

                                                    <!-- Join Call Button - Only for Patients when session exists -->
                                                    <button
                                                        v-if="canJoinVideoCall(appointment)"
                                                        @click="joinVideoCall(appointment)"
                                                        class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200"
                                                        title="Join video call"
                                                    >
                                                        <i class="fas fa-phone mr-1"></i>
                                                        Join Call
                                                    </button>

                                                    <!-- Waiting Message for Patients when no session exists -->
                                                    <span
                                                        v-if="getUserRoleForAppointment(appointment) === 'patient' && !appointment.video_session_id"
                                                        class="text-gray-500 dark:text-gray-400 text-sm italic"
                                                        title="Waiting for provider to start the call"
                                                    >
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Waiting for provider...
                                                    </span>
                                                </template>

                                                <button
                                                    v-if="appointment.status === 'scheduled'"
                                                    @click="editAppointment(appointment)"
                                                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200"
                                                    title="Edit appointment"
                                                >
                                                    <i class="fas fa-edit mr-1"></i>
                                                    Edit
                                                </button>
                                                <button
                                                    v-if="appointment.status === 'scheduled'"
                                                    @click="cancelAppointment(appointment)"
                                                    :disabled="isActionLoading(appointment.id)"
                                                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    title="Cancel appointment"
                                                >
                                                    <i v-if="isActionLoading(appointment.id)" class="fas fa-spinner fa-spin mr-1"></i>
                                                    <i v-else class="fas fa-times mr-1"></i>
                                                    {{ isActionLoading(appointment.id) ? 'Cancelling...' : 'Cancel' }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Call Modal -->
        <VideoCallModal
            v-if="selectedAppointment"
            :is-open="showVideoCall"
            :appointment-id="selectedAppointment.id"
            :appointment="selectedAppointment"
            :user-role="userRole"
            @close="closeVideoCall"
            @error="handleVideoCallError"
        />
    </AppLayout>
</template>
