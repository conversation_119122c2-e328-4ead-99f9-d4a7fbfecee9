<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  stats: {
    type: Object,
    default: () => ({
      upcomingAppointments: 0,
      completedAppointments: 0,
      healthRecords: 0
    })
  },
  recentAppointments: {
    type: Array,
    default: () => []
  },
  quickActions: {
    type: Array,
    default: () => []
  },
  kpiData: {
    type: Object,
    default: () => ({})
  },
  user: {
    type: Object,
    required: true
  }
});

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Reactive data
const loading = ref(false);
const lastUpdated = ref('Never');
const selectedRole = ref('admin');
const error = ref(null);

const kpiData = ref({
  // User metrics
  total_users: 0,
  monthly_active_users: 0,
  daily_active_users: 0,
  user_growth_rate: 0,

  // Appointment metrics
  total_appointments: 0,
  monthly_appointments: 0,
  daily_appointments: 0,
  appointment_completion_rate: 0,

  // Consult metrics
  total_consults: 0,
  monthly_consults: 0,
  daily_consults: 0,

  // Conversion metrics
  consult_to_appointment_ratio: 0,
  repeat_consult_users: 0,

  // Diagnostic metrics
  diagnostic_accuracy: 0,
  diagnostic_feedback_count: 0,
  top_accurate_diagnoses: [],
  top_inaccurate_diagnoses: [],

  // Location data
  user_locations: [],

  // Revenue metrics
  total_revenue: 0,
  monthly_revenue: 0,
  average_appointment_value: 0,
  ...props.kpiData
});

// Computed properties
const isAdmin = computed(() => {
  return props.user && props.user.role === 'admin';
});

const canViewAnalytics = computed(() => {
  return props.user && (props.user.role === 'admin' || props.user.user_permissions?.includes('view analytics'));
});

// Methods
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusClass = (status) => {
  const classes = {
    'scheduled': 'bg-blue-100 text-blue-800',
    'confirmed': 'bg-green-100 text-green-800',
    'in_progress': 'bg-yellow-100 text-yellow-800',
    'completed': 'bg-gray-100 text-gray-800',
    'cancelled': 'bg-red-100 text-red-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getDashboardTitle = () => {
  const titles = {
    admin: 'Admin Dashboard',
    provider: 'Provider Dashboard',
    patient: 'Patient Dashboard',
    care_manager: 'Care Manager Dashboard'
  };
  return titles[selectedRole.value] || 'Dashboard';
};

const getDashboardSubtitle = () => {
  const subtitles = {
    admin: 'Manage your healthcare platform from one place',
    provider: 'Manage your patients and appointments',
    patient: 'Track your health and appointments',
    care_manager: 'Monitor patient care and compliance'
  };
  return subtitles[selectedRole.value] || 'Manage your healthcare services';
};

const calculatePercentage = (value, total) => {
  if (!total) return 0;
  return Math.round((value / total) * 100);
};

const calculateLocationPercentage = (count) => {
  if (!kpiData.value.user_locations || kpiData.value.user_locations.length === 0) return 0;

  const maxCount = Math.max(...kpiData.value.user_locations.map(loc => loc.count));
  return Math.round((count / maxCount) * 100);
};

const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const onRoleChange = () => {
  fetchDashboardData(true);
};

const fetchDashboardData = async (refresh = false) => {
  if (!canViewAnalytics.value) {
    error.value = 'You do not have permission to view analytics data.';
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get('/api/management/dashboard/kpi', {
      params: { refresh },
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    kpiData.value = {
      ...kpiData.value,
      ...response.data
    };

    lastUpdated.value = new Date().toLocaleString();
  } catch (err) {
    console.error('Error fetching KPI data:', err);
    if (err.response) {
      error.value = `Failed to load KPI data: ${err.response.data.message || err.message}`;
    } else if (err.request) {
      error.value = 'Failed to load KPI data: No response from server';
    } else {
      error.value = `Failed to load KPI data: ${err.message}`;
    }
  } finally {
    loading.value = false;
  }
};

// Initialize on mount
onMounted(() => {
  if (props.user) {
    selectedRole.value = props.user.role || 'admin';
  }
  // Temporarily disable automatic KPI fetching to debug the basic dashboard
  // if (canViewAnalytics.value) {
  //   fetchDashboardData();
  // }
});
</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="unified-dashboard">
            <div class="dashboard-container">
                <!-- Header Section -->
                <div class="dashboard-header">
                    <div class="header-content">
                        <h1 class="dashboard-title">{{ getDashboardTitle() }}</h1>
                        <p class="dashboard-subtitle">{{ getDashboardSubtitle() }}</p>
                    </div>
                    <div class="header-actions">
                        <!-- Role Selector for Admin -->
                        <select
                            v-if="isAdmin"
                            v-model="selectedRole"
                            @change="onRoleChange"
                            class="role-selector"
                        >
                            <option value="admin">Admin View</option>
                            <option value="provider">Provider View</option>
                            <option value="patient">Patient View</option>
                            <option value="care_manager">Care Manager View</option>
                        </select>

                        <!-- Refresh Button -->
                        <button
                            v-if="canViewAnalytics"
                            @click="fetchDashboardData(true)"
                            class="refresh-btn"
                            :disabled="loading"
                        >
                            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
                            <i v-else class="fas fa-sync-alt"></i>
                            {{ loading ? 'Refreshing...' : 'Refresh Data' }}
                        </button>
                    </div>
                </div>

                <!-- Enhanced Admin Stats Section -->
                <div v-if="isAdmin" class="dashboard-content bg-white rounded-lg shadow p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Platform Overview</h3>

                    <!-- Primary KPIs -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Total Users</p>
                                    <p class="text-2xl font-bold text-blue-600">{{ stats.totalUsers || 0 }}</p>
                                    <p class="text-xs text-gray-400">{{ stats.newUsersThisMonth || 0 }} new this month</p>
                                </div>
                                <div class="text-blue-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Total Appointments</p>
                                    <p class="text-2xl font-bold text-green-600">{{ stats.totalAppointments || 0 }}</p>
                                    <p class="text-xs text-gray-400">{{ stats.newAppointmentsThisMonth || 0 }} new this month</p>
                                </div>
                                <div class="text-green-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Healthcare Providers</p>
                                    <p class="text-2xl font-bold text-purple-600">{{ stats.totalProviders || 0 }}</p>
                                    <p class="text-xs text-gray-400">Active providers</p>
                                </div>
                                <div class="text-purple-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-orange-50 rounded-lg p-4 border-l-4 border-orange-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Monthly Revenue</p>
                                    <p class="text-2xl font-bold text-orange-600">${{ stats.monthlyRevenue || 0 }}</p>
                                    <p class="text-xs text-gray-400">Total: ${{ stats.totalRevenue || 0 }}</p>
                                </div>
                                <div class="text-orange-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div v-if="quickActions.length > 0" class="dashboard-content bg-white rounded-lg shadow p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Link
                            v-for="action in quickActions"
                            :key="action.href"
                            :href="action.href"
                            class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-150"
                        >
                            <div class="flex-shrink-0 mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ action.title }}</h4>
                            </div>
                        </Link>
                    </div>
                </div>

                <!-- KPI Metrics Section -->
                <template v-if="canViewAnalytics">
                    <div class="dashboard-content bg-white rounded-lg shadow p-6 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-700">Key Performance Indicators</h3>
                            <div class="text-sm text-gray-500">
                                Last updated: {{ lastUpdated }}
                            </div>
                        </div>

                        <!-- Error message -->
                        <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <div class="flex">
                                <div class="py-1">
                                    <svg class="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                        <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-bold">Error</p>
                                    <p class="text-sm">{{ error }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Loading indicator -->
                        <div v-if="loading" class="flex justify-center items-center py-8">
                            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                            <span class="ml-3 text-gray-600">Loading KPI data...</span>
                        </div>

                        <!-- Main KPI Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <!-- User Metrics -->
                            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500 hover:shadow-md transition duration-150">
                                <div class="flex justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Total Users</p>
                                        <p class="text-2xl font-bold">{{ kpiData.total_users || 0 }}</p>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs text-green-600 flex items-center" v-if="kpiData.user_growth_rate > 0">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                                {{ kpiData.user_growth_rate || 0 }}%
                                            </span>
                                            <span class="text-xs text-red-600 flex items-center" v-else-if="kpiData.user_growth_rate < 0">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                                {{ Math.abs(kpiData.user_growth_rate || 0) }}%
                                            </span>
                                            <span class="text-xs text-gray-500" v-else>No change</span>
                                        </div>
                                    </div>
                                    <div class="text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500 hover:shadow-md transition duration-150">
                                <div class="flex justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Monthly Active Users</p>
                                        <p class="text-2xl font-bold">{{ kpiData.monthly_active_users || 0 }}</p>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs text-gray-500">{{ calculatePercentage(kpiData.monthly_active_users, kpiData.total_users) }}% of total</span>
                                        </div>
                                    </div>
                                    <div class="text-green-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500 hover:shadow-md transition duration-150">
                                <div class="flex justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Daily Active Users</p>
                                        <p class="text-2xl font-bold">{{ kpiData.daily_active_users || 0 }}</p>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs text-gray-500">{{ calculatePercentage(kpiData.daily_active_users, kpiData.monthly_active_users) }}% of monthly</span>
                                        </div>
                                    </div>
                                    <div class="text-purple-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-red-500 hover:shadow-md transition duration-150">
                                <div class="flex justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Diagnostic Accuracy</p>
                                        <p class="text-2xl font-bold">{{ kpiData.diagnostic_accuracy || 0 }}%</p>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs text-gray-500">Based on {{ kpiData.diagnostic_feedback_count || 0 }} feedbacks</span>
                                        </div>
                                    </div>
                                    <div class="text-red-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Metrics Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <!-- Appointment Metrics -->
                            <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition duration-150">
                                <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    Appointments
                                </h4>
                                <div class="grid grid-cols-3 gap-2">
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Total</p>
                                        <p class="text-xl font-bold">{{ kpiData.total_appointments || 0 }}</p>
                                    </div>
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Monthly</p>
                                        <p class="text-xl font-bold">{{ kpiData.monthly_appointments || 0 }}</p>
                                    </div>
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Daily</p>
                                        <p class="text-xl font-bold">{{ kpiData.daily_appointments || 0 }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Consult Metrics -->
                            <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition duration-150">
                                <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                    </svg>
                                    AI Consults
                                </h4>
                                <div class="grid grid-cols-3 gap-2">
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Total</p>
                                        <p class="text-xl font-bold">{{ kpiData.total_consults || 0 }}</p>
                                    </div>
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Monthly</p>
                                        <p class="text-xl font-bold">{{ kpiData.monthly_consults || 0 }}</p>
                                    </div>
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Daily</p>
                                        <p class="text-xl font-bold">{{ kpiData.daily_consults || 0 }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Conversion Metrics -->
                            <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition duration-150">
                                <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                    Conversion Metrics
                                </h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Consult to Appt Ratio</p>
                                        <p class="text-xl font-bold">{{ kpiData.consult_to_appointment_ratio || 0 }}%</p>
                                    </div>
                                    <div class="text-center p-2 bg-gray-50 rounded">
                                        <p class="text-sm text-gray-500">Repeat Consult Users</p>
                                        <p class="text-xl font-bold">{{ kpiData.repeat_consult_users || 0 }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Location Data -->
                        <div v-if="kpiData.user_locations && kpiData.user_locations.length > 0" class="mt-4">
                            <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                User Locations
                            </h4>
                            <div class="bg-white rounded-lg shadow p-4">
                                <div v-for="(location, index) in kpiData.user_locations" :key="index" class="mb-3">
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-gray-700">{{ location.location }}</span>
                                        <span class="text-gray-900 font-semibold">{{ location.count }}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-teal-600 h-2.5 rounded-full" :style="{ width: calculateLocationPercentage(location.count) + '%' }"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Management Cards Section -->
                    <div class="dashboard-content bg-white rounded-lg shadow p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Management Dashboard</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Users Card -->
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-150">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-white p-3 rounded-full">
                                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-white">Users</h3>
                                            <p class="text-blue-100">Manage system users</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <Link href="/users" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            View Users
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Providers Card -->
                            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-150">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-white p-3 rounded-full">
                                            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-white">Providers</h3>
                                            <p class="text-green-100">Manage healthcare providers</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <Link href="/providers" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                            View Providers
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Patients Card -->
                            <div class="bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-150">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-white p-3 rounded-full">
                                            <svg class="h-6 w-6 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-white">Patients</h3>
                                            <p class="text-cyan-100">Manage patients</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <Link href="/patients" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-cyan-600 bg-white hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500">
                                            View Patients
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Message for users without analytics permission -->
                <template v-if="!canViewAnalytics">
                    <div class="dashboard-content bg-white rounded-lg shadow p-6 mb-6">
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            <h3 class="mt-2 text-lg font-medium text-gray-900">Analytics Access Required</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                You don't have permission to view analytics data. Please contact an administrator if you need access.
                            </p>
                        </div>
                    </div>
                </template>

                <!-- Recent Appointments -->
                <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Recent Appointments</h3>
                            <Link
                                href="/appointments"
                                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                                View All
                            </Link>
                        </div>

                        <div v-if="recentAppointments.length === 0" class="text-center py-8">
                            <p class="text-gray-500">No appointments yet.</p>
                            <Link
                                href="/providers"
                                class="mt-2 inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
                            >
                                Book Your First Appointment
                            </Link>
                        </div>

                        <div v-else class="space-y-4">
                            <div
                                v-for="appointment in recentAppointments"
                                :key="appointment.id"
                                class="border border-gray-200 rounded-lg p-4"
                            >
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-gray-900">
                                            Dr. {{ appointment.provider.user.name }}
                                        </h4>
                                        <p class="text-sm text-gray-600">{{ appointment.provider?.specialization || 'General Practice' }}</p>
                                        <p class="text-sm text-gray-500 mt-1">
                                            {{ formatDate(appointment.scheduled_at) }}
                                        </p>
                                    </div>
                                    <span
                                        :class="getStatusClass(appointment.status)"
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                    >
                                        {{ appointment.status }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <Link
                                href="/chat"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-gray-900">AI Chat</span>
                            </Link>

                            <Link
                                href="/providers"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-gray-900">Find Doctors</span>
                            </Link>

                            <Link
                                href="/appointments/create"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-gray-900">Book Appointment</span>
                            </Link>

                            <Link
                                href="/settings/profile"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-gray-900">Settings</span>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Unified Dashboard Styles */
.unified-dashboard {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.header-content p.dashboard-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.role-selector {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.role-selector:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Override Tailwind styles for consistency */
.unified-dashboard .bg-white {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.unified-dashboard .bg-gray-50 {
  background: #f7fafc;
  border-radius: 12px;
}

.unified-dashboard .text-gray-800 {
  color: #2d3748;
}

.unified-dashboard .text-gray-600 {
  color: #718096;
}

.unified-dashboard .text-gray-700 {
  color: #4a5568;
}

/* Animations */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .role-selector,
  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
