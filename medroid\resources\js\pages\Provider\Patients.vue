<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import axios from 'axios';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'My Patients', href: '/provider/patients' },
];

// Reactive data
const loading = ref(false);
const patients = ref([]);
const searchQuery = ref('');

// Methods
const fetchPatients = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/provider/patients');
        patients.value = response.data.patients || [];
    } catch (error) {
        console.error('Error fetching patients:', error);
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Initialize on mount
onMounted(() => {
    fetchPatients();
});
</script>

<template>
    <Head title="My Patients" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">My Patients</h1>
                <p class="mt-2 text-gray-600">Manage your patient records and history</p>
            </div>

            <!-- Search and Filters -->
            <div class="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search patients..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                </div>

                <button
                    @click="fetchPatients"
                    :disabled="loading"
                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                    <i :class="['fas', loading ? 'fa-spinner fa-spin' : 'fa-sync-alt', 'mr-2']"></i>
                    {{ loading ? 'Loading...' : 'Refresh' }}
                </button>
            </div>

            <!-- Patients List -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-xl font-semibold text-gray-900">Patient List</h2>
                </div>

                <div class="p-6">
                    <div v-if="loading" class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">Loading patients...</p>
                    </div>

                    <div v-else-if="patients.length === 0" class="text-center py-8">
                        <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-600">No patients found</p>
                        <p class="text-sm text-gray-500 mt-2">Patients will appear here after their first appointment</p>
                    </div>

                    <div v-else class="space-y-4">
                        <div
                            v-for="patient in patients"
                            :key="patient.id"
                            class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                        >
                            <div class="flex justify-between items-start">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-gray-500"></i>
                                    </div>
                                    
                                    <div>
                                        <h3 class="font-medium text-gray-900">
                                            {{ patient.user?.name || 'Unknown Patient' }}
                                        </h3>
                                        <p class="text-sm text-gray-600">{{ patient.user?.email }}</p>
                                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                                            <span v-if="patient.date_of_birth">
                                                <i class="fas fa-birthday-cake mr-1"></i>
                                                {{ formatDate(patient.date_of_birth) }}
                                            </span>
                                            <span v-if="patient.phone">
                                                <i class="fas fa-phone mr-1"></i>
                                                {{ patient.phone }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-col items-end space-y-2">
                                    <div class="text-sm text-gray-600">
                                        Last visit: {{ patient.last_appointment ? formatDate(patient.last_appointment) : 'Never' }}
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 text-sm">
                                            <i class="fas fa-eye mr-1"></i>
                                            View Records
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 text-sm">
                                            <i class="fas fa-calendar-plus mr-1"></i>
                                            Schedule
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
