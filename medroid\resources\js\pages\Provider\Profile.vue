<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import axios from 'axios';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Provider Profile', href: '/provider/profile' },
];

// Reactive data
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const successMessage = ref('');

const profile = ref({
    specialization: '',
    bio: '',
    education: '',
    years_of_experience: 0,
    license_number: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    consultation_fee: 0,
    languages: [],
    certifications: []
});

const newLanguage = ref('');
const newCertification = ref('');

// Methods
const fetchProfile = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/api/provider/profile');
        if (response.data.provider) {
            profile.value = { ...profile.value, ...response.data.provider };
        }
    } catch (err) {
        console.error('Error fetching profile:', err);
        error.value = 'Failed to load profile data';
    } finally {
        loading.value = false;
    }
};

const saveProfile = async () => {
    saving.value = true;
    error.value = null;
    successMessage.value = '';
    
    try {
        await axios.post('/api/provider/profile', profile.value);
        successMessage.value = 'Profile updated successfully!';
        setTimeout(() => {
            successMessage.value = '';
        }, 3000);
    } catch (err) {
        console.error('Error saving profile:', err);
        error.value = 'Failed to save profile. Please try again.';
    } finally {
        saving.value = false;
    }
};

const addLanguage = () => {
    if (newLanguage.value.trim() && !profile.value.languages.includes(newLanguage.value.trim())) {
        profile.value.languages.push(newLanguage.value.trim());
        newLanguage.value = '';
    }
};

const removeLanguage = (index) => {
    profile.value.languages.splice(index, 1);
};

const addCertification = () => {
    if (newCertification.value.trim() && !profile.value.certifications.includes(newCertification.value.trim())) {
        profile.value.certifications.push(newCertification.value.trim());
        newCertification.value = '';
    }
};

const removeCertification = (index) => {
    profile.value.certifications.splice(index, 1);
};

// Initialize on mount
onMounted(() => {
    fetchProfile();
});
</script>

<template>
    <Head title="Provider Profile" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Provider Profile</h1>
                <p class="mt-2 text-gray-600">Manage your professional information and credentials</p>
            </div>

            <!-- Error/Success Messages -->
            <div v-if="error" class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                    <p class="text-red-700">{{ error }}</p>
                </div>
            </div>

            <div v-if="successMessage" class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                    <p class="text-green-700">{{ successMessage }}</p>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="text-center py-12">
                <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">Loading profile...</p>
            </div>

            <!-- Profile Form -->
            <div v-else class="bg-white rounded-lg shadow-sm border">
                <form @submit.prevent="saveProfile" class="p-6 space-y-6">
                    <!-- Professional Information -->
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Professional Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Specialization</label>
                                <input
                                    v-model="profile.specialization"
                                    type="text"
                                    placeholder="e.g., Cardiology, Dermatology"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Years of Experience</label>
                                <input
                                    v-model.number="profile.years_of_experience"
                                    type="number"
                                    min="0"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">License Number</label>
                                <input
                                    v-model="profile.license_number"
                                    type="text"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Consultation Fee ($)</label>
                                <input
                                    v-model.number="profile.consultation_fee"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Education</label>
                            <textarea
                                v-model="profile.education"
                                rows="3"
                                placeholder="Your educational background and qualifications"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            ></textarea>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                            <textarea
                                v-model="profile.bio"
                                rows="4"
                                placeholder="Tell patients about yourself and your practice"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            ></textarea>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                <input
                                    v-model="profile.phone"
                                    type="tel"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                <input
                                    v-model="profile.address"
                                    type="text"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                <input
                                    v-model="profile.city"
                                    type="text"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                <input
                                    v-model="profile.state"
                                    type="text"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">ZIP Code</label>
                                <input
                                    v-model="profile.zip_code"
                                    type="text"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Languages -->
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Languages</h2>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span
                                v-for="(language, index) in profile.languages"
                                :key="index"
                                class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                            >
                                {{ language }}
                                <button
                                    @click="removeLanguage(index)"
                                    type="button"
                                    class="ml-2 text-blue-600 hover:text-blue-800"
                                >
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </span>
                        </div>
                        <div class="flex space-x-2">
                            <input
                                v-model="newLanguage"
                                @keyup.enter="addLanguage"
                                type="text"
                                placeholder="Add a language"
                                class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                            <button
                                @click="addLanguage"
                                type="button"
                                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                            >
                                Add
                            </button>
                        </div>
                    </div>

                    <!-- Certifications -->
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Certifications</h2>
                        <div class="space-y-2 mb-4">
                            <div
                                v-for="(certification, index) in profile.certifications"
                                :key="index"
                                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                            >
                                <span class="text-sm">{{ certification }}</span>
                                <button
                                    @click="removeCertification(index)"
                                    type="button"
                                    class="text-red-600 hover:text-red-800"
                                >
                                    <i class="fas fa-trash text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <input
                                v-model="newCertification"
                                @keyup.enter="addCertification"
                                type="text"
                                placeholder="Add a certification"
                                class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                            <button
                                @click="addCertification"
                                type="button"
                                class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
                            >
                                Add
                            </button>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end pt-6 border-t">
                        <button
                            type="submit"
                            :disabled="saving"
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                        >
                            <i v-if="saving" class="fas fa-spinner fa-spin mr-2"></i>
                            <i v-else class="fas fa-save mr-2"></i>
                            {{ saving ? 'Saving...' : 'Save Profile' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
