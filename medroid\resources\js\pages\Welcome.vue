<script setup lang="ts">
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, nextTick, onMounted, onUnmounted, computed } from 'vue';
import MedroidLogo from '@/components/MedroidLogo.vue';
import ChatInput from '@/components/ChatInput.vue';
import axios from 'axios';

// Reactive data
const messages = ref<Array<{
    id: number;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
}>>([]);

const currentMessageIndex = ref(0);
const chatTimer = ref<NodeJS.Timeout | null>(null);
const isPageVisible = ref(false);

// Get auth state from Inertia
const page = usePage();
const user = page.props.auth?.user;

// Anonymous chat state
const isChatMode = ref(false);
const isLoading = ref(false);
const userMessage = ref('');
const conversationId = ref<string | null>(null);
const anonymousId = ref<string>('');
const realChatMessages = ref<Array<{
    id: string;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
}>>([]);

// Popup states
const showAgeGenderPopup = ref(false);
const showLoginPopup = ref(false);
const userAge = ref('');
const userGender = ref('');
const hasCollectedDemographics = ref(false);
const messageCount = ref(0);

// Chat input ref
const chatInputRef = ref<HTMLTextAreaElement | null>(null);

// Chat messages for animation (matching Flutter implementation)
const chatMessages = [
    {
        id: 1,
        type: 'ai' as const,
        content: 'Hi! I\'m your AI health assistant. How are you feeling today?',
        timestamp: new Date()
    },
    {
        id: 2,
        type: 'user' as const,
        content: 'I\'ve been having trouble sleeping lately. Any suggestions?',
        timestamp: new Date()
    },
    {
        id: 3,
        type: 'ai' as const,
        content: 'Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep.',
        timestamp: new Date()
    },
    {
        id: 4,
        type: 'user' as const,
        content: 'That makes sense. I\'ll try that tonight. Thank you!',
        timestamp: new Date()
    },
    {
        id: 5,
        type: 'ai' as const,
        content: 'You\'re welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?',
        timestamp: new Date()
    },
    {
        id: 6,
        type: 'user' as const,
        content: 'Yes, that would be helpful. I think stress might be affecting my sleep.',
        timestamp: new Date()
    },
    {
        id: 7,
        type: 'ai' as const,
        content: 'Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system.',
        timestamp: new Date()
    },
    {
        id: 8,
        type: 'user' as const,
        content: 'I\'ll definitely try that breathing technique. Thank you for the personalized advice!',
        timestamp: new Date()
    }
];

// Initialize chat animation
const initializeChatAnimation = () => {
    messages.value = [];
    currentMessageIndex.value = 0;
    startChatAnimation();
};

const startChatAnimation = () => {
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
    }

    chatTimer.value = setInterval(() => {
        if (currentMessageIndex.value < chatMessages.length) {
            const message = { ...chatMessages[currentMessageIndex.value] };
            message.timestamp = new Date();
            messages.value.push(message);
            currentMessageIndex.value++;
        } else {
            // Restart animation after a pause
            setTimeout(() => {
                if (isPageVisible.value) {
                    initializeChatAnimation();
                }
            }, 4000);
            if (chatTimer.value) {
                clearInterval(chatTimer.value);
                chatTimer.value = null;
            }
        }
    }, 1500);
};

onMounted(() => {
    isPageVisible.value = true;
    setTimeout(() => {
        initializeChatAnimation();
    }, 1000);
});

// Generate anonymous ID
const generateAnonymousId = () => {
    return 'anon_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
};

// Start anonymous chat
const startAnonymousChat = async () => {
    if (!userMessage.value.trim()) return;

    try {
        isLoading.value = true;
        console.log('Starting anonymous chat...');

        // Generate anonymous ID if not exists
        if (!anonymousId.value) {
            anonymousId.value = generateAnonymousId();
        }

        console.log('Anonymous ID:', anonymousId.value);
        console.log('Initial message:', userMessage.value.trim());

        // Start conversation (just creates the conversation, doesn't send message yet)
        const startResponse = await axios.post('/api/anonymous/chat/start');

        console.log('Start API Response:', startResponse.data);

        conversationId.value = startResponse.data.conversation_id;
        anonymousId.value = startResponse.data.anonymous_id;

        // Add user message to chat
        realChatMessages.value.push({
            id: Date.now().toString(),
            type: 'user',
            content: userMessage.value.trim(),
            timestamp: new Date()
        });

        const messageToSend = userMessage.value.trim();
        userMessage.value = '';

        // Transition to chat mode first
        await transitionToChatMode();

        // Now send the first message
        const messageResponse = await axios.post('/api/anonymous/chat/message', {
            conversation_id: String(conversationId.value),
            anonymous_id: anonymousId.value,
            message: messageToSend,
            gender: userGender.value || null,
            age: userAge.value || null,
            request_full_response: false,
            generate_title: true
        });

        console.log('Message API Response:', messageResponse.data);

        // Add AI response
        if (messageResponse.data.message) {
            realChatMessages.value.push({
                id: (Date.now() + 1).toString(),
                type: 'ai',
                content: messageResponse.data.message,
                timestamp: new Date()
            });
        }

        messageCount.value++;

        // Show age/gender popup after first message
        if (messageCount.value === 1 && !hasCollectedDemographics.value) {
            setTimeout(() => {
                showAgeGenderPopup.value = true;
            }, 1000);
        }

        // Check if appointment booking is detected
        if (messageResponse.data.has_appointment_intent && messageResponse.data.requires_auth) {
            setTimeout(() => {
                showLoginPopup.value = true;
            }, 1000);
        }

        // Scroll to bottom
        await nextTick();
        scrollToBottom();

        // Refocus the input after starting chat
        setTimeout(() => {
            if (chatInputRef.value && chatInputRef.value.focus) {
                chatInputRef.value.focus();
            }
        }, 100);

    } catch (error) {
        console.error('Error starting chat:', error);
        console.error('Error details:', error.response?.data);
        // Show error message to user
        alert('Failed to start chat. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Send message in chat mode
const sendMessage = async () => {
    if (!userMessage.value.trim() || !conversationId.value) return;

    try {
        isLoading.value = true;
        console.log('Sending message...');

        // Add user message to chat
        realChatMessages.value.push({
            id: Date.now().toString(),
            type: 'user',
            content: userMessage.value.trim(),
            timestamp: new Date()
        });

        const messageToSend = userMessage.value.trim();
        userMessage.value = '';

        console.log('Message to send:', messageToSend);
        console.log('Conversation ID:', conversationId.value);

        // Send message to API
        const response = await axios.post('/api/anonymous/chat/message', {
            conversation_id: String(conversationId.value),
            message: messageToSend,
            anonymous_id: anonymousId.value,
            gender: userGender.value || null,
            age: userAge.value || null,
            request_full_response: false,
            generate_title: true
        });

        console.log('Message API Response:', response.data);

        // Add AI response
        if (response.data.message) {
            realChatMessages.value.push({
                id: (Date.now() + 1).toString(),
                type: 'ai',
                content: response.data.message,
                timestamp: new Date()
            });
        }

        messageCount.value++;

        // Check if appointment booking is detected
        if (response.data.has_appointment_intent && response.data.requires_auth) {
            setTimeout(() => {
                showLoginPopup.value = true;
            }, 1000);
        }

        // Scroll to bottom
        await nextTick();
        scrollToBottom();

        // Refocus the input after sending message
        setTimeout(() => {
            if (chatInputRef.value && chatInputRef.value.focus) {
                chatInputRef.value.focus();
            }
        }, 100);

    } catch (error) {
        console.error('Error sending message:', error);
        console.error('Error details:', error.response?.data);
        // Show error message to user
        alert('Failed to send message. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Transition to chat mode
const transitionToChatMode = async () => {
    isChatMode.value = true;

    // Stop demo animation
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
        chatTimer.value = null;
    }

    await nextTick();

    // Focus on chat input after a short delay to allow transition
    setTimeout(() => {
        if (chatInputRef.value && chatInputRef.value.focus) {
            chatInputRef.value.focus();
        }
    }, 300);

    // Scroll to bottom
    scrollToBottom();
};

// Scroll to bottom of chat
const scrollToBottom = () => {
    const chatContainer = document.querySelector('.chat-messages-container');
    if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
};

// Handle age/gender submission
const submitAgeGender = () => {
    hasCollectedDemographics.value = true;
    showAgeGenderPopup.value = false;
};

// Handle login/register
const handleLogin = () => {
    // Store conversation data in localStorage for continuation after login
    localStorage.setItem('anonymous_conversation', JSON.stringify({
        conversation_id: conversationId.value,
        anonymous_id: anonymousId.value,
        messages: realChatMessages.value
    }));

    // Redirect to login
    window.location.href = '/login';
};

const handleRegister = () => {
    // Store conversation data in localStorage for continuation after register
    localStorage.setItem('anonymous_conversation', JSON.stringify({
        conversation_id: conversationId.value,
        anonymous_id: anonymousId.value,
        messages: realChatMessages.value
    }));

    // Redirect to register
    window.location.href = '/register';
};

// Handle enter key in textarea
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (isChatMode.value) {
            sendMessage();
        } else {
            startAnonymousChat();
        }
    }
};

onMounted(() => {
    isPageVisible.value = true;
    anonymousId.value = generateAnonymousId();

    setTimeout(() => {
        initializeChatAnimation();
    }, 1000);
});

onUnmounted(() => {
    isPageVisible.value = false;
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
    }
});
</script>

<template>
    <Head title="Medroid AI Doctor">
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <link href="https://fonts.googleapis.com/css2?family=Material+Icons" rel="stylesheet" />
    </Head>

    <div class="min-h-screen bg-white font-sans" :class="{ 'chat-mode': isChatMode }">
        <!-- Header with Logo and Sign In -->
        <header class="flex justify-between items-center px-8 py-6" :class="{ 'fixed top-0 left-0 right-0 z-10 bg-white border-b border-gray-200': isChatMode }">
            <div class="flex items-center space-x-2">
                <!-- Heart Logo -->
                <div class="w-8 h-8 bg-medroid-teal rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                </div>
                <span class="text-xl font-semibold text-gray-900">Medroid</span>
            </div>

            <div class="flex items-center space-x-2">
                <!-- User Icon -->
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex min-h-[calc(100vh-100px)]" :class="{ 'pb-32 lg:pb-40': isChatMode }">
            <!-- Left Panel - Content -->
            <div class="w-full lg:w-1/2 bg-white flex flex-col justify-center px-8 lg:px-16">
                <div class="max-w-lg">
                    <!-- Main Heading -->
                    <div class="mb-12">
                        <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                            Your free<br>
                            personal<br>
                            <span class="text-teal-400">AI Doctor</span> awaits<br>
                            you.
                        </h1>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            Fast, free, and private medical consultations powered by AI.
                        </p>
                    </div>

                    <!-- Features List -->
                    <div class="mb-12 space-y-5">
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-teal-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">100% free and private</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-orange-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">Instant medical advice</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-orange-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">Book real doctor appointments</span>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="space-y-4">
                        <template v-if="!user">
                            <!-- Sign In Button (Primary) -->
                            <button
                                @click="$inertia.visit(route('login'))"
                                class="w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200"
                            >
                                Sign In
                            </button>

                            <p class="text-sm text-gray-500">
                                Don't have an account?
                                <Link
                                    :href="route('register')"
                                    class="text-teal-400 hover:text-teal-500 font-medium ml-1"
                                >
                                    Sign up here
                                </Link>
                            </p>
                        </template>
                        <template v-else>
                            <button
                                @click="$inertia.visit(route('dashboard'))"
                                class="w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200"
                            >
                                Go to Dashboard
                            </button>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Chat Demo/Active Chat -->
            <div class="hidden lg:flex lg:w-1/2 bg-gray-50 flex-col px-8 lg:px-16 justify-center">
                <div class="max-w-lg mx-auto w-full">
                    <!-- Chat Header -->
                    <div class="mb-8">
                        <div class="flex items-start space-x-3 mb-8">
                            <div class="w-8 h-8 bg-teal-400 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-gray-800 font-medium">Hi, I'm Medroid, your personal AI doctor.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div v-if="!isChatMode" class="space-y-6 mb-10">
                        <!-- AI Message 1 -->
                        <div class="bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100">
                            <p class="text-gray-800 leading-relaxed">
                                As an AI doctor, I'm fast and free. I've already conducted 1,000,000+ consultations!
                            </p>
                        </div>

                        <!-- AI Message 2 -->
                        <div class="bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100">
                            <p class="text-gray-800 leading-relaxed">
                                When you're done, you can have a video consultation with a world class doctor, if you want, for just £55.
                            </p>
                        </div>
                    </div>

                    <!-- Real Chat Messages (Chat Mode) -->
                    <div v-if="isChatMode" class="space-y-6 mb-6 max-h-96 overflow-y-auto chat-messages-container pb-32">
                        <div
                            v-for="message in realChatMessages"
                            :key="message.id"
                            class="animate-fade-in-up"
                        >
                            <!-- AI Message (No bubble, left aligned with avatar) -->
                            <div v-if="message.type === 'ai'" class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-medroid-teal rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-gray-800 leading-relaxed text-sm">{{ message.content }}</p>
                                </div>
                            </div>

                            <!-- User Message (With bubble, right aligned) -->
                            <div v-else class="flex justify-end">
                                <div class="bg-medroid-orange text-white px-4 py-3 rounded-2xl rounded-br-md shadow-lg max-w-xs lg:max-w-md text-sm">
                                    {{ message.content }}
                                </div>
                            </div>
                        </div>

                        <!-- Loading indicator -->
                        <div v-if="isLoading" class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-medroid-teal rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="flex space-x-1">
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Input (Demo Mode Only) -->
                    <div v-if="!isChatMode">
                        <ChatInput
                            ref="chatInputRef"
                            v-model="userMessage"
                            placeholder="Type your health question..."
                            :is-loading="isLoading"
                            @send="startAnonymousChat"
                            @keydown="handleKeyDown"
                        />
                    </div>
                </div>
            </div>
        </main>

        <!-- Age/Gender Popup -->
        <div v-if="showAgeGenderPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                <h3 class="text-2xl font-bold text-medroid-navy mb-2">Help us personalize your care</h3>
                <p class="text-medroid-slate mb-6">This information helps us provide better health recommendations.</p>

                <div class="space-y-4">
                    <!-- Age Input -->
                    <div>
                        <label class="block text-sm font-medium text-medroid-navy mb-2">Age (Optional)</label>
                        <select
                            v-model="userAge"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange bg-white"
                        >
                            <option value="">Select age range</option>
                            <option value="18-25">18-25</option>
                            <option value="26-35">26-35</option>
                            <option value="36-45">36-45</option>
                            <option value="46-60">46-60</option>
                            <option value="61-75">61-75</option>
                            <option value="75+">75+</option>
                        </select>
                    </div>

                    <!-- Gender Selection -->
                    <div>
                        <label class="block text-sm font-medium text-medroid-navy mb-3">Gender (Optional)</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input
                                    v-model="userGender"
                                    type="radio"
                                    value="male"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Male</span>
                            </label>
                            <label class="flex items-center">
                                <input
                                    v-model="userGender"
                                    type="radio"
                                    value="female"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Female</span>
                            </label>
                            <label class="flex items-center">
                                <input
                                    v-model="userGender"
                                    type="radio"
                                    value="other"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Other</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3 mt-8">
                    <button
                        @click="submitAgeGender"
                        class="flex-1 bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Continue
                    </button>
                    <button
                        @click="showAgeGenderPopup = false"
                        class="px-6 py-3 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"
                    >
                        Skip
                    </button>
                </div>
            </div>
        </div>

        <!-- Login/Register Popup -->
        <div v-if="showLoginPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                <h3 class="text-2xl font-bold text-medroid-navy mb-2">Continue with an account</h3>
                <p class="text-medroid-slate mb-6">To book an appointment, please sign in or create an account. Your chat will continue after login.</p>

                <div class="space-y-3">
                    <button
                        @click="handleLogin"
                        class="w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Sign In
                    </button>
                    <button
                        @click="handleRegister"
                        class="w-full border border-medroid-border text-medroid-navy hover:bg-medroid-sage font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Create Account
                    </button>
                </div>

                <button
                    @click="showLoginPopup = false"
                    class="w-full mt-4 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"
                >
                    Continue without account
                </button>
            </div>
        </div>

        <!-- Chat Input (Fixed at bottom - positioned like right panel) -->
        <div v-if="isChatMode" class="fixed bottom-0 left-0 right-0 lg:left-1/2 lg:right-0 bg-white border-t border-gray-200 z-20">
            <div class="px-8 lg:px-16 py-4 lg:py-8">
                <div class="max-w-lg mx-auto w-full">
                    <ChatInput
                        ref="chatInputRef"
                        v-model="userMessage"
                        placeholder="Type your health question..."
                        :is-loading="isLoading"
                        :show-version="true"
                        @send="sendMessage"
                        @keydown="handleKeyDown"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
}

/* Chat mode transitions */
.chat-mode {
    transition: all 0.5s ease-in-out;
}

.chat-mode main {
    transition: all 0.5s ease-in-out;
}

/* Chat messages container */
.chat-messages-container {
    scroll-behavior: smooth;
    max-height: calc(100vh - 400px);
    padding-bottom: 8rem; /* Extra space to prevent messages going under input */
}

/* Ensure proper spacing when chat mode is active */
.chat-mode .chat-messages-container {
    max-height: calc(100vh - 300px); /* Increased to account for fixed input */
    padding-bottom: 10rem; /* More space in chat mode */
}

.chat-messages-container::-webkit-scrollbar {
    width: 6px;
}

.chat-messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Auto-resize textarea */
textarea {
    field-sizing: content;
}

/* Smooth transitions for layout changes */
.lg\:w-1\/2 {
    transition: width 0.5s ease-in-out;
}

/* Loading animation for dots */
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

/* Popup animations */
.fixed {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fixed > div {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
</style>