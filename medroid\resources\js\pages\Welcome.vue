<script setup lang="ts">
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, nextTick, onMounted, onUnmounted } from 'vue';
import MedroidLogo from '@/components/MedroidLogo.vue';

// Reactive data
const messages = ref<Array<{
    id: number;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
}>>([]);

const currentMessageIndex = ref(0);
const chatTimer = ref<NodeJS.Timeout | null>(null);
const isPageVisible = ref(false);

// Get auth state from Inertia
const page = usePage();
const user = page.props.auth?.user;

// Chat messages for animation (matching Flutter implementation)
const chatMessages = [
    {
        id: 1,
        type: 'ai' as const,
        content: 'Hi! I\'m your AI health assistant. How are you feeling today?',
        timestamp: new Date()
    },
    {
        id: 2,
        type: 'user' as const,
        content: 'I\'ve been having trouble sleeping lately. Any suggestions?',
        timestamp: new Date()
    },
    {
        id: 3,
        type: 'ai' as const,
        content: 'Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep.',
        timestamp: new Date()
    },
    {
        id: 4,
        type: 'user' as const,
        content: 'That makes sense. I\'ll try that tonight. Thank you!',
        timestamp: new Date()
    },
    {
        id: 5,
        type: 'ai' as const,
        content: 'You\'re welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?',
        timestamp: new Date()
    },
    {
        id: 6,
        type: 'user' as const,
        content: 'Yes, that would be helpful. I think stress might be affecting my sleep.',
        timestamp: new Date()
    },
    {
        id: 7,
        type: 'ai' as const,
        content: 'Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system.',
        timestamp: new Date()
    },
    {
        id: 8,
        type: 'user' as const,
        content: 'I\'ll definitely try that breathing technique. Thank you for the personalized advice!',
        timestamp: new Date()
    }
];

// Initialize chat animation
const initializeChatAnimation = () => {
    messages.value = [];
    currentMessageIndex.value = 0;
    startChatAnimation();
};

const startChatAnimation = () => {
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
    }

    chatTimer.value = setInterval(() => {
        if (currentMessageIndex.value < chatMessages.length) {
            const message = { ...chatMessages[currentMessageIndex.value] };
            message.timestamp = new Date();
            messages.value.push(message);
            currentMessageIndex.value++;
        } else {
            // Restart animation after a pause
            setTimeout(() => {
                if (isPageVisible.value) {
                    initializeChatAnimation();
                }
            }, 4000);
            if (chatTimer.value) {
                clearInterval(chatTimer.value);
                chatTimer.value = null;
            }
        }
    }, 1500);
};

onMounted(() => {
    isPageVisible.value = true;
    setTimeout(() => {
        initializeChatAnimation();
    }, 1000);
});

onUnmounted(() => {
    isPageVisible.value = false;
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
    }
});
</script>

<template>
    <Head title="Medroid AI Doctor">
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <link href="https://fonts.googleapis.com/css2?family=Material+Icons" rel="stylesheet" />
    </Head>

    <div class="min-h-screen bg-white font-sans">
        <!-- Header with Logo and Sign In -->
        <header class="flex justify-between items-center px-8 py-6">
            <div class="flex items-center space-x-2">
                <!-- Heart Logo -->
                <div class="w-8 h-8 bg-teal-400 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                </div>
                <span class="text-xl font-semibold text-gray-900">Medroid</span>
            </div>
            
            <div class="flex items-center space-x-2">
                <!-- User Icon -->
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex min-h-[calc(100vh-100px)]">
            <!-- Left Panel - Content -->
            <div class="w-full lg:w-1/2 bg-white flex flex-col justify-center px-8 lg:px-16">
                <div class="max-w-lg">
                    <!-- Main Heading -->
                    <div class="mb-12">
                        <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                            Your free<br>
                            personal<br>
                            <span class="text-teal-400">AI Doctor</span> awaits<br>
                            you.
                        </h1>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            Fast, free, and private medical consultations powered by AI.
                        </p>
                    </div>

                    <!-- Features List -->
                    <div class="mb-12 space-y-5">
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-teal-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">100% free and private</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-orange-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">Instant medical advice</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-orange-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">Book real doctor appointments</span>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="space-y-4">
                        <template v-if="!user">
                            <!-- Sign In Button (Primary) -->
                            <button
                                @click="$inertia.visit(route('login'))"
                                class="w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200"
                            >
                                Sign In
                            </button>

                            <p class="text-sm text-gray-500">
                                Don't have an account?
                                <Link
                                    :href="route('register')"
                                    class="text-teal-400 hover:text-teal-500 font-medium ml-1"
                                >
                                    Sign up here
                                </Link>
                            </p>
                        </template>
                        <template v-else>
                            <button
                                @click="$inertia.visit(route('dashboard'))"
                                class="w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200"
                            >
                                Go to Dashboard
                            </button>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Chat Demo (Desktop Only) -->
            <div class="hidden lg:flex lg:w-1/2 bg-gray-50 flex-col justify-center px-8 lg:px-16">
                <div class="max-w-lg mx-auto w-full">
                    <!-- Chat Header -->
                    <div class="mb-8">
                        <div class="flex items-start space-x-3 mb-8">
                            <div class="w-8 h-8 bg-teal-400 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-gray-800 font-medium">Hi, I'm Medroid, your personal AI doctor.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="space-y-6 mb-10">
                        <!-- AI Message 1 -->
                        <div class="bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100">
                            <p class="text-gray-800 leading-relaxed">
                                As an AI doctor, I'm fast and free. I've already conducted 1,000,000+ consultations!
                            </p>
                        </div>

                        <!-- AI Message 2 -->
                        <div class="bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100">
                            <p class="text-gray-800 leading-relaxed">
                                When you're done, you can have a video consultation with a world class doctor, if you want, for just £55.
                            </p>
                        </div>
                    </div>

                    <!-- Claude-like Chat Input -->
                    <div class="space-y-4">
                        <div class="relative">
                            <!-- Input Container -->
                            <div class="bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-200">
                                <!-- Tools Bar -->
                                <div class="flex items-center justify-between px-4 py-2 border-b border-gray-100">
                                    <div class="flex items-center space-x-2">
                                        <button class="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-150">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 008 10.172V5L8 4z" />
                                            </svg>
                                            <span>Tools</span>
                                        </button>
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        v1.0.27.2025-05-30
                                    </div>
                                </div>
                                
                                <!-- Input Area -->
                                <div class="relative">
                                    <textarea
                                        placeholder="Type your health question..."
                                        class="w-full px-4 py-4 text-gray-800 placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 text-base leading-relaxed min-h-[60px] max-h-[200px]"
                                        rows="1"
                                        readonly
                                    ></textarea>
                                    
                                    <!-- Bottom Bar -->
                                    <div class="flex items-center justify-between px-4 pb-3">
                                        <div class="flex items-center space-x-2">
                                            <!-- Attach Button -->
                                            <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-150">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                                </svg>
                                            </button>
                                            
                                            <!-- Microphone Button -->
                                            <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-150">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                                </svg>
                                            </button>
                                        </div>
                                        
                                        <!-- Send Button -->
                                        <button class="p-2 bg-gray-200 text-gray-500 rounded-lg hover:bg-gray-300 transition-colors duration-150">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Disclaimer -->
                        <p class="text-xs text-gray-500 text-center leading-relaxed px-4">
                            Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our 
                            <button class="text-gray-600 hover:text-gray-800 underline underline-offset-2">Terms of Service</button> and 
                            <button class="text-gray-600 hover:text-gray-800 underline underline-offset-2">Privacy Policy</button>.
                        </p>
                    </div>
                </div>
            </div>
        </main>
    </div>
</template>

<style scoped>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
}

/* Auto-resize textarea */
textarea {
    field-sizing: content;
}
</style>