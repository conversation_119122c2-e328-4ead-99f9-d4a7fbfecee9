<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

// Props from the controller
const props = defineProps({
    canResetPassword: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        default: null,
    },
    isLoginMode: {
        type: Boolean,
        default: false,
    },
});

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const showPassword = ref(false);
const showPasswordConfirmation = ref(false);
const isLoading = ref(false);
const isLoginMode = ref(props.isLoginMode); // Toggle between login and register
const chatMessages = ref([
    {
        type: 'bot',
        text: "Hi! I'm your AI health assistant. How are you feeling today?"
    },
    {
        type: 'user',
        text: "I've been having trouble sleeping lately. Any suggestions?"
    },
    {
        type: 'bot',
        text: "Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep."
    },
    {
        type: 'user',
        text: "That makes sense. I'll try that tonight. Thank you!"
    },
    {
        type: 'bot',
        text: "You're welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?"
    },
    {
        type: 'user',
        text: "Yes, that would be helpful. I think stress might be affecting my sleep."
    },
    {
        type: 'bot',
        text: "Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system."
    },
    {
        type: 'user',
        text: "I'll definitely try that breathing technique. Thank you for the personalized advice!"
    }
]);

const submit = () => {
    isLoading.value = true;
    const route_name = isLoginMode.value ? 'login' : 'register';
    form.post(route(route_name), {
        onFinish: () => {
            if (!isLoginMode.value) {
                form.reset('password', 'password_confirmation');
            } else {
                form.reset('password');
            }
            isLoading.value = false;
        },
    });
};

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};

const togglePasswordConfirmationVisibility = () => {
    showPasswordConfirmation.value = !showPasswordConfirmation.value;
};

const toggleMode = () => {
    isLoginMode.value = !isLoginMode.value;
    form.reset();
};
</script>

<template>
    <Head :title="isLoginMode ? 'Sign In' : 'Register'" />

    <div class="min-h-screen bg-gray-50 flex">
        <!-- Left Panel - Auth Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-white">
            <div class="max-w-md w-full">
                <!-- Header -->
                <div class="text-left mb-8">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">
                        Your health,
                    </h1>
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">
                        our priority
                    </h2>
                    <p class="text-gray-600 mb-8">
                        AI-powered healthcare that puts your wellness first.
                    </p>
                </div>

                <!-- Google Sign In Button -->
                <button
                    type="button"
                    class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 mb-6"
                >
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Continue with Google
                </button>

                <!-- Divider -->
                <div class="relative mb-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">OR</span>
                    </div>
                </div>

                <!-- Status Message -->
                <div v-if="status" class="mb-4 text-center text-sm font-medium text-green-600">
                    {{ status }}
                </div>

                <!-- Auth Form -->
                <form class="space-y-4" @submit.prevent="submit">
                    <!-- Name Field (Register mode only) -->
                    <div v-if="!isLoginMode">
                        <input
                            id="name"
                            v-model="form.name"
                            name="name"
                            type="text"
                            autocomplete="name"
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                            placeholder="Enter your full name"
                        />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <!-- Email Field -->
                    <div>
                        <input
                            id="email"
                            v-model="form.email"
                            name="email"
                            type="email"
                            autocomplete="email"
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                            :placeholder="isLoginMode ? 'Enter your personal or work email' : 'Enter your email address'"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <!-- Password Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password"
                                v-model="form.password"
                                name="password"
                                :type="showPassword ? 'text' : 'password'"
                                :autocomplete="isLoginMode ? 'current-password' : 'new-password'"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                                :placeholder="isLoginMode ? 'Enter your password' : 'Create a strong password'"
                            />
                            <button
                                type="button"
                                @click="togglePasswordVisibility"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-gray-600 transition-colors duration-200"
                            >
                                <svg v-if="showPassword" class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <!-- Password Confirmation Field (Register mode only) -->
                    <div v-if="!isLoginMode">
                        <div class="relative">
                            <input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                name="password_confirmation"
                                :type="showPasswordConfirmation ? 'text' : 'password'"
                                autocomplete="new-password"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                                placeholder="Confirm your password"
                            />
                            <button
                                type="button"
                                @click="togglePasswordConfirmationVisibility"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-gray-600 transition-colors duration-200"
                            >
                                <svg v-if="showPasswordConfirmation" class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password_confirmation" />
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button
                            type="submit"
                            :disabled="form.processing || isLoading"
                            class="w-full bg-orange-400 hover:bg-orange-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {{ form.processing || isLoading ? (isLoginMode ? 'Signing In...' : 'Creating Account...') : (isLoginMode ? 'Sign In' : 'Sign Up') }}
                        </button>
                    </div>

                    <!-- Toggle Mode Link -->
                    <div class="text-center pt-4">
                        <p class="text-sm text-gray-600">
                            {{ isLoginMode ? "Don't have an account?" : "Already have an account?" }}
                            <button
                                type="button"
                                @click="toggleMode"
                                class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200 underline"
                            >
                                {{ isLoginMode ? 'Sign up' : 'Sign in here' }}
                            </button>
                        </p>
                    </div>

                    <!-- Forgot Password (Login mode only) -->
                    <div v-if="isLoginMode && canResetPassword" class="text-center">
                        <Link
                            :href="route('password.request')"
                            class="text-sm text-gray-600 hover:text-gray-500 transition-colors duration-200 underline"
                        >
                            Forgot password?
                        </Link>
                    </div>

                    <!-- Privacy Policy (Register mode only) -->
                    <div v-if="!isLoginMode" class="text-center">
                        <p class="text-xs text-gray-500">
                            By continuing, you acknowledge Medroid's 
                            <a href="#" class="text-blue-600 hover:text-blue-500 underline">Privacy Policy</a>
                        </p>
                    </div>
                </form>
            </div>
        </div>

        <!-- Right Panel - Chat Demo -->
        <div class="hidden lg:flex lg:w-1/2 bg-gray-100 flex-col">
            <!-- Chat Header -->
            <div class="bg-white p-6 border-b border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Experience Medroid</h3>
                <p class="text-gray-600 text-sm">Chat with our AI doctor for instant health insights</p>
            </div>

            <!-- Chat Messages -->
            <div class="flex-1 p-6 overflow-y-auto space-y-4">
                <div 
                    v-for="(message, index) in chatMessages" 
                    :key="index"
                    :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'"
                >
                    <div 
                        :class="[
                            'max-w-xs lg:max-w-md px-4 py-3 rounded-2xl text-sm',
                            message.type === 'user' 
                                ? 'bg-blue-600 text-white rounded-br-md' 
                                : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md shadow-sm'
                        ]"
                    >
                        {{ message.text }}
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-6 bg-white border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <input
                        type="text"
                        placeholder="Type your health question here..."
                        class="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        disabled
                    />
                    <button 
                        class="bg-orange-400 hover:bg-orange-500 text-white p-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                        disabled
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- Learn More Link -->
                <div class="mt-4 text-center">
                    <a href="#" class="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200">
                        Learn more about Medroid →
                    </a>
                </div>
            </div>

            <!-- Version Info -->
            <div class="absolute bottom-4 right-4 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                v1.0.27 (2025-05-30)
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Custom animations can be added here if needed */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>