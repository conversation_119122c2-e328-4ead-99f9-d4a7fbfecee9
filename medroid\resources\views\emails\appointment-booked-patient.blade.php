@extends('emails.layouts.app')

@section('content')
    <h1>Your Appointment Has Been Booked</h1>
    
    <p>Hello {{ $patient->name }},</p>
    
    <p>Your appointment with Dr. {{ $provider->name }} has been successfully booked.</p>
    
    <div class="appointment-details">
        <p><strong>Date:</strong> {{ $date }}</p>
        <p><strong>Time:</strong> {{ $startTime }} - {{ $endTime }}</p>
        <p><strong>Provider:</strong> Dr. {{ $provider->name }}</p>
        <p><strong>Reason:</strong> {{ $appointment->reason }}</p>
        @if($appointment->is_telemedicine)
            <p><strong>Type:</strong> Telemedicine (Video Consultation)</p>
        @else
            <p><strong>Type:</strong> In-person Visit</p>
        @endif
        <p><strong>Status:</strong> {{ ucfirst($appointmentStatus) }}</p>
        <p><strong>Payment Status:</strong> {{ ucfirst($paymentStatus) }}</p>
    </div>
    
    @if($appointmentStatus == 'pending_payment')
        <p>Please complete your payment to confirm this appointment. You can do this through the Medroid app.</p>
        
        <p style="text-align: center; margin: 25px 0;">
            <a href="{{ config('app.url') }}/app/appointments/{{ $appointment->id }}/payment" class="btn">Complete Payment</a>
        </p>
    @endif
    
    <p>You can view and manage your appointments in the Medroid app.</p>
    
    @if($appointment->is_telemedicine)
        <p>For telemedicine appointments, you'll receive a link to join the video consultation 15 minutes before your scheduled time.</p>
    @endif
    
    <p>If you need to reschedule or cancel this appointment, please do so at least 24 hours in advance.</p>
    
    <p>Thank you for choosing Medroid Health Care for your healthcare needs.</p>
    
    <p>Best regards,<br>
    The Medroid Health Care Team</p>
@endsection
