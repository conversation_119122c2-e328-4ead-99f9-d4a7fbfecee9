@extends('emails.layouts.app')

@section('content')
    <h1>New Appointment Booked</h1>
    
    <p>Hello Dr. {{ $provider->name }},</p>
    
    <p>A new appointment has been booked with you by {{ $patient->name }}.</p>
    
    <div class="appointment-details">
        <p><strong>Date:</strong> {{ $date }}</p>
        <p><strong>Time:</strong> {{ $startTime }} - {{ $endTime }}</p>
        <p><strong>Patient:</strong> {{ $patient->name }}</p>
        <p><strong>Reason:</strong> {{ $appointment->reason }}</p>
        @if($appointment->is_telemedicine)
            <p><strong>Type:</strong> Telemedicine (Video Consultation)</p>
        @else
            <p><strong>Type:</strong> In-person Visit</p>
        @endif
        <p><strong>Status:</strong> {{ ucfirst($appointmentStatus) }}</p>
        <p><strong>Payment Status:</strong> {{ ucfirst($paymentStatus) }}</p>
    </div>
    
    <p>You can view and manage your appointments in your Medroid provider dashboard.</p>
    
    @if($appointment->is_telemedicine)
        <p>For telemedicine appointments, you'll receive a link to join the video consultation 15 minutes before the scheduled time.</p>
    @endif
    
    <p>If you need to reschedule or cancel this appointment, please do so at least 24 hours in advance and notify the patient.</p>
    
    <p>Thank you for being part of the Medroid Health Care provider network.</p>
    
    <p>Best regards,<br>
    The Medroid Health Care Team</p>
@endsection
