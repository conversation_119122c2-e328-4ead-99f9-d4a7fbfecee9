@extends('emails.layouts.app')

@section('content')
    <h1>Your Appointment Payment Has Been Confirmed</h1>
    
    <p>Hello {{ $patient->name }},</p>
    
    <p>Your payment for the appointment with Dr. {{ $provider->name }} has been successfully processed and your appointment is now confirmed.</p>
    
    <div class="appointment-details">
        <p><strong>Date:</strong> {{ $date }}</p>
        <p><strong>Time:</strong> {{ $startTime }} - {{ $endTime }}</p>
        <p><strong>Provider:</strong> Dr. {{ $provider->name }}</p>
        <p><strong>Reason:</strong> {{ $appointment->reason }}</p>
        @if($appointment->is_telemedicine)
            <p><strong>Type:</strong> Telemedicine (Video Consultation)</p>
        @else
            <p><strong>Type:</strong> In-person Visit</p>
        @endif
        <p><strong>Status:</strong> Scheduled</p>
    </div>
    
    <div class="appointment-details">
        <h3>Payment Details</h3>
        <p><strong>Amount:</strong> ${{ number_format($payment->amount, 2) }}</p>
        <p><strong>Payment Date:</strong> {{ $payment->paid_at->format('F j, Y') }}</p>
        <p><strong>Payment Method:</strong> {{ ucfirst($payment->payment_method_type) }}</p>
        <p><strong>Transaction ID:</strong> {{ $payment->payment_id }}</p>
    </div>
    
    <p>You can view and manage your appointments in the Medroid app.</p>
    
    @if($appointment->is_telemedicine)
        <p>For telemedicine appointments, you'll receive a link to join the video consultation 15 minutes before your scheduled time.</p>
    @endif
    
    <p>If you need to reschedule or cancel this appointment, please do so at least 24 hours in advance.</p>
    
    <p>Thank you for choosing Medroid Health Care for your healthcare needs.</p>
    
    <p>Best regards,<br>
    The Medroid Health Care Team</p>
@endsection
