@extends('emails.layouts.app')

@section('content')
    <h1>Appointment Rescheduled</h1>
    
    <p>Hello Dr. {{ $provider->name }},</p>
    
    <p>An appointment with {{ $patient->name }} has been rescheduled to a new date and time.</p>
    
    <div class="appointment-details">
        <h3>Previous Appointment</h3>
        <p><strong>Date:</strong> {{ $oldDate }}</p>
        <p><strong>Time:</strong> {{ $oldStartTime }} - {{ $oldEndTime }}</p>
    </div>
    
    <div class="appointment-details">
        <h3>New Appointment</h3>
        <p><strong>Date:</strong> {{ $newDate }}</p>
        <p><strong>Time:</strong> {{ $newStartTime }} - {{ $newEndTime }}</p>
        <p><strong>Patient:</strong> {{ $patient->name }}</p>
        <p><strong>Reason:</strong> {{ $appointment->reason }}</p>
        @if($appointment->is_telemedicine)
            <p><strong>Type:</strong> Telemedicine (Video Consultation)</p>
        @else
            <p><strong>Type:</strong> In-person Visit</p>
        @endif
        <p><strong>Status:</strong> {{ ucfirst($appointment->status) }}</p>
    </div>
    
    <p>You can view and manage your appointments in your Medroid provider dashboard.</p>
    
    @if($appointment->is_telemedicine)
        <p>For telemedicine appointments, you'll receive a link to join the video consultation 15 minutes before the scheduled time.</p>
    @endif
    
    <p>If you need to reschedule or cancel this appointment, please do so at least 24 hours in advance and notify the patient.</p>
    
    <p>Thank you for being part of the Medroid Health Care provider network.</p>
    
    <p>Best regards,<br>
    The Medroid Health Care Team</p>
@endsection
