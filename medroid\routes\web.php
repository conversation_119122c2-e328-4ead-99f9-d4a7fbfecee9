<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\VideoConsultationController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProviderAvailabilityController;
use App\Http\Controllers\AnalyticsController;
use Inertia\Inertia;

Route::get('/', [WebController::class, 'index'])->name('home');

// Anonymous chat route - no authentication required
Route::get('anonymous-chat', [WebController::class, 'anonymousChat'])->name('anonymous-chat');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->name('dashboard');

// Dashboard data routes for authenticated users
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
    Route::get('provider/dashboard-data', [\App\Http\Controllers\ProviderDashboardController::class, 'getDashboardData'])->name('provider.dashboard.data');
    Route::get('management/dashboard/kpi', [\App\Http\Controllers\AnalyticsController::class, 'getKpiMetrics'])->name('management.dashboard.kpi');
    Route::get('appointments-list', [AppointmentController::class, 'userAppointments'])->name('appointments.user');
    Route::get('provider/get-availability', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getWeeklyAvailability'])->name('provider.availability.get');
    Route::get('provider/get-absences', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getAbsences'])->name('provider.absences.get');
});

// Healthcare routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('providers', [ProviderController::class, 'webIndex'])->name('providers');
    Route::get('providers/{provider}', [ProviderController::class, 'webShow'])->name('providers.show');

    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('appointments');
    Route::get('appointments/create', [AppointmentController::class, 'webCreate'])->name('appointments.create');
    Route::get('appointments/{appointment}', [AppointmentController::class, 'webShow'])->name('appointments.show');
    Route::get('appointments/{appointment}/edit', [AppointmentController::class, 'webEdit'])->name('appointments.edit');

    // Video consultation web routes (for webapp)
    Route::post('video/initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession'])->name('video.initialize');
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData'])->name('video.session');
    Route::post('video/join/{appointmentId}', [VideoConsultationController::class, 'joinSession'])->name('video.join');
    Route::post('video/leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession'])->name('video.leave');
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus'])->name('video.status');
    Route::post('video/participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected'])->name('video.participant-disconnected');
    Route::post('video/end/{appointmentId}', [VideoConsultationController::class, 'endSession'])->name('video.end');

    Route::get('chat', [ChatController::class, 'webIndex'])->name('chat');
});

// Provider-specific routes
Route::middleware(['auth', 'verified', 'role:provider'])->prefix('provider')->group(function () {
    Route::get('availability', function () {
        return Inertia::render('Provider/Availability');
    })->name('provider.availability');

    Route::get('services', function () {
        return Inertia::render('Provider/Services');
    })->name('provider.services');

    Route::get('schedule', function () {
        return Inertia::render('Provider/Schedule');
    })->name('provider.schedule');

    Route::get('patients', function () {
        return Inertia::render('Provider/Patients');
    })->name('provider.patients');

    Route::get('earnings', function () {
        return Inertia::render('Provider/Earnings');
    })->name('provider.earnings');

    Route::get('profile', function () {
        return Inertia::render('Provider/Profile');
    })->name('provider.profile');
});

// Management routes - protected by role-based permissions
Route::middleware(['auth', 'verified'])->group(function () {
    // User management - requires 'view users' permission
    Route::get('users', function () {
        return Inertia::render('Users');
    })->name('users')->middleware('permission:view users');

    // Provider management - requires 'view providers' permission
    Route::get('providers', function () {
        return Inertia::render('Providers');
    })->name('providers.manage')->middleware('permission:view providers');

    // Patient management - requires 'view patients' permission
    Route::get('patients', function () {
        return Inertia::render('Patients');
    })->name('patients')->middleware('permission:view patients');

    // Appointment management - requires 'view appointments' permission
    Route::get('appointments', function () {
        return Inertia::render('Appointments');
    })->name('appointments.manage')->middleware('permission:view appointments');

    // Payment management - requires 'view payments' permission
    Route::get('payments', function () {
        return Inertia::render('Payments');
    })->name('payments')->middleware('permission:view payments');

    // Chat management - requires 'view chats' permission
    Route::get('chats', function () {
        return Inertia::render('Chats');
    })->name('chats.manage')->middleware('permission:view chats');

    // Permission management - requires 'manage permissions' permission
    Route::get('permissions', function () {
        return Inertia::render('Permissions');
    })->name('permissions')->middleware('permission:manage permissions');

    // Email template management - requires 'manage email templates' permission
    Route::get('email-templates', function () {
        return Inertia::render('EmailTemplates');
    })->name('email-templates')->middleware('permission:manage email templates');

    // Notification management - requires 'manage notifications' permission
    Route::get('notifications', function () {
        return Inertia::render('Notifications');
    })->name('notifications')->middleware('permission:manage notifications');

    // Service management - requires 'manage services' permission
    Route::get('services', function () {
        return Inertia::render('Services');
    })->name('services')->middleware('permission:manage services');
});

// API routes for web application (session-based authentication)
Route::middleware(['auth', 'verified'])->group(function () {
    // Services API routes
    Route::get('services', [ServiceController::class, 'index']);
    Route::get('services/{id}', [ServiceController::class, 'show']);
    Route::get('services/category/{category}', [ServiceController::class, 'getByCategory']);
    Route::get('service-categories', [ServiceController::class, 'getCategories']);

    // Providers API routes
    Route::get('providers', [ProviderController::class, 'index']);
    Route::get('providers/{id}', [ProviderController::class, 'show']);

    // Provider availability API routes
    Route::middleware('role:provider')->prefix('provider')->group(function () {
        Route::get('availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::post('availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);
        Route::put('availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);

        // Provider absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);

        // Provider dashboard data
        Route::get('dashboard-data', [ProviderController::class, 'getDashboardData']);
        Route::get('appointments', [ProviderController::class, 'getAppointments']);
        Route::get('patients', [ProviderController::class, 'getPatients']);

        // Provider profile
        Route::get('profile', [ProviderController::class, 'getProfile']);
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);
    });

    // Management dashboard API routes
    Route::middleware('permission:view dashboard')->prefix('management')->group(function () {
        Route::prefix('dashboard')->group(function () {
            Route::get('kpi', [AnalyticsController::class, 'getKpiMetrics']);
        });
    });

    // General appointment API routes
    Route::get('appointments/api', [AppointmentController::class, 'getUserAppointmentsApi']);
    Route::post('appointments/api', [AppointmentController::class, 'createAppointmentApi']);
    Route::delete('appointments/{appointmentId}/api', [AppointmentController::class, 'cancelAppointmentApi']);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
