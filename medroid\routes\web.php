<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\VideoConsultationController;
use App\Http\Controllers\Admin\AdminController;
use Inertia\Inertia;

Route::get('/', [WebController::class, 'index'])->name('home');

// Anonymous chat route - no authentication required
Route::get('anonymous-chat', [WebController::class, 'anonymousChat'])->name('anonymous-chat');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->name('dashboard');

// Dashboard data routes for authenticated users
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
    Route::get('provider/dashboard-data', [\App\Http\Controllers\ProviderDashboardController::class, 'getDashboardData'])->name('provider.dashboard.data');
    Route::get('management/dashboard/kpi', [\App\Http\Controllers\AnalyticsController::class, 'getKpiMetrics'])->name('management.dashboard.kpi');
    Route::get('appointments-list', [AppointmentController::class, 'userAppointments'])->name('appointments.user');
    Route::get('provider/get-availability', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getWeeklyAvailability'])->name('provider.availability.get');
    Route::get('provider/get-absences', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getAbsences'])->name('provider.absences.get');
});

// Healthcare routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('providers', [ProviderController::class, 'webIndex'])->name('providers');
    Route::get('providers/{provider}', [ProviderController::class, 'webShow'])->name('providers.show');

    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('appointments');
    Route::get('appointments/create', [AppointmentController::class, 'webCreate'])->name('appointments.create');
    Route::get('appointments/{appointment}', [AppointmentController::class, 'webShow'])->name('appointments.show');
    Route::get('appointments/{appointment}/edit', [AppointmentController::class, 'webEdit'])->name('appointments.edit');

    // Debug route to check appointments and users
    Route::get('debug/appointments', function () {
        $user = auth()->user();
        $appointments = \App\Models\Appointment::with(['patient.user', 'provider.user', 'service'])->get();

        $issues = [];

        return response()->json([
            'current_user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'has_patient_profile' => $user->patient ? true : false,
                'has_provider_profile' => $user->provider ? true : false,
            ],
            'total_appointments' => $appointments->count(),
            'telemedicine_appointments' => $appointments->where('is_telemedicine', true)->count(),
            'appointments' => $appointments->map(function($a) use (&$issues) {
                $hasPatient = $a->patient && $a->patient->user;
                $hasProvider = $a->provider && $a->provider->user;

                if (!$hasPatient) {
                    $issues[] = "Appointment {$a->id} missing patient or patient user";
                }
                if (!$hasProvider) {
                    $issues[] = "Appointment {$a->id} missing provider or provider user";
                }

                return [
                    'id' => $a->id,
                    'patient_name' => $hasPatient ? $a->patient->user->name : 'MISSING',
                    'patient_id' => $a->patient_id,
                    'provider_name' => $hasProvider ? $a->provider->user->name : 'MISSING',
                    'provider_id' => $a->provider_id,
                    'service_name' => $a->service ? $a->service->name : 'No Service',
                    'is_telemedicine' => $a->is_telemedicine,
                    'status' => $a->status,
                    'date' => $a->date,
                    'has_patient' => $hasPatient,
                    'has_provider' => $hasProvider,
                ];
            }),
            'data_issues' => $issues
        ]);
    })->name('debug.appointments');

    // Fix missing patient/provider profiles
    Route::get('debug/fix-profiles', function () {
        $fixed = [];

        // Create missing patient profiles
        $usersWithoutPatient = \App\Models\User::where('role', 'patient')
            ->whereDoesntHave('patient')
            ->get();

        foreach ($usersWithoutPatient as $user) {
            \App\Models\Patient::create([
                'user_id' => $user->id,
                'gender' => 'unknown',
                'date_of_birth' => '1990-01-01',
                'health_history' => [],
                'allergies' => [],
                'medications' => [],
                'medical_conditions' => [],
                'preferences' => [],
                'appointment_preferences' => [],
            ]);
            $fixed[] = "Created patient profile for user {$user->name}";
        }

        // Create missing provider profiles
        $usersWithoutProvider = \App\Models\User::where('role', 'provider')
            ->whereDoesntHave('provider')
            ->get();

        foreach ($usersWithoutProvider as $user) {
            \App\Models\Provider::create([
                'user_id' => $user->id,
                'specialization' => 'General Practice',
                'license_number' => 'LIC' . str_pad($user->id, 6, '0', STR_PAD_LEFT),
                'bio' => 'Healthcare provider',
                'education' => 'Medical Degree',
                'years_of_experience' => 5,
                'gender' => 'unknown',
                'languages' => ['English'],
                'accepts_insurance' => true,
                'insurance_providers' => [],
                'practice_locations' => [],
                'weekly_availability' => [],
            ]);
            $fixed[] = "Created provider profile for user {$user->name}";
        }

        return response()->json([
            'message' => 'Profile fix completed',
            'fixes_applied' => $fixed
        ]);
    })->name('debug.fix-profiles');

    // Create test scheduled telemedicine appointments
    Route::get('debug/create-test-appointments', function () {
        $created = [];

        // Get some patients and providers
        $patients = \App\Models\Patient::with('user')->take(2)->get();
        $providers = \App\Models\Provider::with('user')->take(2)->get();

        if ($patients->count() > 0 && $providers->count() > 0) {
            // Create appointments starting 10 minutes from now
            $startTime = now()->addMinutes(10);

            // Create 2 scheduled telemedicine appointments
            for ($i = 0; $i < 2; $i++) {
                $patient = $patients->random();
                $provider = $providers->random();

                $appointmentTime = $startTime->copy()->addMinutes($i * 30); // 30 minutes apart

                $appointment = \App\Models\Appointment::create([
                    'patient_id' => $patient->id,
                    'provider_id' => $provider->id,
                    'service_id' => null,
                    'date' => $appointmentTime->format('Y-m-d'),
                    'time_slot' => [
                        'start_time' => $appointmentTime->format('H:i'),
                        'end_time' => $appointmentTime->copy()->addHour()->format('H:i')
                    ],
                    'status' => 'scheduled',
                    'is_telemedicine' => true,
                    'amount' => 150.00,
                    'reason' => 'Video Consultation Test',
                    'notes' => 'Test appointment for video calling - Ready for testing!',
                ]);

                $created[] = "Created scheduled telemedicine appointment ID {$appointment->id} - Patient: {$patient->user->name}, Provider: {$provider->user->name}, Time: {$appointmentTime->format('Y-m-d H:i')}";
            }
        }

        return response()->json([
            'message' => 'Test appointments created for immediate testing',
            'current_time' => now()->format('Y-m-d H:i:s'),
            'first_appointment_time' => now()->addMinutes(10)->format('Y-m-d H:i:s'),
            'created' => $created
        ]);
    })->name('debug.create-test-appointments');


    // Video consultation web routes (for webapp)
    Route::post('video/initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession'])->name('video.initialize');
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData'])->name('video.session');
    Route::post('video/join/{appointmentId}', [VideoConsultationController::class, 'joinSession'])->name('video.join');
    Route::post('video/leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession'])->name('video.leave');
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus'])->name('video.status');
    Route::post('video/participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected'])->name('video.participant-disconnected');
    Route::post('video/end/{appointmentId}', [VideoConsultationController::class, 'endSession'])->name('video.end');

    Route::get('chat', [ChatController::class, 'webIndex'])->name('chat');
});

// Provider-specific routes
Route::middleware(['auth', 'verified', 'role:provider'])->prefix('provider')->group(function () {
    Route::get('availability', function () {
        return Inertia::render('Provider/Availability');
    })->name('provider.availability');

    Route::get('services', function () {
        return Inertia::render('Provider/Services');
    })->name('provider.services');

    Route::get('schedule', function () {
        return Inertia::render('Provider/Schedule');
    })->name('provider.schedule');

    Route::get('patients', function () {
        return Inertia::render('Provider/Patients');
    })->name('provider.patients');

    Route::get('earnings', function () {
        return Inertia::render('Provider/Earnings');
    })->name('provider.earnings');

    Route::get('profile', function () {
        return Inertia::render('Provider/Profile');
    })->name('provider.profile');
});

// Management routes - protected by role-based permissions
Route::middleware(['auth', 'verified'])->group(function () {
    // User management - requires 'view users' permission
    Route::get('users', function () {
        return Inertia::render('Users');
    })->name('users')->middleware('permission:view users');

    // Provider management - requires 'view providers' permission
    Route::get('providers', function () {
        return Inertia::render('Providers');
    })->name('providers.manage')->middleware('permission:view providers');

    // Patient management - requires 'view patients' permission
    Route::get('patients', function () {
        return Inertia::render('Patients');
    })->name('patients')->middleware('permission:view patients');

    // Appointment management - requires 'view appointments' permission
    Route::get('appointments', function () {
        return Inertia::render('Appointments');
    })->name('appointments.manage')->middleware('permission:view appointments');

    // Payment management - requires 'view payments' permission
    Route::get('payments', function () {
        return Inertia::render('Payments');
    })->name('payments')->middleware('permission:view payments');

    // Chat management - requires 'view chats' permission
    Route::get('chats', function () {
        return Inertia::render('Chats');
    })->name('chats.manage')->middleware('permission:view chats');

    // Permission management - requires 'manage permissions' permission
    Route::get('permissions', function () {
        return Inertia::render('Permissions');
    })->name('permissions')->middleware('permission:manage permissions');

    // Email template management - requires 'manage email templates' permission
    Route::get('email-templates', function () {
        return Inertia::render('EmailTemplates');
    })->name('email-templates')->middleware('permission:manage email templates');

    // Notification management - requires 'manage notifications' permission
    Route::get('notifications', function () {
        return Inertia::render('Notifications');
    })->name('notifications')->middleware('permission:manage notifications');

    // Service management - requires 'manage services' permission
    Route::get('services', function () {
        return Inertia::render('Services');
    })->name('services')->middleware('permission:manage services');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
