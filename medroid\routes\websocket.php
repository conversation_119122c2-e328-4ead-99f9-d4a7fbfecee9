<?php

use App\Services\SignalingService;
use Ratchet\Http\HttpServer;
use Ratchet\Server\IoServer;
use Ratchet\WebSocket\WsServer;

/*
|--------------------------------------------------------------------------
| WebSocket Routes
|--------------------------------------------------------------------------
|
| Here is where you can register WebSocket routes for your application.
|
*/

$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new SignalingService()
        )
    ),
    8080
);

$server->run();
